#广告标签关联表 2025-02-08 16:00:00(彭会闯)
CREATE TABLE `sohu_ad_info_label_relation` (
       `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
       `ad_info_id` bigint NOT NULL COMMENT '广告id',
       `label_id` bigint NOT NULL COMMENT '标签id',
       `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
       PRIMARY KEY (`id`),
       UNIQUE KEY `unique_adinfo_label_type_id` (`ad_info_id`,`label_id`)
) COMMENT='广告标签关联表';

#广告新增字段 025-02-10 10:00:00(彭会闯)
ALTER TABLE sohu_ad_info ADD COLUMN `show_form` varchar(10) DEFAULT NULL COMMENT '展现形式:[IMAGE:纯图片，NATIVE:原生广告]';
ALTER TABLE sohu_ad_info ADD COLUMN `ad_txt` varchar(100) DEFAULT NULL COMMENT '广告文案';
# 2025-02-10 14:21:00 雷博
CREATE TABLE `sohu_busy_task_user` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `user_id` bigint NOT NULL COMMENT '作者id',
    `busy_task_id` bigint DEFAULT NULL COMMENT '主任务ID',
    `task_number` varchar(30) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主任务编号',
    `title` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
    `is_collect` tinyint(1) DEFAULT '0' COMMENT '是否收藏 0.否 1.是',
    `create_by` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商单观看或收藏记录表';

ALTER TABLE sohu_audit ADD COLUMN `apply_msg` mediumtext COLLATE utf8mb4_general_ci COMMENT '申请理由';
ALTER TABLE sohu_audit ADD COLUMN `apply_annex` text COLLATE utf8mb4_general_ci COMMENT '申请附件';

# 2025-02-11 14:21:00 汪伟
ALTER TABLE `sohu_busy_task_refund` ADD `user_id` bigint unsigned NOT NULL COMMENT '发起退款人ID' AFTER task_number;

# 菜单配置新增游戏功能 2025-02-08 17:30:00(汪伟)
INSERT INTO `ry-cloud`.`sohu_app_config` ( `name`, `icon`, `page_url`, `type`, `sort`, `del_flag`, `enable`, `is_edit`, `create_time`, `create_by`, `update_time`, `update_by`, `config_ext`) VALUES ('游戏', 'https://sohugloba.oss-cn-beijing.aliyuncs.com/app_image/function/game.png', 'GAME', 1, 9, '0', b'1', 1, '2025-01-03 11:55:12', ' 1', '2025-01-03 11:55:13', 'admin', NULL);
INSERT INTO `ry-cloud`.`sohu_app_config` ( `name`, `icon`, `page_url`, `type`, `sort`, `del_flag`, `enable`, `is_edit`, `create_time`, `create_by`, `update_time`, `update_by`, `config_ext`) VALUES ('游戏', 'https://sohugloba.oss-cn-beijing.aliyuncs.com/app_image/function/game.png', 'mine/game', 2, 9, '0', b'1', 1, '2025-01-03 11:55:12', ' 1', '2025-01-03 11:55:13', 'admin', NULL);
#新增保证金字段 2025-02-11 17:00:00(彭会闯)
ALTER TABLE sohu_account ADD `bail_state` varchar(30) DEFAULT 'WaitApprove' COMMENT '保证金（WaitApprove=待审核，Paid=已支付，Refunded=已退款）';
ALTER TABLE sohu_account ADD  `bail_source` varchar(30) CHARACTER DEFAULT NULL COMMENT '保证金来源（Online=线上支付，Offline=线下打款）';

#新增入驻字段 2025-02-11 17:00:00(彭会闯)
ALTER TABLE sohu_account_enter ADD `industry_type` varchar(30) DEFAULT NULL COMMENT '入驻行业';
ALTER TABLE sohu_account_enter ADD `industry_name` varchar(30) DEFAULT NULL COMMENT '入驻行业名称';
ALTER TABLE sohu_account_enter ADD `goods_type` varchar(255) DEFAULT NULL COMMENT '商品类型';


#广告新增字段 025-02-11 10:00:00(彭会闯)
ALTER TABLE sohu_ad_info ADD COLUMN `video_id` bigint DEFAULT NULL COMMENT '视频id';
ALTER TABLE sohu_ad_info ADD COLUMN `video_title` varchar(255) DEFAULT NULL COMMENT '视频标题';

#新增服务商保证金支付表 2025-02-11 17:00:00(汪伟)
CREATE TABLE `sohu_service_provider_pay` (
         `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
         `user_id` bigint unsigned DEFAULT NULL COMMENT '付款人ID',
         `account_enter_id` bigint unsigned DEFAULT NULL COMMENT '入驻记录Id',
         `order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单支付单号',
         `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三方支付订单号',
         `pay_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支付流水号',
         `pay_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付方式 Online 线上  Offline 线下',
         `pay_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'unpay' COMMENT '支付状态 Waitpay Paid,cancel,Refund-退款',
         `pay_amount` decimal(10,2) NOT NULL COMMENT '支付金额',
         `pay_time` datetime DEFAULT NULL COMMENT '实际支付时间',
         `voucher_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '凭证url',
         `charge_amount` decimal(10,2) DEFAULT NULL COMMENT '手续费',
         `refund_amount` decimal(10,2) DEFAULT NULL COMMENT '已退金额',
         `refund_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退单流水号',
         `pay_channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支付渠道：pc、mobile',
         `create_by` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者',
         `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
         `update_by` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人',
         `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
         `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
         PRIMARY KEY (`id`) USING BTREE,
         UNIQUE KEY `udx_orderno_paytype` (`order_no`,`pay_type`) USING BTREE,
         KEY `idx_paynumber` (`account_enter_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='服务商支付保证金';

CREATE TABLE `sohu_service_provider_refund` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
            `account_enter_id` bigint unsigned DEFAULT NULL COMMENT '入驻记录Id',
            `user_id` bigint unsigned NOT NULL COMMENT '发起退款人ID',
            `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三方支付流水号',
            `refund_order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退单编码',
            `refund_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '退款流水号',
            `refund_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '退款状态 success,fail',
            `refund_amount` decimal(10,2) NOT NULL COMMENT '退款金额',
            `refund_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退款方式 Online 线上  Offline 线下',
            `voucher_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '凭证url',
            `create_by` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建者',
            `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
            `update_by` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
            `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
            PRIMARY KEY (`id`) USING BTREE,
            UNIQUE KEY `udx_refundorderno` (`refund_order_no`) USING BTREE,
            KEY `idx_enterid` (`account_enter_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='服务商退款表';



#广告新增字段 025-02-11 10:00:00(彭会闯)
ALTER TABLE sohu_ad_info ADD COLUMN `video_id` bigint DEFAULT NULL COMMENT '视频id';
ALTER TABLE sohu_ad_info ADD COLUMN `video_title` varchar(255) DEFAULT NULL COMMENT '视频标题';

# 商单新增字段 2025-02-17 16:38:00 (雷博)
ALTER TABLE sohu_audit ADD COLUMN `pass_num` int NOT NULL DEFAULT '0' COMMENT '达标数量';

# 广告新增字段 2025-02-19 09:45:00 (雷博)
ALTER TABLE sohu_ad_info ADD COLUMN `extra_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附加图片地址';

# 发单时的ip地址
ALTER TABLE sohu_busy_task ADD COLUMN `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发单时ip定位地址';