package com.sohu.app.platformutils.weibo.utils;

import com.github.scribejava.apis.SinaWeiboApi20;
import com.github.scribejava.core.builder.ServiceBuilder;
import com.github.scribejava.core.model.OAuth2AccessToken;
import com.github.scribejava.core.oauth.OAuth20Service;
import com.sohu.app.platformutils.CommonUtils;
import com.sohu.app.platformutils.weibo.entity.model.Status;
import com.sohu.app.platformutils.weibo.entity.Timeline;
import com.sohu.app.platformutils.weibo.entity.http.ImageItem;
import lombok.extern.slf4j.Slf4j;
import java.net.URLEncoder;

/**
 * 微博工具类
 */
@Slf4j
public class WeiBoUtil {
    // access_token 为用户授权第三方接口调用的凭证
    public static final String accessTokenURL = "https://api.weibo.com/oauth2/access_token";
    public static final String authorizeURL = "https://api.weibo.com/oauth2/authorize";
    public static final String rmURL = "https://rm.api.weibo.com/2/";
    public static final String baseURL = "https://api.weibo.com/2/";
    public static final String rip = "**************";

    // 常量配置
    private static final String clientId = "1317256665";
    private static final String clientSecret = "81b0625f7252e3cb39692e5303f2a4cb";
    private static final String callbackURL = "https://world.sohuglobal.com";

    private static OAuth20Service service;
    public static OAuth20Service getService() {
        if (service == null) {
            service = new ServiceBuilder(clientId)
                    .apiSecret(clientSecret)
                    .callback(callbackURL)
                    .build(SinaWeiboApi20.instance());
        }
        return service;
    }

    /**
     * 在用户同意授权之后，会返回授权临时票据(code)给到我
     * @return codeUrl
     */
    public static String getCodeUrl(){
        /*
           https://api.weibo.com/oauth2/authorize?
           client_id=1317256665
           &redirect_uri=https://world.sohuglobal.com
        */
        String redirectUri = "https://world.sohuglobal.com";
        return "https://api.weibo.com/oauth2/authorize?client_id="+clientId+"&redirect_uri="+redirectUri;
    }

    public static String getAuthorizationUrl() {
        return getService().getAuthorizationUrl();
    }

    /**
     * 获取AccessToken
     * @param code 授权码
     * @return
     */
    public static OAuth2AccessToken getAccessToken(String code){
        OAuth2AccessToken accessToken = null;
        try {
            accessToken = getService().getAccessToken(code);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return accessToken;
    }

    /**
     * 发送一条不带图片的微博
     * @param accessToken
     * @param text 文案
     */
    public static void sendWeiBoMsg(String accessToken, String text){
        Timeline tm = new Timeline(accessToken);
        try {
            Status status = tm.updateStatusNew(URLEncoder.encode(text, "utf-8"));
            log.info(status.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 发送一条微博带图片
     * @param accessToken
     * @param text 文案
     * @param imgUrl 图片路径（目前仅支持png）：C:/Users/<USER>/Desktop/data/aaa.png
     */
    public static void sendWeiBoMsg(String accessToken, String text, String imgUrl){
        Timeline tm = new Timeline(accessToken);
        try {
            byte[] content = CommonUtils.imgToByte(imgUrl.replaceAll("/", "\\\\"));
            ImageItem pic = new ImageItem("pic", content);
            Status status = tm.uploadStatusNewPic(URLEncoder.encode(text, "utf-8"),pic);
            log.info(status.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
