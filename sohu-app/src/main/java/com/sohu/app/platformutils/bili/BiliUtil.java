package com.sohu.app.platformutils.bili;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.sohu.app.platformutils.CommonUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.RandomAccessFile;

/**
 * 哔哩站工具类
 */
@Slf4j
public class BiliUtil {

    private static final String clientId = "";
    private static final String clientSecret = "";

    /**
     * 在用户同意授权之后，会返回授权临时票据(code)给到我
     * @return codeUrl
     */
    public static String getCodeUrl(){
        /*
           https://passport.bilibili.com/register/pc_oauth2.html#/?
           client_id=${client_id}
           &return_url=${return_url}
           &response_type=code
           &state=${state}
        */
        String returnUrl = "https://world.sohuglobal.com";
        // state为随机字符串，认证服务器会原封不动地返回这个值，用于防止跨站请求伪造攻击
        String state = "1";
        return "https://passport.bilibili.com/register/pc_oauth2.html#/?client_id="+clientId+"&return_url="+returnUrl+"&response_type=code"+"&state="+state;
    }

    /**
     * 第三方使用授权临时票据(code)通过接口置换 access_token
     * @param code 网页登陆后授权码
     * @return oauthResult
     */
    public static JSONObject getAccessToken(String code){
        String params = "client_id="+clientId+"&client_secret="+clientSecret+"&grant_type=authorization_code"+"&code="+code;
        String result = HttpRequest.post("https://api.bilibili.com/x/account-oauth2/v1/token?" + params)
                .header("Content-Type","application/x-www-form-urlencoded").execute().body();
        if (result != null && result.trim().contains("\"code\":0")) {
            JSONObject json = JSONObject.parseObject(result);
            return json;
        }else {
            log.info("获取哔哩站AccessToken失败getAccessToken：{}",result);
            return null;
        }
    }

    /**
     * 描述：该接口用于初始化上传资源，后续文件上传及稿件提交所需部分参数通过该接口获取
     * 1.文件不超过100M时，可通过单个小视频文件上传流程进行交互
     * 调用文件上传预处理接口，参数utype设置为”1”
     * 调用单个小视频文件上传接口
     * 调用稿件提交接口
     * 2.文件超过100M时，须通过分片上传流程进行交互
     * 调用文件上传预处理接口，参数utype设置为”0”
     * 调用文件分片上传接口
     * 调用文件分片合片接口
     * 调用稿件提交接口
     * @param accessToken oauth 令牌
     * @param name 文件名字，需携带正确的扩展名，例如test.mp4
     * @param uType 上传类型：0-多分片，1-单个小文件（不超过100M）。默认值为0
     * @return startUploadResult
     */
    private static JSONObject startUpload(String accessToken,String name,String uType){
        String params = "access_token="+accessToken+"&client_id="+clientId;
        JSONObject body = new JSONObject();
        body.put("name",name);
        body.put("utype",uType);
        String result = HttpRequest.post("https://member.bilibili.com/arcopen/fn/archive/video/init?"+params)
                .header("Content-Type","application/json")
                .body(body.toJSONString()).execute().body();
        if (result != null && result.trim().contains("\"code\":0")) {
            JSONObject json = JSONObject.parseObject(result);
            return json;
        }else {
            log.info("哔哩站发起上传失败startUpload：{}",result);
            return null;
        }
    }

    /**
     * 分片上传视频（上传的文件过大的时候）
     * @param uploadToken 用户唯一标识
     * @param partNumber 分片id 从1开始
     * @param body 传递二进制数据
     * @return uploadListResult
     */
    private static JSONObject uploadList(String uploadToken,int partNumber,byte[] body){
        String params = "upload_token="+uploadToken+"&part_number="+partNumber;
        String result = HttpRequest.post("https://openupos.bilivideo.com/video/v2/part/upload?"+params)
                .header("Content-Type","application/json")
                .body(body).execute().body();
        if (result != null && result.trim().contains("\"code\":0")) {
            JSONObject json = JSONObject.parseObject(result);
            log.info("哔哩站分片{}上传成功：{}",partNumber,result);
            return json;
        }else {
            log.info("哔哩站分片上传视频失败uploadList：{}",result);
            return null;
        }
    }

    /**
     * 分片上传视频
     * 1.上传分片 2.合片
     * @param uploadToken 用户唯一标识
     * @param videoPath 视频路径
     * @return uploadFragmentResult
     */
    public static JSONObject uploadFragment(String uploadToken,String videoPath){
        try {
            File inputVideoFile = CommonUtils.urlToFile(videoPath);
            long fileSizeInBytes = inputVideoFile.length();
            if (fileSizeInBytes > Integer.MAX_VALUE) {
                log.info("视频文件太大无法处理");
                return null;
            }
            int splitSizeInBytes = 8 * 1024 * 1024; // 每个切片的大小（单位字节）按20M分
            byte[] buffer = new byte[splitSizeInBytes];
            // fragmentId 分片id 从0开始
            int fragmentCount = 0;
            try (RandomAccessFile raf = new RandomAccessFile(inputVideoFile, "r")) {
                // 分片上传
                for (int i = 0; i < fileSizeInBytes / splitSizeInBytes + 1; ++i) {
                    int bytesRead;
                    while ((bytesRead = raf.read(buffer)) != -1 && bytesRead >= splitSizeInBytes) {
                        uploadList(uploadToken, fragmentCount,buffer);
                        fragmentCount++;
                    }
                    if (bytesRead != -1) {
                        uploadList(uploadToken, fragmentCount,buffer);
                        fragmentCount++;
                    }
                }
            }
            // 完成上传
            String params = "upload_token="+uploadToken;
            String result = HttpRequest.post("https://member.bilibili.com/arcopen/fn/archive/video/complete?"+params)
                    .header("Content-Type","application/json")
                    .execute().body();
            if (result != null && result.trim().contains("\"code\":0")) {
                JSONObject json = JSONObject.parseObject(result);
                return json;
            }else {
                log.info("哔哩站分片上传视频失败uploadFragment：{}",result);
                return null;
            }
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 分区查询
     * @param accessToken oauth 令牌
     * @return typeResult
     */
    public static JSONObject typeList(String accessToken){
        String params = "access_token="+accessToken+"&client_id="+clientId;
        String result = HttpRequest.get("https://member.bilibili.com/arcopen/fn/archive/type/list?"+params).execute().body();
        if (result != null && result.trim().contains("\"code\":0")) {
            JSONObject json = JSONObject.parseObject(result);
            return json;
        }else {
            log.info("哔哩站分区查询失败typeList：{}",result);
            return null;
        }
    }

    /**
     * 封面上传
     * @param accessToken oauth 令牌
     * @param coverUrl 封面图片url
     * @return coverResult
     */
    public static JSONObject uploadCover(String accessToken,String coverUrl){
        String params = "access_token="+accessToken+"&client_id="+clientId;
        JSONObject body = new JSONObject();
        body.put("file",CommonUtils.urlToFile(coverUrl));
        String result = HttpRequest.post("https://member.bilibili.com/arcopen/fn/archive/cover/upload?"+params)
                .header("Content-Type","multipart/form-data")
                .form(body).execute().body();
        if (result != null && result.trim().contains("\"code\":0")) {
            JSONObject json = JSONObject.parseObject(result);
            return json;
        }else {
            log.info("哔哩站封面上传失败uploadCover：{}",result);
            return null;
        }
    }

    /**
     * 调用该接口完成稿件投递
     * 调用此接口前需要保证视频/封面上传完成
     * 稿件提交之后会存在审核过程，期间不对外开放
     * 非正式会员单日最多投递5个稿件，可在主站通过答题转为正式会员解除限制
     * @param uploadToken 用户唯一标识
     * @param accessToken oauth 令牌
     * @param title 稿件标题，长度小于80，短时间内标题不能相同
     * @param coverUrl 封面地址，必须由上传封面接口得到
     * @param tid 分区id，由获取分区信息接口得到
     * @return publishResult
     */
    public static JSONObject publish(String uploadToken,String accessToken,String title,String coverUrl,String tid){
        String params = "access_token="+accessToken+"&client_id="+clientId+"&upload_token="+uploadToken;
        JSONObject body = new JSONObject();
        body.put("cover",coverUrl);
        body.put("title",title);
        body.put("tid",tid);
        body.put("no_reprint",0);
        //body.put("desc","这是一个呀咩嘚的视频"); // 视频描述，长度小于250
        body.put("tag","生活"); // 视频标签，多个标签用英文逗号分隔，总长度小于200
        body.put("copyright",1); // 1-原创，2-转载(转载时source必填)
        //body.put("source","爱奇艺"); // 如果copyright为转载，则此字段表示转载来源
        //body.put("topic_id",123); // 参加的话题ID，默认情况下不填写，需要填写和运营联系
        String result = HttpRequest.post("https://member.bilibili.com/arcopen/fn/archive/add-by-utoken?"+params)
                .header("Content-Type","application/json")
                .form(body).execute().body();
        if (result != null && result.trim().contains("\"code\":0")) {
            JSONObject json = JSONObject.parseObject(result);
            return json;
        }else {
            log.info("哔哩站发布视频失败publish：{}",result);
            return null;
        }
    }
}
