package com.sohu.app.platformutils.weibo;
import com.github.scribejava.core.model.OAuth2AccessToken;
import com.sohu.app.platformutils.weibo.utils.WeiBoUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class WeiBoDemo {

    public static void main(String[] args) {
        try {
            // 获取code 获取accessToken 后期需做缓存 根据时间节点更新
            // https://api.weibo.com/oauth2/authorize?client_id=1317256665&redirect_uri=https://world.sohuglobal.com
            //OAuth2AccessToken accessToken = WeiBoUtil.getAccessToken("aa28fed378f0d4e1441103148febcc82");
            //log.info("accessToken={}",accessToken.getAccessToken());
            String text = "这是一条通过java调用的微博!https://world.sohuglobal.com";

            // 发送一条微博
            //WeiBoUtils.sendWeiBoMsg("2.00SjJY8I0hLpjf951ede4fdapqYiwB",text);

            // 发送一条带图片的微博
            WeiBoUtil.sendWeiBoMsg("2.00SjJY8IPdEJ8B2dab161495jEBbQD",text,"C:/Users/<USER>/Desktop/data/aaa.png");
        }catch (Exception e){
            e.printStackTrace();
        }
    }

}
