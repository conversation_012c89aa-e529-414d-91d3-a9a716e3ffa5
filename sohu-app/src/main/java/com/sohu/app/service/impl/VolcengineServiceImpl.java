package com.sohu.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.sohu.app.api.domain.bo.volcengine.SingleShowLogBo;
import com.sohu.app.api.domain.bo.volcengine.VideoOverLogBo;
import com.sohu.app.api.domain.bo.volcengine.VideoPlayLogBo;
import com.sohu.app.service.VolcengineService;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.DictEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.middle.api.vo.SohuContentMainVo;
import com.sohu.system.api.RemoteDictService;
import com.sohu.system.api.domain.SysDictData;
import com.volcengine.model.stream.*;
import com.volcengine.model.stream.consumer.MultiArticlesRequest;
import com.volcengine.model.stream.consumer.SingleArticleRequest;
import com.volcengine.model.stream.consumer.SingleArticleResponse;
import com.volcengine.model.stream.log.*;
import com.volcengine.service.stream.LogService;
import com.volcengine.service.stream.StreamConsumerService;
import com.volcengine.service.stream.StreamService;
import com.volcengine.service.stream.impl.LogServiceImpl;
import com.volcengine.service.stream.impl.StreamConsumerServiceImpl;
import com.volcengine.service.stream.impl.StreamServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Service
@Slf4j
public class VolcengineServiceImpl implements VolcengineService {

    @DubboReference
    private RemoteDictService remoteDictService;

    @Override
    public TableDataInfo<SohuContentMainVo> waterfall(Long loginUserId, String visitorId) {
        JSONObject object = getVolcEntries();
        String accessKey = object.getStr("accessKey");
        String secretKey = object.getStr("secretKey");
        String partner = object.getStr("partner");
        String category = object.getStr("category");

        //初始化一个service
        StreamService streamService = StreamServiceImpl.getInstance();
        //设置ak，sk 此为请求的凭证
        streamService.setAccessKey(accessKey);
        streamService.setSecretKey(secretKey);

        String accessToken = wapRegister(streamService, partner, loginUserId, visitorId);

        GetListResponse response = getList(streamService, partner, category, accessToken);
        List<GetListResponse.Result> results = response.getResult();

        List<SohuContentMainVo> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(results)) {
            List<String> likeIds = new ArrayList<>();
            List<String> collectIds = new ArrayList<>();
            if (loginUserId != null) {
//                List<Long> groupIds = results.stream().map(i -> i.getGroupId()).map(Long::new).collect(Collectors.toList());
//                List<SohuPerLikeCollectDO> likeList = sohuPerLikeCollectService.listBusy(OperType.Like.name(), LikeBusyType.Volcengine.name(), groupIds, loginUserId);
//                List<SohuPerLikeCollectDO> collectList = sohuPerLikeCollectService.listBusy(OperType.Collect.name(), LikeBusyType.Volcengine.name(), groupIds, loginUserId);
//                if (CollectionUtils.isNotEmpty(likeList)) {
//                    //是否点赞当前对象
//                    likeIds = likeList.stream().map(i -> i.getBusyCode()).map(String::valueOf).collect(Collectors.toList());
//                }
//                if (CollectionUtils.isNotEmpty(collectList)) {
//                    //是否收藏当前对象
//                    collectIds = collectList.stream().map(i -> i.getBusyCode()).map(String::valueOf).collect(Collectors.toList());
//                }
            }
            for (GetListResponse.Result result : results) {
                SohuContentMainVo vo = new SohuContentMainVo();
                if (CollectionUtils.isNotEmpty(likeIds)) {
                    //是否点赞当前对象
                    vo.setPraiseObj(likeIds.contains(result.getGroupId()));
                }
                if (CollectionUtils.isNotEmpty(collectIds)) {
                    //是否收藏当前对象
                    vo.setCollectObj(collectIds.contains(result.getGroupId()));
                }
                //链接封面内容标题
                vo.setId(Long.valueOf(result.getGroupId()));
                GetListResponse.VideoDetail videoDetail = result.getVideoDetail();
                if (Objects.isNull(videoDetail)) {
                    continue;
                }
                List<GetListResponse.VideoList> videoList = videoDetail.getVideoList();
                if (CollUtil.isEmpty(videoList)) {
                    log.warn("火山瀑布流错误：{}", JSONUtil.toJsonStr(result));
                    continue;
                }
                vo.setVideoUrl(videoList.get(0).getMainUrl());
                vo.setObjTitle(result.getTitle());
                vo.setArticleUrl(result.getArticleUrl());
                vo.setCommentUrl(result.getCommentUrl());
                vo.setObjContent(result.getAbstracts());
                vo.setCoverImage(result.getCoverImageList().get(0).getUrl());
                CommonPo.UserInfo userInfo = result.getUserInfo();
                vo.setUserAvatar(userInfo.getAvatarUrl());
                vo.setUserName(userInfo.getName());
                vo.setFollowUser(userInfo.isFollowed());
                vo.setPraiseCount((int) result.getDiggCount());
                vo.setCommentCount((int) result.getCommentCount());
                vo.setReqId(response.getResponseMetadata().getRequestId());
                // todo 构建用户是否点赞，收藏
                list.add(vo);
            }
        }
        return TableDataInfoUtils.build(list);
    }

    @Override
    public JSONObject getVolcEntries() {
        SysDictData dictData = remoteDictService.getDictData("system_config", DictEnum.Volc.getKey());
        if (dictData == null) {
            log.error("系统未配置微信APP登录配置");
            throw new ServiceException("volc 字典【volc】不存在 error");
        }
        String config = dictData.getDictValue();
        JSONObject object = new JSONObject();
        try {
            object = JSONUtil.parseObj(config);
        } catch (Exception e) {
            throw new ServiceException("volc 字典【volc】JSON解析错误 error");
        }
        if (StrUtil.isEmpty(object.getStr("accessKey")) || StrUtil.isEmpty(object.getStr("secretKey")) ||
            StrUtil.isEmpty(object.getStr("partner")) || StrUtil.isEmpty(object.getStr("category"))
        ) {
            throw new ServiceException("volc 字典【volc】值不存在 error");
        }
        return object;
    }

    @Override
    public LogResponse singleShowLog(SingleShowLogBo request, Long loginUserId) {
        JSONObject object = getVolcEntries();
        String accessKey = object.getStr("accessKey");
        String secretKey = object.getStr("secretKey");
        String partner = object.getStr("partner");
        String category = object.getStr("category");

        //初始化一个service
        StreamService streamService = StreamServiceImpl.getInstance();
        //设置ak，sk 此为请求的凭证
        streamService.setAccessKey(accessKey);
        streamService.setSecretKey(secretKey);

        String accessToken = wapRegister(streamService, partner, loginUserId, request.getVisitorId());

        //初始化一个service
        LogService logService = LogServiceImpl.getInstance();
        //设置ak，sk 此为请求的凭证
        logService.setAccessKey(accessKey);
        logService.setSecretKey(secretKey);

        SingleShowLogRequest req = new SingleShowLogRequest();
        BeanUtil.copyProperties(request, req);
        //初始化一个request
        req.setPartner(partner);
        req.setAccessToken(accessToken);
        req.setCategory(category);
        LogResponse response = null;
        try {
            response = logService.singleShowLog(req);
        } catch (Exception e) {
            throw new ServiceException("action singleShowLog error");
        }
        return response;
    }

    @Override
    public LogResponse videoPlayLog(VideoPlayLogBo request, Long loginUserId) {
        JSONObject object = getVolcEntries();
        String accessKey = object.getStr("accessKey");
        String secretKey = object.getStr("secretKey");
        String partner = object.getStr("partner");
        String category = object.getStr("category");

        //初始化一个service
        StreamService streamService = StreamServiceImpl.getInstance();
        //设置ak，sk 此为请求的凭证
        streamService.setAccessKey(accessKey);
        streamService.setSecretKey(secretKey);
        String accessToken = wapRegister(streamService, partner, loginUserId, request.getVisitorId());

        //初始化一个service
        LogService logService = LogServiceImpl.getInstance();
        //设置ak，sk 此为请求的凭证
        logService.setAccessKey(accessKey);
        logService.setSecretKey(secretKey);

        LogResponse response = null;
        //event::事件:[VideoPlayLog:手动/点击播放,VideoPlayAutoLog:自动播放,VideoPlayDrawLog:滑动播放]
        try {
            switch (request.getAction()) {
                case "VideoPlayLog":
                    VideoPlayLogRequest req = new VideoPlayLogRequest();
                    BeanUtil.copyProperties(request, req);
                    req.setPartner(partner);
                    req.setAccessToken(accessToken);
                    req.setCategory(category);
                    response = logService.videoPlayLog(req);
                    break;
                case "VideoPlayAutoLog":
                    VideoPlayAutoLogRequest req2 = new VideoPlayAutoLogRequest();
                    BeanUtil.copyProperties(request, req2);
                    req2.setPartner(partner);
                    req2.setAccessToken(accessToken);
                    req2.setCategory(category);
                    response = logService.videoPlayAutoLog(req2);
                    break;
                case "VideoPlayDrawLog":
                    VideoPlayDrawLogRequest req3 = new VideoPlayDrawLogRequest();
                    BeanUtil.copyProperties(request, req3);
                    req3.setPartner(partner);
                    req3.setCategory(category);
                    req3.setAccessToken(accessToken);
                    response = logService.videoPlayDrawLog(req3);
                    break;
                default:
                    throw new IllegalArgumentException();
            }
        } catch (IllegalArgumentException e) {
            throw new ServiceException("未知事件类型：" + request.getAction());
        } catch (Exception e) {
            throw new ServiceException("videoPlayLog err");
        }
        return response;
    }

    @Override
    public LogResponse videoOverLog(VideoOverLogBo request, Long loginUserId) {
        JSONObject object = getVolcEntries();
        String accessKey = object.getStr("accessKey");
        String secretKey = object.getStr("secretKey");
        String partner = object.getStr("partner");
        String category = object.getStr("category");

        //初始化一个service
        StreamService streamService = StreamServiceImpl.getInstance();
        //设置ak，sk 此为请求的凭证
        streamService.setAccessKey(accessKey);
        streamService.setSecretKey(secretKey);
        String accessToken = wapRegister(streamService, partner, loginUserId, request.getVisitorId());

        //初始化一个service
        LogService logService = LogServiceImpl.getInstance();
        //设置ak，sk 此为请求的凭证
        logService.setAccessKey(accessKey);
        logService.setSecretKey(secretKey);

        //event::事件:[VideoOverLog:手动/点击播放时长,VideoOverAutoLog:自动播放时长,VideoOverDrawLog:滑动播放时长
        LogResponse response = null;
        try {
            switch (request.getAction()) {
                case "VideoOverLog":
                    VideoOverLogRequest req = new VideoOverLogRequest();
                    BeanUtil.copyProperties(request, req);
                    req.setPartner(partner);
                    req.setAccessToken(accessToken);
                    req.setCategory(category);
                    response = logService.videoOverLog(req);
                    break;
                case "VideoOverAutoLog":
                    VideoOverAutoLogRequest req2 = new VideoOverAutoLogRequest();
                    BeanUtil.copyProperties(request, req2);
                    req2.setPartner(partner);
                    req2.setAccessToken(accessToken);
                    req2.setCategory(category);
                    response = logService.videoOverAutoLog(req2);
                    break;
                case "VideoOverDrawLog":
                    VideoOverDrawLogRequest req3 = new VideoOverDrawLogRequest();
                    BeanUtil.copyProperties(request, req3);
                    req3.setPartner(partner);
                    req3.setAccessToken(accessToken);
                    req3.setCategory(category);
                    response = logService.videoOverDrawLog(req3);
                    break;
                default:
                    throw new IllegalArgumentException();
            }
        } catch (IllegalArgumentException e) {
            throw new ServiceException("未知事件类型：" + request.getAction());
        } catch (Exception e) {
            throw new ServiceException("videoOverLog err");
        }

        return response;
    }

//    @Override
//    public void praise(Long id, Long loginUserId, Long siteId) {
//        if (id != null && id > 0L) {
//            sohuPerLikeCollectService.likeOrUnlike(LikeBusyType.Volcengine, id, 0L, loginUserId);
////            sohuProjectInstService.extracted(loginUserId, siteId);
//        }
//    }
//
//    @Override
//    public void collect(Long id, Long loginUserId, Long siteId) {
//        if (id != null && id > 0L) {
//            sohuPerLikeCollectService.collectOrUnCollect(LikeBusyType.Volcengine, id, 0L, loginUserId);
////            sohuProjectInstService.extracted(loginUserId, siteId);
//        }
//    }

    @Override
    public List<SohuContentMainVo> getList(Long loginUserId, List<String> groupIds) {
        JSONObject object = getVolcEntries();
        String accessKey = object.getStr("accessKey");
        String secretKey = object.getStr("secretKey");
        String partner = object.getStr("partner");
        String category = object.getStr("category");

        //初始化一个service
        StreamService streamService = StreamServiceImpl.getInstance();
        //设置ak，sk 此为请求的凭证
        streamService.setAccessKey(accessKey);
        streamService.setSecretKey(secretKey);
        String accessToken = wapRegister(streamService, partner, loginUserId, null);

        //初始化一个service
        StreamConsumerService streamConsumerService = StreamConsumerServiceImpl.getInstance();

        //设置ak，sk 此为请求的凭证
        streamConsumerService.setAccessKey(accessKey);
        streamConsumerService.setSecretKey(secretKey);
        List<SingleArticleResponse.Result> results = multiArticles(streamConsumerService, accessToken, partner, groupIds);

        List<SohuContentMainVo> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(results)) {
            for (SingleArticleResponse.Result result : results) {
                SohuContentMainVo vo = new SohuContentMainVo();
                //链接封面内容标题
                vo.setId(Long.valueOf(result.getGroupId()));
                SingleArticleResponse.VideoDetail videoDetail = result.getVideoDetail();
                if (Objects.isNull(videoDetail)) {
                    continue;
                }
                List<SingleArticleResponse.VideoList> videoList1 = videoDetail.getVideoList();
                if (CollUtil.isEmpty(videoList1)) {
                    log.warn("火山瀑布流错误：{}", JSONUtil.toJsonStr(result));
                    continue;
                }
                vo.setVideoUrl(videoList1.get(0).getMainUrl());
                vo.setArticleUrl(result.getArticleUrl());
                vo.setCommentUrl(result.getCommentUrl());
                vo.setObjTitle(result.getTitle());
                vo.setObjContent(result.getAbstracts());
                vo.setCoverImage(result.getCoverImageList().get(0).getUrl());
                CommonPo.UserInfo userInfo = result.getUserInfo();
                vo.setUserAvatar(userInfo.getAvatarUrl());
                vo.setUserName(userInfo.getName());
                vo.setFollowUser(userInfo.isFollowed());
                vo.setPraiseCount((int) result.getDiggCount());
                vo.setCommentCount((int) result.getCommentCount());
                vo.setObjType(BusyType.Volcengine.name());
//                List<GetUnionProductResponse.Result> productUnion = result.getProductUnion();
//                if (CollUtil.isNotEmpty(productUnion)) {
//                    GetUnionProductResponse.Result product = productUnion.get(0);
//                    String productType = product.getProductType();
//                    ContentMainRelateVO relateVO = new ContentMainRelateVO();
//                    if (StrUtil.equalsIgnoreCase("long_video", productType)) {
//                        relateVO.setBusyType(ContentMainRelateEnum.Video.name());
//                    } else {
//                        relateVO.setBusyType(ContentMainRelateEnum.Good.name());
//                    }
//                }
                // todo 构建用户是否点赞，收藏
                list.add(vo);
            }
        }
        return list;
    }

    @Override
    public SohuContentMainVo info(Long loginUserId, Long groupId) {
        JSONObject object = getVolcEntries();
        String accessKey = object.getStr("accessKey");
        String secretKey = object.getStr("secretKey");
        String partner = object.getStr("partner");
        String category = object.getStr("category");

        //初始化一个service
        StreamService streamService = StreamServiceImpl.getInstance();
        //设置ak，sk 此为请求的凭证
        streamService.setAccessKey(accessKey);
        streamService.setSecretKey(secretKey);
        String accessToken = wapRegister(streamService, partner, loginUserId, null);

        //初始化一个service
        StreamConsumerService streamConsumerService = StreamConsumerServiceImpl.getInstance();

        //设置ak，sk 此为请求的凭证
        streamConsumerService.setAccessKey(accessKey);
        streamConsumerService.setSecretKey(secretKey);
        SingleArticleResponse.Result result = singleArticle(streamConsumerService, accessToken, partner, groupId);
        SohuContentMainVo vo = new SohuContentMainVo();
        if (ObjectUtil.isNotEmpty(result)) {
            //链接封面内容标题
            vo.setId(groupId);
            SingleArticleResponse.VideoDetail videoDetail = result.getVideoDetail();
            if (Objects.isNull(videoDetail)) {
                return vo;
            }
            List<SingleArticleResponse.VideoList> videoList1 = videoDetail.getVideoList();
            if (CollUtil.isEmpty(videoList1)) {
                log.warn("火山瀑布流错误：{}", JSONUtil.toJsonStr(result));
                return vo;
            }
            vo.setVideoUrl(videoList1.get(0).getMainUrl());
            vo.setArticleUrl(result.getArticleUrl());
            vo.setCommentUrl(result.getCommentUrl());
            vo.setObjTitle(result.getTitle());
            vo.setObjContent(result.getAbstracts());
            vo.setCoverImage(result.getCoverImageList().get(0).getUrl());
            CommonPo.UserInfo userInfo = result.getUserInfo();
            vo.setUserAvatar(userInfo.getAvatarUrl());
            vo.setUserName(userInfo.getName());
            vo.setFollowUser(userInfo.isFollowed());
            vo.setPraiseCount((int) result.getDiggCount());
            vo.setCommentCount((int) result.getCommentCount());
        }
        return vo;
    }

    /**
     * 构建列表页-获取个性化内容
     *
     * @param partner     渠道号
     * @param groupIds    火山视频内容ID
     * @param accessToken 火山视频token
     * @return 视频响应对象
     */
    private static List<SingleArticleResponse.Result> multiArticles(StreamConsumerService streamConsumerService, String accessToken, String partner, List<String> groupIds) {
        //初始化一个request
        MultiArticlesRequest req = new MultiArticlesRequest();
        req.setTimestamp(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
        req.setAccessToken(accessToken);
        req.setPartner(partner);
        req.setGroupIds(groupIds);
        List<SingleArticleResponse.Result> result = null;
        try {
            result = streamConsumerService.multiArticles(req).getResult().getArticleInfos();
        } catch (Exception e) {
            throw new ServiceException("VLOC_MULTIARTICLES_WRONG_PARAMS");
        }
        return result;
    }

    /**
     * 构建列表页-获取单条视频内容
     *
     * @param partner     渠道号
     * @param groupIds    火山视频内容ID
     * @param accessToken 火山视频token
     * @return 视频响应对象
     */
    @Override
    public SingleArticleResponse.Result singleArticle(StreamConsumerService streamConsumerService, String accessToken, String partner, Long groupIds) {
        //初始化一个request
        SingleArticleRequest req = new SingleArticleRequest();
        req.setTimestamp(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
        req.setAccessToken(accessToken);
        req.setPartner(partner);
        req.setGroupId(String.valueOf(groupIds));
        SingleArticleResponse.Result result = null;
        try {
            result = streamConsumerService.singleArticle(req).getResult();
        } catch (Exception e) {
            throw new ServiceException("VLOC_SINGLEARTICLE_WRONG_PARAMS");
        }
        return result;
    }

    /**
     * 构建列表页-获取个性化内容
     *
     * @param partner     渠道号
     * @param category    频道ID
     * @param accessToken 火山视频token
     * @return 视频响应对象
     */
    private static GetListResponse getList(StreamService streamService, String partner, String category, String accessToken) {
        GetListRequest request = new GetListRequest();
        request.setPartner(partner);
        request.setAccessToken(accessToken);
        request.setTimestamp(System.currentTimeMillis() / 1000);
        request.setCategory(category);
        GetListResponse result = null;
        try {
            result = streamService.getList(request);
        } catch (Exception e) {
            throw new ServiceException("VLOC_LIST_WRONG_PARAMS");
        }
        return result;
    }


    /**
     * wap注册
     *
     * @param partner     渠道号
     * @param loginUserId 用户ID
     * @param visitorId   游客ID
     * @return accessToken 火山视频token
     */
    @Override
    public String wapRegister(StreamService streamService, String partner, Long loginUserId, String visitorId) {
        //默认游客
        String key = "volc:yk:";
        String uuid = visitorId;
        long timeout = 1;
        //判断是否用户 true:用户 false:游客
        if (loginUserId != null) {
            key = "volc:user:";
            timeout = 55;
            uuid = String.valueOf(loginUserId);
        }
        if (StrUtil.isBlank(uuid)) {
            uuid = "************";
        }
        key = key + uuid;

        Object value = RedisUtils.getCacheObject(key);
        if (ObjectUtils.isNotNull(value)) {
            return String.valueOf(value);
        }
        //初始化Request
        WapRegisterRequest req = new WapRegisterRequest();
        req.setPartner(partner);
        req.setUuid(uuid);
        req.setOuid(uuid);
        WapRegisterResponse response = null;
        try {
            response = streamService.wapRegister(req);
        } catch (Exception e) {
            throw new ServiceException("VLOC_REGISTER_WRONG_PARAMS");
        }
        if (Objects.isNull(response) || Objects.isNull(response.getResult()) || StrUtil.isEmpty(response.getResult().getAccessToken())) {
            throw new RuntimeException("VLOC_REGISTER_WRONG_PARAMS");
        }
        RedisUtils.setCacheObject(key, response.getResult().getAccessToken());
//        RedisUtils.setCacheObject(key, response.getResult().getAccessToken(), timeout, TimeUnit.DAYS);
        return response.getResult().getAccessToken();
    }

}
