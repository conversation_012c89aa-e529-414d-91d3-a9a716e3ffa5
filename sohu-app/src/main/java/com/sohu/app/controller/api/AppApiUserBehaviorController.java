package com.sohu.app.controller.api;

import cn.hutool.core.date.DateUtil;
import com.sohu.common.core.domain.R;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.middle.api.bo.SohuUserBehaviorRecordPointBo;
import com.sohu.middle.api.service.RemoteMiddleUserBehaviorRecordService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户行为埋点
 * <AUTHOR>
 * @date 2025/4/1 9:20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/user/behavior")
public class AppApiUserBehaviorController {

    @DubboReference
    private RemoteMiddleUserBehaviorRecordService remoteMiddleUserBehaviorRecordService;

    @Log(title = "用户行为数据埋点", businessType = BusinessType.INSERT)
    @PostMapping(value = "/addList")
    public R<Boolean> addList(@RequestBody List<SohuUserBehaviorRecordPointBo> list) {
        return R.ok(remoteMiddleUserBehaviorRecordService.addList(list));
    }

    @Log(title = "获取服务器时间", businessType = BusinessType.OTHER)
    @GetMapping(value = "/getTime")
    public R<Long> getTime() {
        return R.ok(System.currentTimeMillis());
    }
}
