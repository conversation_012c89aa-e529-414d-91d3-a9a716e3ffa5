package com.sohu.app.controller;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuViewRecordBo;
import com.sohu.middle.api.service.RemoteMiddleViewRecordService;
import com.sohu.middle.api.vo.SohuViewRecordVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 浏览记录
 * 前端访问路由地址为:/app/view
 *
 * <AUTHOR>
 * @date 2023-08-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/view")
public class AppViewRecordController extends BaseController {

    @DubboReference
    private RemoteMiddleViewRecordService remoteMiddleViewRecordService;

    /**
     * 查询浏览记录列表
     */
    @GetMapping("/list")
    @Log(title = "查询浏览记录列表")
    public TableDataInfo<SohuViewRecordVo> list(SohuViewRecordBo bo, PageQuery pageQuery) {
        return remoteMiddleViewRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取浏览记录详细信息
     */
    @GetMapping("/{id}")
    public R<SohuViewRecordVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteMiddleViewRecordService.queryById(id));
    }

    /**
     * 新增记录
     */
    @Log(title = "新增记录", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuViewRecordBo bo) {
        return toAjax(remoteMiddleViewRecordService.insertByBo(bo));
    }

    /**
     * 删除浏览记录
     */
    @Log(title = "删除浏览记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(remoteMiddleViewRecordService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
