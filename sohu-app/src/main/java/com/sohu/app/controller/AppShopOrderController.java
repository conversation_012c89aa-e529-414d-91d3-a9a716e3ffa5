package com.sohu.app.controller;

import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuUserAddressBo;
import com.sohu.middle.api.service.RemoteMiddleUserAddressService;
import com.sohu.middle.api.vo.SohuUserAddressVo;
import com.sohu.shoporder.api.RemoteShopOrderService;
import com.sohu.shoporder.api.RemoteShopRefundOrderService;
import com.sohu.shoporder.api.bo.SohuShopOrderFreightBo;
import com.sohu.shoporder.api.bo.SohuStoreOrderBo;
import com.sohu.shoporder.api.domain.*;
import com.sohu.shoporder.api.model.*;
import com.sohu.shoporder.api.vo.SohuShopOrderVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * app订单
 *
 * @author: zc
 * @date: 2023/7/19 19:41
 * @version: 1.0.0
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order")
public class AppShopOrderController extends BaseController {

    @DubboReference
    private RemoteShopOrderService remoteShopOrderService;
    @DubboReference
    private RemoteShopRefundOrderService remoteShopRefundOrderService;
    @DubboReference
    private RemoteMiddleUserAddressService remoteMiddleUserAddressService;

    /**
     * app查询用户订单列表
     *
     * @param "订单状态（WAIT_SENT 待发货,WAIT_RECEIVE 待收货,RECEIVE 已收货,COMPLETED 已完成,CANCEL 已取消）
     */
    @GetMapping("/get/order")
    public TableDataInfo<SohuShopOrderModel> getOrderList(SohuOrderReqBo bo, PageQuery pageQuery) {
        bo.setSysSource(getSysSource());
        return remoteShopOrderService.getOrderListByType(bo, pageQuery);
    }

    /**
     * app查询用户退款订单列表
     */
    @GetMapping("/get/refund/order")
    public TableDataInfo<SohuRefundOrderModel> getRefundOrderList(PageQuery pageQuery, SohuOrderReqBo bo) {
        bo.setSysSource(getSysSource());
        return remoteShopOrderService.getRefundOrderList(pageQuery, bo);
    }

    /**
     * app主订单详情
     *
     * @param orderNo 主订单号
     */
    @GetMapping("/get/master/info/{orderNo}")
    public R<SohuMasterOrderInfoModel> getMasterOrderInfo(@PathVariable String orderNo) {
        return R.ok(remoteShopOrderService.getMasterOrderInfo(orderNo));
    }

    /**
     * app商户订单详情
     *
     * @param orderNo 商户订单号
     */
    @GetMapping("/get/shop/order/info/{orderNo}")
    public R<SohuStoreOrderInfoResModel> getShopOrderInfo(@PathVariable String orderNo) {
        return R.ok(remoteShopOrderService.getShopOrderInfo(orderNo));
    }

    /**
     * app退款订单详情
     *
     * @param refundOrderNo 退款订单号
     */
    @GetMapping("/refund/info/{refundOrderNo}")
    public R<SohuRefundOrderInfoModel> refundOrderDetail(@PathVariable String refundOrderNo) {
        return R.ok(remoteShopOrderService.refundOrderInfo(refundOrderNo));
    }

    /**
     * app用户待支付订单列表
     */
    @GetMapping("/wait/pay")
    public TableDataInfo<SohuMasterOrderAwaitPayModel> getWaitOrderList(SohuOrderReqBo bo, PageQuery pageQuery) {
        bo.setSysSource(getSysSource());
        return remoteShopOrderService.getWaitOrderListByType(pageQuery, bo);
    }

    /**
     * app创建订单
     */
    @PostMapping("/create/order")
    public R<Map<String, Object>> createOrder(@RequestBody SohuCreateOrderReqBo createOrderReqBo) {
        createOrderReqBo.setSysSource(getSysSource());
        return R.ok(remoteShopOrderService.createOrder(createOrderReqBo));
    }

    /**
     * app预下单
     *
     * @param request
     */
    @PostMapping("/pre/order")
    public R<Map<String, Object>> preOrder(@RequestBody SohuPreOrderReqBo request) {
        request.setSysSource(getSysSource());
        return R.ok(remoteShopOrderService.preOrder(request));
    }

    /**
     * 加载预下单
     */
    @GetMapping("/load/pre/{preOrderNo}")
    public R<SohuPreOrderModel> loadPreOrder(@PathVariable String preOrderNo) {
        return R.ok(remoteShopOrderService.loadPreOrder(preOrderNo));
    }

    /**
     * app订单取消
     *
     * @param orderNo 商户订单号
     */
    @PostMapping("/cancel/order/{orderNo}")
    public R<Boolean> cancelOrder(@PathVariable(value = "orderNo") String orderNo) {
        return R.ok(remoteShopOrderService.cancelOrder(orderNo));
    }


    /**
     * app订单删除
     *
     * @param orderNo 商户订单号
     */
    @DeleteMapping("/delete/order/{orderNo}")
    public R<Boolean> deleteOrder(@PathVariable(value = "orderNo") String orderNo) {
        return R.ok(remoteShopOrderService.deleteOrder(orderNo));
    }

    /**
     * app售后订单删除
     *
     * @param refundOrderNo 售后单单号
     */
    @DeleteMapping("/delete/refundOrder/{refundOrderNo}")
    public R<Boolean> deleteRefundOrder(@PathVariable(value = "refundOrderNo") String refundOrderNo) {
        return R.ok(remoteShopRefundOrderService.deleteRefundOrder(refundOrderNo));
    }

    /**
     * app订单确认收货
     *
     * @param orderNo 商户订单号
     */
    @PostMapping("/take/order/{orderNo}")
    public R<Boolean> takeOrder(@PathVariable(value = "orderNo") String orderNo) {
        return R.ok(remoteShopOrderService.takeOrder(orderNo));
    }

    /**
     * app获取申请订单退款信息
     *
     * @param orderNo 商户订单号
     */
    @PostMapping("/refund/order/{orderNo}")
    public R<SohuApplyRefundOrderInfoModel> refundApplyOrder(@PathVariable String orderNo) {
        return R.ok(remoteShopOrderService.refundApplyOrder(orderNo));
    }


    /**
     * app订单申请退款
     *
     * @param refundApplyReqBo
     */
    @PostMapping("/refund/order")
    public R<Boolean> refundApply(@RequestBody SohuOrderRefundApplyReqBo refundApplyReqBo) {
        refundApplyReqBo.setSysSource(getSysSource());
        return R.ok(remoteShopOrderService.refundApply(refundApplyReqBo));
    }

    /**
     * 获取默认收货地址
     */
    @GetMapping("/address")
    public R<List<SohuUserAddressVo>> getUserAddressList() {
        SohuUserAddressBo addressBo = new SohuUserAddressBo();
        addressBo.setUserId(LoginHelper.getUserId());
        return R.ok(remoteMiddleUserAddressService.queryList(addressBo));
    }

    /**
     * 物流查询
     */
    @GetMapping("/kd/query")
    public R<KuaidiModel> queryTrack(SohuQueryTrackBo param) {
        param.setSysSource(getSysSource());
        KuaidiModel kuaidiModel = remoteShopOrderService.queryTrack(param);
        return R.ok(kuaidiModel);
    }

    /**
     * 查询用户店铺订单
     */
    @GetMapping("/get/store/order")
    public TableDataInfo<SohuShopOrderVo> getStoreOrderList(SohuStoreOrderBo bo, PageQuery pageQuery) {
        bo.setSysSource(getSysSource());
        return remoteShopOrderService.getStoreOrderList(bo, pageQuery);
    }

    /**
     * 根据用户地址获取运费
     */
    @PostMapping("/get/freight")
    public R<Map<String, Object>> getFreight(@RequestBody SohuShopOrderFreightBo bo) {
        bo.setSysSource(getSysSource());
        return R.ok(remoteShopOrderService.getFreight(bo));
    }

}
