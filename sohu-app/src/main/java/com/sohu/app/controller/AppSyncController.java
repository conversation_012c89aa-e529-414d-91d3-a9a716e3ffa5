//package com.sohu.app.controller;
//
//import com.sohu.app.domaim.bo.ArticleSyncBo;
//import com.sohu.app.service.AppSyncService;
//import com.sohu.common.core.domain.R;
//import com.sohu.common.core.web.controller.BaseController;
//import io.swagger.v3.oas.annotations.Operation;
//import lombok.RequiredArgsConstructor;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.validation.Valid;
//
///**
// * 一键分发
// * 前端访问路由地址为:/app/sync
// *
// * <AUTHOR>
// * @date 2023-07-10
// */
//@Validated
//@RequiredArgsConstructor
//@RestController
//@RequestMapping("/sync")
//public class AppSyncController extends BaseController {
//
//    private final AppSyncService appSyncService;
//
//    /**
//     * 弃用
//     *
//     * @param bo
//     * @return
//     */
//    @PostMapping
//    @Operation(summary = "同步头条文章")
//    public R<Boolean> sync(@Valid @RequestBody ArticleSyncBo bo) {
//        return R.ok(appSyncService.sync(bo));
//    }
//
//
//}
