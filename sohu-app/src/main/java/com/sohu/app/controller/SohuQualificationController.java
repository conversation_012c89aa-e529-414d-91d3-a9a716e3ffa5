package com.sohu.app.controller;

import com.sohu.admin.api.RemoteAdminService;
import com.sohu.admin.api.bo.SohuQualificationListBo;
import com.sohu.admin.api.vo.SohuQualificationVo;
import com.sohu.common.core.domain.R;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 资质控制器
 * 前端访问路由地址为:/app/qualification
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/qualification")
public class SohuQualificationController {

    @DubboReference
    private RemoteAdminService remoteAdminService;

    /**
     * 获取类目/品牌资质列表
     */
    @GetMapping("/qualification/list")
    public R<List<SohuQualificationVo>> getQualificationList(SohuQualificationListBo bo) {
        return R.ok(remoteAdminService.getQualificationList(bo));
    }
}
