package com.sohu.app.controller.api;

import com.sohu.common.core.domain.R;
import com.sohu.common.encrypt.annotation.SkipApiEncrypt;
import com.sohu.middle.api.bo.airec.SohuAirecUserVisitorAddBo;
import com.sohu.middle.api.service.airec.RemoteMiddleAirecUserService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:游客智能推荐
 * @author:<PERSON> Jing
 * @create:2024/3/1 15:56
 **/
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/noLogin")
public class AppSohuAirecUserController {

    @DubboReference
    private RemoteMiddleAirecUserService remoteMiddleAirecUserService;

    /**
     * 记录游客并推送
     * @param bo
     * @return
     */
    @PostMapping("/add")
    @Operation(summary = "记录游客并推送")
    @SkipApiEncrypt
    public R<Boolean> insertNologin(@Validated @RequestBody SohuAirecUserVisitorAddBo bo) {
        remoteMiddleAirecUserService.insertByNologin(bo);
        return R.ok(Boolean.TRUE);
    }
}
