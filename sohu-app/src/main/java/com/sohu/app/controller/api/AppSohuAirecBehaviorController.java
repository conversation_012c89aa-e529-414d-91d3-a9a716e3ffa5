package com.sohu.app.controller.api;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.middle.api.bo.airec.SohuAirecBehaviorBo;
import com.sohu.middle.api.service.airec.RemoteMiddleAirecBehaviorService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 智能推荐行为控制器
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/airecBehavior")
public class AppSohuAirecBehaviorController extends BaseController {
    @DubboReference
    private RemoteMiddleAirecBehaviorService remoteMiddleAirecBehaviorService;

    /**
     * 新增智能推荐行为
     */
    @Log(title = "智能推荐行为", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Long> add(@Validated(AddGroup.class) @RequestBody SohuAirecBehaviorBo bo) {
        remoteMiddleAirecBehaviorService.insertByBo(bo);
        return R.ok(bo.getId());
    }

    /**
     * 新增智能推荐行为byList
     */
    @Log(title = "智能推荐行为", businessType = BusinessType.INSERT)
    @PostMapping("/addList")
    public R<Boolean> addList(@Validated(AddGroup.class) @RequestBody List<SohuAirecBehaviorBo> modelList) {
        return toAjax(remoteMiddleAirecBehaviorService.insertBatch(modelList));
    }
}
