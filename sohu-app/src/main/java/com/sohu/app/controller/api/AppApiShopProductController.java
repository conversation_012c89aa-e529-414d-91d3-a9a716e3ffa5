package com.sohu.app.controller.api;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.*;
import com.sohu.shopgoods.api.domain.McnShopProductReqBo;
import com.sohu.shopgoods.api.domain.ShopProductReqBo;
import com.sohu.shopgoods.api.domain.SohuMcnShopProductBo;
import com.sohu.shopgoods.api.domain.SohuProductPcReqNewBo;
import com.sohu.shopgoods.api.model.*;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品
 *
 * @author: zc
 * @date: 2023/7/21 10:13
 * @version: 1.0.0
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/product")
public class AppApiShopProductController extends BaseController {

    @DubboReference
    private RemoteProductCategoryPcService categoryPcService;
    @DubboReference
    private RemoteProductCategoryService categoryService;
    @DubboReference
    private RemoteProductService productService;
    @DubboReference
    private RemoteProductPcService remoteProductPcService;

    /**
     * 查询商品分类--pc
     */
    @RequestMapping(value = "/category", method = RequestMethod.GET)
    public R<List<SohuProductCategoryPcModel>> getCategory() {
        return R.ok(categoryPcService.getCategory(getSysSource()));
    }

    /**
     * 查询商品--首页列表
     */
    @GetMapping("/home/<USER>")
    public TableDataInfo<SohuIndexProductModel> queryHome(ShopProductReqBo shopProductReqBo, PageQuery pageQuery) {
        return productService.getHomeList(shopProductReqBo, pageQuery);
    }

    /**
     * 查询商品--个人商品橱窗
     */
    @GetMapping("/user/window")
    public TableDataInfo<SohuIndexProductModel> userShopWindowPage(ShopProductReqBo shopProductReqBo, PageQuery pageQuery) {
        return productService.userShopWindowPage(shopProductReqBo, pageQuery);
    }

    /**
     * MCN带货库分页
     * PC已迁移
     */
    @GetMapping("/mcn/window")
    public TableDataInfo<SohuIndexProductModel> mcnShopWindowPage(McnShopProductReqBo bo, PageQuery pageQuery) {
        return productService.mcnShopWindowPage(bo, pageQuery);
    }

    /**
     * 查询商品--详情
     *
     * @param id            商品id
     * @param isIndependent 是否是分销商品
     */
    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    public R<SohuIndexProductInfoModel> getProductInfo(@PathVariable Long id, Boolean isIndependent) {
        return R.ok(productService.getIndexInfo(id, isIndependent));
    }

    /**
     * 查询MCN带货库商品--详情
     *
     * @param id
     * @param isIndependent
     */
    @RequestMapping(value = "/mcn/info/{id}", method = RequestMethod.GET)
    public R<SohuIndexProductModel> getMcnProductInfo(@PathVariable Long id, Boolean isIndependent) {
        return R.ok(productService.getMcnIndexInfo(id, isIndependent));
    }

    /**
     * 查询商品评论数量--详情
     *
     * @param id 商品id
     */
    @RequestMapping(value = "/reply/count/{id}", method = RequestMethod.GET)
    public R<SohuProductReplyCountModel> getReplyCount(@PathVariable Long id) {
        return R.ok(productService.getReplyCount(id));
    }

    /**
     * 查询商品评论--详情
     *
     * @param id 商品id
     */
    @RequestMapping(value = "/reply/info/{id}", method = RequestMethod.GET)
    public R<SohuProductReplyInfoModel> getReplyInfo(@PathVariable Long id) {
        return R.ok(productService.getReplyInfo(id));
    }

    /**
     * 查询商品规格--详情
     *
     * @param id 商品id
     */
    @RequestMapping(value = "/sku/info/{id}", method = RequestMethod.GET)
    public R<SohuProductInfoModel> getSkuInfo(@PathVariable Long id) {
        return R.ok(productService.getSkuInfo(id));
    }

    /**
     * 查询商品排行榜--详情
     */
    @RequestMapping(value = "/level", method = RequestMethod.GET)
    public R<List<SohuProductModel>> getLevel() {
        return R.ok(productService.getLevel());
    }

    /**
     * app商户商品分类列表-store
     */
    @RequestMapping(value = "/merchant/category/{merId}", method = RequestMethod.GET)
    public R<List<SohuProductCategoryModel>> getMerchantCategoryList(@PathVariable Long merId) {
        return R.ok(categoryService.getMerchantCategoryList(merId,getSysSource()));
    }

    /**
     * app商户商品列表-sotre
     */
    @GetMapping("/merchant/product/list")
    public TableDataInfo<SohuIndexProductModel> getMerchantProductList(ShopProductReqBo productReqBo, PageQuery pageQuery) {
        return productService.getMerchantProductList(productReqBo, pageQuery);
    }

    /**
     * app分销商户商品列表-sotre
     */
   @GetMapping("/merchant/product/independent/list")
    public TableDataInfo<SohuAppProductMerchantModel> getMerchantIndependentProductList(ShopProductReqBo productReqBo, PageQuery pageQuery) {
//        return productService.getMerchantProductList(productReqBo, pageQuery);
        return productService.getMerchantIndependentProductList(productReqBo, pageQuery);
    }

    /**
     * MCN选品批量添加
     * PC已迁移
     */
    @PostMapping("mcn/window/independent")
    public R<Boolean> insertByIds(@RequestBody SohuMcnShopProductBo bo) {
        return toAjax(productService.insertByIds(bo));
    }

    /**
     * MCN带货库批量添加
     *
     * @param ids
     * @return
     */
    @GetMapping("mcn/window/add")
    public R<Boolean> productInsertByIds(@RequestParam List<Long> ids) {
        return toAjax(productService.productInsertByIds(ids));
    }

    /**
     * MCN带货库删除
     * PC已迁移
     */
    @DeleteMapping("mcn/window/{ids}")
    public R<Boolean> mcnWindowDeleteByIds(@PathVariable("ids") List<Long> ids) {
        return R.ok(productService.mcnShopWindowDeleteByIds(ids));
    }

    /**
     * MCN带货库修改
     * PC已迁移
     */
    @PutMapping("mcn/window")
    public R<Boolean> mcnWindowUpdate(@RequestBody SohuMcnShopProductBo bo) {
        return R.ok(productService.mcnWindowUpdate(bo));
    }

    /**
     * 商品表同步一级分类
     */
    @GetMapping("/syncFirstCategoryToProduct")
    public R<Integer> syncFirstCategoryToProduct() {
        return R.ok(productService.syncFirstCategoryToProduct());
    }

    /**
     * 查询商品列表--短剧商城专用
     * @param bo
     * @param pageQuery
     * @return
     */
    @Operation(summary = "首页搜索",description = "汪伟负责 查询商品列表")
    @GetMapping("/search")
    public TableDataInfo<SohuProductModel> getList(SohuProductPcReqNewBo bo, PageQuery pageQuery) {
        return remoteProductPcService.queryPcPageListNew(bo, pageQuery);
    }

    /**
     * 新品首发 商品列表--短剧商城专用
     * @param bo
     * @param pageQuery
     * @return
     */
    @Operation(summary = "新品首发",description = "汪伟负责 新品首发")
    @GetMapping("/latestList")
    public TableDataInfo<SohuProductModel> latestList(SohuProductPcReqNewBo bo, PageQuery pageQuery) {
        return remoteProductPcService.queryPageLatestList(bo, pageQuery);
    }

    /**
     * 商品销量排行榜--短剧商城专用
     * @param bo
     * @param pageQuery
     * @return
     */
    @Operation(summary = "销量排行版",description = "汪伟负责 商品销量排行榜")
    @GetMapping("/rankList")
    public TableDataInfo<SohuProductModel> rankList(SohuProductPcReqNewBo bo, PageQuery pageQuery) {
        return remoteProductPcService.queryPageRankList(bo, pageQuery);
    }
}
