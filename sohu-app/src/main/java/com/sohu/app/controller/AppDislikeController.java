package com.sohu.app.controller;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuDislikeBo;
import com.sohu.middle.api.service.RemoteMiddleDislikeService;
import com.sohu.middle.api.vo.SohuDislikeVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 不感兴趣
 * 前端访问路由地址为:/app/dislike
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/dislike")
public class AppDislikeController extends BaseController {

    @DubboReference
    private RemoteMiddleDislikeService remoteMiddleDislikeService;

    /**
     * 查询不感兴趣列表
     */
    @GetMapping("/list")
    public TableDataInfo<SohuDislikeVo> list(SohuDislikeBo bo, PageQuery pageQuery) {
        return remoteMiddleDislikeService.queryPageList(bo, pageQuery);
    }


    /**
     * 获取不感兴趣详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<SohuDislikeVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteMiddleDislikeService.queryById(id));
    }

    /**
     * 新增不感兴趣
     */
    @Log(title = "不感兴趣", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuDislikeBo bo) {
        return toAjax(remoteMiddleDislikeService.insertByBo(bo));
    }

    /**
     * 删除不感兴趣
     *
     * @param ids 主键串
     */
    @Log(title = "不感兴趣", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(remoteMiddleDislikeService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 不感兴趣
     */
    @Log(title = "不感兴趣", businessType = BusinessType.INSERT)
    @PostMapping("/insertByDislike")
    public R<Boolean> dislike(@Validated(AddGroup.class) @RequestBody SohuDislikeBo bo) {
        return toAjax(remoteMiddleDislikeService.insertByDislike(bo));
    }

}
