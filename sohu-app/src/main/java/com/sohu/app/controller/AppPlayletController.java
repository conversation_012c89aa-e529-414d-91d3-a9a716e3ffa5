package com.sohu.app.controller;


import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuPlayletBo;
import com.sohu.middle.api.service.RemoteMiddlePlayletService;
import com.sohu.middle.api.vo.SohuPlayletVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * 剧集
 * 前端访问路由地址为:/admin/playlet
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/playlet")
public class AppPlayletController extends BaseController {

    @DubboReference
    private RemoteMiddlePlayletService remoteMiddlePlayletService;

    /**
     * 查询短剧首页分类列表
     */
    @GetMapping("/list/playlet")
    public TableDataInfo<SohuPlayletVo> pagePlay(SohuPlayletBo bo, PageQuery pageQuery) {
        return remoteMiddlePlayletService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取分销短剧列表
     *
     * @param bo        SohuPlayletBo
     * @param pageQuery PageQuery (预留)
     * @return TableDataInfo<SohuPlayletVo>
     */
    @GetMapping("/list/distribution")
    public TableDataInfo<SohuPlayletVo> getDistributionPlayletList(SohuPlayletBo bo, PageQuery pageQuery) {
        return remoteMiddlePlayletService.getDistributionPlayletList(bo, pageQuery);
    }

    /**
     * 获取分销短剧详情信息
     *
     * @param id 主键id
     * @return SohuPlayletVo
     */
    @GetMapping("/{id}")
    public R<SohuPlayletVo> getDistributionPlayletInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteMiddlePlayletService.queryById(id));
    }

}
