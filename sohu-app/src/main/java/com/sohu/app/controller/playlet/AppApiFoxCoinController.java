package com.sohu.app.controller.playlet;

import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.service.RemoteMiddleTradeRecordService;
import com.sohu.middle.api.vo.playlet.PlayletVirtualRecordVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 海外短剧-狐币记录(登录状态下)
 * 前端访问路由地址为:/app/api/fox/coin
 *
 * <AUTHOR>
 * @date 2024-08-27
 */
@Tag(name = "海外短剧-狐币记录")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/fox/coin")
public class AppApiFoxCoinController extends BaseController {

    @DubboReference
    private RemoteMiddleTradeRecordService remoteMiddleTradeRecordService;

    @Operation(summary = "我的-狐币记录", description = "负责人：张良峰，我的-狐币记录")
    @GetMapping("/record")
    public TableDataInfo<PlayletVirtualRecordVo> getPlayletFoxCoinRecord(PageQuery pageQuery) {
        return remoteMiddleTradeRecordService.getPlayletFoxCoinRecord(getUuid(), pageQuery);
    }
}
