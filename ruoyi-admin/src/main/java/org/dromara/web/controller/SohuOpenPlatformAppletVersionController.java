package org.dromara.web.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.bo.SohuOpenPlatformAppletVersionBo;
import org.dromara.system.domain.vo.SohuOpenPlatformAppletVersionVo;
import org.dromara.web.service.ISohuOpenPlatformAppletVersionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 许愿狐开放平台小程序版本控制器
 * 前端访问路由地址为:/system/openPlatformAppletVersion
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/openPlatformAppletVersion")
public class SohuOpenPlatformAppletVersionController extends BaseController {

    private final ISohuOpenPlatformAppletVersionService iSohuOpenPlatformAppletVersionService;

    /**
     * 查询许愿狐开放平台小程序版本列表
     */
    @SaCheckPermission("system:openPlatformAppletVersion:list")
    @GetMapping("/list")
    public TableDataInfo<SohuOpenPlatformAppletVersionVo> list(SohuOpenPlatformAppletVersionBo bo, PageQuery pageQuery) {
        return iSohuOpenPlatformAppletVersionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出许愿狐开放平台小程序版本列表
     */
    @SaCheckPermission("system:openPlatformAppletVersion:export")
    @Log(title = "许愿狐开放平台小程序版本", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuOpenPlatformAppletVersionBo bo, HttpServletResponse response) {
        List<SohuOpenPlatformAppletVersionVo> list = iSohuOpenPlatformAppletVersionService.queryList(bo);
        ExcelUtil.exportExcel(list, "许愿狐开放平台小程序版本", SohuOpenPlatformAppletVersionVo.class, response);
    }

    /**
     * 获取许愿狐开放平台小程序版本详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:openPlatformAppletVersion:query")
    @GetMapping("/{id}")
    public R<SohuOpenPlatformAppletVersionVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuOpenPlatformAppletVersionService.queryById(id));
    }

    /**
     * 新增许愿狐开放平台小程序版本
     */
    @SaCheckPermission("system:openPlatformAppletVersion:add")
    @Log(title = "许愿狐开放平台小程序版本", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SohuOpenPlatformAppletVersionBo bo) {
        return toAjax(iSohuOpenPlatformAppletVersionService.insertByBo(bo));
    }

    /**
     * 修改许愿狐开放平台小程序版本
     */
    @SaCheckPermission("system:openPlatformAppletVersion:edit")
    @Log(title = "许愿狐开放平台小程序版本", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SohuOpenPlatformAppletVersionBo bo) {
        return toAjax(iSohuOpenPlatformAppletVersionService.updateByBo(bo));
    }

    /**
     * 删除许愿狐开放平台小程序版本
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:openPlatformAppletVersion:remove")
    @Log(title = "许愿狐开放平台小程序版本", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotNull(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuOpenPlatformAppletVersionService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
