package org.dromara.web.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.bo.*;
import org.dromara.system.domain.vo.OpenPlatformLoginVo;
import org.dromara.system.service.ISohuOpenPlatformUserService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 开放平台认证控制器
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/open/auth")
public class OpenPlatformAuthController extends BaseController {

    private final ISohuOpenPlatformUserService userService;

    /**
     * 用户登录
     */
    @SaIgnore
    @PostMapping("/login")
    @Log(title = "开放平台用户登录", businessType = BusinessType.OTHER)
    public R<OpenPlatformLoginVo> login(@Validated @RequestBody OpenPlatformLoginBo loginBo) {
        OpenPlatformLoginVo loginVo = userService.login(loginBo);
        return R.ok(loginVo);
    }

    /**
     * 用户注册
     */
    @SaIgnore
    @PostMapping("/register")
    @Log(title = "开放平台用户注册", businessType = BusinessType.INSERT)
    public R<Void> register(@Validated @RequestBody OpenPlatformRegisterBo registerBo) {
        userService.register(registerBo);
        return R.ok();
    }

    /**
     * 发送邮箱验证码
     */
    @SaIgnore
    @PostMapping("/send-email-code")
    @Log(title = "发送邮箱验证码", businessType = BusinessType.OTHER)
    public R<Void> sendEmailCode(@Validated @RequestBody OpenPlatformSendEmailCodeBo sendEmailCodeBo) {
        userService.sendEmailCode(sendEmailCodeBo);
        return R.ok();
    }

    /**
     * 忘记密码
     */
    @SaIgnore
    @PostMapping("/forgot-password")
    @Log(title = "忘记密码", businessType = BusinessType.OTHER)
    public R<Void> forgotPassword(@Validated @RequestBody OpenPlatformForgotPasswordBo forgotPasswordBo) {
        userService.forgotPassword(forgotPasswordBo);
        return R.ok();
    }

    /**
     * 重置密码
     */
    @SaIgnore
    @PostMapping("/reset-password")
    @Log(title = "重置密码", businessType = BusinessType.UPDATE)
    public R<Void> resetPassword(@Validated @RequestBody OpenPlatformResetPasswordBo resetPasswordBo) {
        userService.resetPassword(resetPasswordBo);
        return R.ok();
    }

}
