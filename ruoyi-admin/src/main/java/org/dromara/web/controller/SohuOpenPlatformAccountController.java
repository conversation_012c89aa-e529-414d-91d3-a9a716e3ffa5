package org.dromara.web.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.bo.SohuOpenPlatformAccountBo;
import org.dromara.system.domain.vo.SohuOpenPlatformAccountVo;
import org.dromara.web.service.ISohuOpenPlatformAccountService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 许愿狐开放平台个人/企业主体信息控制器
 * 前端访问路由地址为:/system/openPlatformAccount
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/openPlatformAccount")
public class SohuOpenPlatformAccountController extends BaseController {

    private final ISohuOpenPlatformAccountService iSohuOpenPlatformAccountService;

    /**
     * 查询许愿狐开放平台个人/企业主体信息列表
     */
    @SaCheckPermission("system:openPlatformAccount:list")
    @GetMapping("/list")
    public TableDataInfo<SohuOpenPlatformAccountVo> list(SohuOpenPlatformAccountBo bo, PageQuery pageQuery) {
        return iSohuOpenPlatformAccountService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出许愿狐开放平台个人/企业主体信息列表
     */
    @SaCheckPermission("system:openPlatformAccount:export")
    @Log(title = "许愿狐开放平台个人/企业主体信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuOpenPlatformAccountBo bo, HttpServletResponse response) {
        List<SohuOpenPlatformAccountVo> list = iSohuOpenPlatformAccountService.queryList(bo);
        ExcelUtil.exportExcel(list, "许愿狐开放平台个人/企业主体信息", SohuOpenPlatformAccountVo.class, response);
    }

    /**
     * 获取许愿狐开放平台个人/企业主体信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:openPlatformAccount:query")
    @GetMapping("/{id}")
    public R<SohuOpenPlatformAccountVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuOpenPlatformAccountService.queryById(id));
    }

    /**
     * 新增许愿狐开放平台个人/企业主体信息
     */
    @SaCheckPermission("system:openPlatformAccount:add")
    @Log(title = "许愿狐开放平台个人/企业主体信息", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SohuOpenPlatformAccountBo bo) {
        return toAjax(iSohuOpenPlatformAccountService.insertByBo(bo));
    }

    /**
     * 修改许愿狐开放平台个人/企业主体信息
     */
    @SaCheckPermission("system:openPlatformAccount:edit")
    @Log(title = "许愿狐开放平台个人/企业主体信息", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SohuOpenPlatformAccountBo bo) {
        return toAjax(iSohuOpenPlatformAccountService.updateByBo(bo));
    }

    /**
     * 删除许愿狐开放平台个人/企业主体信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:openPlatformAccount:remove")
    @Log(title = "许愿狐开放平台个人/企业主体信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuOpenPlatformAccountService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
