package org.dromara.web.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.bo.SohuOpenPlatformAppletBo;
import org.dromara.system.domain.vo.SohuOpenPlatformAppletVo;
import org.dromara.web.service.ISohuOpenPlatformAppletService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 许愿狐开放平台小程序控制器
 * 前端访问路由地址为:/system/openPlatformApplet
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/openPlatformApplet")
public class SohuOpenPlatformAppletController extends BaseController {

    private final ISohuOpenPlatformAppletService iSohuOpenPlatformAppletService;

    /**
     * 查询许愿狐开放平台小程序列表
     */
    @SaCheckPermission("system:openPlatformApplet:list")
    @GetMapping("/list")
    public TableDataInfo<SohuOpenPlatformAppletVo> list(SohuOpenPlatformAppletBo bo, PageQuery pageQuery) {
        return iSohuOpenPlatformAppletService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出许愿狐开放平台小程序列表
     */
    @SaCheckPermission("system:openPlatformApplet:export")
    @Log(title = "许愿狐开放平台小程序", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuOpenPlatformAppletBo bo, HttpServletResponse response) {
        List<SohuOpenPlatformAppletVo> list = iSohuOpenPlatformAppletService.queryList(bo);
        ExcelUtil.exportExcel(list, "许愿狐开放平台小程序", SohuOpenPlatformAppletVo.class, response);
    }

    /**
     * 获取许愿狐开放平台小程序详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:openPlatformApplet:query")
    @GetMapping("/{id}")
    public R<SohuOpenPlatformAppletVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuOpenPlatformAppletService.queryById(id));
    }

    /**
     * 新增许愿狐开放平台小程序
     */
    @SaCheckPermission("system:openPlatformApplet:add")
    @Log(title = "许愿狐开放平台小程序", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SohuOpenPlatformAppletBo bo) {
        return toAjax(iSohuOpenPlatformAppletService.insertByBo(bo));
    }

    /**
     * 修改许愿狐开放平台小程序
     */
    @SaCheckPermission("system:openPlatformApplet:edit")
    @Log(title = "许愿狐开放平台小程序", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SohuOpenPlatformAppletBo bo) {
        return toAjax(iSohuOpenPlatformAppletService.updateByBo(bo));
    }

    /**
     * 删除许愿狐开放平台小程序
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:openPlatformApplet:remove")
    @Log(title = "许愿狐开放平台小程序", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuOpenPlatformAppletService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
