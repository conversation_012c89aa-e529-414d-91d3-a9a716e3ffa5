package org.dromara.web.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.bo.SohuOpenPlatformUserBo;
import org.dromara.system.domain.vo.SohuOpenPlatformUserVo;
import org.dromara.system.service.ISohuOpenPlatformUserService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 许愿狐开放平台用户管理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/openPlatformUser")
public class SohuOpenPlatformUserController extends BaseController {

    private final ISohuOpenPlatformUserService userService;

    /**
     * 查询许愿狐开放平台用户列表
     */
    @SaCheckPermission("system:openPlatformUser:list")
    @GetMapping("/list")
    public TableDataInfo<SohuOpenPlatformUserVo> list(SohuOpenPlatformUserBo bo, PageQuery pageQuery) {
        return userService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出许愿狐开放平台用户列表
     */
    @SaCheckPermission("system:openPlatformUser:export")
    @Log(title = "许愿狐开放平台用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuOpenPlatformUserBo bo, HttpServletResponse response) {
        List<SohuOpenPlatformUserVo> list = userService.queryList(bo);
        ExcelUtil.exportExcel(list, "许愿狐开放平台用户", SohuOpenPlatformUserVo.class, response);
    }

    /**
     * 获取许愿狐开放平台用户详细信息
     */
    @SaCheckPermission("system:openPlatformUser:query")
    @GetMapping("/{id}")
    public R<SohuOpenPlatformUserVo> getInfo(@PathVariable Long id) {
        return R.ok(userService.queryById(id));
    }

    /**
     * 新增许愿狐开放平台用户
     */
    @SaCheckPermission("system:openPlatformUser:add")
    @Log(title = "许愿狐开放平台用户", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SohuOpenPlatformUserBo bo) {
        return toAjax(userService.insertByBo(bo));
    }

    /**
     * 修改许愿狐开放平台用户
     */
    @SaCheckPermission("system:openPlatformUser:edit")
    @Log(title = "许愿狐开放平台用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SohuOpenPlatformUserBo bo) {
        return toAjax(userService.updateByBo(bo));
    }

    /**
     * 删除许愿狐开放平台用户
     */
    @SaCheckPermission("system:openPlatformUser:remove")
    @Log(title = "许愿狐开放平台用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable Long[] ids) {
        return toAjax(userService.deleteWithValidByIds(List.of(ids), true));
    }

}
