package org.dromara.web.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.bo.SohuOpenPlatformReviewRecordBo;
import org.dromara.system.domain.vo.SohuOpenPlatformReviewRecordVo;
import org.dromara.web.service.ISohuOpenPlatformReviewRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 许愿狐开放平台小程序审核记录控制器
 * 前端访问路由地址为:/system/openPlatformReviewRecord
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/openPlatformReviewRecord")
public class SohuOpenPlatformReviewRecordController extends BaseController {

    private final ISohuOpenPlatformReviewRecordService iSohuOpenPlatformReviewRecordService;

    /**
     * 查询许愿狐开放平台小程序审核记录列表
     */
    @SaCheckPermission("system:openPlatformReviewRecord:list")
    @GetMapping("/list")
    public TableDataInfo<SohuOpenPlatformReviewRecordVo> list(SohuOpenPlatformReviewRecordBo bo, PageQuery pageQuery) {
        return iSohuOpenPlatformReviewRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出许愿狐开放平台小程序审核记录列表
     */
    @SaCheckPermission("system:openPlatformReviewRecord:export")
    @Log(title = "许愿狐开放平台小程序审核记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuOpenPlatformReviewRecordBo bo, HttpServletResponse response) {
        List<SohuOpenPlatformReviewRecordVo> list = iSohuOpenPlatformReviewRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "许愿狐开放平台小程序审核记录", SohuOpenPlatformReviewRecordVo.class, response);
    }

    /**
     * 获取许愿狐开放平台小程序审核记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:openPlatformReviewRecord:query")
    @GetMapping("/{id}")
    public R<SohuOpenPlatformReviewRecordVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuOpenPlatformReviewRecordService.queryById(id));
    }

    /**
     * 新增许愿狐开放平台小程序审核记录
     */
    @SaCheckPermission("system:openPlatformReviewRecord:add")
    @Log(title = "许愿狐开放平台小程序审核记录", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SohuOpenPlatformReviewRecordBo bo) {
        return toAjax(iSohuOpenPlatformReviewRecordService.insertByBo(bo));
    }

    /**
     * 修改许愿狐开放平台小程序审核记录
     */
    @SaCheckPermission("system:openPlatformReviewRecord:edit")
    @Log(title = "许愿狐开放平台小程序审核记录", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SohuOpenPlatformReviewRecordBo bo) {
        return toAjax(iSohuOpenPlatformReviewRecordService.updateByBo(bo));
    }

    /**
     * 删除许愿狐开放平台小程序审核记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:openPlatformReviewRecord:remove")
    @Log(title = "许愿狐开放平台小程序审核记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotNull(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuOpenPlatformReviewRecordService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
