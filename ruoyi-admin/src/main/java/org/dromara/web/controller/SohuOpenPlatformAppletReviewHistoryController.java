package org.dromara.web.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.bo.SohuOpenPlatformAppletReviewHistoryBo;
import org.dromara.system.domain.vo.SohuOpenPlatformAppletReviewHistoryVo;
import org.dromara.web.service.ISohuOpenPlatformAppletReviewHistoryService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 许愿狐开放平台小程序审核历史记录控制器
 * 前端访问路由地址为:/system/openPlatformAppletReviewHistory
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/openPlatformAppletReviewHistory")
public class SohuOpenPlatformAppletReviewHistoryController extends BaseController {

    private final ISohuOpenPlatformAppletReviewHistoryService iSohuOpenPlatformAppletReviewHistoryService;

    /**
     * 查询许愿狐开放平台小程序审核历史记录列表
     */
    @SaCheckPermission("system:openPlatformAppletReviewHistory:list")
    @GetMapping("/list")
    public TableDataInfo<SohuOpenPlatformAppletReviewHistoryVo> list(SohuOpenPlatformAppletReviewHistoryBo bo, PageQuery pageQuery) {
        return iSohuOpenPlatformAppletReviewHistoryService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出许愿狐开放平台小程序审核历史记录列表
     */
    @SaCheckPermission("system:openPlatformAppletReviewHistory:export")
    @Log(title = "许愿狐开放平台小程序审核历史记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuOpenPlatformAppletReviewHistoryBo bo, HttpServletResponse response) {
        List<SohuOpenPlatformAppletReviewHistoryVo> list = iSohuOpenPlatformAppletReviewHistoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "许愿狐开放平台小程序审核历史记录", SohuOpenPlatformAppletReviewHistoryVo.class, response);
    }

    /**
     * 获取许愿狐开放平台小程序审核历史记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:openPlatformAppletReviewHistory:query")
    @GetMapping("/{id}")
    public R<SohuOpenPlatformAppletReviewHistoryVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuOpenPlatformAppletReviewHistoryService.queryById(id));
    }

    /**
     * 新增许愿狐开放平台小程序审核历史记录
     */
    @SaCheckPermission("system:openPlatformAppletReviewHistory:add")
    @Log(title = "许愿狐开放平台小程序审核历史记录", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SohuOpenPlatformAppletReviewHistoryBo bo) {
        return toAjax(iSohuOpenPlatformAppletReviewHistoryService.insertByBo(bo));
    }

    /**
     * 修改许愿狐开放平台小程序审核历史记录
     */
    @SaCheckPermission("system:openPlatformAppletReviewHistory:edit")
    @Log(title = "许愿狐开放平台小程序审核历史记录", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SohuOpenPlatformAppletReviewHistoryBo bo) {
        return toAjax(iSohuOpenPlatformAppletReviewHistoryService.updateByBo(bo));
    }

    /**
     * 删除许愿狐开放平台小程序审核历史记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:openPlatformAppletReviewHistory:remove")
    @Log(title = "许愿狐开放平台小程序审核历史记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotNull(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuOpenPlatformAppletReviewHistoryService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
