package org.dromara.web.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.bo.*;
import org.dromara.web.service.IOpenPlatformUserService;
import org.dromara.web.domain.vo.LoginVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 开放平台认证控制器
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/open/auth")
public class OpenPlatformAuthController extends BaseController {

    private final IOpenPlatformUserService userService;

    /**
     * 用户登录
     */
    @SaIgnore
    @PostMapping("/login")
    @Log(title = "开放平台用户登录", businessType = BusinessType.OTHER)
    public R<LoginVo> login(@Validated @RequestBody OpenPlatformLoginBo loginBo) {
        LoginVo loginVo = userService.login(loginBo);
        return R.ok(loginVo);
    }

    /**
     * 用户注册
     */
    @SaIgnore
    @PostMapping("/register")
    @Log(title = "开放平台用户注册", businessType = BusinessType.INSERT)
    public R<Boolean> register(@Validated @RequestBody OpenPlatformRegisterBo registerBo) {
        return R.ok(userService.register(registerBo));
    }

    /**
     * 发送邮箱验证码
     */
    @SaIgnore
    @PostMapping("/send-email-code")
    @Log(title = "发送邮箱验证码", businessType = BusinessType.OTHER)
    public R<Boolean> sendEmailCode(@Validated @RequestBody OpenPlatformSendEmailCodeBo sendEmailCodeBo) {
        return R.ok(userService.sendEmailCode(sendEmailCodeBo));
    }

    /**
     * 忘记密码
     */
    @SaIgnore
    @PostMapping("/forgot-password")
    @Log(title = "忘记密码", businessType = BusinessType.OTHER)
    public R<Boolean> forgotPassword(@Validated @RequestBody OpenPlatformForgotPasswordBo forgotPasswordBo) {
        return R.ok(userService.forgotPassword(forgotPasswordBo));
    }

    /**
     * 重置密码
     */
    @SaIgnore
    @PostMapping("/reset-password")
    @Log(title = "重置密码", businessType = BusinessType.UPDATE)
    public R<Boolean> resetPassword(@Validated @RequestBody OpenPlatformResetPasswordBo resetPasswordBo) {
        return R.ok(userService.resetPassword(resetPasswordBo));
    }

}
