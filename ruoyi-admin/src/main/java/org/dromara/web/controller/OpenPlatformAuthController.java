package org.dromara.web.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.bo.*;
import org.dromara.web.service.IOpenPlatformUserService;
import org.dromara.web.domain.vo.LoginVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 开放平台认证控制器
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/open/auth")
public class OpenPlatformAuthController extends BaseController {

    private final IOpenPlatformUserService openPlatformUserService;

    /**
     * 用户登录
     */
    @SaIgnore
    @PostMapping("/login")
    @Log(title = "开放平台用户登录", businessType = BusinessType.OTHER)
    public R<LoginVo> login(@Validated @RequestBody OpenPlatformLoginBo loginBo) {
        LoginVo loginVo = openPlatformUserService.login(loginBo);
        return R.ok(loginVo);
    }

    /**
     * 用户注册（通用）
     */
    @SaIgnore
    @PostMapping("/register")
    @Log(title = "开放平台用户注册", businessType = BusinessType.INSERT)
    public R<Boolean> register(@Validated @RequestBody OpenPlatformRegisterBo registerBo) {
        return R.ok(openPlatformUserService.register(registerBo));
    }

    /**
     * 个人用户注册
     */
    @SaIgnore
    @PostMapping("/register/personal")
    @Log(title = "开放平台个人用户注册", businessType = BusinessType.INSERT)
    public R<Boolean> registerPersonal(@Validated @RequestBody OpenPlatformRegisterBo registerBo) {
        return R.ok(openPlatformUserService.registerPersonal(registerBo));
    }

    /**
     * 企业用户注册
     */
    @SaIgnore
    @PostMapping("/register/business")
    @Log(title = "开放平台企业用户注册", businessType = BusinessType.INSERT)
    public R<Boolean> registerBusiness(@Validated @RequestBody OpenPlatformRegisterBo registerBo) {
        return R.ok(openPlatformUserService.registerBusiness(registerBo));
    }

    /**
     * 忘记密码
     */
    @SaIgnore
    @PostMapping("/forgot-password")
    @Log(title = "忘记密码", businessType = BusinessType.OTHER)
    public R<Boolean> forgotPassword(@Validated @RequestBody OpenPlatformForgotPasswordBo forgotPasswordBo) {
        return R.ok(openPlatformUserService.forgotPassword(forgotPasswordBo));
    }

    /**
     * 重置密码
     */
    @SaIgnore
    @PostMapping("/reset/password")
    @Log(title = "重置密码", businessType = BusinessType.UPDATE)
    public R<Boolean> resetPassword(@Validated @RequestBody OpenPlatformResetPasswordBo resetPasswordBo) {
        return R.ok(openPlatformUserService.resetPassword(resetPasswordBo));
    }

    /**
     * 获取二维码
     */
    @SaIgnore
    @PostMapping("/qrCode")
    @Log(title = "获取二维码", businessType = BusinessType.OTHER)
    public R<Boolean> getQrCode(@Validated @RequestBody OpenPlatformQrCodeBo qrCodeBo) {
        return R.ok(openPlatformUserService.getQrCode(qrCodeBo));
    }

    /**
     * 获取扫码结果
     */
    @SaIgnore
    @GetMapping("/qrCode/result")
    @Log(title = "获取扫码结果", businessType = BusinessType.OTHER)
    public R<Boolean> getQrCodeResult(@RequestParam String qrCodeKey) {
        return R.ok(openPlatformUserService.getQrCodeResult(qrCodeKey));
    }

}
