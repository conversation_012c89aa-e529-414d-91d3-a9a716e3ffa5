package org.dromara.web.service;

import org.dromara.system.domain.bo.*;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.web.domain.vo.LoginVo;

/**
 * 开放平台用户Service接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IOpenPlatformUserService {

    /**
     * 邮箱密码登录
     */
    LoginVo login(OpenPlatformLoginBo loginBo);

    /**
     * 用户注册（通用）
     */
    Boolean register(OpenPlatformRegisterBo registerBo);

    /**
     * 个人用户注册
     */
    Boolean registerPersonal(OpenPlatformPersonalRegisterBo registerBo);

    /**
     * 企业用户注册
     */
    Boolean registerBusiness(OpenPlatformBusinessRegisterBo registerBo);

    /**
     * 发送验证码
     */
    Boolean sendVerificationCode(OpenPlatformSendVerificationCodeBo sendVerificationCodeBo);

    /**
     * 忘记密码
     */
    Boolean forgotPassword(OpenPlatformForgotPasswordBo forgotPasswordBo);

    /**
     * 重置密码
     */
    Boolean resetPassword(OpenPlatformResetPasswordBo resetPasswordBo);

    /**
     * 根据联系方式查询开放平台用户
     */
    SysUserVo queryByContactValue(String contactValue);

}
