package org.dromara.web.service;

import org.dromara.system.domain.bo.*;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.web.domain.vo.LoginVo;

/**
 * 开放平台用户Service接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IOpenPlatformUserService {

    /**
     * 邮箱密码登录
     */
    LoginVo login(OpenPlatformLoginBo loginBo);

    /**
     * 用户注册
     */
    Boolean register(OpenPlatformRegisterBo registerBo);

    /**
     * 发送邮箱验证码
     */
    Boolean sendEmailCode(OpenPlatformSendEmailCodeBo sendEmailCodeBo);

    /**
     * 忘记密码
     */
    Boolean forgotPassword(OpenPlatformForgotPasswordBo forgotPasswordBo);

    /**
     * 重置密码
     */
    Boolean resetPassword(OpenPlatformResetPasswordBo resetPasswordBo);

    /**
     * 根据邮箱查询开放平台用户
     */
    SysUserVo queryByEmail(String email);

}
