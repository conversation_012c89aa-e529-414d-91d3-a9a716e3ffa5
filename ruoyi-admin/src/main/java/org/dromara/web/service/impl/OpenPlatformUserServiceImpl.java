package org.dromara.web.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.enums.UserType;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mail.utils.MailUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.enums.VerificationCodeTypeEnum;
import org.dromara.system.enums.AccountTypeEnum;
import org.dromara.system.enums.ContactTypeEnum;
import org.dromara.system.utils.VerificationCodeUtil;
import org.dromara.system.domain.SohuOpenPlatformAccount;
import org.dromara.web.mapper.SohuOpenPlatformAccountMapper;
import org.dromara.web.domain.vo.LoginVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.dromara.system.domain.*;
import org.dromara.system.domain.bo.*;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.mapper.*;
import org.dromara.web.service.IOpenPlatformUserService;
import org.dromara.system.utils.PasswordResetTokenUtil;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

/**
 * 开放平台用户Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OpenPlatformUserServiceImpl implements IOpenPlatformUserService {

    private final SysUserMapper userMapper;
    private final SohuOpenPlatformVerificationCodeMapper openPlatformVerificationCodeMapper;
    private final PasswordResetTokenUtil passwordResetTokenUtil;
    private final VerificationCodeUtil verificationCodeUtil;
    private final SohuOpenPlatformAccountMapper openPlatformAccountMapper;

    /**
     * 邮箱密码登录
     */
    @Override
    public LoginVo login(OpenPlatformLoginBo loginBo) {
        // 查询开放平台用户
        SysUser user = userMapper.selectOne(
            Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getEmail, loginBo.getContactValue())
                .eq(SysUser::getUserType, UserType.OPEN_PLATFORM.getUserType())
                .eq(SysUser::getDelFlag, SystemConstants.NORMAL)
        );

        if (user == null) {
            throw new ServiceException("该账号未注册，请先完成注册后登录～");
        }

        if (StrUtil.equals(SystemConstants.DISABLE, user.getStatus())) {
            throw new ServiceException("用户已被停用");
        }

        // 验证密码
        if (!BCrypt.checkpw(loginBo.getPassword(), user.getPassword())) {
            throw new ServiceException("密码错误");
        }

        SaLoginModel model = new SaLoginModel();
        model.setDevice(UserType.OPEN_PLATFORM.getUserType());
        // 自定义分配 不同用户体系 不同 token 授权时间 不设置默认走全局 yml 配置
        // 例如: 后台用户30分钟过期 app用户1天过期
        model.setTimeout(604800L);
        model.setActiveTimeout(1800L);
        // 生成token
        LoginHelper.login(Objects.requireNonNull(BeanUtil.copyProperties(user, LoginUser.class)), model);

        // 构建返回对象
        LoginVo loginVo = new LoginVo();
        loginVo.setAccessToken(StpUtil.getTokenValue());
        loginVo.setExpireIn(StpUtil.getTokenTimeout());

        return loginVo;
    }

    /**
     * 用户注册
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean register(OpenPlatformRegisterBo registerBo) {
        // 根据账户类型分发注册流程
        validateBeforRegister(registerBo);
        // 创建用户
        if (AccountTypeEnum.PERSONAL.getCode().equals(registerBo.getAccountType())) {
            return registerPersonal(registerBo);
        } else if (AccountTypeEnum.BUSINESS.getCode().equals(registerBo.getAccountType())) {
            return registerBusiness(registerBo);
        }
        throw new ServiceException("无效的账户类型");
    }

    /**
     * 注册参数校验
     *
     * @param registerBo 注册参数
     */
    private void validateBeforRegister(OpenPlatformRegisterBo registerBo) {
        // 验证密码一致性
        if (!registerBo.getPassword().equals(registerBo.getConfirmPassword())) {
            throw new ServiceException("两次输入的密码不一致");
        }

        // 验证验证码
        if (!verificationCodeUtil.validateCode(registerBo.getContactValue(), registerBo.getVerificationCode())
            || !verificationCodeUtil.validateCode(registerBo.getManagerPhone(), registerBo.getMangerVerificationCode())) {
            throw new ServiceException("验证码错误或已过期");
        }

        // 自动识别联系方式类型
        ContactTypeEnum contactType = identifyContactType(registerBo.getContactValue());
        if (contactType == null) {
            throw new ServiceException("联系方式格式不正确");
        }

        // 检查联系方式是否已注册
        if (isContactValueRegistered(contactType, registerBo.getContactValue())) {
            throw new ServiceException("该" + contactType.getDesc() + "已被注册");
        }
    }

    /**
     * 忘记密码
     */
    @Override
    public Boolean forgotPassword(OpenPlatformForgotPasswordBo forgotPasswordBo) {
        String email = forgotPasswordBo.getContactValue();

        // 检查开放平台用户是否存在
        SysUser user = userMapper.selectOne(
            Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getEmail, email)
                .eq(SysUser::getUserType, UserType.OPEN_PLATFORM.getUserType())
                .eq(SysUser::getDelFlag, SystemConstants.NORMAL)
        );

        if (user == null) {
            throw new ServiceException("该账号未注册，请先完成注册");
        }

        // 生成重置令牌并存储到 Redis
        String token = passwordResetTokenUtil.generateToken(user.getUserId());

        // 发送重置密码邮件
        String subject = "许愿狐开放平台密码重置";
        // TODO 用户端域名配置
        String resetUrl = "http://192.168.150.252:xxxx/reset-password?token=" + token;
        String content = String.format("你好!\n\n忘记许愿狐开放平台密码了吗?别着急,请点击以下链接,我们协助您找回密码：\n\n%s\n\n如果这不是您的邮件请忽略,很抱歉打扰您,请原谅。", resetUrl);

        try {
            MailUtils.sendText(email, subject, content);
            return true;
        } catch (Exception e) {
            log.error("发送重置密码邮件失败", e);
            throw new ServiceException("发送邮件失败");
        }
    }

    /**
     * 重置密码
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetPassword(OpenPlatformResetPasswordBo resetPasswordBo) {
        // 验证密码一致性
        if (!resetPasswordBo.getNewPassword().equals(resetPasswordBo.getConfirmPassword())) {
            throw new ServiceException("两次输入的密码不一致");
        }

        // 验证并使用重置令牌
        Long userId = passwordResetTokenUtil.useToken(resetPasswordBo.getToken());

        if (userId == null) {
            throw new ServiceException("重置令牌无效或已过期");
        }

        // 更新用户密码
        SysUser user = new SysUser();
        user.setUserId(userId);
        user.setPassword(BCrypt.hashpw(resetPasswordBo.getNewPassword()));

        userMapper.updateById(user);

        return true;
    }

    /**
     * 根据邮箱查询开放平台用户
     */
    @Override
    public SysUserVo queryByContactValue(String email) {
        return userMapper.selectVoOne(
            Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getEmail, email)
                .eq(SysUser::getUserType, UserType.OPEN_PLATFORM.getUserType())
                .eq(SysUser::getDelFlag, SystemConstants.NORMAL)
        );
    }

    /**
     * 验证邮箱验证码
     */
    private boolean validateEmailCode(String email, String code, String codeType) {
        SohuOpenPlatformVerificationCode sohuOpenPlatformVerificationCode = openPlatformVerificationCodeMapper.selectOne(
            Wrappers.lambdaQuery(SohuOpenPlatformVerificationCode.class)
                .eq(SohuOpenPlatformVerificationCode::getContactValue, email)
                .eq(SohuOpenPlatformVerificationCode::getCode, code)
                .eq(SohuOpenPlatformVerificationCode::getCodeType, codeType)
                .eq(SohuOpenPlatformVerificationCode::getUsed, false)
                .gt(SohuOpenPlatformVerificationCode::getExpireTime, LocalDateTime.now())
                .orderByDesc(SohuOpenPlatformVerificationCode::getCreateTime)
                .last("LIMIT 1")
        );

        if (sohuOpenPlatformVerificationCode != null) {
            // 标记验证码为已使用
            sohuOpenPlatformVerificationCode.setUsed(true);
            openPlatformVerificationCodeMapper.updateById(sohuOpenPlatformVerificationCode);
            return true;
        }

        return false;
    }

    /**
     * 个人用户注册
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean registerPersonal(OpenPlatformRegisterBo registerBo) {
        // 创建用户
        SysUser user = createUser(registerBo.getContactValue(), registerBo.getPassword(), registerBo.getNickname());

        // 创建个人账号信息
        SohuOpenPlatformAccount account = new SohuOpenPlatformAccount();
        account.setUserId(user.getUserId());
        account.setAccountType(AccountTypeEnum.PERSONAL.getCode());
        account.setRealName(registerBo.getRealName());
        account.setIdCardNumber(registerBo.getIdCardNumber());
        account.setIdCardFrontUrl(registerBo.getIdCardFrontUrl());
        account.setIdCardBackUrl(registerBo.getIdCardBackUrl());
        account.setManagerPhone(registerBo.getManagerPhone());
        account.setIsDel(0);

        return openPlatformAccountMapper.insert(account) > 0;
    }

    /**
     * 企业用户注册
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean registerBusiness(OpenPlatformRegisterBo registerBo) {
        // 创建用户
        SysUser user = createUser(registerBo.getContactValue(), registerBo.getPassword(), registerBo.getNickname());

        // 创建企业账号信息
        SohuOpenPlatformAccount account = new SohuOpenPlatformAccount();
        account.setUserId(user.getUserId());
        account.setAccountType(AccountTypeEnum.BUSINESS.getCode());
        account.setCompanyName(registerBo.getCompanyName());
        account.setBusinessLicenseNumber(registerBo.getBusinessLicenseNumber());
        account.setBusinessLicenseUrl(registerBo.getBusinessLicenseUrl());
        account.setRealName(registerBo.getRealName());
        account.setIdCardNumber(registerBo.getIdCardNumber());
        account.setIdCardFrontUrl(registerBo.getIdCardFrontUrl());
        account.setIdCardBackUrl(registerBo.getIdCardBackUrl());
        account.setManagerPhone(registerBo.getManagerPhone());
        account.setIsDel(0);

        return openPlatformAccountMapper.insert(account) > 0;
    }

    /**
     * 自动识别联系方式类型
     */
    private ContactTypeEnum identifyContactType(String contactValue) {
        if (StrUtil.isBlank(contactValue)) {
            return null;
        }

        // 邮箱格式检查
        if (contactValue.contains("@") && contactValue.contains(".")) {
            return ContactTypeEnum.EMAIL;
        }

        // 手机号格式检查（中国大陆）
        if (contactValue.matches("^1[3-9]\\d{9}$")) {
            return ContactTypeEnum.MOBILE;
        }

        return null;
    }

    /**
     * 检查联系方式是否已注册
     */
    private boolean isContactValueRegistered(ContactTypeEnum contactType, String contactValue) {
        switch (contactType) {
            case EMAIL:
                return userMapper.exists(Wrappers.lambdaQuery(SysUser.class)
                    .eq(SysUser::getEmail, contactValue)
                    .eq(SysUser::getUserType, UserType.OPEN_PLATFORM.getUserType())
                    .eq(SysUser::getDelFlag, SystemConstants.NORMAL));
            case MOBILE:
                return userMapper.exists(Wrappers.lambdaQuery(SysUser.class)
                    .eq(SysUser::getPhonenumber, contactValue)
                    .eq(SysUser::getUserType, UserType.OPEN_PLATFORM.getUserType())
                    .eq(SysUser::getDelFlag, SystemConstants.NORMAL));
            default:
                return false;
        }
    }

    /**
     * 创建用户
     */
    private SysUser createUser(String contactValue, String password, String nickname) {
        ContactTypeEnum contactType = identifyContactType(contactValue);

        SysUser user = new SysUser();
        user.setUserName(contactValue);
        user.setNickName(StringUtils.isNotBlank(nickname) ?
            nickname : "openUser" + RandomUtil.randomString(6));
        user.setUserType(UserType.OPEN_PLATFORM.getUserType());
        user.setPassword(BCrypt.hashpw(password));
        user.setStatus(SystemConstants.NORMAL);
        user.setDelFlag(SystemConstants.NORMAL);

        // 根据联系方式类型设置相应字段
        if (contactType == ContactTypeEnum.EMAIL) {
            user.setEmail(contactValue);
        } else if (contactType == ContactTypeEnum.MOBILE) {
            user.setPhonenumber(contactValue);
        }

        if (userMapper.insert(user) <= 0) {
            throw new ServiceException("用户创建失败");
        }

        return user;
    }


    @Override
    public Boolean getQrCode(OpenPlatformQrCodeBo qrCodeBo) {
        String qrKey = "qr_code:" + UUID.randomUUID();
        long timestamp = System.currentTimeMillis();

        RedisUtils.setHashValue(qrKey,
            Map.of(
                "data", qrCodeBo,
                "status", 0,
                "timestamp", timestamp
            ));
        RedisUtils.expire(qrKey, 1800); // 30分钟过期
        return true;
    }

    @Override
    public Boolean getQrCodeResult(String qrCodeKey) {
        Map<Object, Object> qrData = RedisUtils.getHash(qrCodeKey);
        if (CollUtil.isEmpty(qrData)) {
            return false;
        }

        // 原子更新状态
        Long status = RedisUtils.execute("HINCRBY", qrCodeKey, "status", 1);
        return status != null && status == 1;
    }
}
