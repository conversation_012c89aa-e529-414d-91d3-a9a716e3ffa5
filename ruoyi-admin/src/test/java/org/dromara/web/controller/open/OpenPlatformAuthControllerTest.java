package org.dromara.web.controller.open;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.dromara.system.domain.bo.*;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

/**
 * 开放平台认证控制器测试
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@SpringBootTest
@AutoConfigureTestMvc
public class OpenPlatformAuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testSendEmailCode() throws Exception {
        OpenPlatformSendEmailCodeBo bo = new OpenPlatformSendEmailCodeBo();
        bo.setEmail("<EMAIL>");
        bo.setCodeType("register");

        mockMvc.perform(MockMvcRequestBuilders.post("/open/auth/send-email-code")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(bo)))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void testRegister() throws Exception {
        OpenPlatformRegisterBo bo = new OpenPlatformRegisterBo();
        bo.setEmail("<EMAIL>");
        bo.setPassword("123456");
        bo.setConfirmPassword("123456");
        bo.setNickname("测试用户");
        bo.setEmailCode("123456");

        mockMvc.perform(MockMvcRequestBuilders.post("/open/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(bo)))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void testLogin() throws Exception {
        OpenPlatformLoginBo bo = new OpenPlatformLoginBo();
        bo.setEmail("<EMAIL>");
        bo.setPassword("123456");

        mockMvc.perform(MockMvcRequestBuilders.post("/open/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(bo)))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void testForgotPassword() throws Exception {
        OpenPlatformForgotPasswordBo bo = new OpenPlatformForgotPasswordBo();
        bo.setEmail("<EMAIL>");

        mockMvc.perform(MockMvcRequestBuilders.post("/open/auth/forgot-password")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(bo)))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void testResetPassword() throws Exception {
        OpenPlatformResetPasswordBo bo = new OpenPlatformResetPasswordBo();
        bo.setToken("test-token");
        bo.setNewPassword("newpassword");
        bo.setConfirmPassword("newpassword");

        mockMvc.perform(MockMvcRequestBuilders.post("/open/auth/reset-password")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(bo)))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

}
