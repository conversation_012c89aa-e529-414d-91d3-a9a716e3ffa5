<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.entry.mapper.SohuEntryInfoMapper">

    <resultMap type="com.sohu.entry.domain.SohuEntryInfo" id="SohuEntryInfoResult">
        <result property="id" column="id"/>
        <result property="entryType" column="entry_type"/>
        <result property="entryId" column="entry_id"/>
        <result property="viewCount" column="view_count"/>
        <result property="commentCount" column="comment_count"/>
        <result property="praiseCount" column="praise_count"/>
        <result property="forwardCount" column="forward_count"/>
        <result property="collectCount" column="collect_count"/>
        <result property="publishTime" column="publish_time"/>
    </resultMap>

    <select id="getCountByUser" resultType="com.sohu.entry.api.vo.DataStatVO">
        SELECT
            user_id userId,
            SUM(view_count) viewCount,
            SUM(comment_count) commentCount,
            SUM(forward_count) forwardCount,
            SUM(praise_count) praiseCount,
            SUM(collect_count) collectCount
        FROM
            sohu_entry_info i
                LEFT JOIN sohu_entry_main m on i.entry_id = m.id
        WHERE
            m.user_id = #{userId}
    </select>

</mapper>
