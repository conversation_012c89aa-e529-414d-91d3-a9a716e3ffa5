<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.entry.mapper.SohuEntryMainMapper">
    <resultMap type="com.sohu.entry.domain.SohuEntryMain" id="SohuEntryMainResult">
        <result property="id" column="id"/>
        <result property="siteId" column="site_id"/>
        <result property="categoryId" column="category_id"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="digest" column="digest"/>
        <result property="content" column="content"/>
        <result property="currencyId" column="currency_id"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="minAmount" column="min_amount"/>
        <result property="maxAmount" column="max_amount"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="address" column="address"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="coverImage" column="cover_image"/>
        <result property="images" column="images"/>
        <result property="state" column="state"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="promiseMoney" column="promise_money"/>
        <result property="promiseState" column="promise_state"/>
        <result property="sortIndex" column="sort_index"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="video" column="video"/>
        <result property="qualification" column="qualification"/>
    </resultMap>

    <select id="collectPage" resultType="com.sohu.entry.api.vo.SohuEntryMainVo">
        SELECT sp.*
        FROM sohu_user_collect sc
        INNER JOIN sohu_entry_main sp ON sc.busy_code = sp.id AND sp.state = 'OnShelf'
        WHERE sc.user_id = #{userId} AND sc.busy_type ='Project'
        <if test="title != null and title != ''">
            AND sp.name like (concat('%', #{title}, '%'))
        </if>
        ORDER BY sc.id DESC
    </select>

    <select id="pageEntryCenter" resultType="com.sohu.entry.api.vo.SohuEntryMainVo">
        SELECT
        m.*
        FROM
        sohu_entry_main m
        LEFT JOIN sohu_person_share s ON m.id = s.busy_code
        WHERE
        m.state IN
        <foreach collection="dto.stateList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND ( m.user_id = #{dto.userId}
        OR
        ( s.apply_person = #{dto.userId}
        AND s.busy_type = 'Project'
        AND s.audit_state = 'Pass'
        <if test="dto.viewType">
            AND s.view_type =#{dto.viewType}
        </if>
        )
        )
        GROUP BY
        m.id
        ORDER BY
        CASE
        WHEN s.pass_time IS NOT NULL THEN
        s.pass_time ELSE m.create_time
        END DESC
    </select>

    <update id="updateViewCount" parameterType="java.lang.Long">
        UPDATE sohu_entry_info
        SET view_count=view_count + 1
        WHERE entry_id = #{id}
    </update>
</mapper>
