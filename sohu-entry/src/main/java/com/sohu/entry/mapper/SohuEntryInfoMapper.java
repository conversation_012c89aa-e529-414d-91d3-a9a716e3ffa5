package com.sohu.entry.mapper;

import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.entry.api.vo.DataStatVO;
import com.sohu.entry.api.vo.SohuEntryInfoVo;
import com.sohu.entry.domain.SohuEntryInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 项目统计Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
public interface SohuEntryInfoMapper extends BaseMapperPlus<SohuEntryInfoMapper, SohuEntryInfo, SohuEntryInfoVo> {

    DataStatVO getCountByUser(@Param("userId") Long userId);
}
