package com.sohu.entry.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.math.BigDecimal;
import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 房地产行业附加对象 sohu_entry_house
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_entry_house")
public class SohuEntryHouse extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * sohu_entry_main已发布行业主体表ID
     */
    private Long entryMain;
    /**
     * 建筑面积
     */
    private String acreage;
    /**
     * 项目用途(公寓，别墅，酒店公寓，民宿，办公室，其它)
     */
    private String projectUse;
    /**
     * 项目进展：现房，期房，二手房
     */
    private String projectEvolve;
    /**
     * 项目销售状态：可售中，沟通中，已售出，下线
     */
    private String projectSell;
    /**
     * 项目产权：0表示永久产权
     */
    private Long projectProperty;
    /**
     * 是否有抵押(Y:有抵押，N:无抵押)
     */
    private String projectPledge;
    /**
     * 抵押金额
     */
    private BigDecimal pledge;
    /**
     * 可售楼栋数量（栋）
     */
    private Integer projectSellBuild;
    /**
     * 可售房源数量（户）
     */
    private Integer projectSellHouse;
    /**
     * 室外图
     */
    private String imageOutdoor;
    /**
     * 室内图
     */
    private String imageIndoor;
    /**
     * 项目进展图
     */
    private String imageEvolve;
    /**
     * 周边图
     */
    private String imageAround;
    /**
     * 附件
     */
    private String annex;
    /**
     * 项目移民说明
     */
    private String projectMigrate;
    /**
     * 购房要求
     */
    private String projectBuyRequire;
    /**
     * 房产税
     */
    private String projectTaxFee;
    /**
     * 房屋保险
     */
    private String projectInsureFee;
    /**
     * 其它购房成本
     */
    private String otherFee;
    /**
     * 物业费
     */
    private String projectFee;
    /**
     * 维护费
     */
    private String projectServiceFee;
    /**
     * 房屋租房市场
     */
    private String leaseMarket;
    /**
     * 继承房屋政策
     */
    private String inheritMarket;
    /**
     * 转让房屋政策
     */
    private String transferMarket;
    /**
     * 福利政策说明
     */
    private String welfareMarket;
    /**
     * 购买方式（公司名义，个人名义，合伙形式，其它）
     */
    private String buyType;
    /**
     * 购买方式说明
     */
    private String busyMsg;
    /**
     * 是否有当地对接人
     */
    private String contactPerson;
    /**
     * 对接人姓名
     */
    private String contactName;
    /**
     * 对接人电话
     */
    private String contactPhone;
    /**
     * 对接人邮箱
     */
    private String contactEmail;
    /**
     * 对接人微信
     */
    private String contactWechat;
    /**
     * 项目授权（图）
     */
    private String projectAuthorize;
    /**
     * 项目承诺
     */
    private String projectPromise;
    /**
     * 项目楼书（图）
     */
    private String projectBook;

}
