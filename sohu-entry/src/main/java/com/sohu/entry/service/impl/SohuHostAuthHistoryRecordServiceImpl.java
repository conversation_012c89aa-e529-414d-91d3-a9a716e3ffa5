package com.sohu.entry.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.entry.api.bo.SohuHostAuthHistoryRecordBo;
import com.sohu.entry.api.vo.SohuHostAuthHistoryRecordVo;
import com.sohu.entry.domain.SohuHostAuthHistoryRecord;
import com.sohu.entry.mapper.SohuHostAuthHistoryRecordMapper;
import com.sohu.entry.service.ISohuHostAuthHistoryRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 入驻认证历史记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-29
 */
@RequiredArgsConstructor
@Service
public class SohuHostAuthHistoryRecordServiceImpl implements ISohuHostAuthHistoryRecordService {

    private final SohuHostAuthHistoryRecordMapper baseMapper;

    /**
     * 查询入驻认证历史记录
     */
    @Override
    public SohuHostAuthHistoryRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询入驻认证历史记录列表
     */
    @Override
    public TableDataInfo<SohuHostAuthHistoryRecordVo> queryPageList(SohuHostAuthHistoryRecordBo bo, PageQuery pageQuery) {
        bo.setUserId(LoginHelper.getUserId());
        LambdaQueryWrapper<SohuHostAuthHistoryRecord> lqw = buildQueryWrapper(bo);
        Page<SohuHostAuthHistoryRecordVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询入驻认证历史记录列表
     */
    @Override
    public List<SohuHostAuthHistoryRecordVo> queryList(SohuHostAuthHistoryRecordBo bo) {
        bo.setUserId(LoginHelper.getUserId());
        LambdaQueryWrapper<SohuHostAuthHistoryRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询入驻认证历史记录列表
     */
    @Override
    public List<SohuHostAuthHistoryRecord> queryListByUserId(SohuHostAuthHistoryRecordBo bo) {
        bo.setUserId(LoginHelper.getUserId());
        LambdaQueryWrapper<SohuHostAuthHistoryRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    private LambdaQueryWrapper<SohuHostAuthHistoryRecord> buildQueryWrapper(SohuHostAuthHistoryRecordBo bo) {
        LambdaQueryWrapper<SohuHostAuthHistoryRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getAuditResult()), SohuHostAuthHistoryRecord::getAuditResult, bo.getAuditResult());
        lqw.eq(null != bo.getAuditTime(), SohuHostAuthHistoryRecord::getAuditTime, bo.getAuditTime());
        lqw.eq(null != bo.getUserId(), SohuHostAuthHistoryRecord::getUserId, bo.getUserId());
        lqw.eq(null != bo.getHostType(), SohuHostAuthHistoryRecord::getHostType, bo.getHostType());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), SohuHostAuthHistoryRecord::getReason, bo.getReason());
        lqw.eq(null != bo.getAuditor(), SohuHostAuthHistoryRecord::getAuditor, bo.getAuditor());
        lqw.orderByDesc(SohuHostAuthHistoryRecord::getAuditTime);
        return lqw;
    }

    /**
     * 新增入驻认证历史记录
     */
    @Override
    public Boolean insertByBo(SohuHostAuthHistoryRecordBo bo) {
        SohuHostAuthHistoryRecord add = BeanUtil.toBean(bo, SohuHostAuthHistoryRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改入驻认证历史记录
     */
    @Override
    public Boolean updateByBo(SohuHostAuthHistoryRecordBo bo) {
        SohuHostAuthHistoryRecord update = BeanUtil.toBean(bo, SohuHostAuthHistoryRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuHostAuthHistoryRecord entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public SohuHostAuthHistoryRecord queryByAuditId(Long auditId) {
        LambdaQueryWrapper<SohuHostAuthHistoryRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuHostAuthHistoryRecord::getAuditId, auditId);
        return this.baseMapper.selectOne(wrapper);
    }

    @Override
    public boolean updateByEntity(SohuHostAuthHistoryRecord hostAuthHistoryRecord) {
        return this.baseMapper.updateById(hostAuthHistoryRecord) > 0;
    }

    /**
     * 批量删除入驻认证历史记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
