package com.sohu.entry.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.entry.api.bo.SohuEntryIndustryProfileBo;
import com.sohu.entry.api.vo.IndustryEffect;
import com.sohu.entry.api.vo.SohuEntryIndustryProfileVo;
import com.sohu.entry.domain.SohuEntryIndustryProfile;

import java.util.Collection;
import java.util.List;

/**
 * 入驻行业所需资料说明Service接口
 *
 * <AUTHOR>
 * @date 2023-08-30
 */
public interface ISohuEntryIndustryProfileService {

    /**
     * 查询入驻行业所需资料说明
     */
    SohuEntryIndustryProfileVo queryById(Long id);

    /**
     * 查询入驻行业所需资料说明列表
     */
    TableDataInfo<SohuEntryIndustryProfileVo> queryPageList(SohuEntryIndustryProfileBo bo, PageQuery pageQuery);

    /**
     * 查询入驻行业所需资料说明列表
     */
    List<SohuEntryIndustryProfileVo> queryList(SohuEntryIndustryProfileBo bo);

    /**
     * 修改入驻行业所需资料说明
     */
    Boolean insertByBo(SohuEntryIndustryProfileBo bo);

    /**
     * 修改入驻行业所需资料说明
     */
    Boolean updateByBo(SohuEntryIndustryProfileBo bo);

    /**
     * 校验并批量删除入驻行业所需资料说明信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 通过行业ID获取入驻行业所需资料说明详细信息
     *
     * @param id 行业ID
     * @return
     */
    SohuEntryIndustryProfile queryByIndustryId(Long id);

    /**
     * 已实现行业列表
     *
     * @return
     */
    @Deprecated
    List<IndustryEffect> effect();

    /**
     * 删除入驻行业资料
     *
     * @param industryId
     */
    void deleteByIndustryId(Long industryId);
}
