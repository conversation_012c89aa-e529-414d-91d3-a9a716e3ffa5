package com.sohu.entry.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.UserConstants;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.entry.api.bo.*;
import com.sohu.entry.api.vo.SohuPlatformConfigAptitudeVo;
import com.sohu.entry.api.vo.SohuPlatformConfigVo;
import com.sohu.entry.domain.SohuPlatformConfig;
import com.sohu.entry.domain.SohuPlatformConfigAptitude;
import com.sohu.entry.mapper.SohuPlatformConfigAptitudeMapper;
import com.sohu.entry.mapper.SohuPlatformConfigMapper;
import com.sohu.entry.service.ISohuPlatformConfigAptitudeService;
import com.sohu.entry.service.ISohuPlatformConfigService;
import com.sohu.middle.api.service.RemoteMiddleUserAgreeVersionService;
import com.sohu.middle.api.vo.SohuAgreeVersionVo;
import com.sohu.system.api.RemotePlatformRoleService;
import com.sohu.system.api.RemoteSysRoleService;
import com.sohu.system.api.vo.SysPlatformRoleVo;
import com.sohu.system.api.vo.SysRoleVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色入驻配置管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-20
 */
@RequiredArgsConstructor
@Service
public class SohuPlatformConfigServiceImpl implements ISohuPlatformConfigService {

    private final SohuPlatformConfigMapper baseMapper;
    private final SohuPlatformConfigAptitudeMapper platformConfigAptitudeMapper;

    private final ISohuPlatformConfigAptitudeService platformConfigAptitudeService;

    @DubboReference
    private RemoteMiddleUserAgreeVersionService remoteMiddleUserAgreeVersionService;
    @DubboReference
    private RemoteSysRoleService remoteSysRoleService;

    /**
     * 查询角色入驻配置管理
     */
    @Override
    public SohuPlatformConfigVo queryById(Long id) {
        SohuPlatformConfigQueryBo bo = new SohuPlatformConfigQueryBo();
        bo.setId(id);
        SohuPlatformConfigVo sohuPlatformConfigVo = baseMapper.queryPageList(bo);

        // 资质内容
        LambdaQueryWrapper<SohuPlatformConfigAptitude> wrapper = Wrappers.lambdaQuery();
        wrapper.in(SohuPlatformConfigAptitude::getPlatformConfigId, id);
        List<SohuPlatformConfigAptitudeVo> aptitudeList = platformConfigAptitudeMapper.selectVoList(wrapper);
        if (CollUtil.isNotEmpty(aptitudeList)) {
            sohuPlatformConfigVo.setAptitudeList(aptitudeList);
        }

        // 协议版本号
        SohuAgreeVersionVo sohuAgreeVersionVo = remoteMiddleUserAgreeVersionService.queryByAgreeId(sohuPlatformConfigVo.getAgreeId());
        if (Objects.nonNull(sohuAgreeVersionVo)) {
            sohuPlatformConfigVo.setVersionId(sohuAgreeVersionVo.getId());
        }
        return sohuPlatformConfigVo;
    }

    /**
     * 查询角色入驻配置管理列表
     */
    @Override
    public TableDataInfo<SohuPlatformConfigVo> queryPageList(SohuPlatformConfigQueryBo bo, PageQuery pageQuery) {
        Page<SohuPlatformConfigVo> result = baseMapper.queryPageList(bo, PageQueryUtils.build(pageQuery));
        List<SohuPlatformConfigVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            Set<Long> platformConfigIds = new HashSet<>();
            for (SohuPlatformConfigVo record : records) {
                Long id = record.getId();
                platformConfigIds.add(id);
            }
            // 资质数量
            List<SohuPlatformConfigAptitudeVo> aptitudeList = platformConfigAptitudeService.queryAptitudeList(platformConfigIds);
            Map<Long, Long> aptitudeMap = aptitudeList.stream()
                    .collect(Collectors.toMap(
                            SohuPlatformConfigAptitudeVo::getPlatformConfigId,
                            e -> 1L,
                            Long::sum));
            records.forEach(f -> {
                Long aptitudeCount = aptitudeMap.get(f.getId());
                f.setAptitudeCount(aptitudeCount);
            });
        }
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询角色入驻配置管理列表
     */
    @Override
    public List<SohuPlatformConfigVo> queryList(SohuPlatformConfigBo bo) {
        LambdaQueryWrapper<SohuPlatformConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuPlatformConfig> buildQueryWrapper(SohuPlatformConfigBo bo) {
        LambdaQueryWrapper<SohuPlatformConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getRoleKey()), SohuPlatformConfig::getRoleKey, bo.getRoleKey());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), SohuPlatformConfig::getType, bo.getType());
        return lqw;
    }

    /**
     * 新增角色入驻配置管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SohuPlatformConfigBo bo) {
        SohuPlatformConfig add = BeanUtil.toBean(bo, SohuPlatformConfig.class);
        validEntityBeforeSave(bo);
        List<SysRoleVo> sysRoleVos = remoteSysRoleService.selectListOfEnableByPlatformRoleKey(bo.getRoleKey());
        if(CollUtil.isNotEmpty(sysRoleVos)){
            String roleKeys = sysRoleVos.stream().map(SysRoleVo::getRoleKey).collect(Collectors.joining(","));
            add.setRoleKeysStr(roleKeys);
        }
        int insert = baseMapper.insert(add);

        // 资质内容
        if (CollUtil.isNotEmpty(bo.getAptitudeList())) {
            bo.getAptitudeList().forEach(f -> f.setPlatformConfigId(add.getId()));
            List<SohuPlatformConfigAptitude> aptitudeList = BeanUtil.copyToList(bo.getAptitudeList(), SohuPlatformConfigAptitude.class);
            platformConfigAptitudeMapper.insertBatch(aptitudeList);
        }
        return insert > 0;
    }

    /**
     * 修改角色入驻配置管理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(SohuPlatformConfigBo bo) {
        SohuPlatformConfig update = BeanUtil.toBean(bo, SohuPlatformConfig.class);
        int result = baseMapper.updateById(update);

        // 资质内容
        platformConfigAptitudeMapper.delete(SohuPlatformConfigAptitude::getPlatformConfigId, bo.getId());
        List<SohuPlatformConfigAptitudeBo> aptitudeList = bo.getAptitudeList();
        if (CollUtil.isNotEmpty(aptitudeList)) {
            aptitudeList.forEach(f -> f.setPlatformConfigId(bo.getId()));
            List<SohuPlatformConfigAptitude> list = BeanUtil.copyToList(aptitudeList, SohuPlatformConfigAptitude.class);
            platformConfigAptitudeMapper.insertBatch(list);
        }
        return result > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuPlatformConfigBo bo) {
        // 校验该角色是否已存在
        if (StringUtils.isNotBlank(bo.getRoleKey())) {
            LambdaQueryWrapper<SohuPlatformConfig> lqw = Wrappers.lambdaQuery();
            lqw.eq(SohuPlatformConfig::getRoleKey, bo.getRoleKey());
            Long count = baseMapper.selectCount(lqw);
            if (count > 0) {
                throw new ServiceException("该角色已存在");
            }
        }
    }

    /**
     * 批量删除角色入驻配置管理
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 角色入驻介绍页编辑
     */
    @Override
    public Boolean updateIntroPage(SohuPlatformConfigPageBo bo) {
        LambdaUpdateWrapper<SohuPlatformConfig> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(SohuPlatformConfig::getIntroPage, bo.getIntroPage());
        wrapper.eq(SohuPlatformConfig::getId, bo.getId());
        int result = baseMapper.update(new SohuPlatformConfig(), wrapper);
        return result > 0;
    }

    /**
     * 角色入驻协议编辑
     */
    @Override
    public Boolean updateProtocol(SohuPlatformConfigPageBo bo) {
        LambdaUpdateWrapper<SohuPlatformConfig> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(SohuPlatformConfig::getAgreeId, bo.getAgreeId());
        wrapper.eq(SohuPlatformConfig::getId, bo.getId());
        int result = baseMapper.update(new SohuPlatformConfig(), wrapper);
        return result > 0;
    }

    /**
     * 角色入驻状态开启/关闭
     */
    @Override
    public Boolean updateEntryStatus(SohuPlatformConfigPageBo bo) {
        SohuPlatformConfig sohuPlatformConfig = baseMapper.selectById(bo.getId());
        String introPage = sohuPlatformConfig.getIntroPage();
        Long agreeId = sohuPlatformConfig.getAgreeId();
        // 开启，校验介绍页、协议是否填写
        if (bo.getEntryStatus()) {
            if (StringUtils.isBlank(introPage) || agreeId == null) {
                throw new ServiceException("开启入驻需要介绍页和协议，请填写后再次尝试");
            }
        }
        sohuPlatformConfig.setEntryStatus(bo.getEntryStatus());
        int result = baseMapper.updateById(sohuPlatformConfig);
        return result > 0;
    }

    /**
     * 根据角色权限查询资质内容列表
     */
    @Override
    public SohuPlatformConfigVo queryAptitudeList(String roleKey) {
        LambdaQueryWrapper<SohuPlatformConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuPlatformConfig::getRoleKey, roleKey);
        lqw.eq(SohuPlatformConfig::getStatus, Boolean.TRUE);
        SohuPlatformConfigVo vo = baseMapper.selectVoOne(lqw);
        if (Objects.isNull(vo)) {
            return null;
        }
        // 资质内容
        LambdaQueryWrapper<SohuPlatformConfigAptitude> wrapper = Wrappers.lambdaQuery();
        wrapper.in(SohuPlatformConfigAptitude::getPlatformConfigId, vo.getId());
        List<SohuPlatformConfigAptitudeVo> aptitudeList = platformConfigAptitudeMapper.selectVoList(wrapper);
        if (CollUtil.isNotEmpty(aptitudeList)) {
            vo.setAptitudeList(aptitudeList);
        }
        return vo;
    }

    /**
     * 角色入驻角色列表
     */
    @Override
    public List<SysPlatformRoleVo> queryRoleList() {
        return baseMapper.queryRoleList();
    }

    @Override
    public Boolean updateConfigRole(SohuPlatformConfigUpdateRoleBo bo) {
        String[] roleList = bo.getRoleKeysStr().split(",");
        if (Arrays.stream(roleList).anyMatch(p -> p.equals(UserConstants.ADMIN_ROLE_KEY))) {
            throw new ServiceException("管理员角色不能入驻");
        }
        SohuPlatformConfig updateEntity = new SohuPlatformConfig();
        updateEntity.setId(bo.getId());
        updateEntity.setRoleKeysStr(bo.getRoleKeysStr());
        this.baseMapper.updateById(updateEntity);
        return true;
    }

    @Override
    public SohuPlatformConfigVo queryByRoleKey(String roleKey) {
        LambdaQueryWrapper<SohuPlatformConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuPlatformConfig::getRoleKey, roleKey);
        lqw.eq(SohuPlatformConfig::getEntryStatus, Boolean.TRUE);
        lqw.last("limit 1");
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public List<SohuPlatformConfigVo> queryListByAgreeId(Long agreeId) {
        LambdaQueryWrapper<SohuPlatformConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuPlatformConfig::getAgreeId, agreeId);
        return baseMapper.selectVoList(lqw);
    }

}
