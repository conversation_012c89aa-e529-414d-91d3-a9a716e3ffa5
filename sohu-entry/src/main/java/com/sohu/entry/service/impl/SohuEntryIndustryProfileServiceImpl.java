package com.sohu.entry.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.UserConstants;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.entry.api.bo.SohuEntryIndustryProfileBo;
import com.sohu.entry.api.vo.IndustryEffect;
import com.sohu.entry.api.vo.SohuEntryIndustryProfileVo;
import com.sohu.entry.domain.SohuEntryIndustryProfile;
import com.sohu.entry.domain.enums.IndustryStatus;
import com.sohu.entry.mapper.SohuEntryIndustryProfileMapper;
import com.sohu.entry.service.ISohuEntryIndustryProfileService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 入驻行业所需资料说明Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-30
 */
@RequiredArgsConstructor
@Service
public class SohuEntryIndustryProfileServiceImpl implements ISohuEntryIndustryProfileService {

    private final SohuEntryIndustryProfileMapper baseMapper;

    /**
     * 查询入驻行业所需资料说明
     */
    @Override
    public SohuEntryIndustryProfileVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询入驻行业所需资料说明列表
     */
    @Override
    public TableDataInfo<SohuEntryIndustryProfileVo> queryPageList(SohuEntryIndustryProfileBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuEntryIndustryProfile> lqw = buildQueryWrapper(bo);
        Page<SohuEntryIndustryProfileVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询入驻行业所需资料说明列表
     */
    @Override
    public List<SohuEntryIndustryProfileVo> queryList(SohuEntryIndustryProfileBo bo) {
        LambdaQueryWrapper<SohuEntryIndustryProfile> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuEntryIndustryProfile> buildQueryWrapper(SohuEntryIndustryProfileBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuEntryIndustryProfile> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getIndustryId() != null, SohuEntryIndustryProfile::getIndustryId, bo.getIndustryId());
        lqw.eq(StringUtils.isNotBlank(bo.getExt()), SohuEntryIndustryProfile::getExt, bo.getExt());
        lqw.eq(bo.getSortIndex() != null, SohuEntryIndustryProfile::getSortIndex, bo.getSortIndex());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuEntryIndustryProfile::getState, bo.getState());
        return lqw;
    }

    /**
     * 新增入驻行业所需资料说明
     */
    @Override
    public Boolean insertByBo(SohuEntryIndustryProfileBo bo) {
        SohuEntryIndustryProfile add = BeanUtil.toBean(bo, SohuEntryIndustryProfile.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改入驻行业所需资料说明
     */
    @Override
    public Boolean updateByBo(SohuEntryIndustryProfileBo bo) {
        SohuEntryIndustryProfile update = BeanUtil.toBean(bo, SohuEntryIndustryProfile.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuEntryIndustryProfile entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除入驻行业所需资料说明
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public SohuEntryIndustryProfile queryByIndustryId(Long id) {
        return this.baseMapper.selectOne(SohuEntryIndustryProfile::getIndustryId, id);
    }

    @Override
    public List<IndustryEffect> effect() {
        List<IndustryEffect> result = new ArrayList<>();
        for (IndustryStatus industryStatus : IndustryStatus.values()) {
            IndustryEffect effect = new IndustryEffect();
            effect.setIdent(industryStatus.name());
            effect.setName(industryStatus.getDescription());
            result.add(effect);
        }
        return result;
    }

    @Override
    public void deleteByIndustryId(Long industryId) {
        baseMapper.delete(SohuEntryIndustryProfile::getIndustryId, industryId);
    }
}
