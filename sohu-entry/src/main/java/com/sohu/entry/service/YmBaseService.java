package com.sohu.entry.service;

import cn.hutool.json.JSONUtil;
import com.sohu.common.core.enums.DictEnum;
import com.sohu.middle.api.enums.PictrueTypeEnum;
import com.sohu.middle.api.vo.YiMaPayConfig;
import com.sohu.system.api.RemoteDictService;
import com.sohu.system.api.domain.SysDictData;
import com.wangcaio2o.ipossa.sdk.client.OpenClient;
import com.wangcaio2o.ipossa.sdk.request.merchantpicture.MerchantPicture;
import com.wangcaio2o.ipossa.sdk.request.merchantpicture.MerchantPictureRequest;
import com.wangcaio2o.ipossa.sdk.response.merchantpicture.MerchantPictureResponse;
import org.apache.dubbo.config.annotation.DubboReference;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Base64;
import java.util.Objects;

/**
 * 翼码支付基础类
 *
 * @author: lyw
 * @date: 2023/10/12 16:41
 * @version: 1.0.0
 */
public abstract class YmBaseService {

    @DubboReference
    private RemoteDictService remoteDictService;


    /**
     * 获取请求客户端
     *
     * @return
     */
    protected OpenClient getYmClient() {
        SysDictData dictData = remoteDictService.getDictData(DictEnum.payConfig.getKey(), DictEnum.YMPayConfig.getKey());
        if (Objects.isNull(dictData)) {
            throw new RuntimeException("翼码支付配置为空");
        }
        String dictValue = dictData.getDictValue();
        YiMaPayConfig yiMaPayConfig = JSONUtil.toBean(dictValue, YiMaPayConfig.class);
        return new OpenClient(yiMaPayConfig.getUrl(), yiMaPayConfig.getIssPid(), yiMaPayConfig.getSystemId(), yiMaPayConfig.getPrivateKey());
    }

    /**
     * 网络图片转base64及翼码上传图片
     *
     * @param imageUrl
     */
    protected String urlChangeBase64(String imageUrl, PictrueTypeEnum typeEnum) {
        try {
            // 获取图片
            URL url = new URL(imageUrl);
            InputStream is = url.openStream();
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            byte[] buffer = new byte[2048];
            int bytesRead;
            while ((bytesRead = is.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            // 将图片内容转换为Base64编码
            byte[] imageBytes = os.toByteArray();
            String base64Image = Base64.getEncoder().encodeToString(imageBytes);
            MerchantPictureRequest request = new MerchantPictureRequest();
            MerchantPicture merchantPicture = new MerchantPicture();
            merchantPicture.setImageContent(base64Image);
            merchantPicture.setType(typeEnum.getCode());
            merchantPicture.setImageName(typeEnum.getName());
            request.setMerchantPictureRequest(merchantPicture);
            request.setPosSeq("12345678901234567890");
            MerchantPictureResponse response = getYmClient().execute(request);
            //log.info("MerchantPictureResponse :{}", JSONObject.toJSONString(response));
            // 输出Base64编码
            return response.getId();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
