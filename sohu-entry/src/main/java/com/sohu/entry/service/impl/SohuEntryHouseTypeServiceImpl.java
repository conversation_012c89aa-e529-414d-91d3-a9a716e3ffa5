package com.sohu.entry.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.entry.api.bo.SohuEntryHouseTypeBo;
import com.sohu.entry.api.vo.SohuEntryHouseTypeVo;
import com.sohu.entry.domain.SohuEntryHouseType;
import com.sohu.entry.mapper.SohuEntryHouseTypeMapper;
import com.sohu.entry.service.ISohuEntryHouseTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 房源户型Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-29
 */
@RequiredArgsConstructor
@Service
public class SohuEntryHouseTypeServiceImpl implements ISohuEntryHouseTypeService {

    private final SohuEntryHouseTypeMapper baseMapper;

    /**
     * 查询房源户型
     */
    @Override
    public SohuEntryHouseTypeVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询房源户型列表
     */
    @Override
    public TableDataInfo<SohuEntryHouseTypeVo> queryPageList(SohuEntryHouseTypeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuEntryHouseType> lqw = buildQueryWrapper(bo);
        Page<SohuEntryHouseTypeVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询房源户型列表
     */
    @Override
    public List<SohuEntryHouseTypeVo> queryList(SohuEntryHouseTypeBo bo) {
        LambdaQueryWrapper<SohuEntryHouseType> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuEntryHouseType> buildQueryWrapper(SohuEntryHouseTypeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuEntryHouseType> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getHouseId() != null, SohuEntryHouseType::getHouseId, bo.getHouseId());
        lqw.eq(bo.getRoomNum() != null, SohuEntryHouseType::getRoomNum, bo.getRoomNum());
        lqw.eq(bo.getOfficeNum() != null, SohuEntryHouseType::getOfficeNum, bo.getOfficeNum());
        lqw.eq(bo.getToiletNum() != null, SohuEntryHouseType::getToiletNum, bo.getToiletNum());
        lqw.eq(bo.getKitchenNum() != null, SohuEntryHouseType::getKitchenNum, bo.getKitchenNum());
        lqw.eq(bo.getTotalPrice() != null, SohuEntryHouseType::getTotalPrice, bo.getTotalPrice());
        return lqw;
    }

    /**
     * 新增房源户型
     */
    @Override
    public Boolean insertByBo(SohuEntryHouseTypeBo bo) {
        SohuEntryHouseType add = BeanUtil.toBean(bo, SohuEntryHouseType.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改房源户型
     */
    @Override
    public Boolean updateByBo(SohuEntryHouseTypeBo bo) {
        SohuEntryHouseType update = BeanUtil.toBean(bo, SohuEntryHouseType.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuEntryHouseType entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除房源户型
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public void deleteByHouseId(Long houseId) {
        baseMapper.delete(SohuEntryHouseType::getHouseId,houseId);
    }
}
