package com.sohu.entry.controller;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.entry.api.bo.SohuDeveloperSettlementBo;
import com.sohu.entry.api.vo.SohuDeveloperSettlementVo;
import com.sohu.entry.service.ISohuDeveloperSettlementService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 开发商入驻
 * 前端访问路由地址为:/system/developerSettlement
 *
 * <AUTHOR>
 * @date 2023-08-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/developerSettlement")
public class SohuDeveloperSettlementController extends BaseController {

    private final ISohuDeveloperSettlementService iSohuDeveloperSettlementService;

    /**
     * 查询开发商入驻列表
     */
    @GetMapping("/list")
    public TableDataInfo<SohuDeveloperSettlementVo> list(SohuDeveloperSettlementBo bo, PageQuery pageQuery) {
        return iSohuDeveloperSettlementService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出开发商入驻列表
     */
    @Log(title = "开发商入驻", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuDeveloperSettlementBo bo, HttpServletResponse response) {
        List<SohuDeveloperSettlementVo> list = iSohuDeveloperSettlementService.queryList(bo);
        ExcelUtil.exportExcel(list, "开发商入驻", SohuDeveloperSettlementVo.class, response);
    }

    /**
     * 获取开发商入驻详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<SohuDeveloperSettlementVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuDeveloperSettlementService.queryById(id));
    }

    /**
     * 新增开发商入驻
     */
    @Log(title = "开发商入驻", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuDeveloperSettlementBo bo) {
        return toAjax(iSohuDeveloperSettlementService.insertByBo(bo));
    }

    /**
     * 修改开发商入驻
     */
    @Log(title = "开发商入驻", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuDeveloperSettlementBo bo) {
        return toAjax(iSohuDeveloperSettlementService.updateByBo(bo));
    }

    /**
     * 删除开发商入驻
     *
     * @param ids 主键串
     */
    @Log(title = "开发商入驻", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuDeveloperSettlementService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

}
