package com.sohu.entry.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.entry.api.bo.SohuEntryMainBo;
import com.sohu.entry.api.vo.SohuEntryMainVo;
import com.sohu.entry.service.ISohuEntryMainService;
import com.sohu.middle.api.service.RemoteMiddleHistoryWordService;
import com.sohu.middle.api.vo.SohuCategoryVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 项目主体
 * 前端访问路由地址为:/entry/entryMain
 *
 * <AUTHOR>
 * @date 2023-08-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/entry/main")
public class SohuEntryMainController extends BaseController {

    private final ISohuEntryMainService iSohuEntryMainService;
    @DubboReference
    private RemoteMiddleHistoryWordService remoteMiddleHistoryWordService;

    /**
     * 分页查询项目列表
     */
    @GetMapping("/list")
    @SaIgnore
    public TableDataInfo<SohuEntryMainVo> list(SohuEntryMainBo bo, PageQuery pageQuery) {
        // 非游客状态记录搜索历史
        Long userId = LoginHelper.getUserId();
        if (StringUtils.isNotBlank(bo.getName()) && (userId != null && userId > 0L)) {
            remoteMiddleHistoryWordService.insertWord(userId,bo.getName());
        }
        return iSohuEntryMainService.queryPageList(bo, pageQuery);
    }

    /**
     * 用户已发项目
     */
    @GetMapping("/my/list")
    public TableDataInfo<SohuEntryMainVo> myList(SohuEntryMainBo bo, PageQuery pageQuery) {
        bo.setUserId(LoginHelper.getUserId());
        return iSohuEntryMainService.queryPageList(bo, pageQuery);
    }

    /**
     * 个人中心项目列表
     */
    @GetMapping("/page/center")
    public TableDataInfo<SohuEntryMainVo> pageEntryCenter(@RequestParam(required = false) Long userId, PageQuery pageQuery) {
        return iSohuEntryMainService.pageEntryCenter(userId, pageQuery);
    }

    /**
     * 项目收藏列表
     *
     * @param title     搜索关键词
     * @param pageQuery 分页参数
     */
    @GetMapping("/collect")
    public R<List<SohuEntryMainVo>> collectPage(@RequestParam(required = false) String title, PageQuery pageQuery) {
        return R.ok(iSohuEntryMainService.collectPage(title, pageQuery));
    }

    /**
     * 导出
     */
    @SaCheckPermission("entry:entryMain:export")
    @Log(title = "已发布的行业主体", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuEntryMainBo bo, HttpServletResponse response) {
        List<SohuEntryMainVo> list = iSohuEntryMainService.queryList(bo);
        ExcelUtil.exportExcel(list, "已发布的行业主体", SohuEntryMainVo.class, response);
    }

    /**
     * 项目详情
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    @SaIgnore
    public R<SohuEntryMainVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuEntryMainService.queryById(id));
    }

    /**
     * 新增项目
     */
    @Log(title = "已发布的行业主体", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuEntryMainBo bo) {
        return toAjax(iSohuEntryMainService.insertByBo(bo));
    }

    /**
     * 修改项目
     */
    @Log(title = "已发布的行业主体", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuEntryMainBo bo) {
        return toAjax(iSohuEntryMainService.updateByBo(bo));
    }

    /**
     * 删除项目
     *
     * @param ids 主键串
     */
    @SaCheckPermission("entry:entryMain:remove")
    @Log(title = "已发布的行业主体", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuEntryMainService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 行业审核或拒绝
     *
     * @param bo
     */
    @Log(title = "行业审核或拒绝", businessType = BusinessType.OTHER)
    @PostMapping("/process")
    public R<Boolean> process(@RequestBody SohuEntryMainBo bo) {
        return R.ok(iSohuEntryMainService.process(bo));
    }

    /**
     * 项目上下架
     *
     * @return {@link R}
     */
    @Operation(summary = "项目上下架")
    @PostMapping("/shelfOnOrOff")
    @Log(title = "项目上下架", businessType = BusinessType.UPDATE)
    public R<Boolean> shelfOnOrOff(@RequestBody SohuEntryMainBo bo) {
        iSohuEntryMainService.shelfOnOrOff(bo);
        return R.ok(Boolean.TRUE);
    }

    /**
     * 项目分类
     *
     * @param ident 标识
     */
    @Deprecated
    @GetMapping("/category")
    @SaIgnore
    public R<List<SohuCategoryVo>> categorys(@RequestParam String ident) {
        return R.ok(iSohuEntryMainService.categorys(ident));
    }
}
