package com.sohu.entry.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.entry.api.bo.SohuEntryHouseBo;
import com.sohu.entry.api.vo.SohuEntryHouseVo;
import com.sohu.entry.service.ISohuEntryHouseService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 房地产
 * 前端访问路由地址为:/entry/entryHouse
 *
 * <AUTHOR>
 * @date 2023-08-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/entry/house")
public class SohuEntryHouseController extends BaseController {

    private final ISohuEntryHouseService iSohuEntryHouseService;

    /**
     * 查询房地产行业附加列表
     */
    @SaCheckPermission("entry:entryHouse:list")
    @GetMapping("/list")
    public TableDataInfo<SohuEntryHouseVo> list(SohuEntryHouseBo bo, PageQuery pageQuery) {
        return iSohuEntryHouseService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出房地产行业附加列表
     */
    @SaCheckPermission("entry:entryHouse:export")
    @Log(title = "房地产行业附加", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuEntryHouseBo bo, HttpServletResponse response) {
        List<SohuEntryHouseVo> list = iSohuEntryHouseService.queryList(bo);
        ExcelUtil.exportExcel(list, "房地产行业附加", SohuEntryHouseVo.class, response);
    }

    /**
     * 获取房地产行业附加详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("entry:entryHouse:query")
    @GetMapping("/{id}")
    public R<SohuEntryHouseVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuEntryHouseService.queryById(id));
    }

    /**
     * 新增房地产行业附加
     */
    @SaCheckPermission("entry:entryHouse:add")
    @Log(title = "房地产行业附加", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuEntryHouseBo bo) {
        return toAjax(iSohuEntryHouseService.insertByBo(bo));
    }

    /**
     * 修改房地产行业附加
     */
    @SaCheckPermission("entry:entryHouse:edit")
    @Log(title = "房地产行业附加", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuEntryHouseBo bo) {
        return toAjax(iSohuEntryHouseService.updateByBo(bo));
    }

    /**
     * 删除房地产行业附加
     *
     * @param ids 主键串
     */
    @SaCheckPermission("entry:entryHouse:remove")
    @Log(title = "房地产行业附加", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuEntryHouseService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
