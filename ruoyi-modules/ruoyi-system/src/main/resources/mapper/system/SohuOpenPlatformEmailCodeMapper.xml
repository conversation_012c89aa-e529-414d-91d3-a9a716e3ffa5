<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.SohuOpenPlatformEmailCodeMapper">

    <resultMap type="org.dromara.system.domain.SohuOpenPlatformEmailCode" id="SohuOpenPlatformEmailCodeResult">
        <result property="id"               column="id"               />
        <result property="email"            column="email"            />
        <result property="code"             column="code"             />
        <result property="codeType"         column="code_type"        />
        <result property="expireTime"       column="expire_time"      />
        <result property="used"             column="used"             />
        <result property="createTime"       column="create_time"      />
    </resultMap>

</mapper>
