<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.SohuOpenPlatformUserMapper">

    <resultMap type="org.dromara.system.domain.SohuOpenPlatformUser" id="SohuOpenPlatformUserResult">
        <result property="id"               column="id"               />
        <result property="email"            column="email"            />
        <result property="password"         column="password"         />
        <result property="nickname"         column="nickname"         />
        <result property="avatar"           column="avatar"           />
        <result property="status"           column="status"           />
        <result property="emailVerified"    column="email_verified"   />
        <result property="lastLoginTime"    column="last_login_time"  />
        <result property="lastLoginIp"      column="last_login_ip"    />
        <result property="createTime"       column="create_time"      />
        <result property="updateTime"       column="update_time"      />
        <result property="createBy"         column="create_by"        />
        <result property="updateBy"         column="update_by"        />
        <result property="isDel"            column="is_del"           />
    </resultMap>

</mapper>
