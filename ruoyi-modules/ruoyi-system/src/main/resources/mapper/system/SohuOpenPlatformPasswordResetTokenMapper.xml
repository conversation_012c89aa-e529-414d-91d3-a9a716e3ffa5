<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.SohuOpenPlatformPasswordResetTokenMapper">

    <resultMap type="org.dromara.system.domain.SohuOpenPlatformPasswordResetToken" id="SohuOpenPlatformPasswordResetTokenResult">
        <result property="id"               column="id"               />
        <result property="userId"           column="user_id"          />
        <result property="token"            column="token"            />
        <result property="expireTime"       column="expire_time"      />
        <result property="used"             column="used"             />
        <result property="createTime"       column="create_time"      />
    </resultMap>

</mapper>
