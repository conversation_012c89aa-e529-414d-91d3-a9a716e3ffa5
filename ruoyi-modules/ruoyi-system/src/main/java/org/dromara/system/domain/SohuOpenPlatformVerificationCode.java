package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import org.dromara.common.core.domain.model.SohuEntity;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;

/**
 * 通用验证码对象 sohu_open_platform_verification_code
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@TableName("sohu_open_platform_verification_code")
public class SohuOpenPlatformVerificationCode extends SohuEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 联系方式类型（email邮箱 mobile手机号）
     */
    private String contactType;

    /**
     * 联系方式值（邮箱地址或手机号）
     */
    private String contactValue;

    /**
     * 验证码
     */
    private String code;

    /**
     * 验证码类型（register注册 reset_password重置密码 login登录 bind_account绑定账号）
     */
    private String codeType;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 是否已使用（0未使用 1已使用）
     */
    private Boolean used;

    /**
     * 发送次数
     */
    private Integer sendCount;

    /**
     * 请求IP地址
     */
    private String ipAddress;

    /**
     * 用户代理
     */
    private String userAgent;

}
