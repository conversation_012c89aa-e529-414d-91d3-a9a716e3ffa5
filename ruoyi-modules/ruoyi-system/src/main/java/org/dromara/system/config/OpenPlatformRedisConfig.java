package org.dromara.system.config;

import org.springframework.context.annotation.Configuration;

/**
 * 开放平台Redis配置
 * 
 * 密码重置令牌存储配置说明：
 * 1. Key格式: password_reset_token:{token}
 * 2. Value: 用户ID
 * 3. TTL: 3600秒（1小时）
 * 4. 自动过期清理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Configuration
public class OpenPlatformRedisConfig {

    /**
     * 密码重置令牌前缀
     */
    public static final String PASSWORD_RESET_TOKEN_PREFIX = "password_reset_token:";

    /**
     * 邮箱验证码前缀（如果需要也可以用Redis存储）
     */
    public static final String EMAIL_CODE_PREFIX = "email_code:";

    /**
     * 用户登录令牌前缀（如果需要存储登录状态）
     */
    public static final String USER_LOGIN_TOKEN_PREFIX = "user_login_token:";

}
