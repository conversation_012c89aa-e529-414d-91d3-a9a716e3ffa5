package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.domain.model.SohuEntity;

import java.util.Date;

/**
 * 许愿狐开放平台小程序对象 sohu_open_platform_applet
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_open_platform_applet")
public class SohuOpenPlatformApplet extends SohuEntity {

    /**
     * 小程序id，主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 小程序名称
     */
    private String name;
    /**
     * 小程序简称
     */
    private String shortName;
    /**
     * 小程序简介(预留)
     */
    private String description;
    /**
     * 小程序详细介绍
     */
    private String intro;
    /**
     * 小程序头像URL
     */
    private String avatar;
    /**
     * 小程序AppID
     */
    private String appId;
    /**
     * 小程序AppSecret
     */
    private String appSecret;
    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status;
    /**
     * 拥有者用户ID
     */
    private Long userId;
    /**
     * 最近一次修改名称/简介/头像的日期
     */
    private Date lastModifiedDate;
    /**
     * 本年度修改次数
     */
    private Long modificationCount;
    /**
     * 修改类型：name-名称, avatar-头像, description-简介, short_name-简称, none-无修改
     */
    private String modificationType;
    /**
     * ICP备案号
     */
    private String icpRecordNumber;
    /**
     * 是否删除 0.未删除 1.已删除
     */
    private Integer isDel;

}
