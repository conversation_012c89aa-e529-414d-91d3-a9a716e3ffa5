package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.domain.model.SohuEntity;

import java.util.Date;

/**
 * 许愿狐开放平台小程序版本对象 sohu_open_platform_applet_version
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_open_platform_applet_version")
public class SohuOpenPlatformAppletVersion extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 版本ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 小程序ID
     */
    private Long appletId;
    /**
     * 发布者用户id
     */
    private Long userId;
    /**
     * 版本号（如1.1.0）
     */
    @Version
    private String version;
    /**
     * 版本描述
     */
    private String versionDesc;
    /**
     * 更新说明
     */
    private String updateDesc;
    /**
     * 关键词
     */
    private String keywords;
    /**
     * 源码包URL
     */
    private String sourceCodeUrl;
    /**
     * 源码包哈希值
     */
    private String sourceCodeHash;
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    /**
     * 版本状态：0-草稿，1-待审核，2-审核中，3-审核通过，4-审核拒绝，5-发布，6-已回退
     */
    private Long status;
    /**
     * 是否自动发布：0-否，1-是
     */
    private Integer isPublished;
    /**
     * 提交审核时间
     */
    private Date submitTime;
    /**
     * 审核完成时间
     */
    private Date reviewTime;
    /**
     * 发布时间
     */
    private Date publishTime;
    /**
     * 下线时间
     */
    private Date offlineTime;

}
