package org.dromara.system.enums;

/**
 * 用户类型枚举
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public enum UserTypeEnum {

    /**
     * 系统用户
     */
    SYS_USER("sys_user", "系统用户"),

    /**
     * 开放平台用户
     */
    OPEN_PLATFORM("open_platform", "开放平台用户");

    private final String code;
    private final String desc;

    UserTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     */
    public static UserTypeEnum getByCode(String code) {
        for (UserTypeEnum userType : values()) {
            if (userType.getCode().equals(code)) {
                return userType;
            }
        }
        return null;
    }

}
