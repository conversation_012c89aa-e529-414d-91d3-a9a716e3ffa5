package org.dromara.system.domain.bo;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 开放平台注册业务对象
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
public class OpenPlatformRegisterBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 联系方式值（邮箱地址或手机号，系统会自动识别类型）
     */
    @NotBlank(message = "联系方式不能为空")
    @Schema(name = "contactValue", description = "联系方式值（邮箱地址或手机号，系统会自动识别类型）", example = "<EMAIL>")
    private String contactValue;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Schema(name = "password", description = "密码", example = "123456")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;

    /**
     * 确认密码
     */
    @NotBlank(message = "确认密码不能为空")
    @Schema(name = "confirmPassword", description = "确认密码", example = "123456")
    private String confirmPassword;

    /**
     * 昵称
     */
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    @Schema(name = "nickname", description = "昵称", example = "sohu")
    private String nickname;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    @Schema(name = "verificationCode", description = "验证码", example = "123456")
    private String verificationCode;

    /**
     * 账号类型（personal个人 business企业）
     */
    @NotBlank(message = "账号类型不能为空")
    @Schema(name = "accountType", description = "账号类型（personal个人 business企业）", example = "personal")
    private String accountType;

    /**
     * 真实姓名（个人必填，企业为法人代表姓名）
     */
    @NotBlank(message = "真实姓名不能为空")
    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    @Schema(name = "realName", description = "真实姓名（个人必填，企业为法人代表姓名）", example = "张三")
    private String realName;

    /**
     * 身份证号码（个人必填，企业为法人代表身份证）
     */
    @NotBlank(message = "身份证号码不能为空")
    @Size(max = 20, message = "身份证号码长度不能超过20个字符")
    @Schema(name = "idCardNumber", description = "身份证号码（个人必填，企业为法人代表身份证）", example = "123456789012345678")
    private String idCardNumber;

    /**
     * 身份证正面照片URL
     */
    @NotBlank(message = "身份证正面照片不能为空")
    @Schema(name = "idCardFrontUrl", description = "身份证正面照片URL", example = "http://xxxx.png")
    private String idCardFrontUrl;

    /**
     * 身份证反面照片URL
     */
    @NotBlank(message = "身份证反面照片不能为空")
    @Schema(name = "idCardBackUrl", description = "身份证反面照片URL", example = "http://xxxx.png")
    private String idCardBackUrl;

    // ========== 企业用户专用字段 ==========

    /**
     * 企业名称（企业用户必填）
     */
    @Schema(name = "companyName", description = "企业名称（企业用户必填）", example = "sohu")
    private String companyName;

    /**
     * 营业执照注册号（企业用户必填）
     */
    @Schema(name = "businessLicenseNumber", description = "营业执照注册号（企业用户必填）", example = "123456789012345678")
    private String businessLicenseNumber;

    /**
     * 营业执照照片URL（企业用户必填）
     */
    @Schema(name = "businessLicenseUrl", description = "营业执照照片URL", example = "http://123.png")
    private String businessLicenseUrl;

    /**
     * 管理员手机号
     */
    @NotBlank(message = "管理员手机号不能为空")
    @Schema(name = "managerPhone", description = "管理员手机号", example = "***********")
    private String managerPhone;

    /**
     * 手机验证码
     */
    @NotBlank(message = "手机验证码不能为空")
    private String mangerVerificationCode;

}
