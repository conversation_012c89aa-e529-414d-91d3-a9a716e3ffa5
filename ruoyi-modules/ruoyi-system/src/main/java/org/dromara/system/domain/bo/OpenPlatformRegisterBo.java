package org.dromara.system.domain.bo;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 开放平台注册业务对象
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
public class OpenPlatformRegisterBo {

    /**
     * 联系方式值（邮箱地址或手机号，系统会自动识别类型）
     */
    @NotBlank(message = "联系方式不能为空")
    private String contactValue;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;

    /**
     * 确认密码
     */
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

    /**
     * 昵称
     */
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    private String verificationCode;

    /**
     * 用户主体信息类型(个人-personal,企业-enterprise)
     */
    @NotBlank(message = "用户主体信息类型不能为空")
    @Schema(name = "userType", description = "用户主体信息类型(个人-personal,企业-enterprise)")
    private String userType;
}
