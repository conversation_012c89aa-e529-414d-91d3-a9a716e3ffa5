package org.dromara.system.utils;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.Constants;
import org.dromara.common.core.constant.GlobalConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.exception.user.CaptchaExpireException;
import org.dromara.common.core.utils.MessageUtils;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mail.utils.MailUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.system.domain.SohuOpenPlatformVerificationCode;
import org.dromara.system.enums.ContactTypeEnum;
import org.dromara.system.enums.VerificationCodeTypeEnum;
import org.dromara.system.mapper.SohuOpenPlatformVerificationCodeMapper;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * 通用验证码工具类
 * 支持邮箱验证码、手机号验证码等多种验证方式
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VerificationCodeUtil {

    private final SohuOpenPlatformVerificationCodeMapper verificationCodeMapper;

    /**
     * 邮箱正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );

    /**
     * 手机号正则表达式（中国大陆）
     */
    private static final Pattern MOBILE_PATTERN = Pattern.compile(
        "^1[3-9]\\d{9}$"
    );

    /**
     * 验证验证码
     *
     * @param contactValue 联系方式值
     * @param codeValue    验证码
     * @return 是否验证成功
     */
    public Boolean validateCode(String contactValue, String codeValue) {
        // 自动识别联系方式类型
        ContactTypeEnum contactType = identifyContactType(contactValue);
        if (contactType == null) {
            return false;
        }
        String code = RedisUtils.getCacheObject(GlobalConstants.CAPTCHA_CODE_KEY + contactValue);
        if (StringUtils.isBlank(code)) {
            throw new CaptchaExpireException();
        }
        return code.equals(codeValue);
    }

    /**
     * 自动识别联系方式类型
     */
    private ContactTypeEnum identifyContactType(String contactValue) {
        if (StrUtil.isBlank(contactValue)) {
            return null;
        }

        if (EMAIL_PATTERN.matcher(contactValue).matches()) {
            return ContactTypeEnum.EMAIL;
        }

        if (MOBILE_PATTERN.matcher(contactValue).matches()) {
            return ContactTypeEnum.MOBILE;
        }

        return null;
    }

}
