package org.dromara.system.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 开放平台企业注册业务对象
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
public class OpenPlatformBusinessRegisterBo {

    /**
     * 联系方式值（邮箱地址或手机号，系统会自动识别类型）
     */
    @NotBlank(message = "联系方式不能为空")
    private String contactValue;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;

    /**
     * 确认密码
     */
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

    /**
     * 昵称
     */
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    private String verificationCode;

    /**
     * 企业名称
     */
    @NotBlank(message = "企业名称不能为空")
    @Size(max = 100, message = "企业名称长度不能超过100个字符")
    private String companyName;

    /**
     * 营业执照注册号
     */
    @NotBlank(message = "营业执照注册号不能为空")
    @Pattern(regexp = "^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$", 
             message = "营业执照注册号格式不正确")
    private String businessLicenseNumber;

    /**
     * 营业执照照片URL
     */
    @NotBlank(message = "营业执照照片不能为空")
    private String businessLicenseUrl;

    /**
     * 法人代表真实姓名
     */
    @NotBlank(message = "法人代表姓名不能为空")
    @Size(max = 50, message = "法人代表姓名长度不能超过50个字符")
    private String realName;

    /**
     * 法人代表身份证号码
     */
    @NotBlank(message = "法人代表身份证号码不能为空")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号码格式不正确")
    private String idCardNumber;

    /**
     * 法人代表身份证正面照片URL
     */
    @NotBlank(message = "法人代表身份证正面照片不能为空")
    private String idCardFrontUrl;

    /**
     * 法人代表身份证反面照片URL
     */
    @NotBlank(message = "法人代表身份证反面照片不能为空")
    private String idCardBackUrl;

    /**
     * 管理员手机号
     */
    @NotBlank(message = "管理员手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String managerPhone;

}
