package org.dromara.system.domain.vo;

import java.io.Serializable;
import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;



/**
 * 许愿狐开放平台小程序视图对象
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Data
@ExcelIgnoreUnannotated
public class SohuOpenPlatformAppletVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 小程序id，主键
     */
    @ExcelProperty(value = "小程序id，主键")
    private Long id;

    /**
     * 小程序名称
     */
    @ExcelProperty(value = "小程序名称")
    private String name;

    /**
     * 小程序简称
     */
    @ExcelProperty(value = "小程序简称")
    private String shortName;

    /**
     * 小程序简介(预留)
     */
    @ExcelProperty(value = "小程序简介(预留)")
    private String description;

    /**
     * 小程序详细介绍
     */
    @ExcelProperty(value = "小程序详细介绍")
    private String intro;

    /**
     * 小程序头像URL
     */
    @ExcelProperty(value = "小程序头像URL")
    private String avatar;

    /**
     * 小程序AppID
     */
    @ExcelProperty(value = "小程序AppID")
    private String appId;

    /**
     * 小程序AppSecret
     */
    @ExcelProperty(value = "小程序AppSecret")
    private String appSecret;

    /**
     * 状态：1-正常，0-禁用
     */
    @ExcelProperty(value = "状态：1-正常，0-禁用")
    private Integer status;

    /**
     * 拥有者用户ID
     */
    @ExcelProperty(value = "拥有者用户ID")
    private Long userId;

    /**
     * 最近一次修改名称/简介/头像的日期
     */
    @ExcelProperty(value = "最近一次修改名称/简介/头像的日期")
    private Date lastModifiedDate;

    /**
     * 本年度修改次数
     */
    @ExcelProperty(value = "本年度修改次数")
    private Long modificationCount;

    /**
     * 修改类型：name-名称, avatar-头像, description-简介, short_name-简称, none-无修改
     */
    @ExcelProperty(value = "修改类型：name-名称, avatar-头像, description-简介, short_name-简称, none-无修改")
    private String modificationType;

    /**
     * ICP备案号
     */
    @ExcelProperty(value = "ICP备案号")
    private String icpRecordNumber;

    /**
     * 是否删除 0.未删除 1.已删除
     */
    @ExcelProperty(value = "是否删除 0.未删除 1.已删除")
    private Integer isDel;

}
