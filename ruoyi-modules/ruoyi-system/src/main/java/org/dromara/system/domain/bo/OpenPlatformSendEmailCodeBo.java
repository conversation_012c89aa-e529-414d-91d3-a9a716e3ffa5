package org.dromara.system.domain.bo;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 开放平台发送邮箱验证码业务对象
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
public class OpenPlatformSendEmailCodeBo {

    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 验证码类型（register注册 reset_password重置密码）
     */
    @NotBlank(message = "验证码类型不能为空")
    private String codeType;

}
