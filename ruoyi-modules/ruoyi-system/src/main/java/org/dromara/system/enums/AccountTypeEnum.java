package org.dromara.system.enums;

/**
 * 账号类型枚举
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public enum AccountTypeEnum {

    /**
     * 个人账号
     */
    PERSONAL("personal", "个人"),

    /**
     * 企业账号
     */
    BUSINESS("business", "企业");

    private final String code;
    private final String desc;

    AccountTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     */
    public static AccountTypeEnum getByCode(String code) {
        for (AccountTypeEnum accountType : values()) {
            if (accountType.getCode().equals(code)) {
                return accountType;
            }
        }
        return null;
    }

}
