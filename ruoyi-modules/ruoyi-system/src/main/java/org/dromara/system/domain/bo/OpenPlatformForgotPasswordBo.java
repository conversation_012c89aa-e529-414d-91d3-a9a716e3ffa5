package org.dromara.system.domain.bo;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 开放平台忘记密码业务对象
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
public class OpenPlatformForgotPasswordBo implements Serializable {


    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 联系方式值（邮箱地址或手机号，系统会自动识别类型）
     */
    @NotBlank(message = "联系方式不能为空")
    @Schema(name = "contactValue", description = "联系方式不能为空", example = "<EMAIL>")
    private String contactValue;

}
