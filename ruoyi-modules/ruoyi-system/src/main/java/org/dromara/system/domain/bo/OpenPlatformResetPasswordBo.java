package org.dromara.system.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 开放平台重置密码业务对象
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
public class OpenPlatformResetPasswordBo {

    /**
     * 重置令牌
     */
    @NotBlank(message = "重置令牌不能为空")
    private String token;

    /**
     * 新密码
     */
    @NotBlank(message = "新密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String newPassword;

    /**
     * 确认新密码
     */
    @NotBlank(message = "确认新密码不能为空")
    private String confirmPassword;

}
