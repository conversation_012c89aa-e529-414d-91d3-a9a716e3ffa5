package org.dromara.system.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 开放平台重置密码业务对象
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
public class OpenPlatformResetPasswordBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 重置令牌
     */
    @NotBlank(message = "重置令牌不能为空")
    @Schema(name = "token", description = "重置令牌", example = "123456")
    private String token;

    /**
     * 新密码
     */
    @NotBlank(message = "新密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    @Schema(name = "newPassword", description = "新密码", example = "123456")
    private String newPassword;

    /**
     * 确认新密码
     */
    @NotBlank(message = "确认新密码不能为空")
    @Schema(name = "confirmPassword", description = "确认新密码", example = "123456")
    private String confirmPassword;

}
