package org.dromara.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;


/**
 * 许愿狐开放平台个人/企业主体信息视图对象
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Data
@ExcelIgnoreUnannotated
public class SohuOpenPlatformAccountVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 用户ID，外键，sohu_open_platform_user
     */
    @ExcelProperty(value = "用户ID，外键，sohu_open_platform_user")
    private Long userId;

    /**
     * 账号类型（personal=个人 business=企业认证）
     */
    @ExcelProperty(value = "账号类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "p=ersonal=个人,b=usiness=企业认证")
    private String accountType;

    /**
     * 企业名称
     */
    @ExcelProperty(value = "企业名称")
    private String companyName;

    /**
     * 营业执照照片URL
     */
    @ExcelProperty(value = "营业执照照片URL")
    private String businessLicenseUrl;

    /**
     * 营业执照注册号
     */
    @ExcelProperty(value = "营业执照注册号")
    private String businessLicenseNumber;

    /**
     * 真实姓名
     */
    @ExcelProperty(value = "真实姓名")
    private String realName;

    /**
     * 身份证正面照片URL
     */
    @ExcelProperty(value = "身份证正面照片URL")
    private String idCardFrontUrl;

    /**
     * 身份证反面照片URL
     */
    @ExcelProperty(value = "身份证反面照片URL")
    private String idCardBackUrl;

    /**
     * 身份证号码
     */
    @ExcelProperty(value = "身份证号码")
    private String idCardNumber;

    /**
     * 管理员手机号
     */
    @ExcelProperty(value = "管理员手机号")
    private String managerPhone;

    /**
     * 是否删除 0.未删除 1.已删除
     */
    @ExcelProperty(value = "是否删除 0.未删除 1.已删除")
    private Integer isDel;

}
