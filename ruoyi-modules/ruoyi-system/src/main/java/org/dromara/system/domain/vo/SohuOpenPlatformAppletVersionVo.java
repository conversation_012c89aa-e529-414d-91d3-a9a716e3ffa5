package org.dromara.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 许愿狐开放平台小程序版本视图对象
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Data
@ExcelIgnoreUnannotated
public class SohuOpenPlatformAppletVersionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 版本ID
     */
    @ExcelProperty(value = "版本ID")
    private Long id;

    /**
     * 小程序ID
     */
    @ExcelProperty(value = "小程序ID")
    private Long appletId;

    /**
     * 发布者用户id
     */
    @ExcelProperty(value = "发布者用户id")
    private Long userId;

    /**
     * 版本描述
     */
    @ExcelProperty(value = "版本描述")
    private String versionDesc;

    /**
     * 更新说明
     */
    @ExcelProperty(value = "更新说明")
    private String updateDesc;

    /**
     * 关键词
     */
    @ExcelProperty(value = "关键词")
    private String keywords;

    /**
     * 源码包URL
     */
    @ExcelProperty(value = "源码包URL")
    private String sourceCodeUrl;

    /**
     * 源码包哈希值
     */
    @ExcelProperty(value = "源码包哈希值")
    private String sourceCodeHash;

    /**
     * 文件大小（字节）
     */
    @ExcelProperty(value = "文件大小", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "字=节")
    private Long fileSize;

    /**
     * 版本状态：0-草稿，1-待审核，2-审核中，3-审核通过，4-审核拒绝，5-发布，6-已回退
     */
    @ExcelProperty(value = "版本状态：0-草稿，1-待审核，2-审核中，3-审核通过，4-审核拒绝，5-发布，6-已回退")
    private Long status;

    /**
     * 是否自动发布：0-否，1-是
     */
    @ExcelProperty(value = "是否自动发布：0-否，1-是")
    private Integer isPublished;

    /**
     * 提交审核时间
     */
    @ExcelProperty(value = "提交审核时间")
    private Date submitTime;

    /**
     * 审核完成时间
     */
    @ExcelProperty(value = "审核完成时间")
    private Date reviewTime;

    /**
     * 发布时间
     */
    @ExcelProperty(value = "发布时间")
    private Date publishTime;

    /**
     * 下线时间
     */
    @ExcelProperty(value = "下线时间")
    private Date offlineTime;


}
