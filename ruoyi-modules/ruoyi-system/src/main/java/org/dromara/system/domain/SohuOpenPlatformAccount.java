package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.domain.model.SohuEntity;

/**
 * 许愿狐开放平台个人/企业主体信息对象 sohu_open_platform_account
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_open_platform_account")
public class SohuOpenPlatformAccount extends SohuEntity {

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID，外键，sohu_open_platform_user
     */
    private Long userId;
    /**
     * 账号类型（personal=个人 business=企业认证）
     */
    private String accountType;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 营业执照照片URL
     */
    private String businessLicenseUrl;
    /**
     * 营业执照注册号
     */
    private String businessLicenseNumber;
    /**
     * 真实姓名
     */
    private String realName;
    /**
     * 身份证正面照片URL
     */
    private String idCardFrontUrl;
    /**
     * 身份证反面照片URL
     */
    private String idCardBackUrl;
    /**
     * 身份证号码
     */
    private String idCardNumber;
    /**
     * 管理员手机号
     */
    private String managerPhone;
    /**
     * 是否删除 0.未删除 1.已删除
     */
    private Integer isDel;

}
