package org.dromara.system.utils;

import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.system.config.OpenPlatformRedisConfig;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 密码重置令牌工具类
 * 使用 Redis 存储密码重置令牌
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Component
@RequiredArgsConstructor
public class PasswordResetTokenUtil {

    /**
     * 密码重置令牌前缀
     */
    private static final String PASSWORD_RESET_TOKEN_PREFIX = OpenPlatformRedisConfig.PASSWORD_RESET_TOKEN_PREFIX;

    /**
     * 令牌有效期（1小时）
     */
    private static final Duration TOKEN_EXPIRE_TIME = Duration.ofHours(1);

    /**
     * 生成密码重置令牌
     *
     * @param userId 用户ID
     * @return 重置令牌
     */
    public String generateToken(Long userId) {
        String token = IdUtil.fastSimpleUUID();
        String key = PASSWORD_RESET_TOKEN_PREFIX + token;

        // 存储到 Redis，设置1小时过期
        RedisUtils.setCacheObject(key, userId.toString(), TOKEN_EXPIRE_TIME);

        return token;
    }

    /**
     * 验证并获取用户ID
     *
     * @param token 重置令牌
     * @return 用户ID，如果令牌无效返回null
     */
    public Long validateAndGetUserId(String token) {
        String key = PASSWORD_RESET_TOKEN_PREFIX + token;
        String userIdStr = RedisUtils.getCacheObject(key);

        if (userIdStr != null) {
            try {
                return Long.parseLong(userIdStr);
            } catch (NumberFormatException e) {
                // 删除无效的令牌
                RedisUtils.deleteObject(key);
                return null;
            }
        }

        return null;
    }

    /**
     * 使用令牌（验证成功后删除令牌，确保一次性使用）
     *
     * @param token 重置令牌
     * @return 用户ID，如果令牌无效返回null
     */
    public Long useToken(String token) {
        String key = PASSWORD_RESET_TOKEN_PREFIX + token;
        String userIdStr = RedisUtils.getCacheObject(key);

        if (userIdStr != null) {
            // 删除令牌，确保一次性使用
            RedisUtils.deleteObject(key);

            try {
                return Long.parseLong(userIdStr);
            } catch (NumberFormatException e) {
                return null;
            }
        }

        return null;
    }

    /**
     * 删除用户的所有重置令牌
     *
     * @param userId 用户ID
     */
    public void deleteUserTokens(Long userId) {
        // 获取所有密码重置令牌的key
        var keys = RedisUtils.keys(PASSWORD_RESET_TOKEN_PREFIX + "*");

        for (String key : keys) {
            String userIdStr = RedisUtils.getCacheObject(key);
            if (userId.toString().equals(userIdStr)) {
                RedisUtils.deleteObject(key);
            }
        }
    }

    /**
     * 检查令牌是否存在
     *
     * @param token 重置令牌
     * @return 是否存在
     */
    public boolean exists(String token) {
        String key = PASSWORD_RESET_TOKEN_PREFIX + token;
        return RedisUtils.hasKey(key);
    }

    /**
     * 获取令牌剩余有效时间（秒）
     *
     * @param token 重置令牌
     * @return 剩余有效时间，-1表示令牌不存在
     */
    public long getTokenTTL(String token) {
        String key = PASSWORD_RESET_TOKEN_PREFIX + token;
        return RedisUtils.getTimeToLive(key);
    }

}
