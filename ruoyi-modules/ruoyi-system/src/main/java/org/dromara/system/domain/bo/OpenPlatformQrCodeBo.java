package org.dromara.system.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.io.Serial;
import java.io.Serializable;

/**
 * 获取二维码业务对象
 *
 * <AUTHOR>
 */
public class OpenPlatformQrCodeBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 真实姓名（个人必填，企业为法人代表姓名）
     */
    @NotBlank(message = "真实姓名不能为空")
    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    @Schema(name = "realName", description = "真实姓名（个人必填，企业为法人代表姓名）", example = "张三")
    private String realName;

    /**
     * 管理员手机号
     */
    @NotBlank(message = "管理员手机号不能为空")
    @Schema(name = "managerPhone", description = "管理员手机号", example = "13800138000")
    private String managerPhone;

    /**
     * 身份证号码（个人必填，企业为法人代表身份证）
     */
    @NotBlank(message = "身份证号码不能为空")
    @Size(max = 20, message = "身份证号码长度不能超过20个字符")
    @Schema(name = "idCardNumber", description = "身份证号码（个人必填，企业为法人代表身份证）", example = "123456789012345678")
    private String idCardNumber;

}
