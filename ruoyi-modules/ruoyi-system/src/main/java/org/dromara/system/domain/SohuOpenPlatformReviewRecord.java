package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.domain.model.SohuEntity;

import java.util.Date;

/**
 * 许愿狐开放平台小程序审核记录对象 sohu_open_platform_review_record
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_open_platform_review_record")
public class SohuOpenPlatformReviewRecord extends SohuEntity {

    /**
     * 审核记录ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 版本ID
     */
    private Long versionId;
    /**
     * 审核状态：1-待审核，2-审核中，3-审核通过，4-审核拒绝
     */
    private Long reviewStatus;
    /**
     * 审核员ID
     */
    private Long reviewerId;
    /**
     * 审核内容/反馈
     */
    private String reviewContent;
    /**
     * 审核结果详情
     */
    private String reviewResult;
    /**
     * 拒绝原因
     */
    private String rejectReason;
    /**
     * 审核附件
     */
    private String reviewAnnex;
    /**
     * 审核开始时间
     */
    private Date reviewStartTime;
    /**
     * 审核结束时间
     */
    private Date reviewEndTime;

}
