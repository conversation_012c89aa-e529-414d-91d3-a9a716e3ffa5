package org.dromara.system.domain.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;


/**
 * 许愿狐开放平台小程序审核历史记录视图对象
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Data
@ExcelIgnoreUnannotated
public class SohuOpenPlatformAppletReviewHistoryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 审核历史记录ID
     */
    @ExcelProperty(value = "审核历史记录ID")
    private Long id;

    /**
     * 小程序ID
     */
    @ExcelProperty(value = "小程序ID")
    private Long appletId;

    /**
     * 版本ID
     */
    @ExcelProperty(value = "版本ID")
    private Long versionId;

    /**
     * 操作描述
     */
    @NotNull(message = "操作描述：固定文本描述", groups = { AddGroup.class, EditGroup.class })
    private String operationDesc;

    /**
     * 审核员ID
     */
    @ExcelProperty(value = "审核员ID")
    private Long operatorId;

    /**
     * 审核时间
     */
    @ExcelProperty(value = "审核时间")
    private Date operationTime;

    /**
     * 更改前的JSON
     */
    private String beforeData;

    /**
     * 更改后的JSON字符串
     */
    private String afterData;

}
