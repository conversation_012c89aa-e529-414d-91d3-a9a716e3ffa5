package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.domain.model.SohuEntity;

import java.util.Date;

/**
 * 许愿狐开放平台小程序审核历史记录对象 sohu_open_platform_applet_review_history
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_open_platform_applet_review_history")
public class SohuOpenPlatformAppletReviewHistory extends SohuEntity {

    /**
     * 审核历史记录ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 小程序ID
     */
    private Long appletId;
    /**
     * 版本ID
     */
    private Long versionId;
    /**
     * 操作描述
     * 固定文本描述
     */
    private String operationDesc;
    /**
     * 审核员ID
     */
    private Long operatorId;
    /**
     * 审核时间
     */
    private Date operationTime;
    /**
     * 更改前的JSON
     */
    private String beforeData;
    /**
     * 更改后的JSON字符串
     */
    private String afterData;

}
