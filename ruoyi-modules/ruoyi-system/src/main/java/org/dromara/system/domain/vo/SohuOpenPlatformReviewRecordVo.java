package org.dromara.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 许愿狐开放平台小程序审核记录视图对象
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Data
@ExcelIgnoreUnannotated
public class SohuOpenPlatformReviewRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 审核记录ID
     */
    @ExcelProperty(value = "审核记录ID")
    private Long id;

    /**
     * 版本ID
     */
    @ExcelProperty(value = "版本ID")
    private Long versionId;

    /**
     * 审核状态：1-待审核，2-审核中，3-审核通过，4-审核拒绝
     */
    @ExcelProperty(value = "审核状态：1-待审核，2-审核中，3-审核通过，4-审核拒绝")
    private Long reviewStatus;

    /**
     * 审核员ID
     */
    @ExcelProperty(value = "审核员ID")
    private Long reviewerId;

    /**
     * 审核内容/反馈
     */
    @ExcelProperty(value = "审核内容/反馈")
    private String reviewContent;

    /**
     * 审核结果详情
     */
    @ExcelProperty(value = "审核结果详情")
    private String reviewResult;

    /**
     * 拒绝原因
     */
    @ExcelProperty(value = "拒绝原因")
    private String rejectReason;

    /**
     * 审核附件
     */
    @ExcelProperty(value = "审核附件")
    private String reviewAnnex;

    /**
     * 审核开始时间
     */
    @ExcelProperty(value = "审核开始时间")
    private Date reviewStartTime;

    /**
     * 审核结束时间
     */
    @ExcelProperty(value = "审核结束时间")
    private Date reviewEndTime;


}
