package org.dromara.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.dev33.satoken.secure.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mail.utils.MailUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.TableDataInfoUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.dromara.system.domain.*;
import org.dromara.system.domain.bo.*;
import org.dromara.system.domain.vo.OpenPlatformLoginVo;
import org.dromara.system.domain.vo.SohuOpenPlatformUserVo;
import org.dromara.system.mapper.*;
import org.dromara.system.service.ISohuOpenPlatformUserService;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 许愿狐开放平台用户Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SohuOpenPlatformUserServiceImpl implements ISohuOpenPlatformUserService {

    private final SohuOpenPlatformUserMapper baseMapper;
    private final SohuOpenPlatformPasswordResetTokenMapper passwordResetTokenMapper;
    private final SohuOpenPlatformEmailCodeMapper emailCodeMapper;

    /**
     * 查询许愿狐开放平台用户
     */
    @Override
    public SohuOpenPlatformUserVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询许愿狐开放平台用户列表
     */
    @Override
    public TableDataInfo<SohuOpenPlatformUserVo> queryPageList(SohuOpenPlatformUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuOpenPlatformUser> lqw = buildQueryWrapper(bo);
        Page<SohuOpenPlatformUserVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询许愿狐开放平台用户列表
     */
    @Override
    public List<SohuOpenPlatformUserVo> queryList(SohuOpenPlatformUserBo bo) {
        LambdaQueryWrapper<SohuOpenPlatformUser> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuOpenPlatformUser> buildQueryWrapper(SohuOpenPlatformUserBo bo) {
        LambdaQueryWrapper<SohuOpenPlatformUser> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getEmail()), SohuOpenPlatformUser::getEmail, bo.getEmail());
        lqw.like(StringUtils.isNotBlank(bo.getNickname()), SohuOpenPlatformUser::getNickname, bo.getNickname());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SohuOpenPlatformUser::getStatus, bo.getStatus());
        lqw.eq(bo.getEmailVerified() != null, SohuOpenPlatformUser::getEmailVerified, bo.getEmailVerified());
        return lqw;
    }

    /**
     * 新增许愿狐开放平台用户
     */
    @Override
    public Boolean insertByBo(SohuOpenPlatformUserBo bo) {
        SohuOpenPlatformUser add = MapstructUtils.convert(bo, SohuOpenPlatformUser.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改许愿狐开放平台用户
     */
    @Override
    public Boolean updateByBo(SohuOpenPlatformUserBo bo) {
        SohuOpenPlatformUser update = MapstructUtils.convert(bo, SohuOpenPlatformUser.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuOpenPlatformUser entity) {
        // 检查邮箱是否已存在
        if (StringUtils.isNotBlank(entity.getEmail())) {
            LambdaQueryWrapper<SohuOpenPlatformUser> lqw = Wrappers.lambdaQuery();
            lqw.eq(SohuOpenPlatformUser::getEmail, entity.getEmail());
            if (entity.getId() != null) {
                lqw.ne(SohuOpenPlatformUser::getId, entity.getId());
            }
            if (baseMapper.exists(lqw)) {
                throw new ServiceException("邮箱已存在");
            }
        }
    }

    /**
     * 批量删除许愿狐开放平台用户
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 邮箱密码登录
     */
    @Override
    public OpenPlatformLoginVo login(OpenPlatformLoginBo loginBo) {
        // 查询用户
        SohuOpenPlatformUser user = baseMapper.selectOne(
            Wrappers.lambdaQuery(SohuOpenPlatformUser.class)
                .eq(SohuOpenPlatformUser::getEmail, loginBo.getEmail())
        );

        if (user == null) {
            throw new ServiceException("用户不存在");
        }

        if (!"0".equals(user.getStatus())) {
            throw new ServiceException("用户已被停用");
        }

        // 验证密码
        if (!BCrypt.checkpw(loginBo.getPassword(), user.getPassword())) {
            throw new ServiceException("密码错误");
        }

        // 更新最后登录时间
        user.setLastLoginTime(LocalDateTime.now());
        baseMapper.updateById(user);

        // 生成token（这里简化处理，实际应该使用JWT或其他token机制）
        String accessToken = IdUtil.fastSimpleUUID();

        // 构建返回对象
        OpenPlatformLoginVo loginVo = new OpenPlatformLoginVo();
        loginVo.setAccessToken(accessToken);
        loginVo.setRefreshToken(IdUtil.fastSimpleUUID());
        loginVo.setExpiresIn(7200L); // 2小时

        OpenPlatformLoginVo.UserInfo userInfo = new OpenPlatformLoginVo.UserInfo();
        userInfo.setId(user.getId());
        userInfo.setEmail(user.getEmail());
        userInfo.setNickname(user.getNickname());
        userInfo.setAvatar(user.getAvatar());
        userInfo.setEmailVerified(user.getEmailVerified());
        loginVo.setUserInfo(userInfo);

        return loginVo;
    }

    /**
     * 用户注册
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean register(OpenPlatformRegisterBo registerBo) {
        // 验证密码一致性
        if (!registerBo.getPassword().equals(registerBo.getConfirmPassword())) {
            throw new ServiceException("两次输入的密码不一致");
        }

        // 验证邮箱验证码
        if (!validateEmailCode(registerBo.getEmail(), registerBo.getEmailCode(), "register")) {
            throw new ServiceException("邮箱验证码错误或已过期");
        }

        // 检查邮箱是否已注册
        if (baseMapper.exists(Wrappers.lambdaQuery(SohuOpenPlatformUser.class)
            .eq(SohuOpenPlatformUser::getEmail, registerBo.getEmail()))) {
            throw new ServiceException("邮箱已被注册");
        }

        // 创建用户
        SohuOpenPlatformUser user = new SohuOpenPlatformUser();
        user.setEmail(registerBo.getEmail());
        user.setPassword(BCrypt.hashpw(registerBo.getPassword(), BCrypt.gensalt()));
        user.setNickname(StringUtils.isNotBlank(registerBo.getNickname()) ?
            registerBo.getNickname() : "用户" + RandomUtil.randomString(6));
        user.setStatus("0");
        user.setEmailVerified(true); // 通过邮箱验证码注册，默认邮箱已验证

        return baseMapper.insert(user) > 0;
    }

    /**
     * 发送邮箱验证码
     */
    @Override
    public Boolean sendEmailCode(OpenPlatformSendEmailCodeBo sendEmailCodeBo) {
        String email = sendEmailCodeBo.getEmail();
        String codeType = sendEmailCodeBo.getCodeType();

        // 生成6位数字验证码
        String code = RandomUtil.randomNumbers(6);

        // 保存验证码到数据库
        SohuOpenPlatformEmailCode emailCode = new SohuOpenPlatformEmailCode();
        emailCode.setEmail(email);
        emailCode.setCode(code);
        emailCode.setCodeType(codeType);
        emailCode.setExpireTime(LocalDateTime.now().plusMinutes(10)); // 10分钟过期
        emailCode.setUsed(false);

        emailCodeMapper.insert(emailCode);

        // 发送邮件
        String subject = "register".equals(codeType) ? "注册验证码" : "密码重置验证码";
        String content = String.format("您的验证码是：%s，有效期10分钟，请勿泄露给他人。", code);

        try {
            MailUtils.sendText(email, subject, content);
            return true;
        } catch (Exception e) {
            log.error("发送邮件失败", e);
            throw new ServiceException("发送邮件失败");
        }
    }

    /**
     * 忘记密码
     */
    @Override
    public Boolean forgotPassword(OpenPlatformForgotPasswordBo forgotPasswordBo) {
        String email = forgotPasswordBo.getEmail();

        // 检查用户是否存在
        SohuOpenPlatformUser user = baseMapper.selectOne(
            Wrappers.lambdaQuery(SohuOpenPlatformUser.class)
                .eq(SohuOpenPlatformUser::getEmail, email)
        );

        if (user == null) {
            throw new ServiceException("该邮箱喂zhu'c");
        }

        // 生成重置令牌
        String token = IdUtil.fastSimpleUUID();

        // 保存重置令牌
        SohuOpenPlatformPasswordResetToken resetToken = new SohuOpenPlatformPasswordResetToken();
        resetToken.setUserId(user.getId());
        resetToken.setToken(token);
        resetToken.setExpireTime(LocalDateTime.now().plusHours(1)); // 1小时过期
        resetToken.setUsed(false);

        passwordResetTokenMapper.insert(resetToken);

        // 发送重置密码邮件
        String subject = "密码重置";
        String resetUrl = "http://your-domain.com/reset-password?token=" + token;
        String content = String.format("请点击以下链接重置您的密码：%s\n\n链接有效期1小时，请及时处理。", resetUrl);

        try {
            MailUtils.sendText(email, subject, content);
            return true;
        } catch (Exception e) {
            log.error("发送重置密码邮件失败", e);
            throw new ServiceException("发送邮件失败");
        }
    }

    /**
     * 重置密码
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetPassword(OpenPlatformResetPasswordBo resetPasswordBo) {
        // 验证密码一致性
        if (!resetPasswordBo.getNewPassword().equals(resetPasswordBo.getConfirmPassword())) {
            throw new ServiceException("两次输入的密码不一致");
        }

        // 验证重置令牌
        SohuOpenPlatformPasswordResetToken resetToken = passwordResetTokenMapper.selectOne(
            Wrappers.lambdaQuery(SohuOpenPlatformPasswordResetToken.class)
                .eq(SohuOpenPlatformPasswordResetToken::getToken, resetPasswordBo.getToken())
                .eq(SohuOpenPlatformPasswordResetToken::getUsed, false)
                .gt(SohuOpenPlatformPasswordResetToken::getExpireTime, LocalDateTime.now())
        );

        if (resetToken == null) {
            throw new ServiceException("重置令牌无效或已过期");
        }

        // 更新用户密码
        SohuOpenPlatformUser user = new SohuOpenPlatformUser();
        user.setId(resetToken.getUserId());
        user.setPassword(BCrypt.hashpw(resetPasswordBo.getNewPassword(), BCrypt.gensalt()));

        baseMapper.updateById(user);

        // 标记令牌为已使用
        resetToken.setUsed(true);
        passwordResetTokenMapper.updateById(resetToken);

        return true;
    }

    /**
     * 根据邮箱查询用户
     */
    @Override
    public SohuOpenPlatformUserVo queryByEmail(String email) {
        return baseMapper.selectVoOne(
            Wrappers.lambdaQuery(SohuOpenPlatformUser.class)
                .eq(SohuOpenPlatformUser::getEmail, email)
        );
    }

    /**
     * 验证邮箱验证码
     */
    private boolean validateEmailCode(String email, String code, String codeType) {
        SohuOpenPlatformEmailCode emailCode = emailCodeMapper.selectOne(
            Wrappers.lambdaQuery(SohuOpenPlatformEmailCode.class)
                .eq(SohuOpenPlatformEmailCode::getEmail, email)
                .eq(SohuOpenPlatformEmailCode::getCode, code)
                .eq(SohuOpenPlatformEmailCode::getCodeType, codeType)
                .eq(SohuOpenPlatformEmailCode::getUsed, false)
                .gt(SohuOpenPlatformEmailCode::getExpireTime, LocalDateTime.now())
                .orderByDesc(SohuOpenPlatformEmailCode::getCreateTime)
                .last("LIMIT 1")
        );

        if (emailCode != null) {
            // 标记验证码为已使用
            emailCode.setUsed(true);
            emailCodeMapper.updateById(emailCode);
            return true;
        }

        return false;
    }

}
