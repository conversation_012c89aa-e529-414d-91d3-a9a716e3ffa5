package org.dromara.system.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.bo.*;
import org.dromara.system.domain.vo.OpenPlatformLoginVo;
import org.dromara.system.domain.vo.SohuOpenPlatformUserVo;

import java.util.Collection;
import java.util.List;

/**
 * 许愿狐开放平台用户Service接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ISohuOpenPlatformUserService {

    /**
     * 查询许愿狐开放平台用户
     */
    SohuOpenPlatformUserVo queryById(Long id);

    /**
     * 查询许愿狐开放平台用户列表
     */
    TableDataInfo<SohuOpenPlatformUserVo> queryPageList(SohuOpenPlatformUserBo bo, PageQuery pageQuery);

    /**
     * 查询许愿狐开放平台用户列表
     */
    List<SohuOpenPlatformUserVo> queryList(SohuOpenPlatformUserBo bo);

    /**
     * 新增许愿狐开放平台用户
     */
    Boolean insertByBo(SohuOpenPlatformUserBo bo);

    /**
     * 修改许愿狐开放平台用户
     */
    Boolean updateByBo(SohuOpenPlatformUserBo bo);

    /**
     * 校验并批量删除许愿狐开放平台用户信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 邮箱密码登录
     */
    OpenPlatformLoginVo login(OpenPlatformLoginBo loginBo);

    /**
     * 用户注册
     */
    Boolean register(OpenPlatformRegisterBo registerBo);

    /**
     * 发送邮箱验证码
     */
    Boolean sendEmailCode(OpenPlatformSendEmailCodeBo sendEmailCodeBo);

    /**
     * 忘记密码
     */
    Boolean forgotPassword(OpenPlatformForgotPasswordBo forgotPasswordBo);

    /**
     * 重置密码
     */
    Boolean resetPassword(OpenPlatformResetPasswordBo resetPasswordBo);

    /**
     * 根据邮箱查询用户
     */
    SohuOpenPlatformUserVo queryByEmail(String email);

}
