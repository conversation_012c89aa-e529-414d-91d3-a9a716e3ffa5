package org.dromara.system.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.enums.UserType;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mail.utils.MailUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.web.domain.vo.LoginVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.dromara.system.domain.*;
import org.dromara.system.domain.bo.*;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.mapper.*;
import org.dromara.system.service.IOpenPlatformUserService;
import org.dromara.system.utils.PasswordResetTokenUtil;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 开放平台用户Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OpenPlatformUserServiceImpl implements IOpenPlatformUserService {

    private final SysUserMapper userMapper;
    private final SohuOpenPlatformEmailCodeMapper emailCodeMapper;
    private final PasswordResetTokenUtil passwordResetTokenUtil;

    /**
     * 邮箱密码登录
     */
    @Override
    public LoginVo login(OpenPlatformLoginBo loginBo) {
        // 查询开放平台用户
        SysUser user = userMapper.selectOne(
            Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getEmail, loginBo.getEmail())
                .eq(SysUser::getUserType, UserType.OPEN_PLATFORM.getUserType())
                .eq(SysUser::getDelFlag, SystemConstants.NORMAL)
        );

        if (user == null) {
            throw new ServiceException("该账号未注册，请先完成注册后登录～");
        }

        if (StrUtil.equals(SystemConstants.DISABLE, user.getStatus())) {
            throw new ServiceException("用户已被停用");
        }

        // 验证密码
        if (!BCrypt.checkpw(loginBo.getPassword(), user.getPassword())) {
            throw new ServiceException("密码错误");
        }

        // 更新最后登录时间
        user.setLoginDate(new java.util.Date());
        userMapper.updateById(user);

        SaLoginModel model = new SaLoginModel();
        model.setDevice(UserType.OPEN_PLATFORM.getUserType());
        // 自定义分配 不同用户体系 不同 token 授权时间 不设置默认走全局 yml 配置
        // 例如: 后台用户30分钟过期 app用户1天过期
        model.setTimeout(604800L);
        model.setActiveTimeout(1800L);
        // 生成token
        LoginHelper.login(Objects.requireNonNull(MapstructUtils.convert(user, LoginUser.class)), model);

        // 构建返回对象
        LoginVo loginVo = new LoginVo();
        loginVo.setAccessToken(StpUtil.getTokenValue());
        loginVo.setExpireIn(StpUtil.getTokenTimeout());

        return loginVo;
    }

    /**
     * 用户注册
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean register(OpenPlatformRegisterBo registerBo) {
        // 验证密码一致性
        if (!registerBo.getPassword().equals(registerBo.getConfirmPassword())) {
            throw new ServiceException("两次输入的密码不一致");
        }

        // 验证邮箱验证码
        if (!validateEmailCode(registerBo.getEmail(), registerBo.getEmailCode(), "register")) {
            throw new ServiceException("邮箱验证码错误或已过期");
        }

        // 检查邮箱是否已注册
        if (userMapper.exists(Wrappers.lambdaQuery(SysUser.class)
            .eq(SysUser::getEmail, registerBo.getEmail())
            .eq(SysUser::getUserType, UserType.OPEN_PLATFORM.getUserType())
            .eq(SysUser::getDelFlag, SystemConstants.NORMAL))) {
            throw new ServiceException("邮箱已被注册");
        }

        // 创建用户
        SysUser user = new SysUser();
        user.setUserName(registerBo.getEmail());
        user.setNickName(StringUtils.isNotBlank(registerBo.getNickname()) ?
            registerBo.getNickname() : "openUser" + RandomUtil.randomString(6));
        user.setUserType(UserType.OPEN_PLATFORM.getUserType());
        user.setEmail(registerBo.getEmail());
        user.setPassword(BCrypt.hashpw(registerBo.getPassword()));
        user.setStatus(SystemConstants.NORMAL);
        user.setDelFlag(SystemConstants.NORMAL);

        return userMapper.insert(user) > 0;
    }

    /**
     * 发送邮箱验证码
     */
    @Override
    public Boolean sendEmailCode(OpenPlatformSendEmailCodeBo sendEmailCodeBo) {
        String email = sendEmailCodeBo.getEmail();
        String codeType = sendEmailCodeBo.getCodeType();

        // 生成6位数字验证码
        String code = RandomUtil.randomNumbers(6);

        // 保存验证码到数据库
        SohuOpenPlatformEmailCode emailCode = new SohuOpenPlatformEmailCode();
        emailCode.setEmail(email);
        emailCode.setCode(code);
        emailCode.setCodeType(codeType);
        emailCode.setExpireTime(LocalDateTime.now().plusMinutes(10));
        emailCode.setUsed(false);

        emailCodeMapper.insert(emailCode);

        // 发送邮件
        String subject = "register".equals(codeType) ? "注册验证码" : "密码重置验证码";
        String content = String.format("您的验证码是：%s，有效期10分钟，请勿泄露给他人。", code);

        try {
            MailUtils.sendText(email, subject, content);
            return true;
        } catch (Exception e) {
            log.error("发送邮件失败", e);
            throw new ServiceException("发送邮件失败");
        }
    }

    /**
     * 忘记密码
     */
    @Override
    public Boolean forgotPassword(OpenPlatformForgotPasswordBo forgotPasswordBo) {
        String email = forgotPasswordBo.getEmail();

        // 检查开放平台用户是否存在
        SysUser user = userMapper.selectOne(
            Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getEmail, email)
                .eq(SysUser::getUserType, UserType.OPEN_PLATFORM.getUserType())
                .eq(SysUser::getDelFlag, SystemConstants.NORMAL)
        );

        if (user == null) {
            throw new ServiceException("该账号未注册，请先完成注册");
        }

        // 生成重置令牌并存储到 Redis
        String token = passwordResetTokenUtil.generateToken(user.getUserId());

        // 发送重置密码邮件
        String subject = "许愿狐开放平台密码重置";
        String resetUrl = "http://192.168.150.252:xxxx/reset-password?token=" + token;
        String content = String.format("你好!\n忘记许愿狐开放平台密码了吗?别着急,请点击以下链接,我们协助您找回密码：\n\n%s\n\n如果这不是您的邮件请忽略,很抱歉打扰您,请原谅。", resetUrl);

        try {
            MailUtils.sendText(email, subject, content);
            return true;
        } catch (Exception e) {
            log.error("发送重置密码邮件失败", e);
            throw new ServiceException("发送邮件失败");
        }
    }

    /**
     * 重置密码
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetPassword(OpenPlatformResetPasswordBo resetPasswordBo) {
        // 验证密码一致性
        if (!resetPasswordBo.getNewPassword().equals(resetPasswordBo.getConfirmPassword())) {
            throw new ServiceException("两次输入的密码不一致");
        }

        // 验证并使用重置令牌
        Long userId = passwordResetTokenUtil.useToken(resetPasswordBo.getToken());

        if (userId == null) {
            throw new ServiceException("重置令牌无效或已过期");
        }

        // 更新用户密码
        SysUser user = new SysUser();
        user.setUserId(userId);
        user.setPassword(BCrypt.hashpw(resetPasswordBo.getNewPassword()));

        userMapper.updateById(user);

        return true;
    }

    /**
     * 根据邮箱查询开放平台用户
     */
    @Override
    public SysUserVo queryByEmail(String email) {
        return userMapper.selectVoOne(
            Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getEmail, email)
                .eq(SysUser::getUserType, UserType.OPEN_PLATFORM.getUserType())
                .eq(SysUser::getDelFlag, SystemConstants.NORMAL)
        );
    }

    /**
     * 验证邮箱验证码
     */
    private boolean validateEmailCode(String email, String code, String codeType) {
        SohuOpenPlatformEmailCode emailCode = emailCodeMapper.selectOne(
            Wrappers.lambdaQuery(SohuOpenPlatformEmailCode.class)
                .eq(SohuOpenPlatformEmailCode::getEmail, email)
                .eq(SohuOpenPlatformEmailCode::getCode, code)
                .eq(SohuOpenPlatformEmailCode::getCodeType, codeType)
                .eq(SohuOpenPlatformEmailCode::getUsed, false)
                .gt(SohuOpenPlatformEmailCode::getExpireTime, LocalDateTime.now())
                .orderByDesc(SohuOpenPlatformEmailCode::getCreateTime)
                .last("LIMIT 1")
        );

        if (emailCode != null) {
            // 标记验证码为已使用
            emailCode.setUsed(true);
            emailCodeMapper.updateById(emailCode);
            return true;
        }

        return false;
    }

}
