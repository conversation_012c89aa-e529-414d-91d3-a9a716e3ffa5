package org.dromara.system.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mail.utils.MailUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.dromara.system.domain.*;
import org.dromara.system.domain.bo.*;
import org.dromara.system.domain.vo.OpenPlatformLoginVo;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.enums.UserTypeEnum;
import org.dromara.system.mapper.*;
import org.dromara.system.service.IOpenPlatformUserService;

import java.time.LocalDateTime;

/**
 * 开放平台用户Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OpenPlatformUserServiceImpl implements IOpenPlatformUserService {

    private final SysUserMapper userMapper;
    private final SohuOpenPlatformPasswordResetTokenMapper passwordResetTokenMapper;
    private final SohuOpenPlatformEmailCodeMapper emailCodeMapper;

    /**
     * 邮箱密码登录
     */
    @Override
    public OpenPlatformLoginVo login(OpenPlatformLoginBo loginBo) {
        // 查询开放平台用户
        SysUser user = userMapper.selectOne(
            Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getEmail, loginBo.getEmail())
                .eq(SysUser::getUserType, UserTypeEnum.OPEN_PLATFORM.getCode())
                .eq(SysUser::getDelFlag, "0")
        );

        if (user == null) {
            throw new ServiceException("用户不存在");
        }

        if (!"0".equals(user.getStatus())) {
            throw new ServiceException("用户已被停用");
        }

        // 验证密码
        if (!BCrypt.checkpw(loginBo.getPassword(), user.getPassword())) {
            throw new ServiceException("密码错误");
        }

        // 更新最后登录时间
        user.setLoginDate(new java.util.Date());
        userMapper.updateById(user);

        // 生成token（这里简化处理，实际应该使用JWT或其他token机制）
        String accessToken = IdUtil.fastSimpleUUID();

        // 构建返回对象
        OpenPlatformLoginVo loginVo = new OpenPlatformLoginVo();
        loginVo.setAccessToken(accessToken);
        loginVo.setRefreshToken(IdUtil.fastSimpleUUID());
        loginVo.setExpiresIn(7200L); // 2小时

        OpenPlatformLoginVo.UserInfo userInfo = new OpenPlatformLoginVo.UserInfo();
        userInfo.setId(user.getUserId());
        userInfo.setEmail(user.getEmail());
        userInfo.setNickname(user.getNickName());
        userInfo.setAvatar(user.getAvatar() != null ? user.getAvatar().toString() : null);
        userInfo.setEmailVerified(true); // 开放平台用户注册时已验证邮箱
        loginVo.setUserInfo(userInfo);

        return loginVo;
    }

    /**
     * 用户注册
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean register(OpenPlatformRegisterBo registerBo) {
        // 验证密码一致性
        if (!registerBo.getPassword().equals(registerBo.getConfirmPassword())) {
            throw new ServiceException("两次输入的密码不一致");
        }

        // 验证邮箱验证码
        if (!validateEmailCode(registerBo.getEmail(), registerBo.getEmailCode(), "register")) {
            throw new ServiceException("邮箱验证码错误或已过期");
        }

        // 检查邮箱是否已注册
        if (userMapper.exists(Wrappers.lambdaQuery(SysUser.class)
            .eq(SysUser::getEmail, registerBo.getEmail())
            .eq(SysUser::getUserType, UserTypeEnum.OPEN_PLATFORM.getCode())
            .eq(SysUser::getDelFlag, "0"))) {
            throw new ServiceException("邮箱已被注册");
        }

        // 创建用户
        SysUser user = new SysUser();
        user.setUserName(registerBo.getEmail()); // 使用邮箱作为用户名
        user.setNickName(StringUtils.isNotBlank(registerBo.getNickname()) ?
            registerBo.getNickname() : "用户" + RandomUtil.randomString(6));
        user.setUserType(UserTypeEnum.OPEN_PLATFORM.getCode());
        user.setEmail(registerBo.getEmail());
        user.setPassword(BCrypt.hashpw(registerBo.getPassword()));
        user.setStatus("0");
        user.setDelFlag("0");

        return userMapper.insert(user) > 0;
    }

    /**
     * 发送邮箱验证码
     */
    @Override
    public Boolean sendEmailCode(OpenPlatformSendEmailCodeBo sendEmailCodeBo) {
        String email = sendEmailCodeBo.getEmail();
        String codeType = sendEmailCodeBo.getCodeType();

        // 生成6位数字验证码
        String code = RandomUtil.randomNumbers(6);

        // 保存验证码到数据库
        SohuOpenPlatformEmailCode emailCode = new SohuOpenPlatformEmailCode();
        emailCode.setEmail(email);
        emailCode.setCode(code);
        emailCode.setCodeType(codeType);
        emailCode.setExpireTime(LocalDateTime.now().plusMinutes(10)); // 10分钟过期
        emailCode.setUsed(false);

        emailCodeMapper.insert(emailCode);

        // 发送邮件
        String subject = "register".equals(codeType) ? "注册验证码" : "密码重置验证码";
        String content = String.format("您的验证码是：%s，有效期10分钟，请勿泄露给他人。", code);

        try {
            MailUtils.sendText(email, subject, content);
            return true;
        } catch (Exception e) {
            log.error("发送邮件失败", e);
            throw new ServiceException("发送邮件失败");
        }
    }

    /**
     * 忘记密码
     */
    @Override
    public Boolean forgotPassword(OpenPlatformForgotPasswordBo forgotPasswordBo) {
        String email = forgotPasswordBo.getEmail();

        // 检查开放平台用户是否存在
        SysUser user = userMapper.selectOne(
            Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getEmail, email)
                .eq(SysUser::getUserType, UserTypeEnum.OPEN_PLATFORM.getCode())
                .eq(SysUser::getDelFlag, "0")
        );

        if (user == null) {
            throw new ServiceException("用户不存在");
        }

        // 生成重置令牌
        String token = IdUtil.fastSimpleUUID();

        // 保存重置令牌
        SohuOpenPlatformPasswordResetToken resetToken = new SohuOpenPlatformPasswordResetToken();
        resetToken.setUserId(user.getUserId());
        resetToken.setToken(token);
        resetToken.setExpireTime(LocalDateTime.now().plusHours(1));
        resetToken.setUsed(false);

        passwordResetTokenMapper.insert(resetToken);

        // 发送重置密码邮件
        String subject = "密码重置";
        String resetUrl = "http://your-domain.com/reset-password?token=" + token;
        String content = String.format("请点击以下链接重置您的密码：%s\n\n链接有效期1小时，请及时处理。", resetUrl);

        try {
            MailUtils.sendText(email, subject, content);
            return true;
        } catch (Exception e) {
            log.error("发送重置密码邮件失败", e);
            throw new ServiceException("发送邮件失败");
        }
    }

    /**
     * 重置密码
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetPassword(OpenPlatformResetPasswordBo resetPasswordBo) {
        // 验证密码一致性
        if (!resetPasswordBo.getNewPassword().equals(resetPasswordBo.getConfirmPassword())) {
            throw new ServiceException("两次输入的密码不一致");
        }

        // 验证重置令牌
        SohuOpenPlatformPasswordResetToken resetToken = passwordResetTokenMapper.selectOne(
            Wrappers.lambdaQuery(SohuOpenPlatformPasswordResetToken.class)
                .eq(SohuOpenPlatformPasswordResetToken::getToken, resetPasswordBo.getToken())
                .eq(SohuOpenPlatformPasswordResetToken::getUsed, false)
                .gt(SohuOpenPlatformPasswordResetToken::getExpireTime, LocalDateTime.now())
        );

        if (resetToken == null) {
            throw new ServiceException("重置令牌无效或已过期");
        }

        // 更新用户密码
        SysUser user = new SysUser();
        user.setUserId(resetToken.getUserId());
        user.setPassword(BCrypt.hashpw(resetPasswordBo.getNewPassword()));

        userMapper.updateById(user);

        // 标记令牌为已使用
        resetToken.setUsed(true);
        passwordResetTokenMapper.updateById(resetToken);

        return true;
    }

    /**
     * 根据邮箱查询开放平台用户
     */
    @Override
    public SysUserVo queryByEmail(String email) {
        return userMapper.selectVoOne(
            Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getEmail, email)
                .eq(SysUser::getUserType, UserTypeEnum.OPEN_PLATFORM.getCode())
                .eq(SysUser::getDelFlag, "0")
        );
    }

    /**
     * 验证邮箱验证码
     */
    private boolean validateEmailCode(String email, String code, String codeType) {
        SohuOpenPlatformEmailCode emailCode = emailCodeMapper.selectOne(
            Wrappers.lambdaQuery(SohuOpenPlatformEmailCode.class)
                .eq(SohuOpenPlatformEmailCode::getEmail, email)
                .eq(SohuOpenPlatformEmailCode::getCode, code)
                .eq(SohuOpenPlatformEmailCode::getCodeType, codeType)
                .eq(SohuOpenPlatformEmailCode::getUsed, false)
                .gt(SohuOpenPlatformEmailCode::getExpireTime, LocalDateTime.now())
                .orderByDesc(SohuOpenPlatformEmailCode::getCreateTime)
                .last("LIMIT 1")
        );

        if (emailCode != null) {
            // 标记验证码为已使用
            emailCode.setUsed(true);
            emailCodeMapper.updateById(emailCode);
            return true;
        }

        return false;
    }

}
