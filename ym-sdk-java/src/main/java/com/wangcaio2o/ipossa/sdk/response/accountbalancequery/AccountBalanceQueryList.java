package com.wangcaio2o.ipossa.sdk.response.accountbalancequery;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2023/6/28 14:30
 */
@Data
public class AccountBalanceQueryList implements Serializable {

    @JSONField(name = "type")
    private String type;

    @JSONField(name = "balance_amt")
    private Integer balanceAmt;

    @JSONField(name = "avl_amt")
    private Integer avlAmt;

    @JSONField(name = "frz_amt")
    private Integer frzAmt;

    @JSONField(name = "last_avl_amt")
    private Integer lastAvlAmt;

    @JSONField(name = "status")
    private String status;

}
