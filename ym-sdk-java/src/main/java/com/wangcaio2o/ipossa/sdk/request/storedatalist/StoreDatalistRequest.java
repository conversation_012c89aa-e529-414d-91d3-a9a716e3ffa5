package com.wangcaio2o.ipossa.sdk.request.storedatalist;

import com.alibaba.fastjson.annotation.JSONField;
import com.wangcaio2o.ipossa.sdk.request.BaseRequest;
import com.wangcaio2o.ipossa.sdk.response.storedatalist.StoreDatalistResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/6/25 14:29
 * 门店交易数据列表
 */
@Data
public class StoreDatalistRequest extends BaseRequest<StoreDatalistResponse> {

    @JSONField(name = "store_id")
    private String storeId;

    @JSONField(name = "transaction_id")
    private String transactionId;

    @JSONField(name = "trans_date")
    private String transDate;

    @JSONField(name = "trans_time_begin")
    private String transTimeBegin;

    @JSONField(name = "trans_time_end")
    private String transTimeEnd;

    @J<PERSON>NField(name = "pos_seq")
    private String posSeq;

    @JSONField(name = "pay_type")
    private String payType;

    @JSONField(name = "page")
    private String page;

    @JSONField(name = "size")
    private String size;

    public StoreDatalistRequest(String isspid,String systemId){
        super(isspid, systemId, "store_datalist_request");
    }

    public StoreDatalistRequest(){
        super("store_datalist_request");
    }

}
