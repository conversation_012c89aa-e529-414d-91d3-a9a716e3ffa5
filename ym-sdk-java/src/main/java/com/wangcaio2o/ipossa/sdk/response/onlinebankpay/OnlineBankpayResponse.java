package com.wangcaio2o.ipossa.sdk.response.onlinebankpay;

import com.alibaba.fastjson.annotation.JSONField;
import com.wangcaio2o.ipossa.sdk.response.BaseResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/6/27 10:10
 * 网银支付
 */
@Data
public class OnlineBankpayResponse extends BaseResponse {

    @JSONField(name = "pos_id")
    private String posId;

    @JSONField(name = "pos_seq")
    private String posSeq;

    @JSONField(name = "trans_time")
    private String transTime;

    @JSONField(name = "sys_seq")
    private String sysSeq;

    @JSONField(name = "form_url")
    private String formUrl;

    @Override
    public boolean isSuccess() {
        return "9998".equals(this.getResult().getId());
    }

}
