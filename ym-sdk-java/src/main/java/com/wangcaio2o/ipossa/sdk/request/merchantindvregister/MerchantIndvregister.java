package com.wangcaio2o.ipossa.sdk.request.merchantindvregister;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/6/28 10:35
 */
@Data
public class MerchantIndvregister {

    @J<PERSON><PERSON>ield(name = "merchant_id")
    private String merchantId;

    @JSONField(name = "identity_type")
    private String identityType;

    @JSONField(name = "merchant_name")
    private String merchantName;

    @JSONField(name = "legal_name")
    private String legalName;

    @JSONField(name = "identity_no")
    private String identityNo;

    @JSONField(name = "identity_begin_date")
    private String identityBeginDate;

    @JSONField(name = "identity_end_date")
    private String identityEndDate;

    @JSONField(name = "identity_validity_type")
    private String identityValidityType;

    @JSONField(name = "contact_phone")
    private String contactPhone;

    @JSONField(name = "cert_photo_a")
    private String certPhotoA;

    @JSONField(name = "cert_photo_b")
    private String certPhotoB;

}
