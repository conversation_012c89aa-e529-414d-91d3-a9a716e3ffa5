package com.wangcaio2o.ipossa.sdk.response.onlinerefund;

import com.alibaba.fastjson.annotation.JSONField;
import com.wangcaio2o.ipossa.sdk.model.ResInfo;
import com.wangcaio2o.ipossa.sdk.response.BaseResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/6/27 10:41
 * 线上交易退款
 */
@Data
public class OnlineRefundResponse extends BaseResponse {

    @JSONField(name = "system_id")
    private String systemId;

    @JSONField(name = "pos_id")
    private String posId;

    @JSONField(name = "pos_seq")
    private String posSeq;

    @JSONField(name = "trans_time")
    private String transTime;

    @JSONField(name = "sys_seq")
    private String sysSeq;

    @JSONField(name = "pay_type")
    private String payType;

    @JSONField(name = "res_info")
    private ResInfo resInfo;

}
