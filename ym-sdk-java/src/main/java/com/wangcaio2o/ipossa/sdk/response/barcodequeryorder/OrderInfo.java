package com.wangcaio2o.ipossa.sdk.response.barcodequeryorder;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/6/25 10:17
 */
@Data
public class OrderInfo {

    @JSONField(name = "trans_type")
    private String transType;

    @JSONField(name = "trans_time")
    private String transTime;

    @JSONField(name = "pos_seq")
    private String posSeq;

    @JSONField(name = "pay_type")
    private String payType;

    @JSONField(name = "status")
    private String status;

    @JSONField(name = "tx_amt")
    private String txAmt;

    @JSONField(name = "buss_discount_amt")
    private String bussDiscountAmt;

    @JSONField(name = "platform_discount_amt")
    private String platformDiscountAmt;

    @JSONField(name = "memo")
    private String memo;

}
