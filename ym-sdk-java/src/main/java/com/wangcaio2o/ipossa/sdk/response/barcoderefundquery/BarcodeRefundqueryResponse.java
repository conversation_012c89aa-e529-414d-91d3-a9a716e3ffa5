package com.wangcaio2o.ipossa.sdk.response.barcoderefundquery;

import com.alibaba.fastjson.annotation.JSONField;
import com.wangcaio2o.ipossa.sdk.model.alipay.AlipayResInfo;
import com.wangcaio2o.ipossa.sdk.model.ecny.EcnyResInfo;
import com.wangcaio2o.ipossa.sdk.model.upay.UpayResInfo;
import com.wangcaio2o.ipossa.sdk.model.vxpay.WxpayResInfo;
import com.wangcaio2o.ipossa.sdk.response.BaseResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/6/21 14:56
 * 退款查询交易
 */
@Data
public class BarcodeRefundqueryResponse extends BaseResponse {

    @JSONField(name = "pos_id")
    private String posId;

    @JSONField(name = "pos_seq")
    private String posSeq;

    @JSONField(name = "trans_time")
    private String transTime;

    @JSONField(name = "sys_seq")
    private String sysSeq;

    @JSONField(name = "pay_type")
    private String payType;

    @JSONField(name = "extend")
    private String extend;

    @JSONField(name = "buss_discount_amt")
    private String bussDiscountAmt;

    @JSONField(name = "platform_discount_amt")
    private String platformDiscountAmt;

    @JSONField(name = "memo")
    private String memo;

    @JSONField(name = "alipay_res_info")
    private AlipayResInfo alipayResInfo;

    @JSONField(name = "wxpay_res_info")
    private WxpayResInfo wxpayResInfo;

    @JSONField(name = "upay_res_info")
    private UpayResInfo upayResInfo;

    @JSONField(name = "ecny_res_info")
    private EcnyResInfo ecnyResInfo;
}
