package com.wangcaio2o.ipossa.sdk.request.bcmresultquery;

import com.alibaba.fastjson.annotation.JSONField;
import com.wangcaio2o.ipossa.sdk.request.BaseRequest;
import com.wangcaio2o.ipossa.sdk.response.bcmresultquery.BcmResultqueryResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/6/25 10:59
 * 银行卡刷卡结果查询交易
 */
@Data
public class BcmResultqueryRequest extends BaseRequest<BcmResultqueryResponse> {

    @JSONField(name = "pos_id")
    private String posId;

    @JSONField(name = "store_id")
    private String storeId;

    @JSONField(name = "pos_seq")
    private String posSeq;

    @JSONField(name = "user_id")
    private String userId;

    @JSONField(name = "bcm_resultquery_request")
    private BcmResultquery bcmResultqueryRequest;

    public BcmResultqueryRequest(String isspid,String systemId){
        super(isspid, systemId, "bcm_resultquery_request");
    }

    public BcmResultqueryRequest(){
        super("bcm_resultquery_request");
    }
}
