package com.wangcaio2o.ipossa.sdk.client;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wangcaio2o.ipossa.sdk.common.*;
import com.wangcaio2o.ipossa.sdk.request.BaseRequest;
import com.wangcaio2o.ipossa.sdk.response.BaseResponse;
import com.wangcaio2o.ipossa.sdk.response.ErrorResponse;
import com.wangcaio2o.ipossa.sdk.sign.StringUtils;
import com.wangcaio2o.ipossa.sdk.util.MD5Util;

/**
 * 负责请求操作
 *
 * <AUTHOR>
 */
public class OpenRequest {

    private OpenHttp openHttp;

    private String signKey;

    public OpenRequest(OpenConfig openConfig) {
        this.openHttp = new OpenHttp(openConfig);
        this.signKey = openConfig.getSignKey();
    }

    public <E extends BaseRequest<T>, T extends BaseResponse> String requestJson(String url, E request, Map<String, String> header) {
        try {
            request.setSign(buildSign(request));
        } catch (IllegalAccessException e) {
            return this.causeException(e);
        }

        String json = this.buildJsonString(request);
        if(!StringUtils.isEmpty(json) && json.length()<=1000) {
            System.out.println(json);
        }
        try {
            return this.openHttp.requestJson(url, json, header);
        } catch (IOException e) {
            return this.causeException(e);
        }
    }

    protected String causeException(Exception e) {
        ErrorResponse result = SdkErrors.HTTP_ERROR.getErrorResponse();
        return JSON.toJSONString(result);
    }

    private <E extends BaseRequest<T>, T extends BaseResponse> String buildSign(E request) throws IllegalAccessException {
        Field[] superFields = request.getClass().getSuperclass().getDeclaredFields();
        Field[] fields = request.getClass().getDeclaredFields();

        Map<String, String> signMap = new TreeMap<>();
        for (int i = 0; i < superFields.length; i++) {
            Field field = superFields[i];
            String fieldName = field.getName();
            if ("sign".equals(fieldName)) {
                continue;
            }
            field.setAccessible(true);
            Object value = field.get(request);
            signMap.put(field.getName(), value.toString());
        }
        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            field.setAccessible(true);
            Object value = field.get(request);
            if (value instanceof String || value instanceof Integer) {
                // 这里只取第一层级的参数, 第一层级的全是String
                signMap.put(field.getName(), value.toString());
            }
        }

        StringBuilder sb = new StringBuilder();
        for (String key : signMap.keySet()) {
            if (StringUtils.isEmpty(signMap.get(key))) {
                continue;
            }
            sb.append(StringUtils.toUnderScoreCase(key)).append(signMap.get(key));
        }
        
        sb.insert(0, signKey);
        sb.append(signKey);
        String signStr = sb.toString();
        return MD5Util.encryptUpper(signStr);
    }

    private <T extends BaseResponse, E extends BaseRequest<T>> String buildJsonString(E request) {
        return JSONObject.toJSONString(request);
    }
}