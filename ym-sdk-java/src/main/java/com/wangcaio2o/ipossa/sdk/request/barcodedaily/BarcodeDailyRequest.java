package com.wangcaio2o.ipossa.sdk.request.barcodedaily;

import com.alibaba.fastjson.annotation.JSONField;
import com.wangcaio2o.ipossa.sdk.request.BaseRequest;
import com.wangcaio2o.ipossa.sdk.response.barcodedaily.BarcodeDailyResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/6/21 16:10
 * 支付日结交易
 */
@Data
public class BarcodeDailyRequest extends BaseRequest<BarcodeDailyResponse> {

    @JSONField(name = "pos_id")
    private String posId;

    @JSONField(name = "store_id")
    private String storeId;

    @JSONField(name = "pos_seq")
    private String posSeq;

    @JSONField(name = "user_id")
    private String userId;

    @JSONField(name = "barcode_daily_request")
    private BarcodeDaily barcodeDailyRequest;

    public BarcodeDailyRequest(String isspid,String systemId){
        super(isspid, systemId, "barcode_daily_request");
    }

    public BarcodeDailyRequest(){
        super("barcode_daily_request");
    }

}
