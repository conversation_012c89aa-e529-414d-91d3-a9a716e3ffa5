package com.sohu.focus.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;
import java.util.Date;

/**
 * 手机验证码对象 system_sms_code
 *
 * <AUTHOR>
 * @date 2023-07-27
 */
@Data
@TableName("system_sms_code")
@Alias("focusSystemSmsCode")
public class SystemSmsCode  implements Serializable {


    /**
     * 编号
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 验证码
     */
    private String code;
    /**
     * 创建 IP
     */
    private String createIp;
    /**
     * 发送场景
     */
    private Long scene;
    /**
     * 今日发送的第几条
     */
    private Integer todayIndex;
    /**
     * 是否使用
     */
    private Integer used;
    /**
     * 使用时间
     */
    private Date usedTime;
    /**
     * 使用 IP
     */
    private String usedIp;
    /**
     * 创建者
     */
    private String creator;
    /**
     * 更新者
     */
    private String updater;

}
