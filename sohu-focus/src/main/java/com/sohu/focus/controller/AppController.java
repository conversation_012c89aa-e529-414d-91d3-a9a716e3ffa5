package com.sohu.focus.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.utils.AESBase62Util;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.encrypt.annotation.SkipApiEncrypt;
import com.sohu.common.encrypt.properties.ApiEncryptProperties;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1")
@Slf4j
public class AppController extends BaseController {

    private final ApiEncryptProperties properties;

    @Operation(summary = "获取接口密钥", description = "负责人：柯真")
    @GetMapping("/query/aesKey")
    @SkipApiEncrypt
    public R queryAesKey() {
        String platform = getPlatform();
        if (StrUtil.isBlankIfStr(platform)) {
            platform = Constants.CHANNEL_PC;
        }
        Map<String, Object> result = new HashMap<>();
        String aesKey = null;
        switch (platform.toLowerCase()) {
            // 解密 header 得到 payload ，见ApiVerifyBo.java
            case "android":
                aesKey = properties.getAndroidAesKey();
                break;
            case "ios":
                aesKey = properties.getIosAesKey();
                break;
            default:
                aesKey = properties.getPcAesKey();
                break;
        }
        result.put("iv", properties.getIv());
        result.put("aesKey", aesKey);
        result.put("hmacKey", properties.getHmacKey());
        return R.ok(result);
    }

    @Operation(summary = "解析加密字符串", description = "负责人柯真")
    @GetMapping("/{code}")
    public void decrypt(@PathVariable String code, HttpServletResponse response) {
        log.info("待解析加密的字符串:{}", code);
        try {
            String decrypt = AESBase62Util.decrypt(code);
            log.info("解析加密后的字符串:{}", decrypt);
            JSONObject parsedObj = JSONUtil.parseObj(decrypt);
            if (StrUtil.equalsAnyIgnoreCase(parsedObj.getStr("type"), "inviteGroup")) {
                // 解析解密的群口令
                Long groupId = parsedObj.getLong("id");
                response.sendRedirect("https://world.focus.cn/shouye?groupId=" + groupId);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
