package com.sohu.focus.controller.api;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.focus.api.bo.SohuSiteBo;
import com.sohu.focus.api.vo.SohuSiteVo;
import com.sohu.focus.domain.SohuSite;
import com.sohu.focus.service.ISohuSiteService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 站点开放接口
 *
 * <AUTHOR>
 * @date 2023-06-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/site")
public class ApiSiteController extends BaseController {


    @Resource
    private ISohuSiteService iSohuSiteService;

    /**
     * 查询一级站点列表
     */
    @GetMapping("/country")
    public R<List<SohuSite>> parentList() {
        return R.ok(iSohuSiteService.parentList());
    }

    /**
     * 查询二级站点列表
     */
    @GetMapping("/city")
    public R<List<SohuSiteVo>> list(SohuSiteBo bo) {
        return R.ok(iSohuSiteService.queryList(bo));
    }

}
