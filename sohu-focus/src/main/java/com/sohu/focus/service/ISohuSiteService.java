package com.sohu.focus.service;


import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.focus.api.bo.SohuSiteBo;
import com.sohu.focus.api.vo.SohuRegionVo;
import com.sohu.focus.api.vo.SohuSiteVo;
import com.sohu.focus.domain.SohuSite;

import java.util.Collection;
import java.util.List;

/**
 * 站点Service接口
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
public interface ISohuSiteService {

    /**
     * 查询站点
     */
    SohuSiteVo queryById(Long id);

    /**
     * 查询站点列表
     */
    TableDataInfo<SohuSiteVo> queryPageList(SohuSiteBo bo, PageQuery pageQuery);

    /**
     * 查询站点列表
     */
    List<SohuSiteVo> queryList(SohuSiteBo bo);

    /**
     * 新增站点
     */
    Boolean insertByBo(SohuSiteBo bo);

    /**
     * 修改站点
     */
    Boolean updateByBo(SohuSiteBo bo);

    /**
     * 校验并批量删除站点信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 上级站点列表
     *
     * @return
     */
    List<SohuSite> parentList();

    /**
     * 查询站点结构树
     */
    List<SohuSiteVo> getSiteTree(SohuSiteBo bo);

    /**
     * 根据id删除站点
     *
     * @param id 站点id
     * @return Boolean
     */
    Boolean deleteById(Long id);

    /**
     * 查询城市-属于国外
     * @return
     */
    List<SohuRegionVo> regionTreeOfOverseas();
}
