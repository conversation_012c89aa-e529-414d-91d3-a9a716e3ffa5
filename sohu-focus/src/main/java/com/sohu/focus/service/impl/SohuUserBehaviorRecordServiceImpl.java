package com.sohu.focus.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.core.domain.R;
import com.sohu.focus.api.bo.SohuUserBehaviorRecordBo;
import com.sohu.focus.api.bo.SohuUserBehaviorRecordPointBo;
import com.sohu.focus.domain.SohuUserBehaviorRecord;
import com.sohu.focus.domain.constant.Constants;
import com.sohu.focus.mapper.SohuUserBehaviorRecordMapper;
import com.sohu.focus.service.ISohuUserBehaviorRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户行为记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-01
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuUserBehaviorRecordServiceImpl implements ISohuUserBehaviorRecordService {

    private final SohuUserBehaviorRecordMapper baseMapper;

    @Value("${api.url}")
    private String apiUrl;

    /**
     * 数据透传大小
     */
    private static final int BATCH_SIZE = 1000;

    /**
     * 二次数据透传大小
     */
    private static final int FAIL_BATCH_SIZE = 100;

    /**
     * 新增用户行为记录
     */
    @Override
    public Boolean insertByBo(SohuUserBehaviorRecordBo bo) {
        SohuUserBehaviorRecord add = BeanUtil.toBean(bo, SohuUserBehaviorRecord.class);
        add.setOperResult(JSONUtil.toJsonStr(bo.getEventAttribute()));
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 定时执行数据透传任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processUntransmittedData() {
        // 1. 获取所有未透传的数据
        List<SohuUserBehaviorRecord> allUnTransferredData = getAllUnTransferredData(Constants.ZERO);

        if (CollUtil.isEmpty(allUnTransferredData)) {
            log.info("没有更多未透传数据, 完成处理.");
            return;
        }

        // 2. 分批处理
        List<List<SohuUserBehaviorRecord>> batches = CollUtil.split(allUnTransferredData, BATCH_SIZE);

        for (int i = 0; i < batches.size(); i++) {
            List<SohuUserBehaviorRecord> records = batches.get(i);
            log.info("开始处理第 {} 批次, 共 {} 条未透传数据", i + 1, records.size());

            List<SohuUserBehaviorRecordPointBo> dataList = BeanUtil.copyToList(records, SohuUserBehaviorRecordPointBo.class);
            List<Long> ids = records.stream().map(SohuUserBehaviorRecord::getId).collect(Collectors.toList());
            // 3. 调用API进行数据透传
            boolean success = transmitDataToApi(dataList, ids);

            if (!success) {
                log.error("批次数据透传失败，IDs: {}", ids);
            }
            // 4.更新数据状态
            updateTransferStatus(ids, success ? Constants.ONE : Constants.TWO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleFailedData() {
        // 1. 获取所有透传失败的数据
        List<SohuUserBehaviorRecord> allFailedData = getAllUnTransferredData(Constants.TWO);

        if (CollUtil.isEmpty(allFailedData)) {
            log.info("没有更多透传失败的数据, 完成处理.");
            return;
        }
        // 2. 分批处理失败数据
        List<List<SohuUserBehaviorRecord>> batches = CollUtil.split(allFailedData, FAIL_BATCH_SIZE);

        for (int i = 0; i < batches.size(); i++) {
            List<SohuUserBehaviorRecord> records = batches.get(i);
            log.info("开始处理第 {} 批次, 共 {} 条失败数据", i + 1, records.size());

            List<SohuUserBehaviorRecordPointBo> dataList = BeanUtil.copyToList(records, SohuUserBehaviorRecordPointBo.class);
            List<Long> ids = records.stream().map(SohuUserBehaviorRecord::getId).collect(Collectors.toList());
            // 3. 调用API进行数据透传
            boolean success = transmitDataToApi(dataList, ids);

            if (!success) {
                log.error("失败数据批次重试仍然失败，IDs: {}", ids);
            }
            // 4.更新数据状态
            updateTransferStatus(ids, success ? Constants.ONE : Constants.THREE);
        }
    }

    /**
     * 调用API进行数据透传
     */
    private boolean transmitDataToApi(List<SohuUserBehaviorRecordPointBo> dataList, List<Long> ids) {
        if (CollUtil.isEmpty(dataList)) {
            log.warn("透传数据为空，ids: {}", ids);
            return false;
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<List<SohuUserBehaviorRecordPointBo>> requestEntity = new HttpEntity<>(dataList, headers);

        try {
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<R> response = restTemplate.postForEntity(
                    apiUrl + "/app/api/user/behavior/addList", requestEntity, R.class);

            R body = response.getBody();
            if (body == null) {
                log.error("API返回的response body为空，ids: {}", ids);
                return false;
            }

            if (body.getCode() == R.SUCCESS && Boolean.TRUE.equals(body.getData())) {
                log.info("透传数据成功，ids: {}", ids);
                return true;
            } else {
                log.error("API调用失败，code: {}, message: {}, ids: {}", body.getCode(), body.getMsg(), ids);
                return false;
            }
        } catch (Exception e) {
            log.error("调用API发生异常，ids: {}, 异常信息: {}", ids, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取指定透传状态的所有数据
     *
     * @param transferStatus 透传状态
     * @return 所有符合条件的数据
     */
    private List<SohuUserBehaviorRecord> getAllUnTransferredData(Integer transferStatus) {
        LambdaQueryWrapper<SohuUserBehaviorRecord> queryWrapper = Wrappers.<SohuUserBehaviorRecord>lambdaQuery()
                .eq(SohuUserBehaviorRecord::getTransferStatus, transferStatus)
                .orderByAsc(SohuUserBehaviorRecord::getId);

        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 更新数据状态
     */
    private void updateTransferStatus(List<Long> ids, Integer status) {
        if (CollUtil.isEmpty(ids)) {
            log.warn("要更新的ID列表为空，跳过更新");
            return;
        }

        // 最大重试次数
        int maxRetries = 3;
        // 重试间隔（毫秒）
        int retryDelay = 1000;
        for (int i = 0; i < maxRetries; i++) {
            try {
                // 更新状态的逻辑
                LambdaUpdateWrapper<SohuUserBehaviorRecord> luw = new LambdaUpdateWrapper<>();
                luw.in(SohuUserBehaviorRecord::getId, ids)
                        .set(SohuUserBehaviorRecord::getTransferStatus, status);

                int updateCount = baseMapper.update(null, luw);
                if (updateCount == ids.size()) {
                    // 更新成功，退出循环
                    return;
                } else {
                    log.warn("更新状态数量不匹配，预期更新 {} 条，实际更新 {} 条, ids: {}", ids.size(), updateCount, ids);
                }
            } catch (Exception e) {
                log.error("更新状态失败，ids: {}, 状态: {}, 重试次数: {}, 异常信息: {}", ids, status, i + 1, e.getMessage(), e);
            }

            // 等待一段时间后重试
            try {
                Thread.sleep(retryDelay);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
                log.warn("线程中断", ie);
                break;
            }
        }

        // 如果达到最大重试次数仍然失败，则进行最终处理
        log.error("达到最大重试次数，更新状态仍然失败，ids: {}, 状态: {}", ids, status);
        throw new RuntimeException("达到最大重试次数，更新状态仍然失败");
    }
}
