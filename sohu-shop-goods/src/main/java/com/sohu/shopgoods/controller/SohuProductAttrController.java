package com.sohu.shopgoods.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.bo.SohuProductAttrBo;
import com.sohu.shopgoods.api.vo.SohuProductAttrVo;
import com.sohu.shopgoods.service.ISohuProductAttrService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 商品属性控制器
 * 前端访问路由地址为:/shop/productAttr
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/productAttr")
public class SohuProductAttrController extends BaseController {

    private final ISohuProductAttrService iSohuProductAttrService;

    /**
     * 查询商品属性列表
     */
    @SaCheckPermission("shop:productAttr:list")
    @GetMapping("/list")
    public TableDataInfo<SohuProductAttrVo> list(SohuProductAttrBo bo, PageQuery pageQuery) {
        return iSohuProductAttrService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出商品属性列表
     */
    @SaCheckPermission("shop:productAttr:export")
    @Log(title = "商品属性", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuProductAttrBo bo, HttpServletResponse response) {
        List<SohuProductAttrVo> list = iSohuProductAttrService.queryList(bo);
        ExcelUtil.exportExcel(list, "商品属性", SohuProductAttrVo.class, response);
    }

    /**
     * 获取商品属性详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("shop:productAttr:query")
    @GetMapping("/{id}")
    public R<SohuProductAttrVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuProductAttrService.queryById(id));
    }

    /**
     * 新增商品属性
     */
    @SaCheckPermission("shop:productAttr:add")
    @Log(title = "商品属性", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuProductAttrBo bo) {
        return toAjax(iSohuProductAttrService.insertByBo(bo));
    }

    /**
     * 修改商品属性
     */
    @SaCheckPermission("shop:productAttr:edit")
    @Log(title = "商品属性", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuProductAttrBo bo) {
        return toAjax(iSohuProductAttrService.updateByBo(bo));
    }

    /**
     * 删除商品属性
     *
     * @param ids 主键串
     */
    @SaCheckPermission("shop:productAttr:remove")
    @Log(title = "商品属性", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuProductAttrService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
