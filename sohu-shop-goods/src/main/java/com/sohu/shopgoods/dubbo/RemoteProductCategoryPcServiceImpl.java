package com.sohu.shopgoods.dubbo;

import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.shopgoods.api.RemoteProductCategoryPcService;
import com.sohu.shopgoods.api.bo.SohuProductCategoryPcBo;
import com.sohu.shopgoods.api.domain.SohuProductCategoryPcReqBo;
import com.sohu.shopgoods.api.model.SohuProductCategoryPcModel;
import com.sohu.shopgoods.api.vo.SohuCategoryQualificationVo;
import com.sohu.shopgoods.api.vo.SohuProductCategoryPcVo;
import com.sohu.shopgoods.service.ISohuProductBrandService;
import com.sohu.shopgoods.service.ISohuProductCategoryPcService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品购物车服务
 *
 * @author: zc
 * @date: 2023/7/21 16:14
 * @version: 1.0.0
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteProductCategoryPcServiceImpl implements RemoteProductCategoryPcService {

    private final ISohuProductCategoryPcService productCategoryPcService;
    private final ISohuProductBrandService iSohuProductBrandService;

    @Override
    public List<SohuProductCategoryPcModel> getCategory(String sysSource) {
        List<SohuProductCategoryPcVo> categoryPcVos = productCategoryPcService.pcTree(sysSource);
        List<SohuProductCategoryPcModel> categoryPcModels = Lists.newArrayList();

        for (SohuProductCategoryPcVo sourceObject : categoryPcVos) {
            SohuProductCategoryPcModel targetObject = convertToModel(sourceObject);
            categoryPcModels.add(targetObject);
        }
        return categoryPcModels;
    }

    /**
     * copy树结构
     *
     * @param source
     * @return SohuProductCategoryPcModel
     */
    private SohuProductCategoryPcModel convertToModel(SohuProductCategoryPcVo source) {
        SohuProductCategoryPcModel target = new SohuProductCategoryPcModel();
        BeanUtils.copyProperties(source, target);

        // 处理子节点
        List<SohuProductCategoryPcModel> children = Lists.newArrayList();
        if (source.getChildrenList() != null) {
            for (SohuProductCategoryPcVo child : source.getChildrenList()) {
                SohuProductCategoryPcModel childModel = convertToModel(child);
                children.add(childModel);
            }
        }
        target.setChildrenList(children);
        return target;
    }

    @Override
    public List<SohuProductCategoryPcModel> getList(String sysSource) {
        List<SohuProductCategoryPcVo> list = productCategoryPcService.getList(sysSource);
        return BeanCopyUtils.copyList(list, SohuProductCategoryPcModel.class);
    }

    @Override
    public Boolean insertByBo(SohuProductCategoryPcReqBo bo) {
        return productCategoryPcService.insertByBo(BeanCopyUtils.copy(bo, SohuProductCategoryPcBo.class));
    }

    @Override
    public Boolean updateByBo(SohuProductCategoryPcReqBo bo) {
        return productCategoryPcService.updateByBo(BeanCopyUtils.copy(bo, SohuProductCategoryPcBo.class));
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return productCategoryPcService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public List<SohuProductCategoryPcModel> pcTree(String sysSource) {
        List<SohuProductCategoryPcVo> vos = productCategoryPcService.pcTree(sysSource);
        return BeanCopyUtils.copyList(vos, SohuProductCategoryPcModel.class);
    }

    @Override
    public List<SohuProductCategoryPcModel> pcTree(String source,String sysSource) {
        List<SohuProductCategoryPcVo> vos = productCategoryPcService.pcTree(source,sysSource);
        return BeanCopyUtils.copyList(vos, SohuProductCategoryPcModel.class);
    }

    @Override
    public List<SohuProductCategoryPcVo> platformTree(String sysSource) {
        return productCategoryPcService.pcTree(sysSource);
    }

    @Override
    public SohuProductCategoryPcVo queryById(Long id) {
        return productCategoryPcService.queryById(id);
    }

    @Override
    public List<SohuProductCategoryPcModel> pcCateTree(String shopType) {
        List<SohuProductCategoryPcVo> vos = productCategoryPcService.pcCateTree(shopType);
        return BeanCopyUtils.copyList(vos, SohuProductCategoryPcModel.class);
    }

    @Override
    public List<SohuProductCategoryPcVo> listByIds(List<Long> categoryIds) {
        return productCategoryPcService.listByIds(categoryIds);
    }

    @Override
    public List<SohuProductCategoryPcVo> platformTreeForLevel(String code, Integer level) {
        return null;
    }

    @Override
    public List<SohuCategoryQualificationVo> listQualification(List<Long> categoryIds) {
        return null;
    }

    @Override
    public List<SohuProductCategoryPcVo> selectByIds(Collection<Long> ids) {
        return productCategoryPcService.selectByIds(ids);
    }

    @Override
    public Map<Long, String> buildCategoryNames(List<Long> secondCategoryIds) {
        return iSohuProductBrandService.buildCategoryNames(secondCategoryIds);
    }

    @Override
    public Long getSecondCateByCateId(Long cateId) {
        return productCategoryPcService.getSecondCateByCateId(cateId);
    }

    @Override
    public List<SohuProductCategoryPcVo> merchantCatetree(Long merchantId) {
        return productCategoryPcService.merchantCatetree(merchantId);
    }

}
