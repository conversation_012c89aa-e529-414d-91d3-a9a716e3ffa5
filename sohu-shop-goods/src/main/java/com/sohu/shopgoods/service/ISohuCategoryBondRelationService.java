package com.sohu.shopgoods.service;


import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.bo.SohuCategoryBondRelationBo;
import com.sohu.shopgoods.api.vo.SohuCategoryBondRelationVo;

import java.util.Collection;
import java.util.List;

/**
 * 分类保证金关联Service接口
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
public interface ISohuCategoryBondRelationService {

    /**
     * 查询分类保证金关联
     */
    SohuCategoryBondRelationVo queryById(Long id);

    /**
     * 查询分类保证金关联列表
     */
    TableDataInfo<SohuCategoryBondRelationVo> queryPageList(SohuCategoryBondRelationBo bo, PageQuery pageQuery);

    /**
     * 查询分类保证金关联列表
     */
    List<SohuCategoryBondRelationVo> queryList(SohuCategoryBondRelationBo bo);

    /**
     * 修改分类保证金关联
     */
    Boolean insertByBo(SohuCategoryBondRelationBo bo);

    /**
     * 修改分类保证金关联
     */
    Boolean updateByBo(SohuCategoryBondRelationBo bo);

    /**
     * 校验并批量删除分类保证金关联信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 基于分类查询对应保证金
     * @param categoryId
     * @return
     */
    List<SohuCategoryBondRelationVo> listByCategoryId(Long categoryId);

    /**
     * 保存类目保证金关系
     *
     * @param categoryBondRelationBos 类目保证金关系
     * @return
     */
    void saveBatchCategoryBond(List<SohuCategoryBondRelationBo> categoryBondRelationBos);
}
