package com.sohu.shopgoods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.RemoteAdminService;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.enums.SourceTypeEnum;
import com.sohu.admin.api.vo.SohuCategoryBrandBusinessSettingsVo;
import com.sohu.common.core.constant.CacheNames;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.utils.TreeBuildUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.service.RemotePlatformIndustryService;
import com.sohu.middle.api.vo.SohuPlatformIndustryRelationVo;
import com.sohu.shopgoods.api.bo.SohuProductCategoryPcBo;
import com.sohu.shopgoods.api.vo.SohuProductCategoryPcVo;
import com.sohu.shopgoods.constant.Constants;
import com.sohu.shopgoods.domain.SohuProduct;
import com.sohu.shopgoods.domain.SohuProductCategoryPc;
import com.sohu.shopgoods.mapper.SohuProductCategoryPcMapper;
import com.sohu.shopgoods.mapper.SohuProductMapper;
import com.sohu.shopgoods.service.ISohuProductBrandCategoryService;
import com.sohu.shopgoods.service.ISohuProductCategoryPcService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品分类（平台）Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@RequiredArgsConstructor
@Service
public class SohuProductCategoryPcServiceImpl implements ISohuProductCategoryPcService {

    private final SohuProductCategoryPcMapper baseMapper;

    @Resource
    private ISohuProductBrandCategoryService productBrandCategoryService;
    @Resource
    private SohuProductMapper productMapper;
    @DubboReference
    private RemotePlatformIndustryService platformIndustryService;

    @DubboReference
    private RemoteMerchantService remoteMerchantService;
    @DubboReference
    private RemoteAdminService remoteAdminService;

    /**
     * 查询商品分类（平台）
     */
    @Override
    public SohuProductCategoryPcVo queryById(Long id) {
        SohuProductCategoryPcVo sohuProductCategoryPcVo = baseMapper.selectVoById(id);
        if (ObjectUtil.isNull(sohuProductCategoryPcVo)) {
            return null;
        }
        SohuCategoryBrandBusinessSettingsVo categoryBrandBusinessSettings = remoteAdminService.getCategoryBrandBusinessSettings(id, Constants.CATEGORY);
        if (ObjectUtil.isNull(categoryBrandBusinessSettings)) {
            return sohuProductCategoryPcVo;
        }
        if (categoryBrandBusinessSettings.getIsOpenPersonal() == 1) {
            sohuProductCategoryPcVo.setCategoryType(Constants.PERSONAL);
        }
        if (categoryBrandBusinessSettings.getIsOpenBusiness() == 1) {
            sohuProductCategoryPcVo.setCategoryType(Constants.BUSINESS);
        }
        if (categoryBrandBusinessSettings.getIsOpenPersonal() == 1 && categoryBrandBusinessSettings.getIsOpenBusiness() == 1) {
            sohuProductCategoryPcVo.setCategoryType(Constants.ALL);
        }

        return sohuProductCategoryPcVo;
    }

    /**
     * 查询商品分类（平台）列表
     */
    @Override
    public TableDataInfo<SohuProductCategoryPcVo> queryPageList(SohuProductCategoryPcBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuProductCategoryPc> lqw = buildQueryWrapper(bo);
        Page<SohuProductCategoryPcVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询商品分类（平台）列表
     */
    @Override
    public List<SohuProductCategoryPcVo> queryList(SohuProductCategoryPcBo bo) {
        LambdaQueryWrapper<SohuProductCategoryPc> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuProductCategoryPc> buildQueryWrapper(SohuProductCategoryPcBo bo) {
//        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuProductCategoryPc> lqw = Wrappers.lambdaQuery();
        lqw.eq(null != (bo.getPid()), SohuProductCategoryPc::getPid, bo.getPid());
        lqw.like(StringUtils.isNotBlank(bo.getName()), SohuProductCategoryPc::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getIcon()), SohuProductCategoryPc::getIcon, bo.getIcon());
        lqw.eq(bo.getLevel() != null, SohuProductCategoryPc::getLevel, bo.getLevel());
        lqw.eq(bo.getSort() != null, SohuProductCategoryPc::getSort, bo.getSort());
        lqw.eq(bo.getIsShow() != null, SohuProductCategoryPc::getIsShow, bo.getIsShow());
        lqw.eq(null != bo.getIsDel(), SohuProductCategoryPc::getIsDel, bo.getIsDel());
        lqw.eq(StringUtils.isNotBlank(bo.getSysSource()), SohuProductCategoryPc::getSysSource, bo.getSysSource());
        return lqw;
    }

    /**
     * 新增商品分类（平台）
     */
    @CachePut(cacheNames = CacheNames.PRODUCT_BRAND)
    @Override
    public Boolean insertByBo(SohuProductCategoryPcBo bo) {
        if (checkName(bo.getName(), bo.getPid())) {
            throw new RuntimeException("分类名称已存在");
        }
        SohuProductCategoryPc add = BeanUtil.toBean(bo, SohuProductCategoryPc.class);
        add.setPid(0L);
        if (bo.getLevel() != 1) {
            if (bo.getPid().equals(0)) {
                throw new RuntimeException("子级菜单，父级ID不能为0");
            }
            add.setPid(bo.getPid());
        }
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商品分类（平台）
     */
//    @CachePut(cacheNames = CacheNames.PRODUCT_BRAND)
    @Override
    public Boolean updateByBo(SohuProductCategoryPcBo bo) {
        SohuProductCategoryPc update = BeanUtil.toBean(bo, SohuProductCategoryPc.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuProductCategoryPc entity) {
        // TODO 做一些数据校验,如唯一约束
        LambdaQueryWrapper<SohuProductCategoryPc> lqw = Wrappers.lambdaQuery();
        lqw.select(SohuProductCategoryPc::getId);
        lqw.eq(SohuProductCategoryPc::getPid, entity.getPid());
        lqw.eq(SohuProductCategoryPc::getName, entity.getName());
        lqw.last(" limit 1");
        SohuProductCategoryPc productCategoryPc = this.baseMapper.selectOne(lqw);

        if (ObjectUtil.isNotNull(productCategoryPc) && (!productCategoryPc.getId().equals(entity.getId()))) {
            throw new RuntimeException("分类名称已存在");
        }
    }

    /**
     * 批量删除商品分类（平台）
     */
//    @CacheEvict(cacheNames = CacheNames.PRODUCT_BRAND)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 每一个都要满足
            ids.forEach(s -> {
                SohuProductCategoryPc category = getByIdException(s);
                if (category.getLevel() < 3) {
                    List<SohuProductCategoryPc> categoryList = findAllChildListByPid(category.getId(), category.getLevel());
                    if (CollUtil.isNotEmpty(categoryList)) {
                        throw new RuntimeException("请先删除子级分类");
                    }
                }
                // 判断是否有商品使用该分类
                LambdaQueryWrapper<SohuProduct> lqw = Wrappers.lambdaQuery();
                lqw.select(SohuProduct::getId);
                lqw.eq(SohuProduct::getIsDel, false);
                lqw.eq(SohuProduct::getCategoryId, category.getId());
                lqw.last("limit 1");
                SohuProduct storeProduct = productMapper.selectOne(lqw);
                if (ObjectUtil.isNotNull(storeProduct)) {
                    throw new RuntimeException("有商品使用该分类，无法删除");
                }
                // 判断是否品牌关联该分类
                if (productBrandCategoryService.isExistCategory(category.getId())) {
                    throw new RuntimeException("有品牌关联该分类，无法删除");
                }
//                category.setIsDel(true);
            });
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<SohuProductCategoryPcVo> pcTree(String shopType) {
        // 1. 查询符合初始条件的子类
        List<SohuProductCategoryPcVo> initialCategories = baseMapper.selectCategoriesWithSettings(shopType);

        // 2. 创建一个List来保存所有类别，包括子类和它们的祖先
        List<SohuProductCategoryPcVo> allCategories = new ArrayList<>(initialCategories);

        // 3. 遍历初始类别，递归查找它们的祖先
        for (SohuProductCategoryPcVo category : initialCategories) {
            findAllParents(category, allCategories);
        }

        return buildBrandCateTreeSelect(allCategories);
    }

    private void findAllParents(SohuProductCategoryPcVo category, List<SohuProductCategoryPcVo> allCategories) {
        Long parentId = category.getPid();
        if (parentId != null) {
            // 查询父类别
            SohuProductCategoryPcVo parentCategory = baseMapper.selectVoById(parentId);
            if (parentCategory != null && !allCategories.contains(parentCategory)) {
                allCategories.add(parentCategory);
                findAllParents(parentCategory, allCategories);
            }
        }
    }

    @Override
    public List<SohuProductCategoryPcVo> pcTree(String source, String sysSource, Long platformIndustryId) {
        LambdaQueryWrapper<SohuProductCategoryPc> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuProductCategoryPc::getIsDel, false);
        lqw.eq(StringUtils.isNotBlank(sysSource), SohuProductCategoryPc::getSysSource, sysSource);
        // 给平台的是所有的 给商户的是只开启的，品牌关联也是只开启的
        if (!source.equals(SourceTypeEnum.PLATFORM_PC.getCode())) {
            lqw.eq(SohuProductCategoryPc::getIsShow, 1);
        }
        lqw.orderByDesc(SohuProductCategoryPc::getSort);
        lqw.orderByAsc(SohuProductCategoryPc::getId);
        List<SohuProductCategoryPcVo> productCategoryVos = this.baseMapper.selectVoList(lqw);
        List<Long> categoryIds = new ArrayList<>();
        if (Objects.nonNull(platformIndustryId)) {
            // 查询行业对应的分类
            List<SohuPlatformIndustryRelationVo> relationVoList = platformIndustryService.queryList(platformIndustryId, BusyType.Goods.getType());
            categoryIds = relationVoList.stream().map(SohuPlatformIndustryRelationVo :: getBusyCategoryId).collect(Collectors.toList());
        }
        // 2. 如果没有 categoryIds，直接构建完整树
        if (CollUtil.isEmpty(categoryIds)) {
            return buildBrandCateTreeSelect(productCategoryVos);
        }
        // 3. 从 productCategoryVos 中筛选出 categoryIds 及其所有父节点
        List<SohuProductCategoryPcVo> filteredCategories = filterCategoriesWithParents(productCategoryVos, categoryIds);
        return buildBrandCateTreeSelect(filteredCategories);
    }

    /**
     * 筛选出目标节点及其所有父节点
     */
    private List<SohuProductCategoryPcVo> filterCategoriesWithParents(List<SohuProductCategoryPcVo> all, List<Long> targetIds) {
        Set<Long> processedIds = new HashSet<>(); // 去重
        Queue<Long> queue = new LinkedList<>(targetIds); // 待处理的节点ID
        List<SohuProductCategoryPcVo> result = new ArrayList<>();
        while (!queue.isEmpty()) {
            Long currentId = queue.poll();
            if (processedIds.contains(currentId)) {
                continue; // 已处理过，跳过
            }
            // 查找当前ID对应的节点
            all.stream()
                    .filter(c -> currentId.equals(c.getId()))
                    .findFirst()
                    .ifPresent(c -> {
                        result.add(c); // 添加到结果
                        processedIds.add(currentId); // 标记为已处理

                        // 如果父节点未处理，加入队列
                        if (c.getPid() != null && c.getPid() != 0L && !processedIds.contains(c.getPid())) {
                            queue.add(c.getPid());
                        }
                    });
        }
        return result;
    }


    @Override
    public List<SohuProductCategoryPcVo> pcTree(String source, String sysSource) {
        LambdaQueryWrapper<SohuProductCategoryPc> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuProductCategoryPc::getIsDel, false);
        lqw.eq(StringUtils.isNotBlank(sysSource), SohuProductCategoryPc::getSysSource, sysSource);
        // 给平台的是所有的 给商户的是只开启的，品牌关联也是只开启的
        if (!source.equals(SourceTypeEnum.PLATFORM_PC.getCode())) {
            lqw.eq(SohuProductCategoryPc::getIsShow, 1);
        }
        lqw.orderByDesc(SohuProductCategoryPc::getSort);
        lqw.orderByAsc(SohuProductCategoryPc::getId);
        List<SohuProductCategoryPcVo> productCategoryVos = this.baseMapper.selectVoList(lqw);
        return buildBrandCateTreeSelect(productCategoryVos);
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param brandCategory 部门列表
     * @return 下拉树结构列表
     */
    public List<SohuProductCategoryPcVo> buildBrandCateTreeSelect(List<SohuProductCategoryPcVo> brandCategory) {
        if (CollUtil.isEmpty(brandCategory)) {
            return CollUtil.newArrayList();
        }
        List<SohuProductCategoryPcVo> treeList = new ArrayList<>();

        for (SohuProductCategoryPcVo node : brandCategory) {
            if (0 == node.getPid()) {
                SohuProductCategoryPcVo tree = new SohuProductCategoryPcVo();
                tree.setId(node.getId());
                tree.setPid(node.getPid());
                tree.setName(node.getName());
                tree.setIcon(node.getIcon());
                tree.setSort(node.getSort());
                tree.setIsShow(node.getIsShow());
                tree.setIsDel(node.getIsDel());
                tree.setLevel(node.getLevel());
                tree.setChildrenList(buildSubTree(brandCategory, node.getId()));
                tree.setCreateTime(node.getCreateTime());
                treeList.add(tree);
            }
        }

        return treeList;
    }

    /**
     * 递归组装
     *
     * @param nodeList
     * @param parentId
     * @return List<SohuProductCategoryPcVo> 子结构
     */
    private static List<SohuProductCategoryPcVo> buildSubTree(List<SohuProductCategoryPcVo> nodeList, Long parentId) {
        List<SohuProductCategoryPcVo> subTreeList = new ArrayList<>();
        for (SohuProductCategoryPcVo node : nodeList) {
            if (Objects.equals(parentId, node.getPid())) {
                SohuProductCategoryPcVo tree = new SohuProductCategoryPcVo();
                tree.setId(node.getId());
                tree.setPid(node.getPid());
                tree.setName(node.getName());
                tree.setIcon(node.getIcon());
                tree.setSort(node.getSort());
                tree.setIsShow(node.getIsShow());
                tree.setIsDel(node.getIsDel());
                tree.setLevel(node.getLevel());
                tree.setChildrenList(buildSubTree(nodeList, node.getId()));
                subTreeList.add(tree);
            }
        }
        return subTreeList;
    }

    /**
     * 校验分类名称
     *
     * @param name 分类名称
     * @return Boolean
     */
    private Boolean checkName(String name, Long pid) {
        LambdaQueryWrapper<SohuProductCategoryPc> lqw = Wrappers.lambdaQuery();
        lqw.select(SohuProductCategoryPc::getId);
        lqw.eq(SohuProductCategoryPc::getName, name);
        lqw.eq(SohuProductCategoryPc::getIsDel, false);
        lqw.eq(SohuProductCategoryPc::getPid, pid);
        lqw.last(" limit 1");
        SohuProductCategoryPc productCategory = this.baseMapper.selectOne(lqw);
        return ObjectUtil.isNotNull(productCategory) ? Boolean.TRUE : Boolean.FALSE;
    }


    /**
     * 根据id查询实体类
     *
     * @param id
     * @return
     */
    private SohuProductCategoryPc getByIdException(Long id) {
        SohuProductCategoryPc category = this.baseMapper.selectById(id);
        if (ObjectUtil.isNull(category) || category.getIsDel()) {
            throw new RuntimeException("分类不存在");
        }
        return category;
    }

    /**
     * 根据菜单id获取所有下级对象
     *
     * @param pid   菜单id
     * @param level 分类级别
     * @return List<ProductCategory>
     */
    @Override
    public List<SohuProductCategoryPc> findAllChildListByPid(Long pid, Long level) {
        LambdaQueryWrapper<SohuProductCategoryPc> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuProductCategoryPc::getPid, pid);
        lqw.eq(SohuProductCategoryPc::getIsDel, false);
        if (level == 2) {
            return this.baseMapper.selectList(lqw);
        }
        // level == 1
        List<SohuProductCategoryPc> categoryList = this.baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(categoryList)) {
            return categoryList;
        }
        List<Long> pidList = categoryList.stream().map(SohuProductCategoryPc::getId).collect(Collectors.toList());
        lqw.clear();
        lqw.in(SohuProductCategoryPc::getPid, pidList);
        lqw.eq(SohuProductCategoryPc::getIsDel, false);
        List<SohuProductCategoryPc> childCategoryList = this.baseMapper.selectList(lqw);
        categoryList.addAll(childCategoryList);
        return categoryList;
    }

    @Override
    public List<SohuProductCategoryPcVo> getList(String sysSource) {
        LambdaQueryWrapper<SohuProductCategoryPc> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuProductCategoryPc::getIsDel, false);
        lqw.eq(StringUtils.isNotBlank(sysSource), SohuProductCategoryPc::getSysSource, sysSource);
        return this.baseMapper.selectVoList(lqw);
    }

    /**
     * 查询商品一级分类（平台）列表
     */
    @Override
    public List<SohuProductCategoryPcVo> firstList(String sysSource) {
        LambdaQueryWrapper<SohuProductCategoryPc> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuProductCategoryPc::getLevel, 1);
        lqw.eq(SohuProductCategoryPc::getIsShow, 1);
        lqw.eq(SohuProductCategoryPc::getIsDel, false);
        lqw.eq(StringUtils.isNotBlank(sysSource), SohuProductCategoryPc::getSysSource, sysSource);
        lqw.orderByAsc(SohuProductCategoryPc::getSort);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public Long getCategoryIdByCategoryName(String categoryName) {
        String[] split = categoryName.split("/");
        if (split.length == 0) {
            throw new RuntimeException("平台商品分类格式错误");
        }
        Long pid = 0L;
        for (String name : split) {
            SohuProductCategoryPc entity = getByNameAndPid(name, pid);
            if (Objects.isNull(entity)) {
                throw new RuntimeException("请联系客服维护平台商品分类");
            }
            pid = entity.getId();
        }
        return pid;
    }

    @Override
    public String getFullCategoryNameByCategoryId(Long categoryId) {
        return this.getFullNameById(categoryId, null);
    }

    @Override
    public List<SohuProductCategoryPcVo> pcCateTree(String sysSource) {
        LambdaQueryWrapper<SohuProductCategoryPc> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuProductCategoryPc::getIsDel, false);
        lqw.eq(StringUtils.isNotBlank(sysSource), SohuProductCategoryPc::getSysSource, sysSource);
        // 给平台的是所有的 给商户的是只开启的，品牌关联也是只开启的
        lqw.eq(SohuProductCategoryPc::getIsShow, 1);
        lqw.orderByDesc(SohuProductCategoryPc::getSort);
        lqw.orderByAsc(SohuProductCategoryPc::getId);
        List<SohuProductCategoryPcVo> productCategoryVos = this.baseMapper.selectVoList(lqw);
        return buildBrandCateTreeSelect(productCategoryVos);
    }

    @Override
    public List<SohuProductCategoryPcVo> selectByIds(Collection<Long> ids) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<SohuProductCategoryPc>().in(SohuProductCategoryPc::getId, ids));
    }

    @Override
    public Long getSecondCateByCateId(Long cateId) {
        SohuProductCategoryPc entity = this.baseMapper.selectById(cateId);
        if (Objects.isNull(entity)) {
            return cateId;
        }
        if (entity.getLevel() == 3) {
            return entity.getPid();
        }
        return cateId;
    }

    @Override
    public List<SohuProductCategoryPcVo> merchantCatetree(Long merchantId) {
        List<SohuProductCategoryPcVo> treeList = new ArrayList<>();
        //查询商户开启的类目
        List<Long> cateIds = remoteMerchantService.getCateIdByPass(merchantId);
        if (CollUtil.isEmpty(cateIds)) {
            return treeList;
        }
        //获取完整树
        List<SohuProductCategoryPcVo> allTree = this.pcTree(null);
        filterSubTreeById(allTree, cateIds, treeList);
        return treeList;
    }

    @Override
    public List<SohuProductCategoryPcVo> listByIds(List<Long> categoryIds) {
        return baseMapper.selectVoBatchIds(categoryIds);
    }

    private String getFullNameById(Long id, String lastName) {
        SohuProductCategoryPc entity = this.baseMapper.selectById(id);
        if (Objects.nonNull(entity)) {
            if (StrUtil.isEmpty(lastName)) {
                lastName = entity.getName();
            } else {
                lastName = entity.getName() + "/" + lastName;
            }
            return getFullNameById(entity.getPid(), lastName);
        } else {
            return lastName;
        }
    }


    /**
     * 根据名称和父级ID获取平台商品分类
     */
    private SohuProductCategoryPc getByNameAndPid(String name, Long pid) {
        LambdaQueryWrapper<SohuProductCategoryPc> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuProductCategoryPc::getIsDel, false)
                .eq(SohuProductCategoryPc::getPid, pid)
                .eq(SohuProductCategoryPc::getName, name)
                .last("limit 1");
        return this.baseMapper.selectOne(lqw);
    }

    /**
     * 根据指定节点ID筛选出对应的子树
     *
     * @param allNodes 所有节点组成的树结构列表
     * @param targetId 目标节点ID
     * @return 匹配到的子树根节点
     */
    public void filterSubTreeById(List<SohuProductCategoryPcVo> allNodes, List<Long> targetId, List<SohuProductCategoryPcVo> treeList) {
        for (SohuProductCategoryPcVo node : allNodes) {
            SohuProductCategoryPcVo result = findNodeRecursively(node, targetId);
            if (result != null) {
                treeList.add(result);
            }
        }
    }

    /**
     * 递归查找匹配的节点
     *
     * @param node      当前节点
     * @param targetIds 目标节点ID
     * @return 若当前节点或其子节点包含目标ID，则返回该子树根节点
     */
    private SohuProductCategoryPcVo findNodeRecursively(SohuProductCategoryPcVo node, List<Long> targetIds) {
        if (node.getId().equals(targetIds)) {
            return node; // 找到目标节点，直接返回
        }

        List<SohuProductCategoryPcVo> children = node.getChildrenList();
        if (children != null && !children.isEmpty()) {
            List<Long> cateIds = children.stream().map(SohuProductCategoryPcVo::getId).collect(Collectors.toList());
            List<Long> intersection = cateIds.stream()
                    .filter(targetIds::contains)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(intersection)) {
                List<SohuProductCategoryPcVo> childrenNew = new ArrayList<>();
                for (SohuProductCategoryPcVo child : children) {
                    if (targetIds.contains(child.getId())) {
                        childrenNew.add(child);
                    }
                }
                node.setChildrenList(childrenNew);
                return node;
            }
        }
        return null; // 未找到
    }
}
