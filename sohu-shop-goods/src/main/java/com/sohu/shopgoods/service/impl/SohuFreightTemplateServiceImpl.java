package com.sohu.shopgoods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.shopgoods.api.bo.SohuFreightPayBo;
import com.sohu.shopgoods.api.bo.SohuFreightTemplateBo;
import com.sohu.shopgoods.api.bo.SohuFreightTemplateInsertBo;
import com.sohu.shopgoods.api.bo.SohuFreightTemplateUpdateBo;
import com.sohu.shopgoods.api.enums.ShopFreightEnums;
import com.sohu.shopgoods.api.vo.SohuFreightPayVo;
import com.sohu.shopgoods.api.vo.SohuFreightTemplateVo;
import com.sohu.shopgoods.domain.SohuFreightTemplate;
import com.sohu.shopgoods.domain.SohuProduct;
import com.sohu.shopgoods.mapper.SohuFreightTemplateMapper;
import com.sohu.shopgoods.mapper.SohuProductMapper;
import com.sohu.shopgoods.service.ISohuFreightPayService;
import com.sohu.shopgoods.service.ISohuFreightTemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 运费模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@RequiredArgsConstructor
@Service
public class SohuFreightTemplateServiceImpl implements ISohuFreightTemplateService {

    private final SohuFreightTemplateMapper baseMapper;
    private final SohuProductMapper productMapper;
    @Resource
    private ISohuFreightPayService freightPayService;

    /**
     * 查询运费模板
     */
    @Override
    public SohuFreightTemplateVo queryById(Long id) {
        SohuFreightTemplateVo vo = baseMapper.selectVoById(id);
        if(Objects.isNull(vo)){
            return null;
        }
        // 买家付运费区域
        SohuFreightPayBo payBo = new SohuFreightPayBo();
        payBo.setFreightId(vo.getId());
        List<SohuFreightPayVo> freightPayList = freightPayService.queryList(payBo);
        if (CollUtil.isNotEmpty(freightPayList)) {
            vo.setFreightPayList(freightPayList);
        }
        return vo;
    }

    /**
     * 查询运费模板列表
     */
    @Override
    public TableDataInfo<SohuFreightTemplateVo> queryPageList(SohuFreightTemplateBo bo, PageQuery pageQuery) {
        if (StrUtil.equalsAnyIgnoreCase(bo.getType(), "CUSTOMIZE")) {
            bo.setUserId(LoginHelper.getUserId());
        }
        LambdaQueryWrapper<SohuFreightTemplate> lqw = buildQueryWrapper(bo);
        Page<SohuFreightTemplateVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        // 买家付运费区域
        result.getRecords().forEach(f -> {
            SohuFreightPayBo payBo = new SohuFreightPayBo();
            payBo.setFreightId(f.getId());
            List<SohuFreightPayVo> freightPayList = freightPayService.queryList(payBo);
            if (CollUtil.isNotEmpty(freightPayList)) {
                f.setFreightPayList(freightPayList);
            }

        });
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询运费模板列表
     */
    @Override
    public List<SohuFreightTemplateVo> queryList(SohuFreightTemplateBo bo) {
        LambdaQueryWrapper<SohuFreightTemplate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询运费模板列表(不分页)
     */
    @Override
    public List<SohuFreightTemplateVo> queryList() {
        // 自定义模板
        LambdaQueryWrapper<SohuFreightTemplate> lqw = Wrappers.lambdaQuery();
        lqw.select(SohuFreightTemplate::getId, SohuFreightTemplate::getName);
        lqw.eq(SohuFreightTemplate::getUserId, LoginHelper.getUserId());
        lqw.eq(SohuFreightTemplate::getType, "CUSTOMIZE");
        lqw.eq(SohuFreightTemplate::getIsDel, false);
        List<SohuFreightTemplateVo> list = baseMapper.selectVoList(lqw);
        // 默认模板
        LambdaQueryWrapper<SohuFreightTemplate> defaultParam = Wrappers.lambdaQuery();
        defaultParam.select(SohuFreightTemplate::getId, SohuFreightTemplate::getName);
        defaultParam.eq(SohuFreightTemplate::getType, "DEFAULT");
        defaultParam.eq(SohuFreightTemplate::getIsDel, false);
        List<SohuFreightTemplateVo> defaultList = baseMapper.selectVoList(defaultParam);
        return Stream.concat(list.stream(), defaultList.stream()).collect(Collectors.toList());
    }

    private LambdaQueryWrapper<SohuFreightTemplate> buildQueryWrapper(SohuFreightTemplateBo bo) {
        LambdaQueryWrapper<SohuFreightTemplate> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuFreightTemplate::getUserId, bo.getUserId());
        lqw.eq(bo.getMerId() != null, SohuFreightTemplate::getMerId, bo.getMerId());
        lqw.like(StringUtils.isNotBlank(bo.getName()), SohuFreightTemplate::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), SohuFreightTemplate::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getFreeArea()), SohuFreightTemplate::getFreeArea, bo.getFreeArea());
        lqw.eq(StringUtils.isNotBlank(bo.getNotDeliveryArea()), SohuFreightTemplate::getNotDeliveryArea, bo.getNotDeliveryArea());
        lqw.eq(bo.getIsDel() != null, SohuFreightTemplate::getIsDel, bo.getIsDel());
        return lqw;
    }

    /**
     * 新增运费模板
     */
    @Override
    public Boolean insertByBo(SohuFreightTemplateInsertBo bo) {
        SohuFreightTemplate add = BeanUtil.toBean(bo, SohuFreightTemplate.class);
        add.setUserId(LoginHelper.getUserId());
        validEntityBeforeSave(add);
        int result = baseMapper.insert(add);

        // 买家付运费区域
        if (CollUtil.isNotEmpty(bo.getFreightPayList())) {
            bo.getFreightPayList().forEach(f -> f.setFreightId(add.getId()));
            freightPayService.insertBatch(bo.getFreightPayList());
        }
        return result > 0;
    }

    /**
     * 修改运费模板
     */
    @Override
    public Boolean updateByBo(SohuFreightTemplateUpdateBo bo) {
        // 校验是否是默认模板
        SohuFreightTemplate sohuFreightTemplate = baseMapper.selectById(bo.getId());
        if (StrUtil.equalsAnyIgnoreCase(sohuFreightTemplate.getType(), "DEFAULT")) {
            throw new ServiceException("默认模板不支持编辑");
        }

        // 校验是否有商品绑定
        validFreightTemplate(bo.getId());

        SohuFreightTemplate update = BeanUtil.toBean(bo, SohuFreightTemplate.class);
        validEntityBeforeSave(update);
        int result = baseMapper.updateById(update);

        // 买家付运费区域
        freightPayService.deleteByFreightId(bo.getId());
        if (CollUtil.isNotEmpty(bo.getFreightPayList())) {
            bo.getFreightPayList().forEach(f -> f.setFreightId(bo.getId()));
            freightPayService.insertBatch(bo.getFreightPayList());
        }

        return result > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuFreightTemplate entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除运费模板
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        for (Long f : ids) {
            // 校验是否是默认模板
            SohuFreightTemplate sohuFreightTemplate = baseMapper.selectById(f);
            if (Objects.isNull(sohuFreightTemplate)) {
                throw new ServiceException("运费模板不存在");
            }
            if (StrUtil.equalsAnyIgnoreCase(sohuFreightTemplate.getType(), "DEFAULT")) {
                throw new ServiceException("默认模板不支持删除");
            }

            // 校验是否有商品绑定
            validFreightTemplate(f);

            // 删除运费模板付费区域
            freightPayService.deleteByFreightId(f);
        }

        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public SohuFreightTemplate getByNameAndMerId(String name, Long merId) {
        SohuFreightTemplate entity=null;
        if(StrUtil.isNotBlank(name)){
            LambdaQueryWrapper<SohuFreightTemplate> lqw = Wrappers.lambdaQuery();
            lqw.eq(SohuFreightTemplate::getName, name);
            lqw.eq(SohuFreightTemplate::getIsDel, false);
            lqw.eq(SohuFreightTemplate::getMerId, merId);
            lqw.last("limit 1");
            entity = this.baseMapper.selectOne(lqw);
        }
        if (Objects.isNull(entity)) {
            //取默认模板
            LambdaQueryWrapper<SohuFreightTemplate> lqwDefault = Wrappers.lambdaQuery();
            lqwDefault.eq(SohuFreightTemplate::getIsDel, false);
            lqwDefault.eq(SohuFreightTemplate::getMerId, 0);
            lqwDefault.eq(SohuFreightTemplate::getType, ShopFreightEnums.FreightTemplateTypeEnum.DEFAULT.getCode());
            lqwDefault.last("limit 1");
            entity = this.baseMapper.selectOne(lqwDefault);
        }
        return entity;
    }

    /**
     * 获取默认模板
     * @return
     */
    @Override
    public SohuFreightTemplate getDefaultFreightTemplate(){
        //取默认模板
        LambdaQueryWrapper<SohuFreightTemplate> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuFreightTemplate::getIsDel, false);
        lqw.eq(SohuFreightTemplate::getMerId, 0);
        lqw.eq(SohuFreightTemplate::getType, ShopFreightEnums.FreightTemplateTypeEnum.DEFAULT.getCode());
        lqw.last("limit 1");
        return this.baseMapper.selectOne(lqw);
    }

    private void validFreightTemplate(Long freightTemplateId) {
        LambdaQueryWrapper<SohuProduct> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuProduct::getFreightTemplateId, freightTemplateId);
        lqw.eq(SohuProduct::getSysSource, Constants.SOHUGLOBAL);
        lqw.eq(SohuProduct::getIsDel, false);
        List<SohuProduct> productList = productMapper.selectList(lqw);
        if (CollUtil.isNotEmpty(productList)) {
            throw new ServiceException("该运费模板已有商品生效");
        }
    }

}
