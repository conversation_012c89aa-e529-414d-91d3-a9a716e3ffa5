package com.sohu.shopgoods.service;


import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.bo.SohuBrandMarketSubjectBo;
import com.sohu.shopgoods.api.vo.SohuBrandMarketSubjectVo;

import java.util.Collection;
import java.util.List;

/**
 * 品牌与市场主体关联Service接口
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
public interface ISohuBrandMarketSubjectService {

    /**
     * 查询品牌与市场主体关联
     */
    SohuBrandMarketSubjectVo queryById(Long id);

    /**
     * 查询品牌与市场主体关联列表
     */
    TableDataInfo<SohuBrandMarketSubjectVo> queryPageList(SohuBrandMarketSubjectBo bo, PageQuery pageQuery);

    /**
     * 查询品牌与市场主体关联列表
     */
    List<SohuBrandMarketSubjectVo> queryList(SohuBrandMarketSubjectBo bo);

    /**
     * 修改品牌与市场主体关联
     */
    Boolean insertByBo(SohuBrandMarketSubjectBo bo);

    /**
     * 修改品牌与市场主体关联
     */
    Boolean updateByBo(SohuBrandMarketSubjectBo bo);

    /**
     * 校验并批量删除品牌与市场主体关联信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
