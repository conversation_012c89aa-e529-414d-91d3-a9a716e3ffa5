package com.sohu.shopgoods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.bo.SohuActivityProductBo;
import com.sohu.shopgoods.api.bo.SohuProductActivityBo;
import com.sohu.shopgoods.api.vo.*;
import com.sohu.shopgoods.domain.SohuActivityProduct;
import com.sohu.shopgoods.domain.SohuProductActivity;
import com.sohu.shopgoods.mapper.SohuProductActivityMapper;
import com.sohu.shopgoods.service.ISohuActivityProductService;
import com.sohu.shopgoods.service.ISohuProductActivityService;
import com.sohu.shopgoods.service.ISohuProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 营销活动Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-06
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuProductActivityServiceImpl implements ISohuProductActivityService {

    private final SohuProductActivityMapper baseMapper;
    private final ISohuActivityProductService iSohuActivityProductService;
    private final ISohuProductService iSohuProductService;

    private final static Integer OPEN = 1;
    private final static Integer CLOSE = 0;

    /**
     * 查询营销活动
     */
    @Override
    public ProductActivityInfoVo queryById(Long id){
        SohuProductActivityVo sohuProductActivityVo = baseMapper.selectVoById(id);
        ProductActivityInfoVo vo = new ProductActivityInfoVo();
        BeanUtil.copyProperties(sohuProductActivityVo,vo);
        //查询活动商品
        SohuActivityProductBo bo = new SohuActivityProductBo();
        bo.setAid(id);
        List<SohuActivityProductVo> productList = iSohuActivityProductService.queryList(bo);
        if (CollectionUtil.isNotEmpty(productList)){
            List<Long> proIds = productList.stream().map(SohuActivityProductVo::getProductId).collect(Collectors.toList());
            List<SimpleProductVo> productVos = iSohuProductService.getSimpleListInIds(proIds);
            log.info("productVos={}", JSONObject.toJSONString(productVos));
            List<ActivityProductInfoVo> infoList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(productVos)) {
                productList.forEach(e -> {
                    if (productVos.stream().anyMatch(e1 -> e1.getId().equals(e.getProductId()))) {
                        SimpleProductVo simpleProductVo = productVos.stream().filter(e1 -> e1.getId().equals(e.getProductId())).findFirst().get();
                        ActivityProductInfoVo infoVo = new ActivityProductInfoVo();
                        infoVo.setAid(e.getAid());
                        infoVo.setProductId(e.getProductId());
                        infoVo.setProImage(e.getProImage());
                        infoVo.setSort(e.getSort());
                        infoVo.setProductImage(simpleProductVo.getImage());
                        infoVo.setProName(simpleProductVo.getStoreName());
                        infoList.add(infoVo);
                    }
                });
            }
            vo.setProList(infoList);
        }
        return vo;
    }

    @Override
    public SohuProductActivityVo queryByAcNo(String acNo) {
        LambdaQueryWrapper<SohuProductActivity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuProductActivity::getIsDel, Boolean.FALSE);
        lqw.eq(SohuProductActivity::getAcNo,acNo);
        lqw.eq(SohuProductActivity::getIsOpen,OPEN);
        SohuProductActivityVo vo =  baseMapper.selectVoOne(lqw);
        if (vo!=null){
            //查询活动商品
            SohuActivityProductBo bo = new SohuActivityProductBo();
            bo.setAid(vo.getId());
            List<SohuActivityProductVo> productList = iSohuActivityProductService.queryList(bo);
            vo.setProList(productList);
        }
        return vo;
    }

    /**
     * 查询营销活动列表
     */
    @Override
    public TableDataInfo<SohuProductActivityVo> queryPageList(PageQuery pageQuery) {
        LambdaQueryWrapper<SohuProductActivity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuProductActivity::getIsDel, Boolean.FALSE);
        lqw.orderByDesc(SohuProductActivity::getId);
        Page<SohuProductActivityVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询营销活动列表
     */
    @Override
    public List<SohuProductActivityVo> queryList(SohuProductActivityBo bo) {
        LambdaQueryWrapper<SohuProductActivity> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuProductActivity> buildQueryWrapper(SohuProductActivityBo bo) {
//        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuProductActivity> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), SohuProductActivity::getName, bo.getName());
        lqw.eq(bo.getIsOpen() != null, SohuProductActivity::getIsOpen, bo.getIsOpen());
        lqw.eq(bo.getType() != null, SohuProductActivity::getType, bo.getType());
        lqw.eq(bo.getSort() != null, SohuProductActivity::getSort, bo.getSort());
        lqw.eq(StringUtils.isNotBlank(bo.getIsDel()), SohuProductActivity::getIsDel, bo.getIsDel());
        lqw.eq(StringUtils.isNotBlank(bo.getBanner()), SohuProductActivity::getBanner, bo.getBanner());
        lqw.eq(StringUtils.isNotBlank(bo.getInstruction()), SohuProductActivity::getInstruction, bo.getInstruction());
        return lqw;
    }

    /**
     * 新增营销活动
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SohuProductActivityBo bo) {
        SohuProductActivity add = BeanUtil.toBean(bo, SohuProductActivity.class);
        add.setAcNo("A"+ RandomUtil.randomNumbers(8));
        boolean flag = baseMapper.insert(add) > 0;
        if (!flag){
            throw new RuntimeException("创建活动失败");
        }
        //封装活动商品
        List<SohuActivityProduct> productList  = getActivityProductList(bo.getProList(),add.getId());
        //批量添加
        if (CollectionUtil.isNotEmpty(productList)) {
            iSohuActivityProductService.saveBatch(productList);
        }
        return Boolean.TRUE;
    }

    /**
     * 修改营销活动
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(SohuProductActivityBo bo) {
        SohuProductActivity update = BeanUtil.toBean(bo, SohuProductActivity.class);
//        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update)>0;
        if(!flag){
            throw new RuntimeException("创建活动失败");
        }
        if (CollectionUtil.isNotEmpty(bo.getProList())){
            //删除历史的活动关联的产品
            iSohuActivityProductService.deleteById(bo.getId());
            //封装活动商品
            List<SohuActivityProduct> productList  = getActivityProductList(bo.getProList(),bo.getId());
            //批量添加
            if (CollectionUtil.isNotEmpty(productList)) {
                iSohuActivityProductService.saveBatch(productList);
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuProductActivity entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除营销活动
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean delete(Long id) {
        SohuProductActivity sohuProductActivity = baseMapper.selectById(id);
        sohuProductActivity.setIsDel(1);
        sohuProductActivity.setUpdateTime(new Date());
        boolean flag = baseMapper.updateById(sohuProductActivity) > 0;
        if(!flag){
            throw new RuntimeException("删除活动失败");
        }
        //删除活动商品关联信息
        iSohuActivityProductService.deleteById(id);
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateSwitch(Long id) {
        SohuProductActivity sohuProductActivity = baseMapper.selectById(id);
        if(sohuProductActivity != null){
            sohuProductActivity.setIsOpen(sohuProductActivity.getIsOpen().intValue() == OPEN?CLOSE:OPEN);
        }
        return baseMapper.updateById(sohuProductActivity) > 0;
    }

    public List<SohuActivityProduct> getActivityProductList(List<SohuActivityProductBo> proList,Long aid){
        List<SohuActivityProduct> productList  = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(proList)){
            proList.forEach(item->{
                SohuActivityProduct sohuActivityProduct = new SohuActivityProduct();
                sohuActivityProduct.setAid(aid);
                sohuActivityProduct.setProductId(item.getProductId());
                sohuActivityProduct.setProImage(item.getProImage());
                sohuActivityProduct.setSort(item.getSort());
                productList.add(sohuActivityProduct);
            });
        }
        return productList;
    }
}
