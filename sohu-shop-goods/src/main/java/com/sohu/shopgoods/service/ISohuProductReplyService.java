package com.sohu.shopgoods.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.model.SohuProductReplyInfoModel;
import com.sohu.shopgoods.api.bo.SohuProductReplyBo;
import com.sohu.shopgoods.api.vo.SohuProductReplyVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品评论Service接口
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
public interface ISohuProductReplyService {

    /**
     * 查询商品评论
     */
    SohuProductReplyVo queryById(Long id);

    /**
     * 查询商品评论列表
     */
    TableDataInfo<SohuProductReplyVo> queryPageList(SohuProductReplyBo bo, PageQuery pageQuery);

    /**
     * 查询商品评论列表
     */
    List<SohuProductReplyVo> queryList(SohuProductReplyBo bo);

    /**
     * 修改商品评论
     */
    Boolean insertByBo(SohuProductReplyBo bo);

    /**
     * 修改商品评论
     */
    Boolean updateByBo(SohuProductReplyBo bo);

    /**
     * 校验并批量删除商品评论信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 商品详情评论信息
     *
     * @param id
     * @return SohuProductReplyInfoModel
     */
    SohuProductReplyInfoModel getProductReply(Long id);

    /**
     * 获取商品评论
     *
     * @param id
     * @return map
     */
    Map<String, Object> getCount(Long id);

}
