package com.sohu.shopgoods.service;

import com.sohu.shopgoods.api.bo.SohuProductGuaranteeUpdateStatusBo;
import com.sohu.shopgoods.domain.SohuProductGuarantee;
import com.sohu.shopgoods.api.vo.SohuProductGuaranteeVo;
import com.sohu.shopgoods.api.bo.SohuProductGuaranteeBo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 商品保障服务Service接口
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
public interface ISohuProductGuaranteeService {

    /**
     * 查询商品保障服务
     */
    SohuProductGuaranteeVo queryById(Long id);

    /**
     * 查询商品保障服务列表
     */
    TableDataInfo<SohuProductGuaranteeVo> queryPageList(SohuProductGuaranteeBo bo, PageQuery pageQuery);

    /**
     * 查询商品保障服务列表-pc
     */
    List<SohuProductGuaranteeVo> queryPcList(String sysSource);

    /**
     * 查询商品保障服务列表-store
     */
    List<SohuProductGuaranteeVo> queryStoreList(String sysSource);

    /**
     * 保障服务列表
     *
     * @param idList
     * @return List
     */
    List<SohuProductGuarantee> findByIdList(List<Long> idList);

    /**
     * 修改商品保障服务
     */
    Boolean insertByBo(SohuProductGuaranteeBo bo);

    /**
     * 修改商品保障服务
     */
    Boolean updateByBo(SohuProductGuaranteeBo bo);

    /**
     * 校验并批量删除商品保障服务信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据名称获取保证服务
     *
     * @param namestr 名称，英文逗号分隔
     * @return 英文逗号分隔
     */
    String getIdsByNames(String namestr);

    /**
     * 根据id集合获取保证服务集合
     * @param ids id集合，英文逗号分隔
     * @return 英文逗号分隔
     */
    String getNamesByIds(String ids);

    /**
     * 根据id集合获取保证服务集合(追加默认服务)
     * @param ids
     * @return
     */
    String getIdsByIdsOfAppendDefault(String ids);

    /**
     * 更新默认状态
     * @return
     */
    Boolean updateStatus(SohuProductGuaranteeUpdateStatusBo bo);

}
