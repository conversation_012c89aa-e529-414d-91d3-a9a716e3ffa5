package com.sohu.shopgoods.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品保障服务对象 sohu_product_guarantee
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_product_guarantee")
public class SohuProductGuarantee extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 保障条款名称
     */
    private String name;
    /**
     * 图标
     */
    private String icon;
    /**
     * 条款内容
     */
    private String content;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 显示状态
     */
    private Integer isShow;
    /**
     * 是否删除
     */
    private Boolean isDel;

    /**
     * 是否默认条款
     */
    private Boolean isDefault;

    /**
     * 补充说明
     */
    private String description;
    /**
     * 系统来源(sohuglobal:狐少少,minglereels:海外短剧)
     */
    private String SysSource;

}
