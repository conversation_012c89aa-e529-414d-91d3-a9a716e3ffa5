package com.sohu.shopgoods.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.math.BigDecimal;
import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 分类保证金关联对象 sohu_category_bond_relation
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_category_bond_relation")
public class SohuCategoryBondRelation extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 类目id
     */
    private Long categoryId;
    /**
     * gmv金额
     */
    private BigDecimal gmvAmount;
    /**
     * 保证金金额
     */
    private BigDecimal bondAmount;
    /**
     * 是否删除  0.未删除 
     */
    private Long isDel;

}
