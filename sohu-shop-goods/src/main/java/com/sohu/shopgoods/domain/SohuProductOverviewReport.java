package com.sohu.shopgoods.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/2 17:35
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_product_overview_report")
public class SohuProductOverviewReport extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;
    /**
     * 统计时间(按天划分)
     */
    private String date;
    /**
     * 新增商品数量
     */
    private Integer productNum;
    /**
     * 商品上架数量
     */
    private Integer productListingNum;
    /**
     * 商品下架数量
     */
    private Integer productDelistingNum;
    /**
     * 商品库存小于5的数量
     */
    private Integer productStockNum;

}
