<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.shopgoods.mapper.SohuFreightTemplateMapper">

    <resultMap type="com.sohu.shopgoods.domain.SohuFreightTemplate" id="SohuFreightTemplateResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="merId" column="mer_id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="freeArea" column="free_area"/>
        <result property="notFreeArea" column="not_free_area"/>
        <result property="notDeliveryArea" column="not_delivery_area"/>
        <result property="isDel" column="is_del"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>


</mapper>