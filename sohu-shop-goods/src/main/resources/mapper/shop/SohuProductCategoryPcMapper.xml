<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.shopgoods.mapper.SohuProductCategoryPcMapper">

    <resultMap type="com.sohu.shopgoods.domain.SohuProductCategoryPc" id="SohuProductCategoryPcResult">
    <result property="id" column="id"/>
    <result property="pid" column="pid"/>
    <result property="name" column="name"/>
    <result property="icon" column="icon"/>
    <result property="level" column="level"/>
    <result property="sort" column="sort"/>
    <result property="isShow" column="is_show"/>
    <result property="isDel" column="is_del"/>
    <result property="createTime" column="create_time"/>
    <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectCategoriesWithSettings" resultType="com.sohu.shopgoods.api.vo.SohuProductCategoryPcVo">
        SELECT
        pc.*
        FROM
        sohu_product_category_pc pc
        <if test="shopType != null and shopType != ''">
            JOIN sohu_category_brand_business_settings sbs
            ON pc.id = sbs.business_id AND sbs.business_type = 'Category'
        </if>
        WHERE
        pc.is_del = 0
        AND pc.is_show = 1
        <if test="shopType != null and shopType != ''">
            AND sbs.is_del = 0
            <choose>
                <when test="shopType == 'personal'">
                    AND sbs.is_open_personal = 1
                </when>
                <when test="shopType == 'business'">
                    AND sbs.is_open_business = 1
                </when>
                <otherwise>
                    AND 1 = 1
                </otherwise>
            </choose>
        </if>
        ORDER BY
        pc.sort DESC, pc.id ASC
    </select>


</mapper>
