<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.shopgoods.mapper.SohuProductCountMapper">

    <resultMap type="com.sohu.shopgoods.domain.SohuProductCount" id="SohuProductCountResult">
    <result property="id" column="id"/>
    <result property="productId" column="product_id"/>
    <result property="sales" column="sales"/>
    <result property="stock" column="stock"/>
    <result property="varSales" column="var_sales"/>
    <result property="browse" column="browse"/>
    </resultMap>


</mapper>
