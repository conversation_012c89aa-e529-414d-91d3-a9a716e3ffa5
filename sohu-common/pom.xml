<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.sohu</groupId>
        <artifactId>sohu-dependency</artifactId>
        <version>1.0.0</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sohu</groupId>
    <artifactId>sohu-common</artifactId>
    <version>1.0.0</version>

    <modules>
        <module>sohu-common-bom</module>
        <module>sohu-common-alibaba-bom</module>
        <module>sohu-common-log</module>
        <module>sohu-common-dict</module>
        <module>sohu-common-excel</module>
        <module>sohu-common-core</module>
        <module>sohu-common-redis</module>
        <module>sohu-common-doc</module>
        <module>sohu-common-security</module>
        <module>sohu-common-satoken</module>
        <module>sohu-common-web</module>
        <module>sohu-common-mybatis</module>
        <module>sohu-common-job</module>
        <module>sohu-common-dubbo</module>
        <module>sohu-common-seata</module>
        <module>sohu-common-loadbalancer</module>
        <module>sohu-common-oss</module>
        <module>sohu-common-idempotent</module>
        <module>sohu-common-mail</module>
        <module>sohu-common-sms</module>
        <module>sohu-common-logstash</module>
        <module>sohu-common-elasticsearch</module>
        <module>sohu-common-sentinel</module>
        <module>sohu-common-skylog</module>
        <module>sohu-common-prometheus</module>
        <module>sohu-common-translation</module>
        <module>sohu-common-encrypt</module>
        <module>sohu-common-page</module>
    </modules>

    <packaging>pom</packaging>

    <description>
        sohu-common通用模块
    </description>

</project>