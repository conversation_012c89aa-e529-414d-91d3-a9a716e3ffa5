package com.sohu.common.excel.core;

import com.sohu.common.core.vo.ExcelErrVo;

import java.util.List;

/**
 * excel返回对象
 *
 * <AUTHOR> Li
 */
public interface ExcelResult<T> {

    /**
     * 对象列表
     */
    List<T> getList();

    /**
     * 错误列表
     */
    List<String> getErrorList();

    /**
     * 导入回执
     */
    String getAnalysis();

    /**
     * 错误对象回执
     * @return
     */
    List<ExcelErrVo> getErrVoList();
}
