package com.sohu.common.core.enums;

/**
 * 认证资质类型
 * 认证资质类型（idcard=身份证 =passport护照 pass=港澳通行证 business=企业认证)
 */
public enum UserAuthEnum {


    personal("personal", "个人认证"),
    business("business", "企业认证"),
    idcard("idcard", "身份证"),
    passport("passport", "护照"),
    pass("pass", "港澳通行证"),
    license("license", "营业执照");

    private String type;
    private String desc;

    UserAuthEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
