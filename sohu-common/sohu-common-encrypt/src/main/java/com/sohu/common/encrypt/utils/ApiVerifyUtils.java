package com.sohu.common.encrypt.utils;

import cn.hutool.core.comparator.VersionComparator;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.sohu.common.core.utils.SecretUtil;

import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

public class ApiVerifyUtils {

    /**
     * AES 加密：将对象转为 JSON 字符串并加密成十六进制字符串
     */
    public static String encrypt(Object obj, String aesKey) {
        return SecretUtil.encrypt(obj.toString(), aesKey);
    }

    /**
     * AES 解密：将十六进制字符串解密为 Map
     */
    public static Map<String, Object> decrypt(String hex, String aesKey) {
        String json = SecretUtil.desEncrypt(hex, aesKey);
        return JSONUtil.toBean(json, Map.class);
    }

    /**
     * HMAC-SHA256 签名，传入参数 map 和密钥 , a-z进行排序的
     * 排除 sign 字段，然后按 key 字典序拼接 key=value&...
     */
    public static String sign(Map<String, Object> params, String secret) {
        SortedMap<String, Object> sorted = new TreeMap<>(params);
        sorted.remove("sign");

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : sorted.entrySet()) {
            sb.append(entry.getKey()).append('=').append(entry.getValue()).append('&');
        }
        sb.append("key=").append(secret);

        return SecureUtil.hmacSha256(secret).digestHex(sb.toString());
    }

    public static void main(String[] args) {
        String secret = "abcdefabcdefabcdefabcdefabcdefabcdefabcdefabcdefabcdefabcdef";
        // 构造请求参数 Map
        Map<String, Object> requestData = new TreeMap<>();
        requestData.put("token", "");  // 非登录接口可为空
        requestData.put("userId", "0");        // 非登录接口可为空
        requestData.put("timestamp", "1745292415323"); // 当前毫秒时间戳
        requestData.put("url", "app/api/focus/category/list");    // 当前请求路径
        requestData.put("platform", "ios");          // 设备系统类型：Android，ios
        requestData.put("nonce", "DC3CC742-86CA-4F93-81F3-94EC3B6948B3");     // 随机字符串
        requestData.put("sign", sign(requestData, secret));     // 随机字符串

        // 加密操作
        String aesKey = "0123456789abcdef";
        String encryptedData = SecretUtil.encrypt(JSONUtil.toJsonStr(requestData), aesKey);
        System.out.println("Encrypted Data: " + encryptedData);

        // 解密操作
        String decryptedData = SecretUtil.desEncrypt(encryptedData, aesKey);
        System.out.println("Decrypted Data: " + decryptedData);

        // HMAC-SHA256 签名操作

        String sign = sign(requestData, secret);
        System.out.println("Generated HMAC Signature: " + sign);

        int result = VersionComparator.INSTANCE.compare("1.5.3", "1.5.1");
        System.out.println(result);
    }
}
