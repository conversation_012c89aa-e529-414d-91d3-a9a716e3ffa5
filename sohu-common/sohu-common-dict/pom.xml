<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sohu</groupId>
        <artifactId>sohu-common</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sohu-common-dict</artifactId>

    <description>
        sohu-common-dict 字典
    </description>

    <dependencies>

        <!-- RuoYi Common Security -->
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-core</artifactId>
        </dependency>

    </dependencies>
</project>
