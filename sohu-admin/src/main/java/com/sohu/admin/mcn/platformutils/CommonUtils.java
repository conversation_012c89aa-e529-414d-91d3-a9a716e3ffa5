package com.sohu.admin.mcn.platformutils;

import com.alibaba.csp.sentinel.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;
import java.util.Objects;
@Slf4j
public class CommonUtils {
    /*------------------------------------图片--------------------------------------*/
    private static final int byteData = 1024;
    private static final int connectTimeout = 5 * 1000;
    /**
     * 处理图片流转换
     * @param filePath 本地图片路径
     * @return
     */
    public static byte[] readFileImage(String filePath){
        if(StringUtil.isEmpty(filePath)) {
            return null;
        }
        File f = new File(filePath);	//这里gif动态图不可以，虽然在后面也能输出gif格式，但是却不是动图
        BufferedImage bi;
        try {
            bi = ImageIO.read(f);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bi, "png", baos);
            byte[] bytes = baos.toByteArray();
            return bytes;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 处理图片流转换
     * @param f File类型图片
     * @return
     */
    public static byte[] readFileImage(File f){
       	//这里gif动态图不可以，虽然在后面也能输出gif格式，但是却不是动图
        BufferedImage bi;
        try {
            bi = ImageIO.read(f);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bi, "png", baos);
            byte[] bytes = baos.toByteArray();
            return bytes;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将Url转换为File（本地,网络路径都支持）
     * @param url 图片路径
     * @return
     */
    private static final Integer len = 8192;
    public static File urlToFile(String url){
        try {
            File file = null;
            if (url.contains("http")){
                HttpURLConnection httpUrl = (HttpURLConnection) new URL(url).openConnection();
                httpUrl.connect();
                InputStream ins=httpUrl.getInputStream();
                file = new File(System.getProperty("java.io.tmpdir") + File.separator + "xie");//System.getProperty("java.io.tmpdir")缓存
                if (file.exists()) {
                    file.delete();//如果缓存中存在该文件就删除
                }
                OutputStream os = new FileOutputStream(file);
                int bytesRead;
                byte[] buffer = new byte[len];
                while ((bytesRead = ins.read(buffer, 0, len)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.close();
                ins.close();
            }else {
                file = new File(url);
            }
            return file;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 将File对象转换为byte[]的形式
     * @param file 转换成File类型的图片
     * @return
     */
    public static byte[] fileToByte(File file){
        FileInputStream fileInputStream = null;
        byte[] imgData = null;
        try {
            imgData = new byte[(int) file.length()];
            //read file into bytes[]
            fileInputStream = new FileInputStream(file);
            fileInputStream.read(imgData);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return imgData;
    }

    /**
     * 将网络路径图片转为base64的格式
     * @param imgUrl 请求网络路径
     */
    public static String getUrlImageToBase64(String imgUrl){
        ByteArrayOutputStream data = new ByteArrayOutputStream();
        try {
            // 创建URL
            URL url = new URL(imgUrl);
            byte[] by = new byte[byteData];
            // 创建链接
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(connectTimeout);
            conn.setRequestMethod("GET");
            InputStream is = conn.getInputStream();

            // 将内容读取内存中
            int len = -1;
            while ((len = is.read(by)) != -1) {
                data.write(by, 0, len);
            }
            // 关闭流
            is.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 对字节数组Base64编码
        Base64.Encoder encoder = Base64.getEncoder();
        String base64Str =  "data:image/png;base64," + encoder.encodeToString(data.toByteArray()).replaceAll("\r\n","").trim();
        //String base64Str = encoder.encodeToString(data.toByteArray()).replaceAll("\r\n","").trim();
        log.info(base64Str);
        return base64Str;
    }

    /**
     * 将url（本地,网络图片都支持）图片转换成byte数组
     * @param fileUrl 图片路径
     * @return
     */
    public static byte[] imgToByte(String fileUrl){
        if (fileUrl == null || fileUrl.equals("")){
            return null;
        }
        if (fileUrl.contains("http")){
            return readFileImage(Objects.requireNonNull(urlToFile(fileUrl)));
        }else {
            return readFileImage(fileUrl);
        }
    }

    /*------------------------------------视频--------------------------------------*/

    /**
     * 读取本地视频转换成二进制
     * @param videoPath
     * @return binaryData
     */
    public static byte[] localVideoToBinary(String videoPath){
        try {
            File file = new File(videoPath);
            InputStream inputStream = new FileInputStream(file);
            byte[] buffer = new byte[4096]; // 设置缓冲区大小
            int bytesRead;
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.close();
            inputStream.close();
            byte[] binaryData = outputStream.toByteArray();
            log.info("Binary data length: {}" , binaryData.length);
            return binaryData;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 读取网络视频转换成二进制
     * @param videoPath
     * @return binaryData
     */
    public static byte[] networkVideoToBinary(String videoPath){
        try {
            URL videoUrl = new URL(videoPath);
            InputStream inputStream = videoUrl.openConnection().getInputStream();
            byte[] buffer = new byte[8 * 1024]; // 设置合适的缓冲区大小
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            byte[] binaryData = outputStream.toByteArray();
            log.info("Binary data length: {}" , binaryData.length);
            return binaryData;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 将url（本地,网络图片都支持）视频转换成byte数组
     * @param fileUrl 视频路径
     * @return
     */
    public static byte[] videoToByte(String fileUrl){
        if (fileUrl == null || fileUrl.equals("")){
            return null;
        }
        if (fileUrl.contains("http")){
            return networkVideoToBinary(fileUrl);
        }else {
            return localVideoToBinary(fileUrl);
        }
    }

}
