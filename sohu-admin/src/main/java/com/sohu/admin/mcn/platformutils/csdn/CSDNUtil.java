package com.sohu.admin.mcn.platformutils.csdn;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CSDNUtil {

    public static void main(String[] args) {
        String content = "<p>JAVA标题JAVA标题JAVA标题JAVA标题JAVA标题JAVA标题JAVA标题</p>";
        String cookie = "uuid_tt_dd=10_18737630700-1704343162650-209561; loginbox_strategy=%7B%22taskId%22%3A308%2C%22abCheckTime%22%3A1704505872990%2C%22version%22%3A%22exp1%22%2C%22blog-threeH-dialogtipShowTimes%22%3A8%2C%22blog-threeH-dialog%22%3A1704524624873%7D; UserName=qq_45631928; UserInfo=6e64dd285cd342da972f22f1f78d09b1; UserToken=6e64dd285cd342da972f22f1f78d09b1; UserNick=%E4%BE%9D%E8%B5%96%E6%B3%A8%E5%85%A5; AU=78E; UN=qq_45631928; BT=1704532730713; p_uid=U010000; Hm_up_6bcd52f51e9b3dce32bec4a3997715ac=%7B%22islogin%22%3A%7B%22value%22%3A%221%22%2C%22scope%22%3A1%7D%2C%22isonline%22%3A%7B%22value%22%3A%221%22%2C%22scope%22%3A1%7D%2C%22isvip%22%3A%7B%22value%22%3A%220%22%2C%22scope%22%3A1%7D%2C%22uid_%22%3A%7B%22value%22%3A%22qq_45631928%22%2C%22scope%22%3A1%7D%7D; c_dl_fref=https://www.baidu.com/link; c_dl_um=-; c_dl_prid=1704686503525_523686; c_dl_rid=1704793127571_983917; c_dl_fpage=/download/zlv056903/9334675; __gads=ID=5ffcdc6c04a4012e:T=1704343164:RT=1704954774:S=ALNI_MYAK6K7xrqwpmS59VUvUs_gw-S1OQ; __gpi=UID=00000cd117ba6f7c:T=1704343164:RT=1704954774:S=ALNI_MYQJKg1nHrgMQMaahJ8uLeonKKB4g; dc_session_id=10_1705911056053.849514; c_first_ref=www.baidu.com; c_first_page=https%3A//www.csdn.net/; c_dsid=11_1705911057262.891569; c_segment=9; c_page_id=default; dc_sid=5ad3a1ccd6f3ad57774ce6802cb205a9; Hm_lvt_6bcd52f51e9b3dce32bec4a3997715ac=1704420461,1704505873,1704677027,1705911058; creativeSetApiNew=%7B%22toolbarImg%22%3A%22https%3A//img-home.csdnimg.cn/images/20231011044944.png%22%2C%22publishSuccessImg%22%3A%22https%3A//img-home.csdnimg.cn/images/20231011045003.png%22%2C%22articleNum%22%3A0%2C%22type%22%3A0%2C%22oldUser%22%3Afalse%2C%22useSeven%22%3Atrue%2C%22oldFullVersion%22%3Afalse%2C%22userName%22%3A%22qq_45631928%22%7D; c_pref=https%3A//www.baidu.com/link; c_ref=https%3A//www.csdn.net/; Hm_lpvt_6bcd52f51e9b3dce32bec4a3997715ac=1705911062; log_Id_pv=101; log_Id_view=3253; log_Id_click=123; dc_tos=s7nlh0";
        sendCSDNMsg(cookie,content,null);
    }

    /**
     * 发布图文
     * @param cookie
     * @param content 文本内容
     * @param images 图片列表
     * @return
     */
    public static String sendCSDNMsg(String cookie, String content,String images){
        JSONObject body = new JSONObject();
        body.put("content",content);
        body.put("authorized_status",false);
        body.put("article_id","135751028");
        body.put("check_original",false);
        //body.put("cover_type",1);
        //body.put("is_new",1);
        //body.put("not_auto_saved",1);
        //body.put("scheduled_time",0);
        body.put("source","pc_postedit");
        //body.put("status",2);
        body.put("tags","java");
        body.put("title","JAVA标题");
        body.put("type","original");
        //body.put("vote_id",0);
        String result = HttpRequest.post("https://bizapi.csdn.net/blog-console-api/v1/postedit/saveArticle")
                .header("Content-Type", "application/json")
                .header("Cookie", cookie)
                .header("X-Ca-Key","203803574")
                .header("X-Ca-Signature","ENmCjW36bdpJUyRsh+itxGrJEj/+UFhSEJGchqmaMA4=")
                .header("X-Ca-Signature-Headers","x-ca-key,x-ca-nonce")
                .body(body.toJSONString()).execute().body();
        JSONObject json = JSONObject.parseObject(result);
        log.info("发布图文："+result);
        return json.toJSONString();
    }
}
