package com.sohu.admin.mcn.zhihu;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.sohu.admin.mcn.AbstractMcnSyncService;
import com.sohu.admin.mcn.McnPublishData;
import com.sohu.admin.mcn.McnPublishResult;
import com.sohu.admin.mcn.McnUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import javax.swing.text.html.HTML;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.util.*;

/**
 * 知乎
 */
@Slf4j
@Service
public class ZhihuMcnSyncServiceImpl extends AbstractMcnSyncService {

    // 最大重试次数
    private static final int MAX_RETRIES = 10;
    // 重试间隔，单位毫秒
    private static final long RETRY_INTERVAL_MS = 300;

    /**
     * 来源
     */
    private final static String CONSTANT_HEADERS_ORIGIN = "https://zhuanlan.zhihu.com";

    /**
     * 引荐页-文章
     */
    private final static String CONSTANT_ARTICLE_HEADERS_REFERER = "https://zhuanlan.zhihu.com/write";

    /**
     * 创建草稿请求地址-文章
     */
    private final static String CONSTANT_ARTICLE_DRAFT_URL = "https://zhuanlan.zhihu.com/api/articles/drafts";

    /**
     * 修改草稿请求地址-文章
     */
    private final static String CONSTANT_ARTICLE_Edit_URL = "https://zhuanlan.zhihu.com/api/articles/";

    /**
     * 发布请求地址-文章
     */
    private final static String CONSTANT_ARTICLE_PUBLISH_URL = "";

    /**
     * 上传图片请求地址
     */
    private final static String CONSTANT_UPLOAD_IMAGE_HASH_URL = "https://api.zhihu.com/images";

    /**
     * 上传图片请求地址
     */
    private final static String CONSTANT_UPLOAD_IMAGE_URL = "https://zhihu-pics-upload.zhimg.com/";

    /**
     * 上传封面图片请求地址
     */
    private final static String CONSTANT_UPLOAD_COVER_IMAGE_URL = "";

    /**
     * 文章话题搜索
     */
    private final static String CONSTANT_AUTOCOMPLETE_TOPICS_URL = "https://zhuanlan.zhihu.com/api/autocomplete/topics?max_matches=5&use_similar=0&topic_filter=1&token=";

    @Override
    protected McnPublishResult publishArticle(McnPublishData data) {
        McnPublishResult result=new McnPublishResult();
        ZhihuPublishArticleFormData formData = this.buildPublishArticleFormData(data);
        ZhihuTopicsFormData topicData = (ZhihuTopicsFormData) data.getOtherParam().get("topicData");
        Map<String, Object> formMap = null;
        if (StringUtils.isEmpty(data.getPublishId())) {
            formMap = BeanUtil.beanToMap(formData, true, true);
            ZhihuMcnPublishResult publishResult=this.createArticle(data.getCookie(), JSONObject.toJSONString(formMap));
            data.setPublishId(publishResult.getId().toString());
            result.setPublishId(publishResult.getId().toString());
        }
        //上传内容图片
        List<ZhihuUploadImageStatusResult> contentImageData = this.uploadPic(data.getCookie(), data.getContentImagePathList());
        //上传封面
        ZhihuUploadImageStatusResult coverImageData = this.uploadImage(data.getCookie(), data.getCoverImagePath());
        //内容
        Document document = McnUtils.parseDocument(data.getContent());
        Elements imageElements = McnUtils.getImageElements(document);
        if (CollectionUtil.isNotEmpty(imageElements)) {
            //替换图片元素,未完，待续
            //this.replaceImageElement(imageElements, contentImageData);
        }
        //设置替换后的内容
        formData.setContent(document.body().html());
        //表单内容
        formMap = BeanUtil.beanToMap(formData, true, true);
        this.saveArticle(data.getCookie(), data.getPublishId(), JSONObject.toJSONString(formMap));

        //保存封面
        Map<String, Object> coverMap = new HashMap<>();
        coverMap.put("delta_time", 0);
        coverMap.put("isTitleImageFullScreen", false);
        coverMap.put("titleImage", coverImageData.getWatermark_src() + "." + FileNameUtil.getSuffix(data.getCoverImagePath()));
        this.saveArticle(data.getCookie(), data.getPublishId(), JSONObject.toJSONString(coverMap));
        //保存文章话题
        this.saveTopics(data.getCookie(), data.getPublishId(), topicData);
        //发布图文
        this.publishArticle(data.getCookie(), data.getPublishId());
        result.setSuccess(true);
        return result;
    }

    @Override
    protected McnPublishResult publishVideo(McnPublishData data) {
        return null;
    }

    /**
     * 替换图片元素
     *
     * @param imageElements
     * @param imageDataList
     */
    private void replaceImageElement(@NotNull Elements imageElements, @NotNull List<ZhihuUploadImageStatusResult> imageDataList) {
        if (!Objects.equals(imageElements.size(), imageDataList.size())) {
            throw new RuntimeException("元素不匹配");
        }
        for (int i = 0; i < imageElements.size(); i++) {
            replaceImageElement(imageElements.get(i), imageDataList.get(i));
        }
    }

    /**
     * 发布之前上传图片
     *
     * @param cookie
     * @param imagePathList 图片地址，本地文件
     * @return
     */
    private List<ZhihuUploadImageStatusResult> uploadPic(String cookie, List<String> imagePathList) {
        List<ZhihuUploadImageStatusResult> resultList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(imagePathList)) {
            for (String imagePath : imagePathList) {
                resultList.add(this.uploadImage(cookie, imagePath));
            }
        }
        return resultList;
    }

    /**
     * 构建发布信息-文章
     *
     * @param data
     * @return
     */
    private ZhihuPublishArticleFormData buildPublishArticleFormData(McnPublishData data) {
        ZhihuPublishArticleFormData formData = new ZhihuPublishArticleFormData();
        formData.setTitle(data.getTitle());
        return formData;
    }

    /**
     * 发布
     *
     * @param cookie
     * @param id 表单id
     * @return
     */
    private McnPublishResult publishArticle(String cookie, String id) {
        Map<String,Object> formMap=new HashMap<>();
        formMap.put("commentPermission","anyone");
        formMap.put("disclaimer_status","close");
        formMap.put("disclaimer_type","none");
        formMap.put("table_of_contents_enabled",false);

        String url=CONSTANT_ARTICLE_Edit_URL+id+"/publish";
        McnPublishResult result = new McnPublishResult();
        HttpRequest httpRequest = HttpRequest.put(url)
                .cookie(cookie)
                .header(Header.ORIGIN.getValue(), CONSTANT_HEADERS_ORIGIN)
                .header(Header.REFERER.getValue(), CONSTANT_ARTICLE_HEADERS_REFERER)
                .body(JSONObject.toJSONString(formMap));
        log.info("发布图文httpRequest:{}", httpRequest);
        log.info("发布图文formMap:{}", JSONUtil.toJsonStr(formMap));
        HttpResponse httpResponse = httpRequest.execute();
        String responseBody = httpResponse.body();
        log.info("发布图文返回数据:{}", responseBody);
        if (!httpResponse.isOk()) {
            result.setMessage("请求出错，程序员小哥哥正在努力解决中^_^!");
            log.info("知乎发布请求失败，状态码：{}，响应内容：{}", httpResponse.getStatus(), responseBody);
        }
        return result;
    }

    /**
     * 创建图文草稿
     *
     * @param cookie   知乎cookie
     * @param jsonBody 表单数据
     * @return
     */
    private ZhihuMcnPublishResult createArticle(String cookie, String jsonBody) {
        //表单内容
        HttpRequest httpRequest = HttpRequest.post(CONSTANT_ARTICLE_DRAFT_URL)
                .cookie(cookie)
                .header(Header.ORIGIN.getValue(), CONSTANT_HEADERS_ORIGIN)
                .header(Header.REFERER.getValue(), CONSTANT_ARTICLE_HEADERS_REFERER)
                .body(jsonBody);
        log.info("创建图文草稿httpRequest:{}", httpRequest);
        log.info("创建图文草稿jsonBody:", jsonBody);
        HttpResponse httpResponse = httpRequest.execute();
        String responseBody = httpResponse.body();
        log.info("创建图文草稿返回数据:{}", responseBody);
        if (!httpResponse.isOk()) {
            log.info("创建图文草稿请求失败，状态码：{}，响应内容：{}", httpResponse.getStatus(), responseBody);
            throw new RuntimeException("请求出错，程序员小哥哥正在努力解决中^_^!");
        }
        ZhihuMcnPublishResult mcnResult = JSONUtil.toBean(responseBody, ZhihuMcnPublishResult.class);
        if (Objects.isNull(mcnResult)) {
            throw new RuntimeException("创建图文草稿出错");
        }
        return mcnResult;
    }

    /**
     * 修改图文草稿
     *
     * @param cookie   知乎cookie
     * @param jsonBody 表单数据
     * @return
     */
    private void saveArticle(String cookie, String id, String jsonBody) {
        //表单内容
        String url = CONSTANT_ARTICLE_Edit_URL + id + "/draft";
        HttpRequest httpRequest = HttpRequest.patch(url)
                .cookie(cookie)
                .header(Header.ORIGIN.getValue(), CONSTANT_HEADERS_ORIGIN)
                .header(Header.REFERER.getValue(), CONSTANT_ARTICLE_HEADERS_REFERER)
                .body(jsonBody);
        log.info("修改图文草稿httpRequest:{}", httpRequest);
        log.info("修改图文草稿jsonBody:", jsonBody);
        HttpResponse httpResponse = httpRequest.execute();
        String responseBody = httpResponse.body();
        log.info("修改图文草稿返回数据:{}", responseBody);
        if (!httpResponse.isOk()) {
            log.info("修改图文草稿请求失败，状态码：{}，响应内容：{}", httpResponse.getStatus(), responseBody);
            throw new RuntimeException("请求出错，程序员小哥哥正在努力解决中^_^!");
        }
    }

    /**
     * 上传图片，目前只支持曾经在知乎上已上传过的图片
     *
     * @param cookie   知乎cookie
     * @param filePath 本地路径
     * @return
     */
    private ZhihuUploadImageStatusResult uploadImage(String cookie, String filePath) {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new RuntimeException("图片文件未找到：" + filePath);
        }
        //上传hash值
        ZhihuUploadImageResult uploadImageResult = this.uploadImageHash(cookie, new ZhihuUploadImageFormData(DigestUtil.md5Hex(file)));
        String imageId = uploadImageResult.getUpload_file().getImage_id();
        //知乎平台不存在此文件
        if (!uploadImageResult.getUpload_file().isExistFile()) {
            String token = uploadImageResult.getUpload_token().getAccess_token();
            String accessId = uploadImageResult.getUpload_token().getAccess_id();
            String accessKey = uploadImageResult.getUpload_token().getAccess_key();
            String objectKey = uploadImageResult.getUpload_file().getObject_key();
            //上传文件
            this.uploadImage(token, accessId, accessKey, objectKey, file);
            //更新上传状态
            this.uploadingStatus(cookie, imageId);
        }
        ZhihuUploadImageStatusResult uploadImageStatus = this.checkUploadStatusWithRetries(cookie, imageId);
        if (Objects.isNull(uploadImageStatus)) {
            throw new RuntimeException("获取图片状态失败！");
        }
        return uploadImageStatus;
    }

    /**
     * 获取图片状态
     *
     * @param cookie
     * @param imageId
     * @return
     */
    private ZhihuUploadImageStatusResult checkUploadStatusWithRetries(String cookie, String imageId) {
        ZhihuUploadImageStatusResult uploadImageStatus = null;
        int retries = 0;
        while (retries < MAX_RETRIES) {
            try {
                uploadImageStatus = this.getUploadImageStatus(cookie, imageId);
                if (uploadImageStatus.isSucceed()) {
                    break;
                }
                Thread.sleep(RETRY_INTERVAL_MS);
            } catch (InterruptedException e) {
                // 重新设置中断状态
                Thread.currentThread().interrupt();
                e.printStackTrace();
                break;
            } catch (Exception e) {
                e.printStackTrace();
                break;
            }
            retries++;
        }
        return uploadImageStatus;
    }

    /**
     * 获取图片状态
     *
     * @param cookie  知乎cookie
     * @param imageId 知乎图片id
     * @return
     */
    private ZhihuUploadImageStatusResult getUploadImageStatus(String cookie, String imageId) {
        //表单内容
        String url = CONSTANT_UPLOAD_IMAGE_HASH_URL + "/" + imageId;
        HttpRequest httpRequest = HttpRequest.get(url)
                .cookie(cookie)
                .header(Header.ORIGIN.getValue(), CONSTANT_HEADERS_ORIGIN)
                .header(Header.REFERER.getValue(), CONSTANT_ARTICLE_HEADERS_REFERER);
        log.info("获取图片状态httpRequest:{}", httpRequest);
        HttpResponse httpResponse = httpRequest.execute();
        String responseBody = httpResponse.body();
        log.info("获取图片状态返回数据:{}", responseBody);
        if (!httpResponse.isOk()) {
            log.info("获取图片状态请求失败，状态码：{}，响应内容：{}", httpResponse.getStatus(), responseBody);
            throw new RuntimeException("请求出错，程序员小哥哥正在努力解决中^_^!");
        }
        return JSONUtil.toBean(responseBody, ZhihuUploadImageStatusResult.class);
    }

    /**
     * 上传图片状态
     *
     * @param cookie  知乎cookie
     * @param imageId 知乎图片id
     * @return
     */
    private void uploadingStatus(String cookie, String imageId) {
        //表单内容
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("upload_result", "success");
        String url = CONSTANT_UPLOAD_IMAGE_HASH_URL + "/" + imageId + "/uploading_status";
        HttpRequest httpRequest = HttpRequest.put(url)
                .cookie(cookie)
                .header(Header.ORIGIN.getValue(), CONSTANT_HEADERS_ORIGIN)
                .header(Header.REFERER.getValue(), CONSTANT_ARTICLE_HEADERS_REFERER)
                .body(JSONObject.toJSONString(bodyMap));
        log.info("上传图片httpRequest:{}", httpRequest);
        HttpResponse httpResponse = httpRequest.execute();
        String responseBody = httpResponse.body();
        log.info("上传图片返回数据:{}", responseBody);
        if (!httpResponse.isOk()) {
            log.info("知乎上传封面图片请求失败，状态码：{}，响应内容：{}", httpResponse.getStatus(), responseBody);
            throw new RuntimeException("请求出错，程序员小哥哥正在努力解决中^_^!");
        }
    }

    /**
     * 上传图片,未完，待续，快来一起研究OSS STS
     *
     * @param token     知乎上传文件token
     * @param accessId  知乎上传文件accessId
     * @param accessKey 知乎上传文件accessKey
     * @param objectKey 知乎上传文件objectKey
     * @param file      本地路径
     */
    @Deprecated
    private void uploadImage(String token, String accessId, String accessKey, String objectKey, File file) {
        String url = CONSTANT_UPLOAD_IMAGE_URL + objectKey;
        //表单内容
        // TODO Authorization不知如何获取,当你看到这段代码时，相信你已经有解决方案了
        HttpRequest httpRequest = HttpRequest.put(url)
                .header("X-Oss-Security-Token", token)
                .header("Authorization", accessId + ":")
                .header(Header.ORIGIN.getValue(), CONSTANT_HEADERS_ORIGIN)
                .header(Header.REFERER.getValue(), CONSTANT_ARTICLE_HEADERS_REFERER)
                .body(FileUtil.readBytes(file));
        log.info("上传图片文件httpRequest:{}", httpRequest);
        log.info("上传图片文件filePath:{}", file.getPath());
        HttpResponse httpResponse = httpRequest.execute();
        String responseBody = httpResponse.body();
        log.info("上传图片文件返回数据:{}", responseBody);
        if (!httpResponse.isOk()) {
            log.info("知乎上传图片文件请求失败，状态码：{}，响应内容：{}", httpResponse.getStatus(), responseBody);
            throw new RuntimeException("请求出错，程序员小哥哥正在努力解决中^_^!");
        }
    }

    /**
     * 上传图片Hash
     *
     * @param cookie   知乎cookie
     * @param formData 知乎图片信息
     * @return
     */
    private ZhihuUploadImageResult uploadImageHash(String cookie, ZhihuUploadImageFormData formData) {
        //表单内容
        Map<String, Object> formMap = BeanUtil.beanToMap(formData, true, true);
        HttpRequest httpRequest = HttpRequest.post(CONSTANT_UPLOAD_IMAGE_HASH_URL)
                .cookie(cookie)
                .header(Header.ORIGIN.getValue(), CONSTANT_HEADERS_ORIGIN)
                .header(Header.REFERER.getValue(), CONSTANT_ARTICLE_HEADERS_REFERER)
                .body(JSONObject.toJSONString(formMap));
        log.info("上传图片Hash httpRequest:{}", httpRequest);
        log.info("上传图片Hash formMap:{}", formMap);
        HttpResponse httpResponse = httpRequest.execute();
        String responseBody = httpResponse.body();
        log.info("上传图片Hash返回数据:{}", responseBody);
        if (!httpResponse.isOk()) {
            log.info("知乎上传图片Hash请求失败，状态码：{}，响应内容：{}", httpResponse.getStatus(), responseBody);
            throw new RuntimeException("请求出错，程序员小哥哥正在努力解决中^_^!");
        }
        ZhihuUploadImageResult mcnResult = JSONUtil.toBean(responseBody, ZhihuUploadImageResult.class);
        if (Objects.isNull(mcnResult)) {
            throw new RuntimeException("上传图片出错");
        }
        return mcnResult;
    }

    /**
     * 替换图片元素
     *
     * @param imageElement
     * @param imageData
     */
    private void replaceImageElement(@NotNull Element imageElement, ZhihuUploadImageStatusResult imageData) {
        imageElement.replaceWith(this.createImageElement(imageData));
    }

    /**
     * 创建图片元素
     *
     * @param imageData
     * @return
     */
    private Element createImageElement(ZhihuUploadImageStatusResult imageData) {
        Element elementImg = new Element(HTML.Tag.IMG.toString());
        //TODO 未完，待续
        return elementImg;
    }


    /**
     * 保存话题
     *
     * @param cookie    知乎cookie
     * @param articleId 文章id
     * @param data      话题表单
     * @return
     */
    private ZhihuTopicsResult saveTopics(String cookie, String articleId, ZhihuTopicsFormData data) {
        //表单内容
        Map<String, Object> formMap = BeanUtil.beanToMap(data, false, true);
        String url = CONSTANT_ARTICLE_Edit_URL + articleId + "/topics";
        HttpRequest httpRequest = HttpRequest.post(url)
                .cookie(cookie)
                .header(Header.ORIGIN.getValue(), CONSTANT_HEADERS_ORIGIN)
                .header(Header.REFERER.getValue(), CONSTANT_ARTICLE_HEADERS_REFERER)
                .body(JSONObject.toJSONString(formMap));
        log.info("保存话题httpRequest:{}", httpRequest);
        log.info("保存话题formMap:{}", formMap);
        HttpResponse httpResponse = httpRequest.execute();
        String responseBody = httpResponse.body();
        log.info("保存话题返回数据:{}", responseBody);
        if (!httpResponse.isOk()) {
            log.info("知乎保存话题请求失败，状态码：{}，响应内容：{}", httpResponse.getStatus(), responseBody);
            throw new RuntimeException("请求出错，程序员小哥哥正在努力解决中^_^!");
        }
        ZhihuTopicsResult mcnResult = JSONUtil.toBean(responseBody, ZhihuTopicsResult.class);
        if (Objects.isNull(mcnResult)) {
            throw new RuntimeException("保存话题出错");
        }
        return mcnResult;
    }

    /**
     * 搜索话题
     *
     * @param cookie
     * @param token  搜索的话题关键字
     * @return
     */
    public JSONArray autocompleteTopics(String cookie, String token) {
        String url = CONSTANT_AUTOCOMPLETE_TOPICS_URL + token;
        HttpRequest httpRequest = HttpRequest.get(url)
                .cookie(cookie)
                .header(Header.ORIGIN.getValue(), CONSTANT_HEADERS_ORIGIN)
                .header(Header.REFERER.getValue(), CONSTANT_ARTICLE_HEADERS_REFERER);
        log.info("请求参数httpRequest:{}", httpRequest);
        HttpResponse httpResponse = httpRequest.execute();
        String responseBody = httpResponse.body();
        log.info("返回数据:{}", responseBody);
        if (!httpResponse.isOk()) {
            log.info("知乎请求失败，状态码：{}，响应内容：{}", httpResponse.getStatus(), responseBody);
        }
        return JSONUtil.parseArray(responseBody);
    }


}
