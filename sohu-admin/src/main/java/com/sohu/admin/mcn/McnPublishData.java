package com.sohu.admin.mcn;

import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCN平台发布数据
 */
@Data
public class McnPublishData {

    private String cookie;

    private String token;

    /**
     * 发布id,非必填
     */
    private String publishId;

    /**
     * 内容标题，不能为空，不适用标题的平台随意填充即可
     */
    private String title;

    /**
     * 文章内容（html），不能为空，视频类型时随意填充数据即可
     */
    private String content;

    /**
     * 摘要,视频简介
     */
    private String brief;

    /**
     * 文章,内容图片地址集合，本地路径
     */
    private List<String> contentImagePathList;

    /**
     * 封面图，本地路径
     */
    private String coverImagePath;

    /**
     * 视频，本地路径
     */
    private String videoPath;

    /**
     * 其它参数
     */
    private Map<String,Object> otherParam =new HashMap<>();

}
