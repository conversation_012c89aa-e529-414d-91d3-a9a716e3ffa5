package com.sohu.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.sohu.admin.service.ISohuImTenantConfigExcelService;
import com.sohu.common.core.vo.ExcelErrVo;
import com.sohu.common.excel.utils.PoiExcelUtil;
import com.sohu.middle.api.bo.im.SohuImTenantConfigBo;
import com.sohu.middle.api.bo.im.SohuImTenantConfigImportBo;
import com.sohu.middle.api.service.im.RemoteImTenantConfigService;
import com.sohu.middle.api.vo.im.SohuImTenantConfigVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

@RequiredArgsConstructor
@Service
@Slf4j
public class SohuImTenantConfigExcelServiceImpl implements ISohuImTenantConfigExcelService {

    @DubboReference
    private RemoteImTenantConfigService remoteImTenantConfigService;

    @Override
    public void downloadModel(Long userId, HttpServletResponse response) {
        //初始化表头
        ExcelWriter writer = getProdExcelWriter(userId);
        try (Workbook workbook = writer.getWorkbook()) {
            Sheet sheet = writer.getSheet();
            //设置提示语
            setPrompt(sheet);
            dropDownList(sheet, workbook);
            PoiExcelUtil.writeExcel(response, writer, "ImTenantConfig_template");
        } catch (Exception e) {
            log.error("Exception:", e);
        }
    }

    public ExcelWriter getProdExcelWriter(Long userId) {
        //通过工具类创建writer
        ExcelWriter writer = ExcelUtil.getBigWriter();
        // 导出or模板
        Collection<String> headerList = getHeaderList().values();
        getSheet(writer, headerList.size());
//        writer.merge(headerList.size() - 1, "商品信息整理");
        writer.writeRow(headerList);
        /**
         * 到处自己的数据
         */
        Collection<Object> dataList = getDataList(userId).values();
        writer.writeRow(dataList);
        return writer;
    }

    private Map<Integer, String> getHeaderList() {
        Map<Integer, String> headerMap = new HashMap<>();
        headerMap.put(1, "邮箱SMTP服务器域名");
        headerMap.put(2, "邮箱SMTP服务端口");
        headerMap.put(3, "邮箱是否需要用户名密码验证");
        headerMap.put(4, "邮箱用户名");
        headerMap.put(5, "邮箱密码");
        headerMap.put(6, "邮箱发送方");
        headerMap.put(7, "邮箱使用STARTTLS安全连接");
        headerMap.put(8, "邮箱使用SSL安全连接");
        headerMap.put(9, "邮箱SMTP超时时长");
        headerMap.put(10, "邮箱Socket连接超时值");

        headerMap.put(11, "短信自定义的标识");
        headerMap.put(12, "短信厂商标识");
        headerMap.put(13, "短信您的accessKey");
        headerMap.put(14, "短信您的accessKeySecret");
        headerMap.put(15, "您的短信签名");

        headerMap.put(16, "极光appKey");
        headerMap.put(17, "极光masterSecret");
        headerMap.put(18, "极光平台设定");

        headerMap.put(19, "音视频服务地址");

        headerMap.put(20, "oss配置key");
        headerMap.put(21, "ossAccessKey");
        headerMap.put(22, "oss秘钥");
        headerMap.put(23, "oss桶名称");
        headerMap.put(24, "oss前缀");
        headerMap.put(25, "oss访问站点");
        headerMap.put(26, "oss自定义域名");
        headerMap.put(27, "oss是否https");
        headerMap.put(28, "oss域");
        headerMap.put(29, "oss扩展字段");
        headerMap.put(30, "oss备注");
        headerMap.put(31, "oss桶权限类型");

        headerMap.put(32, "im服务地址");
        headerMap.put(33, "im服务socket地址");
        return headerMap;
    }

    /**
     * 获取数据
     *
     * @return
     */
    private Map<Integer, Object> getDataList(Long userId) {
        Map<Integer, Object> dataMap = new HashMap<>();
        SohuImTenantConfigVo sohuImTenantConfigVo = this.remoteImTenantConfigService.queryByUserId(userId);
        if (Objects.isNull(sohuImTenantConfigVo)) {
            return dataMap;
        }
        if (Objects.isNull(sohuImTenantConfigVo.getMailAccountModel())) {
            //邮箱SMTP服务器域名
            dataMap.put(1, "");
            //邮箱SMTP服务端口
            dataMap.put(2, "");
            //邮箱是否需要用户名密码验证
            dataMap.put(3, "");
            //邮箱用户名
            dataMap.put(4, "");
            //邮箱密码
            dataMap.put(5, "");
            //邮箱发送方
            dataMap.put(6, "");
            //邮箱使用STARTTLS安全连接
            dataMap.put(7, "");
            //邮箱使用SSL安全连接
            dataMap.put(8, "");
            //邮箱SMTP超时时长
            dataMap.put(9, "");
            //邮箱Socket连接超时值
            dataMap.put(10, "");
        } else {
            //邮箱SMTP服务器域名
            dataMap.put(1, sohuImTenantConfigVo.getMailAccountModel().getHost());
            //邮箱SMTP服务端口
            dataMap.put(2, sohuImTenantConfigVo.getMailAccountModel().getPort());
            //邮箱是否需要用户名密码验证
            dataMap.put(3, sohuImTenantConfigVo.getMailAccountModel().getAuth());
            //邮箱用户名
            dataMap.put(4, sohuImTenantConfigVo.getMailAccountModel().getUser());
            //邮箱密码
            dataMap.put(5, sohuImTenantConfigVo.getMailAccountModel().getPass());
            //邮箱发送方
            dataMap.put(6, sohuImTenantConfigVo.getMailAccountModel().getFrom());
            //邮箱使用STARTTLS安全连接
            dataMap.put(7, sohuImTenantConfigVo.getMailAccountModel().getSslProtocols());
            //邮箱使用SSL安全连接
            dataMap.put(8, sohuImTenantConfigVo.getMailAccountModel().getSslEnable());
            //邮箱SMTP超时时长
            dataMap.put(9, sohuImTenantConfigVo.getMailAccountModel().getTimeout());
            //邮箱Socket连接超时值
            dataMap.put(10, sohuImTenantConfigVo.getMailAccountModel().getConnectionTimeout());
        }

        if (Objects.isNull(sohuImTenantConfigVo.getSmsBlendModel())) {
            //短信自定义的标识
            dataMap.put(11, "");
            //短信厂商标识
            dataMap.put(12, "");
            //短信您的accessKey
            dataMap.put(13, "");
            //短信您的accessKeySecret
            dataMap.put(14, "");
            //您的短信签名
            dataMap.put(15, "");
        } else {
            //短信自定义的标识
            dataMap.put(11, sohuImTenantConfigVo.getSmsBlendModel().getSmsBlendKey());
            //短信厂商标识
            dataMap.put(12, sohuImTenantConfigVo.getSmsBlendModel().getSmsSupplier());
            //短信您的accessKey
            dataMap.put(13, sohuImTenantConfigVo.getSmsBlendModel().getSmsAccessKeyId());
            //短信您的accessKeySecret
            dataMap.put(14, sohuImTenantConfigVo.getSmsBlendModel().getSmsAccessKeySecret());
            //您的短信签名
            dataMap.put(15, sohuImTenantConfigVo.getSmsBlendModel().getSmsSignature());
        }

        if (Objects.isNull(sohuImTenantConfigVo.getJiguangModel())) {
            //极光appKey
            dataMap.put(16, "");
            //极光masterSecret
            dataMap.put(17, "");
            //极光平台设定
            dataMap.put(18, "");
        } else {
            //极光appKey
            dataMap.put(16, sohuImTenantConfigVo.getJiguangModel().getAppKey());
            //极光masterSecret
            dataMap.put(17, sohuImTenantConfigVo.getJiguangModel().getMasterSecret());
            //极光平台设定
            dataMap.put(18, sohuImTenantConfigVo.getJiguangModel().getApnsProduction());
        }
        //音视频服务地址
        dataMap.put(19, sohuImTenantConfigVo.getAvUrl());

        if (Objects.isNull(sohuImTenantConfigVo.getOssConfigModel())) {
            //oss配置key
            dataMap.put(20, "");
            //ossAccessKey
            dataMap.put(21, "");
            //oss秘钥
            dataMap.put(22, "");
            //oss桶名称
            dataMap.put(23, "");
            //oss前缀
            dataMap.put(24, "");
            //oss访问站点
            dataMap.put(25, "");
            //oss自定义域名
            dataMap.put(26, "");
            //oss是否https
            dataMap.put(27, "");
            //oss域
            dataMap.put(28, "");
            //oss扩展字段
            dataMap.put(29, "");
            //oss备注
            dataMap.put(30, "");
            //oss桶权限类型
            dataMap.put(31, "");
        }else {
            //oss配置key
            dataMap.put(20, sohuImTenantConfigVo.getOssConfigModel().getConfigKey());
            //ossAccessKey
            dataMap.put(21, sohuImTenantConfigVo.getOssConfigModel().getAccessKey());
            //oss秘钥
            dataMap.put(22, sohuImTenantConfigVo.getOssConfigModel().getSecretKey());
            //oss桶名称
            dataMap.put(23, sohuImTenantConfigVo.getOssConfigModel().getBucketName());
            //oss前缀
            dataMap.put(24, sohuImTenantConfigVo.getOssConfigModel().getPrefix());
            //oss访问站点
            dataMap.put(25, sohuImTenantConfigVo.getOssConfigModel().getEndpoint());
            //oss自定义域名
            dataMap.put(26, sohuImTenantConfigVo.getOssConfigModel().getDomain());
            //oss是否https
            dataMap.put(27, sohuImTenantConfigVo.getOssConfigModel().getIsHttps());
            //oss域
            dataMap.put(28, sohuImTenantConfigVo.getOssConfigModel().getRegion());
            //oss扩展字段
            dataMap.put(29, sohuImTenantConfigVo.getOssConfigModel().getExt1());
            //oss备注
            dataMap.put(30, sohuImTenantConfigVo.getOssConfigModel().getRemark());
            //oss桶权限类型
            dataMap.put(31, sohuImTenantConfigVo.getOssConfigModel().getAccessPolicy());
        }

        //im服务地址
        dataMap.put(32, sohuImTenantConfigVo.getImServerUrl());
        //im服务socket地址
        dataMap.put(33, sohuImTenantConfigVo.getImSocketUrl());
        return dataMap;
    }

    /**
     * 设置提示语
     *
     * @param sheet
     * @param
     * @return
     */
    private Sheet setPrompt(Sheet sheet) {
        int rowNum = 0;
        int index = 0;
        setComment(sheet.getRow(rowNum).getCell(index++), "邮箱SMTP服务器域名必填", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "邮箱SMTP服务端口必填", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "邮箱是否需要用户名密码验证", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "邮箱用户名必填", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "邮箱密码必填", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "邮箱发送方必填，遵循RFC-822标准", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "邮箱使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。它将纯文本连接升级为加密连接（TLS或SSL）， 而不是使用一个单独的加密通信端口。", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "邮箱使用SSL安全连接", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "SMTP超时时长，单位毫秒，缺省值不超时", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "Socket连接超时值，单位毫秒，缺省值不超时", sheet);

        setComment(sheet.getRow(rowNum).getCell(index++), "短信自定义的标识必填,也就是configId这里可以是任意值（最好不要是中文）", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "短信厂商标识必填,标定此配置是哪个厂商，详细请看厂商标识介绍部分", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "短信您的accessKey必填", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "短信您的accessKeySecret必填", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "您的短信签名必填", sheet);

        setComment(sheet.getRow(rowNum).getCell(index++), "极光推送appKey必填", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "极光推送accessKeySecret必填", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "极光平台设定必填必填," +
                "极光推送iOS平台设定推送环境,\n" +
                "该字段仅对 iOS 的 Notification 有效，如果不指定则为推送生产环境。注意：JPush 服务端 SDK 默认设置为推送 “开发环境”。\n" +
                "true：表示推送生产环境。\n" +
                "false：表示推送开发环境。", sheet);

        setComment(sheet.getRow(rowNum).getCell(index++), "音视频服务地址必填", sheet);

        setComment(sheet.getRow(rowNum).getCell(index++), "oss配置key必填", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "ossAccessKey必填", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "oss秘钥必填", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "oss桶名称必填", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "oss前缀", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "oss访问站点必填", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "oss自定义域名必填", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "oss是否https必填", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "oss域", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "oss扩展字段", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "oss备注", sheet);
        setComment(sheet.getRow(rowNum).getCell(index++), "oss桶权限类型(0:private 1:public 2:custom)", sheet);

        setComment(sheet.getRow(rowNum).getCell(index++), "im服务地址必填", sheet);
        setComment(sheet.getRow(rowNum).getCell(index), "im服务socket地址必填", sheet);
        return sheet;
    }

    private void dropDownList(Sheet sheet, Workbook workbook) {
        String[] boolType = {"true", "false"};
        //邮箱是否需要用户名密码验证
        PoiExcelUtil.createDropDownList(sheet, boolType, 1, 50000, 2, 2);
        //邮箱使用STARTTLS安全连接
        PoiExcelUtil.createDropDownList(sheet, boolType, 1, 50000, 6, 6);
        //邮箱使用SSL安全连接
        PoiExcelUtil.createDropDownList(sheet, boolType, 1, 50000, 7, 7);
        //极光平台设定
        PoiExcelUtil.createDropDownList(sheet, boolType, 1, 50000, 17, 17);
        //oss是否https
        String[] ossIsHttpsType = {"0", "1"};
        PoiExcelUtil.createDropDownList(sheet, ossIsHttpsType, 1, 50000, 26, 26);
        //oss桶权限类型
        String[] ossAccessPolicyType = {"0", "1", "2"};
        PoiExcelUtil.createDropDownList(sheet, ossAccessPolicyType, 1, 50000, 30, 30);
    }

    @Override
    public List<ExcelErrVo> importData(List<SohuImTenantConfigImportBo> dataList) {
        List<ExcelErrVo> list = new ArrayList<>();
        if (CollUtil.isEmpty(dataList)) {
            return list;
        }
        try {
            SohuImTenantConfigImportBo importBo = dataList.get(0);
            importBo.buildModel();
            SohuImTenantConfigBo bo = new SohuImTenantConfigBo();
            bo.setMailAccountModel(importBo.getMailAccountModel());
            bo.setSmsBlendModel(importBo.getSmsBlendModel());
            bo.setJiguangModel(importBo.getJiguangModel());
            bo.setAvUrl(importBo.getAvUrl());
            bo.setOssConfigModel(importBo.getOssConfigModel());
            bo.setImServerUrl(importBo.getImServerUrl());
            bo.setImSocketUrl(importBo.getImSocketUrl());
            remoteImTenantConfigService.insertByBo(bo);
        } catch (Exception e) {
            e.printStackTrace();
            ExcelErrVo errVo = new ExcelErrVo();
            errVo.setTitle("存储出错");
            errVo.setErrMsg(e.getMessage());
            list.add(errVo);
        }
        return list;
    }

    /**
     * 初始化Sheet，设置宽度
     *
     * @param writer
     * @param size
     * @return
     */
    private Sheet getSheet(ExcelWriter writer, int size) {
        Sheet sheet = writer.getSheet();
        for (int i = 0; i < size; i++) {
            sheet.setColumnWidth(i, 40 * 256);
        }
        return sheet;
    }

    public static void setComment(Cell cell, String text, Sheet sheet) {
        ClientAnchor anchor = new XSSFClientAnchor();
        anchor.setDx1(0);
        anchor.setDx2(0);
        anchor.setDy1(0);
        anchor.setDy2(0);
        anchor.setCol1(cell.getColumnIndex());
        anchor.setRow1(cell.getRowIndex());
        anchor.setCol2(cell.getColumnIndex() + 5);
        anchor.setRow2(cell.getRowIndex() + 6);
        Drawing drawing = sheet.createDrawingPatriarch();
        Comment comment = drawing.createCellComment(anchor);
        comment.setString(new XSSFRichTextString(text));
        cell.setCellComment(comment);
    }
}
