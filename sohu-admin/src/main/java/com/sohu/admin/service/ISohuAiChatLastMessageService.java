package com.sohu.admin.service;

import com.sohu.admin.domain.SohuAiChatLastMessage;
import com.sohu.admin.api.bo.SohuAiChatLastMessageBo;
import com.sohu.admin.api.vo.SohuAiChatLastMessageVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * AI外层会话Service接口
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
public interface ISohuAiChatLastMessageService {

    /**
     * 查询AI外层会话
     */
    SohuAiChatLastMessageVo queryById(String id);

    /**
     * 根据 msgId 查询
     */
    SohuAiChatLastMessage queryByMsgId(Long msgId);

    /**
     * 查询AI外层会话列表
     */
    TableDataInfo<SohuAiChatLastMessageVo> queryPageList(SohuAiChatLastMessageBo bo, PageQuery pageQuery);

    /**
     * 查询AI外层会话列表
     */
    List<SohuAiChatLastMessageVo> queryList(SohuAiChatLastMessageBo bo);

    /**
     * 修改AI外层会话
     */
    Boolean insertByBo(SohuAiChatLastMessageBo bo);

    /**
     * 修改AI外层会话
     */
    Boolean updateByBo(SohuAiChatLastMessageBo bo);

    /**
     * 校验并批量删除AI外层会话信息
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    /**
     * 根据msgIds 删除AI外层会话信息
     */
    Boolean deleteByMsgIds(Collection<Long> msgIds, Boolean isValid);
}
