package com.sohu.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sohu.admin.api.bo.SohuMerchantAuditLogBo;
import com.sohu.admin.api.vo.SohuMerchantAuditLogVo;
import com.sohu.admin.domain.SohuMerchantAuditLog;
import com.sohu.admin.mapper.SohuMerchantAuditLogMapper;
import com.sohu.admin.service.ISohuMerchantAuditLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商户入驻审核记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@RequiredArgsConstructor
@Service
public class SohuMerchantAuditLogServiceImpl implements ISohuMerchantAuditLogService {

    private final SohuMerchantAuditLogMapper baseMapper;

    @Override
    public List<SohuMerchantAuditLogVo> selectListByMerchantId(Long merchantId) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<SohuMerchantAuditLog>().in(SohuMerchantAuditLog::getMerId, merchantId).orderByDesc(SohuMerchantAuditLog::getCreateTime));
    }

    /**
     * 新增商户入驻审核记录
     */
    @Override
    public Boolean insertByBo(SohuMerchantAuditLogBo bo) {
        SohuMerchantAuditLog add = BeanUtil.toBean(bo, SohuMerchantAuditLog.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean save(Long merId, String merchantType, String auditState, String auditReason) {
        SohuMerchantAuditLog bo = new SohuMerchantAuditLog();
        bo.setMerId(merId);
        bo.setMerchantType(merchantType);
        bo.setState(auditState);
        bo.setRejectReason(auditReason);
        return baseMapper.insert(bo) > 0;
    }
}
