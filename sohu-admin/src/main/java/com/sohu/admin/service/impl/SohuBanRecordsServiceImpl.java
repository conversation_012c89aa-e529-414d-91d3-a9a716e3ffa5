package com.sohu.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.bo.SohuBanRecordsBo;
import com.sohu.admin.api.vo.SohuBanRecordsVo;
import com.sohu.admin.domain.SohuBanRecords;
import com.sohu.admin.mapper.SohuBanRecordsMapper;
import com.sohu.admin.service.ISohuBanRecordsService;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 封禁记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@RequiredArgsConstructor
@Service
public class SohuBanRecordsServiceImpl implements ISohuBanRecordsService {

    private final SohuBanRecordsMapper baseMapper;

    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 查询封禁记录
     */
    @Override
    public SohuBanRecordsVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询封禁记录列表
     */
    @Override
    public TableDataInfo<SohuBanRecordsVo> queryPageList(SohuBanRecordsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuBanRecords> lqw = buildQueryWrapper(bo);
        Page<SohuBanRecordsVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        extendUserInfo(result.getRecords());
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询封禁记录列表
     */
    @Override
    public List<SohuBanRecordsVo> queryList(SohuBanRecordsBo bo) {
        LambdaQueryWrapper<SohuBanRecords> lqw = buildQueryWrapper(bo);
        List<SohuBanRecordsVo> sohuBanRecordsVos = baseMapper.selectVoList(lqw);
        extendUserInfo(sohuBanRecordsVos);
        return sohuBanRecordsVos;
    }

    /**
     * 扩展用户信息
     * @param sohuBanRecordsVos
     */
    private void extendUserInfo(List<SohuBanRecordsVo> sohuBanRecordsVos) {
        if (CollUtil.isNotEmpty(sohuBanRecordsVos)) {
            Set<Long> userIds = new HashSet<>();
            for (SohuBanRecordsVo sohuBanRecordsVo : sohuBanRecordsVos) {
                userIds.add(sohuBanRecordsVo.getUserId());
            }
            Map<Long, LoginUser> userMap = remoteUserService.selectMap(userIds);
            sohuBanRecordsVos.forEach(item -> {
                LoginUser loginUser = userMap.get(item.getUserId());
                if (Objects.nonNull(loginUser)) {
                    item.setAvatar(loginUser.getAvatar());
                }
            });
        }
    }

    private LambdaQueryWrapper<SohuBanRecords> buildQueryWrapper(SohuBanRecordsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuBanRecords> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuBanRecords::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getBanType()), SohuBanRecords::getBanType, bo.getBanType());
        lqw.eq(StringUtils.isNotBlank(bo.getDurationDescription()), SohuBanRecords::getDurationDescription, bo.getDurationDescription());
        lqw.eq(bo.getBanDatetime() != null, SohuBanRecords::getBanDatetime, bo.getBanDatetime());
        lqw.eq(bo.getExpectedEndDatetime() != null, SohuBanRecords::getExpectedEndDatetime, bo.getExpectedEndDatetime());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SohuBanRecords::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getBanReason()), SohuBanRecords::getBanReason, bo.getBanReason());
        lqw.eq(bo.getBanOperatorId() != null, SohuBanRecords::getBanOperatorId, bo.getBanOperatorId());
        lqw.eq(bo.getUnbanDatetime() != null, SohuBanRecords::getUnbanDatetime, bo.getUnbanDatetime());
        lqw.eq(StringUtils.isNotBlank(bo.getUnbanReason()), SohuBanRecords::getUnbanReason, bo.getUnbanReason());
        lqw.eq(bo.getUnbanOperatorId() != null, SohuBanRecords::getUnbanOperatorId, bo.getUnbanOperatorId());
        lqw.eq(bo.getLastOperationDatetime() != null, SohuBanRecords::getLastOperationDatetime, bo.getLastOperationDatetime());
        lqw.like(StrUtil.isNotBlank(bo.getPhoneNumber()), SohuBanRecords::getPhoneNumber, bo.getPhoneNumber());
        lqw.like(StrUtil.isNotBlank(bo.getNickName()), SohuBanRecords::getNickName, bo.getNickName());
        return lqw;
    }

    /**
     * 新增封禁记录
     */
    @Override
    public Long insertByBo(SohuBanRecordsBo bo) {
        SohuBanRecords add = BeanUtil.toBean(bo, SohuBanRecords.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            return add.getId();
        }
        return null;
    }

    /**
     * 修改封禁记录
     */
    @Override
    public Boolean updateByBo(SohuBanRecordsBo bo) {
        SohuBanRecords update = BeanUtil.toBean(bo, SohuBanRecords.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuBanRecords entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除封禁记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public void insertByBoList(List<SohuBanRecordsBo> banRecordsBos) {
        if (banRecordsBos == null || banRecordsBos.isEmpty()) {
            return;
        }
        List<SohuBanRecords> banRecords = BeanUtil.copyToList(banRecordsBos, SohuBanRecords.class);
        baseMapper.insertBatch(banRecords);
    }

    @Override
    public SohuBanRecordsVo findActiveBanByUserId(Long userId, String status) {
        LambdaQueryWrapper<SohuBanRecords> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuBanRecords::getUserId, userId);
        lqw.eq(SohuBanRecords::getStatus, status);
        lqw.orderByDesc(SohuBanRecords::getCreateTime);
        lqw.last("LIMIT 1");

        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public SohuBanRecordsVo findBanRecordById(Long banRecordId) {
        return baseMapper.selectVoById(banRecordId);
    }

    @Override
    public SohuBanRecordsVo findActiveBanByIp(String ip, String status) {
        LambdaQueryWrapper<SohuBanRecords> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuBanRecords::getIp, ip);
        lqw.eq(SohuBanRecords::getStatus, status);
        lqw.orderByDesc(SohuBanRecords::getCreateTime);
        lqw.last("LIMIT 1");

        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public SohuBanRecordsVo findActiveBanByDevice(String device, String status) {
        LambdaQueryWrapper<SohuBanRecords> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuBanRecords::getDevice, device);
        lqw.eq(SohuBanRecords::getStatus, status);
        lqw.orderByDesc(SohuBanRecords::getCreateTime);
        lqw.last("LIMIT 1");

        return baseMapper.selectVoOne(lqw);
    }
}
