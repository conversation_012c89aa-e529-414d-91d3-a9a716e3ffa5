package com.sohu.admin.service;

import com.sohu.admin.api.bo.SohuMerchantSalesReportBo;
import com.sohu.admin.api.vo.SohuMerchantSalesReportVo;
import com.sohu.admin.domain.SohuMerchantSalesReport;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/4 12:34
 */
public interface ISohuMerchantSalesReportService {

    /**
     * 商家销售额统计排名
     * @param startTime
     * @param endTime
     * @return
     */
    List<SohuMerchantSalesReportVo> salesRank(String startTime, String endTime);

    /**
     * 同步商家销售额
     * @param  boList
     */
    void syncSalesReport(List<SohuMerchantSalesReportBo> boList);

    /**
     * 定时执行商家销售额
     * @param salesReportVos
     */
    void excuteSalesReport(List<SohuMerchantSalesReportVo> salesReportVos,String day);
}
