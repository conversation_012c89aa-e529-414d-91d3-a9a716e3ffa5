package com.sohu.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.bo.SohuAiPriceBo;
import com.sohu.admin.api.enums.AIPayTypeEnum;
import com.sohu.admin.api.enums.AIPriceDiscountTypeEnum;
import com.sohu.admin.api.enums.AiRightCategoryEnum;
import com.sohu.admin.api.enums.AiRightVersionEnum;
import com.sohu.admin.api.vo.SohuAiPriceInfoVo;
import com.sohu.admin.api.vo.SohuAiPriceVo;
import com.sohu.admin.domain.SohuAiInfo;
import com.sohu.admin.domain.SohuAiPrice;
import com.sohu.admin.domain.SohuAiRights;
import com.sohu.admin.mapper.SohuAiPriceMapper;
import com.sohu.admin.mapper.SohuAiRightsMapper;
import com.sohu.admin.service.ISohuAiInfoService;
import com.sohu.admin.service.ISohuAiPriceService;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * AI会员价格Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
@RequiredArgsConstructor
@Service
public class SohuAiPriceServiceImpl implements ISohuAiPriceService {

    private final SohuAiPriceMapper baseMapper;

    private final SohuAiRightsMapper sohuAiRightsMapper;

    private final ISohuAiInfoService sohuAiInfoService;


    /**
     * 查询AI会员价格
     */
    @Override
    public SohuAiPriceVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询AI会员价格列表
     */
    @Override
    public TableDataInfo<SohuAiPriceVo> queryPageList(SohuAiPriceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuAiPrice> lqw = buildQueryWrapper(bo);
        Page<SohuAiPriceVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询AI会员价格列表
     */
    @Override
    public List<SohuAiPriceVo> queryList(SohuAiPriceBo bo) {
        LambdaQueryWrapper<SohuAiPrice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuAiPrice> buildQueryWrapper(SohuAiPriceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuAiPrice> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getDuration()), SohuAiPrice::getDuration, bo.getDuration());
        lqw.eq(bo.getOriginalPrice() != null, SohuAiPrice::getOriginalPrice, bo.getOriginalPrice());
        lqw.eq(bo.getDiscountedPrice() != null, SohuAiPrice::getDiscountedPrice, bo.getDiscountedPrice());
        return lqw;
    }

    /**
     * 新增AI会员价格
     */
    @Override
    public Boolean insertByBo(SohuAiPriceBo bo) {
        SohuAiPrice add = BeanUtil.toBean(bo, SohuAiPrice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改AI会员价格
     */
    @Override
    public Boolean updateByBo(SohuAiPriceBo bo) {
        SohuAiPrice update = BeanUtil.toBean(bo, SohuAiPrice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuAiPrice entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除AI会员价格
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<SohuAiPriceInfoVo> list(Long userId, String rightCategory) {
        List<SohuAiRights> rights = sohuAiRightsMapper.selectList(SohuAiRights::getRightCategory, rightCategory);
        if (CollectionUtils.isEmpty(rights)) {
            return Collections.EMPTY_LIST;
        }
        List<Long> rightIds = rights.stream()
                .map(SohuAiRights::getId)
                .collect(Collectors.toList());
        LambdaQueryWrapper<SohuAiPrice> lmq = Wrappers.lambdaQuery();
        lmq.in(SohuAiPrice::getRightId, rightIds);
        List<SohuAiPrice> priceList = baseMapper.selectList(lmq);

        Map<Long, SohuAiRights> version2RightsMap = rights.stream()
                .collect(Collectors.toMap(SohuAiRights::getId, Function.identity()));

        SohuAiInfo aiInfo = sohuAiInfoService.queryByUserId(userId);
        //是否首开
        Boolean firstFlag = Objects.isNull(aiInfo);

        AIPayTypeEnum virtualPayTypeEnum = AIPayTypeEnum.VIRTUAL;
        List<SohuAiPriceInfoVo> voList = Lists.newArrayList();
        for (SohuAiPrice price : priceList) {
            SohuAiPriceInfoVo vo = BeanUtil.toBean(price, SohuAiPriceInfoVo.class);
            Pair<BigDecimal, AIPriceDiscountTypeEnum> discount = getDiscountInfo(firstFlag, price);
            vo.setCurrentPrice(discount.getLeft());
            String discountType = Optional.ofNullable(discount.getRight())
                    .map(AIPriceDiscountTypeEnum::getCode)
                    .orElse(null);
            vo.setDiscountType(discountType);
            vo.setVirtualOriginalPrice(virtualPayTypeEnum.convertPrice(vo.getOriginalPrice()).intValue());
            vo.setVirtualCurrentPrice(virtualPayTypeEnum.convertPrice(vo.getCurrentPrice()).intValue());

            if (AiRightCategoryEnum.VIP.getCode().equals(rightCategory)) {
                BigDecimal priceOfMonth = AiRightVersionEnum.getByCode(price.getVersion())
                        .getPriceOfMonth(vo.getCurrentPrice());
                vo.setPriceOfMonth(priceOfMonth);
                vo.setVirtualPriceOfMonth(virtualPayTypeEnum.convertPrice(vo.getPriceOfMonth()).intValue());
            }
            SohuAiRights right = version2RightsMap.get(price.getRightId());
            vo.setRightCategory(right.getRightCategory());
            vo.setCreateAppCount(right.getCreateAppCount());
            vo.setChatCount(right.getChatCount());
            //FIXME 当前业务规则所有会员版本无差异，默认写死
            vo.setIsChat(true);
            vo.setIsAppPiazza(true);
            voList.add(vo);
        }
        return voList;
    }


    /**
     * 获取当前价格
     *
     * @param firstFlag 是否首开
     * @param price     价格信息
     * @return left:当前价格 right:折扣类型
     */
    private Pair<BigDecimal, AIPriceDiscountTypeEnum> getDiscountInfo(Boolean firstFlag, SohuAiPrice price) {
        if (BooleanUtils.isTrue(firstFlag)
                && Objects.nonNull(price.getFirstPrice())
                && AIPriceDiscountTypeEnum.TIME_LIMIT.name().equals(price.getDiscountType())) {
            return Pair.of(price.getFirstPrice(), AIPriceDiscountTypeEnum.FIRST);
        }
        AIPriceDiscountTypeEnum discountType = AIPriceDiscountTypeEnum.findByCode(price.getDiscountType());
        if (Objects.nonNull(price.getDiscountedPrice())) {
            return Pair.of(price.getDiscountedPrice(), discountType);
        }
        return Pair.of(price.getOriginalPrice(), discountType);
    }
}
