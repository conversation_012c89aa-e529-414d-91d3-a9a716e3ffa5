package com.sohu.admin.service;


import com.sohu.admin.api.bo.SohuTopicContentRelationBo;
import com.sohu.admin.api.vo.SohuTopicContentRelationVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 专题内容关联Service接口
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface ISohuTopicContentRelationService {

    /**
     * 查询专题内容关联
     */
    SohuTopicContentRelationVo queryById(String id);

    /**
     * 查询专题内容关联列表
     */
    TableDataInfo<SohuTopicContentRelationVo> queryPageList(SohuTopicContentRelationBo bo, PageQuery pageQuery);

    /**
     * 查询专题内容关联列表
     */
    List<SohuTopicContentRelationVo> queryList(SohuTopicContentRelationBo bo);

    /**
     * 修改专题内容关联
     */
    Boolean insertByBo(SohuTopicContentRelationBo bo);

    /**
     * 修改专题内容关联
     */
    Boolean updateByBo(SohuTopicContentRelationBo bo);

    /**
     * 校验并批量删除专题内容关联信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
