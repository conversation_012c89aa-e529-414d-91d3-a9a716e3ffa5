package com.sohu.admin.service;

import com.sohu.admin.api.vo.playlet.PlayletMerchantTypeVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.admin.api.bo.SohuMerchantTypeBo;
import com.sohu.admin.api.vo.SohuMerchantTypeVo;
import com.sohu.middle.api.bo.playlet.PlayletMerchantTypeBo;

import java.util.Collection;
import java.util.List;

/**
 * 商户类型Service接口
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
public interface ISohuMerchantTypeService {

    /**
     * 查询商户类型
     */
    SohuMerchantTypeVo queryById(Long id);

    /**
     * 查询商户类型列表
     */
    TableDataInfo<SohuMerchantTypeVo> queryPageList(SohuMerchantTypeBo bo, PageQuery pageQuery);

    /**
     * 查询商户类型列表
     */
    List<SohuMerchantTypeVo> queryList(SohuMerchantTypeBo bo);

    /**
     * 修改商户类型
     */
    Boolean insertByBo(SohuMerchantTypeBo bo);

    /**
     * 修改商户类型
     */
    Boolean updateByBo(SohuMerchantTypeBo bo);

    /**
     * 校验并批量删除商户类型信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询全部商户类型列表
     */
    List<SohuMerchantTypeVo> pageList();

    /**
     * 查询全部商户类型列表
     */
    TableDataInfo<SohuMerchantTypeVo> page(PageQuery pageQuery);

    /**
     * 查询全部商户店铺类型列表--不分页
     *
     * @return List<PlayletMerchantTypeVo>
     */
    List<PlayletMerchantTypeVo> getMerchantTypeList();

    /**
     * 查询商户店铺类型列表--分页
     *
     * @param pageQuery PageQuery
     * @return TableDataInfo<PlayletMerchantTypeVo>
     */
    TableDataInfo<PlayletMerchantTypeVo> getMerchantTypePageList(PageQuery pageQuery);

    /**
     * 新增商户店铺类型
     *
     * @param bo PlayletMerchantTypeBo
     * @return Boolean
     */
    Boolean addMerchantType(PlayletMerchantTypeBo bo);

    /**
     * 修改商户店铺类型
     *
     * @param bo PlayletMerchantTypeBo
     * @return Boolean
     */
    Boolean editMerchantType(PlayletMerchantTypeBo bo);
}

