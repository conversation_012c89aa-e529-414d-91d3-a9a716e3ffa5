package com.sohu.admin.service;

import com.sohu.common.core.vo.ExcelErrVo;
import com.sohu.middle.api.bo.im.SohuImTenantConfigImportBo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * IM租户服务配置Service接口
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
public interface ISohuImTenantConfigExcelService {
    /**
     * 下载IM租户配置模版
     * @param response
     */
    void downloadModel(Long userId,HttpServletResponse response);

    /**
     * 导入IM租户配置
     */
    List<ExcelErrVo> importData(List<SohuImTenantConfigImportBo> dataList);

}
