package com.sohu.admin.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sohu.common.core.web.domain.SohuEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 商户品牌资质关联对象 sohu_merchant_brand_qualification
 *
 * <AUTHOR>
 * @date 2025-04-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_merchant_brand_qualification")
public class SohuMerchantBrandQualification extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 商户ID
     */
    private Long merId;

    /**
     * 类目id
     */
    private Long cateId;

    /**
     * 品牌id
     */
    private Long brandId;
    /**
     * 资质id
     */
    private Long qualificateId;
    /**
     * 资质名称
     */
    private String qualificateName;
    /**
     * 资质证明url
     */
    private String proveUrl;

    @Schema(name = "relateNo",description = "关联编号", example = "123")
    private String relateNo;

}
