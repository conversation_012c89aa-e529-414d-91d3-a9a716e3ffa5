package com.sohu.admin.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.junit.Ignore;

/**
 * 专题内容关联对象 sohu_topic_content_relation
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_topic_content_relation")
public class SohuTopicContentRelation extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 父专题id, 关联sohu_topic表
     */
    private Long topicPid;
    /**
     * 专题id，关联sohu_topic表
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long topicId;
    /**
     * 内容id
     */
    private String contentId;
    /**
     * Question:问题,Answer:回答,Article:图文,Video:视频
     */
    private String contentType;
    /**
     * 删除标记，1=删除，0=未删除
     */
    @TableLogic
    private Long delFlag;

}
