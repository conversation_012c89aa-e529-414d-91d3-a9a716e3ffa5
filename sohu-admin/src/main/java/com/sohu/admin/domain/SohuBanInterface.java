package com.sohu.admin.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 受控接口资源对象 sohu_ban_interface
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@TableName("sohu_ban_interface")
public class SohuBanInterface implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 接口名称（例如：发布帖子）
     */
    private String interfaceName;
    /**
     * 接口路径（例如：/api/content/create）
     */
    private String interfaceUrl;
    /**
     * HTTP方法（GET/POST/PUT/DELETE/ALL）
     */
    private String httpMethod;
    /**
     * 风险权重（1-10）
     */
    private Integer riskWeight;
    /**
     * 接口功能描述
     */
    private String description;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

}
