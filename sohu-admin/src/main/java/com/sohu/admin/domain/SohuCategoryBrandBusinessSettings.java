package com.sohu.admin.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 类目品牌业务设置对象 sohu_category_brand_business_settings
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_category_brand_business_settings")
public class SohuCategoryBrandBusinessSettings extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 业务对象id（如品牌、类目）
     */
    private Long businessId;
    /**
     * 业务对象类型(Brand-品牌,Category-类目)
     */
    private String businessType;
    /**
     * 个人是否可展示 0.不可展示 1.可展示
     */
    private Integer isOpenPersonal;
    /**
     * 企业是否可展示 0.不可展示 1.可展示
     */
    private Integer isOpenBusiness;
    /**
     * 个人是否要求 0.不要求 1.要求
     */
    private Integer isRequiredPersonal;
    /**
     * 企业是否要求 0.不要求 1.要求
     */
    private Integer isRequiredBusiness;
    /**
     * 是否删除 0.未删除 1.已删除
     */
    private Integer isDel;

}
