package com.sohu.admin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 代理用户记录对象 sohu_agent_user
 *
 * <AUTHOR>
 * @date 2024-01-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_agent_user")
public class SohuAgentUser extends BaseEntity {

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 代理id
     */
    private Long agentId;
    /**
     * 代理人角色
     */
    private String agentRole;
    /**
     * 代理人邀请渠道Id
     */
    private Long agentChannelId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户昵称
     */
    private String merchantName;
    /**
     * 用户手机号
     */
    private String phoneNumber;
    /**
     * 用户邮箱
     */
    private String agentEmail;
    /**
     * 账号类型（personal=个人  business=企业认证）
     */
    private String accountType;
    /**
     * 入驻角色权限字符串【mcn:MCN机构,agent:代理商，professor：拆单方】
     */
    private String roleKey;
    /**
     * 状态（WAIT_JOIN=待认证，SUCCESS=邀请成功，REFUSE=邀请失败，TIMEOUT=超时未加入）
     */
    private String state;
    /**
     * 邀请失败原因
     */
    private String rejectReason;
    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 注册时间
     */
    private Date regTime;

    /**
     * 站点Id
     */
    private Long siteId;

}
