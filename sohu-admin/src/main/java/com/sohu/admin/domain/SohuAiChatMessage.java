package com.sohu.admin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AI会话对象 sohu_ai_chat_message
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_ai_chat_message")
public class SohuAiChatMessage extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private String id;
    /**
     * 内容
     */
    private String content;
    /**
     * 被拦截的原因，比如文字涉恐、反共、非好友、或其他规则
     */
    private String err;
    /**
     * 发送人ID
     */
    private Long userId;
    /**
     * 消息类型;(text - 文本、photo - 图片、video - 视频、voice - 语音、share - 分享、voiceCall - 语音通话、groupVoiceCall-群语音通话 ,videoCall - 视频通话、groupCall - 群视频、notice - 公告、file - 文件、command - 命令)
     */
    private String messageType;
    /**
     * 会话id
     */
    private Long msgId;
    /**
     * 1是ai的消息，0是本人消息
     */
    private Integer isAi;

}
