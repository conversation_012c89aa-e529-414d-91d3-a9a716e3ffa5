package com.sohu.admin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商户-狐少少对象 sohu_merchant
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_merchant")
public class SohuMerchant extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 商户ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 商户名称-唯一值
     */
    private String name;

    @Schema(name = "merchantType", description = "商户类型：personal-个人，business-企业", example = "personal")
    private String merchantType;
    /**
     * 商户所有者id-用户id
     */
    private Long userId;
    /**
     * 商户分类ID
     */
    private Long categoryId;
    /**
     * 站点id—国家站
     */
    private Long siteId;
    /**
     * 城市站点id
     */
    private Long citySiteId;
    /**
     * 商户类型ID
     */
    private Long typeId;
    /**
     * 商户姓名
     */
    private String realName;
    /**
     * 商户邮箱
     */
    private String email;
    /**
     * 商户手机号
     */
    private String phone;
    /**
     * 手续费(%)
     */
    private BigDecimal handlingFee;
    /**
     * 商户关键字
     */
    private String keywords;
    /**
     * 商户地址
     */
    private String address;
    /**
     * 是否自营：1-自营，0-非自营
     */
    private Boolean isSelf;
    /**
     * 是否推荐:0-不推荐，1-推荐
     */
    private Boolean isRecommend;
    /**
     * 商户开关:0-关闭，1-开启
     */
    private Boolean isSwitch;
    /**
     * 审核状态：WaitApprove-待审核，Pass-审核成功，Refuse-审核拒绝 AuthFail 认证未过
     */
    private String auditStatus;
    /**
     * 拒绝原因
     */
    private String denialReason;
    /**
     * 审核员ID
     */
    private Long auditorId;
    /**
     * 商品审核开关:0-关闭，1-开启
     */
    private Boolean productSwitch;
    /**
     * 备注
     */
    private String remark;
    /**
     * 排序
     */
    private String sort;
    /**
     * 资质图片
     */
    private String qualificationPicture;
    /**
     * 商户背景图
     */
    private String backImage;
    /**
     * 商户头像
     */
    private String avatar;
    /**
     * 商户街背景图
     */
    private String streetBackImage;
    /**
     * 商户简介
     */
    private String intro;
    /**
     * 商户创建类型：admin-管理员创建，apply-商户入驻申请
     */
    private String createType;
    /**
     * pcBanner
     */
    private String pcBanner;
    /**
     * pc背景图
     */
    private String pcBackImage;
    /**
     * 复制商品数量
     */
    private Long copyProductNum;
    /**
     * 商户余额
     */
    private BigDecimal balance;
    /**
     * 商户星级1-5
     */
    private Integer starLevel;
    /**
     * 是否删除
     */
    private Boolean isDel;

    /**
     * 店铺销量
     */
    private Long saleNums;

    /**
     * 系统来源(sohuglobal:狐少少,minglereels:海外短剧)
     */
    private String SysSource;

    /**
     * 国家区号
     */
    private String countryCode;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 是否升级 0 否 1 是
     */
    private Integer isUpgrage;

    /**
     * 闭店状态：CloseApprove-闭店待审核 CloseRefuse-闭店审核拒绝  ClosePass-闭店审核通过
     */
    private String closeStatus;

}
