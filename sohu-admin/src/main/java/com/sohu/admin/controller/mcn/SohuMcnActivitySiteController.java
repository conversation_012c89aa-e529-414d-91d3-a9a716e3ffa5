package com.sohu.admin.controller.mcn;

import com.sohu.common.core.web.controller.BaseController;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * MCN活动站点控制器
 * 前端访问路由地址为:/admin/mcnActivitySite
 *
 * <AUTHOR>
 * @date 2024-03-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mcnActivitySite")
public class SohuMcnActivitySiteController extends BaseController {

//    private final ISohuMcnActivitySiteService iSohuMcnActivitySiteService;

//    /**
//     * 查询MCN活动站点列表
//     */
//    @SaCheckPermission("system:mcnActivitySite:list")
//    @GetMapping("/list")
//    public TableDataInfo<SohuMcnActivitySiteVo> list(SohuMcnActivitySiteBo bo, PageQuery pageQuery) {
//        return iSohuMcnActivitySiteService.queryPageList(bo, pageQuery);
//    }
//
//    /**
//     * 导出MCN活动站点列表
//     */
//    @Hidden
//    @SaCheckPermission("system:mcnActivitySite:export")
//    @Log(title = "MCN活动站点", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(SohuMcnActivitySiteBo bo, HttpServletResponse response) {
//        List<SohuMcnActivitySiteVo> list = iSohuMcnActivitySiteService.queryList(bo);
//        ExcelUtil.exportExcel(list, "MCN活动站点", SohuMcnActivitySiteVo.class, response);
//    }
//
//    /**
//     * 获取MCN活动站点详细信息
//     *
//     * @param id 主键
//     */
//    @SaCheckPermission("system:mcnActivitySite:query")
//    @GetMapping("/{id}")
//    public R<SohuMcnActivitySiteVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable String id) {
//        return R.ok(iSohuMcnActivitySiteService.queryById(id));
//    }
//
//    /**
//     * 新增MCN活动站点
//     */
//    @SaCheckPermission("system:mcnActivitySite:add")
//    @Log(title = "MCN活动站点", businessType = BusinessType.INSERT)
//    @PostMapping()
//    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuMcnActivitySiteBo bo) {
//        return toAjax(iSohuMcnActivitySiteService.insertByBo(bo));
//    }
//
//    /**
//     * 修改MCN活动站点
//     */
//    @SaCheckPermission("system:mcnActivitySite:edit")
//    @Log(title = "MCN活动站点", businessType = BusinessType.UPDATE)
//    @PutMapping()
//    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuMcnActivitySiteBo bo) {
//        return toAjax(iSohuMcnActivitySiteService.updateByBo(bo));
//    }
//
//    /**
//     * 删除MCN活动站点
//     *
//     * @param ids 主键串
//     */
//    @SaCheckPermission("system:mcnActivitySite:remove")
//    @Log(title = "MCN活动站点", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable String[] ids) {
//        return toAjax(iSohuMcnActivitySiteService.deleteWithValidByIds(Arrays.asList(ids), true));
//    }
}
