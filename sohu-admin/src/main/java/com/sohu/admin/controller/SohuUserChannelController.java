package com.sohu.admin.controller;

import com.sohu.admin.api.bo.SohuUserChannelBo;
import com.sohu.admin.api.vo.SohuUserChannelVo;
import com.sohu.admin.service.ISohuUserChannelService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.vo.SohuCategoryVo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 频道控制器
 * 前端访问路由地址为:/system/userChannel
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/userChannel")
public class SohuUserChannelController extends BaseController {

    private final ISohuUserChannelService iSohuUserChannelService;

    /**
     * 查询频道列表
     */
    @GetMapping("/list")
    public TableDataInfo<SohuUserChannelVo> list(SohuUserChannelBo bo, PageQuery pageQuery) {
        return iSohuUserChannelService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出频道列表
     */
    @Log(title = "admin", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuUserChannelBo bo, HttpServletResponse response) {
        List<SohuUserChannelVo> list = iSohuUserChannelService.queryList(bo);
        ExcelUtil.exportExcel(list, "admin", SohuUserChannelVo.class, response);
    }

    /**
     * 获取频道详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<SohuUserChannelVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuUserChannelService.queryById(id));
    }

    /**
     * 新增频道
     */
    @Log(title = "admin", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuUserChannelBo bo) {
        return toAjax(iSohuUserChannelService.insertByBo(bo));
    }

    /**
     * 修改频道
     */
    @Log(title = "admin", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuUserChannelBo bo) {
        return toAjax(iSohuUserChannelService.updateByBo(bo));
    }

    /**
     * 删除频道
     *
     * @param ids 主键串
     */
    @Log(title = "admin", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuUserChannelService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 获取用户频道集合
     */
    @GetMapping("/getUserChannelList/{userId}")
    public R<List<SohuCategoryVo>> getUserChannelList(@NotNull(message = "主键不能为空") @PathVariable Long userId) {
        return R.ok(iSohuUserChannelService.getUserChannelList(userId));
    }
}
