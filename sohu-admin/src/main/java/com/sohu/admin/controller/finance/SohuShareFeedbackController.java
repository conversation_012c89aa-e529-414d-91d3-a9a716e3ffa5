package com.sohu.admin.controller.finance;

import com.sohu.admin.service.ISohuShareFeedbackService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.finance.SohuShareFeedbackBindingBo;
import com.sohu.middle.api.bo.finance.SohuShareFeedbackQueryBo;
import com.sohu.middle.api.service.finance.RemoteMiddleShareFeedBackService;
import com.sohu.middle.api.vo.finance.SohuShareFeedbackStatVo;
import com.sohu.middle.api.vo.finance.SohuShareFeedbackVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 金融分享-申请反馈控制器
 * 前端访问路由地址为:/admin/shareFeedback
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/shareFeedback")
public class SohuShareFeedbackController extends BaseController {

    @DubboReference
    private RemoteMiddleShareFeedBackService remoteMiddleShareFeedBackService;

    private final ISohuShareFeedbackService feedbackService;

//    /**
//     * 查询金融分享-申请反馈列表
//     */
//    @SaCheckPermission("dao:shareFeedback:list")
//    @GetMapping("/list")
//    public TableDataInfo<SohuShareFeedbackVo> list(SohuShareFeedbackBo bo, PageQuery pageQuery) {
//        return remoteMiddleShareFeedBackService.queryPageList(bo, pageQuery);
//    }

    /**
     * 金融赚钱订单列表
     */
    @GetMapping("/listOfBinding")
    public TableDataInfo<SohuShareFeedbackVo> list(SohuShareFeedbackQueryBo bo, PageQuery pageQuery) {
        return remoteMiddleShareFeedBackService.queryPageListOfBinding(bo, pageQuery);
    }

//    /**
//     * 导出金融分享-申请反馈列表
//     */
//    @SaCheckPermission("dao:shareFeedback:export")
//    @Log(title = "金融分享-申请反馈", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(SohuShareFeedbackBo bo, HttpServletResponse response) {
//        List<SohuShareFeedbackVo> list = remoteMiddleShareFeedBackService.queryList(bo);
//        ExcelUtil.exportExcel(list, "金融分享-申请反馈", SohuShareFeedbackVo.class, response);
//    }

    /**
     * 获取金融分享-申请反馈详细信息
     *
     * @param id 主键
     */
    //@SaCheckPermission("dao:shareFeedback:query")
    @GetMapping("/{id}")
    public R<SohuShareFeedbackVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteMiddleShareFeedBackService.queryById(id));
    }

//    /**
//     * 新增金融分享-申请反馈
//     */
//    @SaCheckPermission("dao:shareFeedback:add")
//    @Log(title = "金融分享-申请反馈", businessType = BusinessType.INSERT)
//    @PostMapping()
//    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuShareFeedbackBo bo) {
//        return toAjax(remoteMiddleShareFeedBackService.insertByBo(bo));
//    }
//
//    /**
//     * 修改金融分享-申请反馈
//     */
//    @SaCheckPermission("dao:shareFeedback:edit")
//    @Log(title = "金融分享-申请反馈", businessType = BusinessType.UPDATE)
//    @PutMapping()
//    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuShareFeedbackBo bo) {
//        return toAjax(remoteMiddleShareFeedBackService.updateByBo(bo));
//    }

    /**
     * 删除金融分享-申请反馈
     *
     * @param ids 主键串
     */
    //@SaCheckPermission("dao:shareFeedback:remove")
    @Log(title = "金融分享-申请反馈", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(remoteMiddleShareFeedBackService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 批量匹配申请
     *
     * @param ids
     * @return
     */
    @PostMapping("/batchMatchApply")
    public R<List<SohuShareFeedbackVo>> batchMatchApply(@RequestBody List<Long> ids) {
        return R.ok(remoteMiddleShareFeedBackService.batchMatchApply(ids));
    }

    /**
     * 绑定申请
     *
     * @param boList
     * @return
     */
    @PostMapping("/bindingApply")
    public R<Boolean> bindingApply(@RequestBody @Valid List<SohuShareFeedbackBindingBo> boList) {
        remoteMiddleShareFeedBackService.bindingApply(boList);
        return R.ok(Boolean.TRUE);
    }

    /**
     * 导入数据
     */
    @PostMapping(value = "/import")
    public R<List<Long>> importData(MultipartFile file, String templateType) {
        return R.ok(feedbackService.importData(file, templateType));
    }

    /**
     * 统计推广数据以及收益
     */
    @GetMapping
    public R<SohuShareFeedbackStatVo> getShareStat() {
        return R.ok(remoteMiddleShareFeedBackService.getShareStat());
    }

}
