package com.sohu.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.sohu.middle.api.bo.SohuTopicContentQueryBo;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.aspect.UserBehavior;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.bo.mcn.SohuMcnArticleReqBo;
import com.sohu.middle.api.enums.behavior.BehaviorBusinessTypeEnum;
import com.sohu.middle.api.enums.behavior.OperaTypeEnum;
import com.sohu.middle.api.service.RemoteMiddleArticleService;
import com.sohu.middle.api.service.RemoteMiddleLiteratureService;
import com.sohu.middle.api.service.RemoteMiddleService;
import com.sohu.middle.api.vo.SohuArticleVo;
import com.sohu.middle.api.vo.SohuConentListStatVo;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;

/**
 * 图文
 * 前端访问路由地址为:/admin/article
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/article")
public class SohuArticleController extends BaseController {

    @DubboReference
    private RemoteMiddleService<SohuArticleBo, SohuArticleVo> remoteMiddleService;
    @DubboReference
    private RemoteMiddleArticleService remoteMiddleArticleService;
    @DubboReference
    private RemoteMiddleLiteratureService remoteMiddleLiteratureService;

    /**
     * 查询图文主体列表
     */
    //@SaCheckPermission("admin:article:list")
    @GetMapping("/list")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.ARTICLE, operType = OperaTypeEnum.LIST)
    public TableDataInfo<SohuArticleVo> list(SohuArticleBo bo, PageQuery pageQuery) {
        return remoteMiddleService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询图文主体列表-OnShelf
     */
    @SaCheckPermission("admin:article:list")
    @GetMapping("/listOfOnShelf")
    public TableDataInfo<SohuArticleVo> listOfOnShelf(SohuArticleBo bo, PageQuery pageQuery) {
        return remoteMiddleArticleService.queryPageListOfOnShelf(bo, pageQuery);
    }

    /**
     * 查询图文主体列表-统计
     */
    @GetMapping("/listStat")
    public R<SohuConentListStatVo> listStat(SohuArticleBo bo) {
        return R.ok(remoteMiddleArticleService.queryPageListStat(bo));
    }

    /**
     * 导出图文主体列表
     */
    @Hidden
    @SaCheckPermission("admin:article:export")
    @Log(title = "导出图文主体", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuArticleBo bo, HttpServletResponse response) {
        List<SohuArticleVo> list = remoteMiddleService.queryList(bo);
        ExcelUtil.exportExcel(list, "图文主体", SohuArticleVo.class, response);
    }

    /**
     * 获取图文主体详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("admin:article:query")
    @GetMapping("/{id}")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.ARTICLE, operType = OperaTypeEnum.INFO)
    public R<SohuArticleVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteMiddleService.query(BusyType.Article, id));
    }

    /**
     * 新增图文主体
     */
    @Log(title = "新增图文", businessType = BusinessType.INSERT)
    @PostMapping()
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.ARTICLE, operType = OperaTypeEnum.ADD)
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuArticleBo bo) {
        if (StrUtil.equalsAnyIgnoreCase(bo.getType(), "general")
                || StrUtil.equalsAnyIgnoreCase(bo.getType(), "lesson")) {
            return toAjax(remoteMiddleService.add(bo));
        } else {
            SohuLiteratureBo literatureBo = BeanUtil.toBean(bo, SohuLiteratureBo.class);
            return toAjax(remoteMiddleLiteratureService.add(literatureBo));
        }
    }

    /**
     * 修改图文主体
     */
    @SaCheckPermission("admin:article:edit")
    @Log(title = "编辑图文", businessType = BusinessType.UPDATE)
    @PutMapping()
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.ARTICLE, operType = OperaTypeEnum.UPDATE)
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuArticleBo bo) {
        if (StrUtil.equalsAnyIgnoreCase(bo.getType(), "general")
                || StrUtil.equalsAnyIgnoreCase(bo.getType(), "lesson")) {
            return toAjax(remoteMiddleService.update(bo));
        } else {
            SohuLiteratureBo literatureBo = BeanUtil.toBean(bo, SohuLiteratureBo.class);
            return toAjax(remoteMiddleLiteratureService.update(literatureBo));
        }
    }

    /**
     * 批量删除图文主体
     *
     * @param ids 主键串
     */
    @SaCheckPermission("admin:article:remove")
    @Log(title = "批量删除图文", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.ARTICLE, operType = OperaTypeEnum.DELETE)
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Collection<Long> ids) {
        return toAjax(remoteMiddleArticleService.deleteWithValidByIds(ids, true));
    }

    /**
     * 删除图文主体
     *
     * @param id 主键ID
     */
    @SaCheckPermission("admin:article:remove")
    @Log(title = "删除图文", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{id}")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.ARTICLE, operType = OperaTypeEnum.DELETE)
    public R<Boolean> delete(@PathVariable("id") Long id) {
        return toAjax(remoteMiddleService.delete(BusyType.Article, id));
    }

    /**
     * 图文草稿提交至审核
     *
     * @param id 图文ID
     * @return {@link R}
     */
    @PostMapping("/commit/{id}")
    @Operation(summary = "图文草稿提交至审核")
    @Parameter(name = "id", description = "图文id", example = "1", required = true)
    @Log(title = "图文草稿提交至审核", businessType = BusinessType.UPDATE)
    @SaCheckPermission("admin:article:commit")
    public R<Boolean> commit(@PathVariable("id") Long id) {
        return R.ok(remoteMiddleArticleService.submitAudit(id));
    }

    /**
     * MCN图文分页查询
     */
    //@SaCheckPermission("admin:article:mcnquery")
    @Log(title = "MCN图文分页查询")
    @GetMapping("/mcnList")
    public TableDataInfo<SohuArticleVo> getMCNArticleList(SohuMcnArticleReqBo bo, PageQuery pageQuery) {
        bo.setMcnUserId(LoginHelper.getUserId());
        return remoteMiddleArticleService.queryMCNArticleList(bo, pageQuery);
    }

    /**
     * 批量修改用户作品状态
     */
    @PostMapping("/update/batch/state")
    @Operation(summary = "批量修改用户作品状态")
    public R<Boolean> updateBatchContentState(@Validated(AddGroup.class) @RequestBody SohuContentBatchBo bo) {
        return R.ok(remoteMiddleArticleService.updateBatchContentState(bo));
    }

    /**
     * 用户申述
     */
    @PostMapping("/user/appeal")
    @Operation(summary = "用户申述")
    public R<Boolean> userAppeal(@Validated(AddGroup.class) @RequestBody SohuUserContentAppealBo bo) {
        return R.ok(remoteMiddleArticleService.userAppeal(bo));
    }

    /**
     * 用户自主下架
     */
    @PostMapping("/update/offShelf/{id}")
    @Operation(summary = "用户自主下架")
    @Parameter(name = "id", description = "图文id", example = "1", required = true)
    @Log(title = "用户自主下架", businessType = BusinessType.UPDATE)
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.ARTICLE, operType = OperaTypeEnum.OFFSHELF)
    public R<Boolean> updateOffShelfById(@PathVariable("id") Long id) {
        return R.ok(remoteMiddleArticleService.updateOffShelfById(id));
    }

    /**
     * 审核强制下架
     */
    @PostMapping("/update/force/offShelf")
    @Operation(summary = "审核强制下架")
    @Log(title = "审核强制下架", businessType = BusinessType.UPDATE)
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.ARTICLE, operType = OperaTypeEnum.OFFSHELF)
    public R<Boolean> updateForceOffShelfById(@RequestBody SohuContentRefuseBo bo) {
        return R.ok(remoteMiddleArticleService.updateCompelOffById(bo));
    }

    /**
     * 回收站恢复
     */
    @PostMapping("/recovery/data/{id}")
    @Operation(summary = "回收站恢复")
    @Parameter(name = "id", description = "图文id", example = "1", required = true)
    @Log(title = "回收站恢复", businessType = BusinessType.UPDATE)
    public R<Boolean> recoveryData(@PathVariable("id") Long id) {
        return R.ok(remoteMiddleArticleService.recoveryData(id));
    }

    /**
     * 回收站删除
     */
    @Log(title = "回收站删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/data/{id}")
    public R<Boolean> deleteDataById(@PathVariable("id") Long id) {
        return R.ok(remoteMiddleArticleService.deleteDataById(id));
    }

    /**
     * 批量删除图文
     */
    @Log(title = "批量删除图文", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch/delete/{ids}")
    public R<Boolean> batchDelete(@NotEmpty(message = "主键不能为空") @PathVariable Collection<Long> ids) {
        return R.ok(remoteMiddleArticleService.logicForceDeleteById(ids));
    }

    /**
     * 批量隐藏图文
     */
    @Log(title = "批量隐藏图文", businessType = BusinessType.UPDATE)
    @PostMapping("/batch/hide/{ids}")
    public R<Boolean> batchHide(@NotEmpty(message = "主键不能为空") @PathVariable Collection<Long> ids) {
        return R.ok(remoteMiddleArticleService.hideDataBatch(ids));
    }

    /**
     * 获取愿望列表(五养专用)
     */
    @GetMapping("/topic/list")
    public TableDataInfo<SohuArticleVo> getTopicList(SohuTopicContentQueryBo bo, PageQuery pageQuery) {
        return remoteMiddleArticleService.getTopicList(bo, pageQuery);
    }

}
