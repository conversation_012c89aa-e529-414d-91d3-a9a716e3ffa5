package com.sohu.admin.controller.airec;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.airec.SohuAirecTagBo;
import com.sohu.middle.api.service.airec.RemoteMiddleAirecTagService;
import com.sohu.middle.api.vo.airec.SohuAirecTagVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * airec标签库控制器
 * 前端访问路由地址为:/admin/airecTag
 *
 * <AUTHOR>
 * @date 2024-06-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/airecTag")
public class SohuAirecTagController extends BaseController {

    @DubboReference
    private RemoteMiddleAirecTagService remoteMiddleAirecTagService;

    /**
     * 查询airec标签库列表
     */
    @SaCheckPermission("system:airecTag:list")
    @GetMapping("/list")
    public TableDataInfo<SohuAirecTagVo> list(SohuAirecTagBo bo, PageQuery pageQuery) {
        return remoteMiddleAirecTagService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出airec标签库列表
     */
    @SaCheckPermission("system:airecTag:export")
    @Log(title = "airec标签库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuAirecTagBo bo, HttpServletResponse response) {
        List<SohuAirecTagVo> list = remoteMiddleAirecTagService.queryList(bo);
        ExcelUtil.exportExcel(list, "airec标签库", SohuAirecTagVo.class, response);
    }

    /**
     * 获取airec标签库详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:airecTag:query")
    @GetMapping("/{id}")
    public R<SohuAirecTagVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteMiddleAirecTagService.queryById(id));
    }

    /**
     * 新增airec标签库
     */
    @SaCheckPermission("system:airecTag:add")
    @Log(title = "airec标签库", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuAirecTagBo bo) {
        return toAjax(remoteMiddleAirecTagService.insertByBo(bo));
    }

    /**
     * 修改airec标签库
     */
    @SaCheckPermission("system:airecTag:edit")
    @Log(title = "airec标签库", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuAirecTagBo bo) {
        return toAjax(remoteMiddleAirecTagService.updateByBo(bo));
    }

    /**
     * 删除airec标签库
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:airecTag:remove")
    @Log(title = "airec标签库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(remoteMiddleAirecTagService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
