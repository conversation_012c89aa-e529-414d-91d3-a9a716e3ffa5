package com.sohu.admin.controller.mcn;


import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.service.mcn.RemoteMiddleYixiaoerMcnBaseService;
import com.sohu.middle.api.service.mcn.RemoteMiddleArticleUserService;
import com.sohu.middle.api.vo.mcn.YixiaoerMcnInfoVo;
import com.sohu.middle.api.vo.mcn.YixiaoerMcnPlatformVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 蚁小二MCN
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mcn")
public class SohuMcnController extends BaseController {

    @DubboReference
    private RemoteMiddleArticleUserService remoteMiddleArticleUserService;
    @DubboReference
    private RemoteMiddleYixiaoerMcnBaseService remoteMiddleYixiaoerMcnBaseService;

    /**
     * 获取蚁小二accessToken
     */
    @GetMapping("/getMCNToken")
    public R<String> getMCNToken() {
        return R.ok(remoteMiddleYixiaoerMcnBaseService.getMcnToken());
    }

    /**
     * 启用MCN
     * 模拟接口，后续待产品经理规划
     * 后续加入子账号概念后，需要更换写法
     */
    @PostMapping("/enableMCN")
    public R<Boolean> enableMCN() {
        //后续加入子账号概念后，需要更换写法
        remoteMiddleArticleUserService.createArticleUser(LoginHelper.getUserId().toString(), LoginHelper.getUsername(), LoginHelper.getUserId(), true);
        return R.ok(Boolean.TRUE);
    }

    /**
     * 获取蚁小二用户商户信息
     * 模拟接口，后续待产品经理规划
     * 后续加入子账号概念后，需要更换写法
     */
    @GetMapping("/getMCNInfo")
    public R<YixiaoerMcnInfoVo> getMcnInfo() {
        return R.ok(remoteMiddleArticleUserService.getMcnInfo(LoginHelper.getUserId()));
    }

    /**
     * 获取目前所支持的分发平台
     */
    @GetMapping("/getMCNPlatformVoList")
    public R<List<YixiaoerMcnPlatformVo>> getMcnPlatformVoList() {
        return R.ok(remoteMiddleYixiaoerMcnBaseService.getMcnPlatformVoList());
    }

}
