package com.sohu.admin.controller;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuMerTradeRecordBo;
import com.sohu.middle.api.bo.SohuTradeRecordIndependentBo;
import com.sohu.middle.api.service.RemoteMiddleMerTradeRecordService;
import com.sohu.middle.api.vo.SohuMerTradeRecordVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/7 11:20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mer/trade")
@ResponseBody
public class AdminMerTradeRecordController extends BaseController {

    @DubboReference
    private RemoteMiddleMerTradeRecordService remoteMiddleMerTradeRecordService;

    /**
     * 商户分账单分页列表
     */
    @Operation(summary = "商户资金流水", description = "负责人：汪伟，结算中心-资金流水")
    @GetMapping("/pageList")
    public TableDataInfo<SohuMerTradeRecordVo> pageList(SohuMerTradeRecordBo bo, PageQuery pageQuery) {
        return remoteMiddleMerTradeRecordService.queryPageList(bo, pageQuery);
    }

    @Operation(summary = "商户资金流水统计", description = "负责人：汪伟，结算中心-资金流水统计")
    @GetMapping("/independent/total")
    public R<BigDecimal> independentTotal(SohuMerTradeRecordBo bo) {
        return R.ok(remoteMiddleMerTradeRecordService.independentTotal(bo));
    }

    /**
     * 商户分账单分页列表
     */
    @Operation(summary = "资金流水导出", description = "负责人：汪伟，结算中心-资金流水导出")
    @GetMapping("/export")
    public void export(SohuMerTradeRecordBo bo, HttpServletResponse response) {
        List<SohuMerTradeRecordVo> list = remoteMiddleMerTradeRecordService.getList(bo);
        ExcelUtil.exportExcel(list, "资金流水", SohuMerTradeRecordVo.class, response);
    }

}
