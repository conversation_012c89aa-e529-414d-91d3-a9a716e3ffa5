package com.sohu.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.middle.api.bo.SohuTopicContentQueryBo;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.aspect.UserBehavior;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.enums.behavior.BehaviorBusinessTypeEnum;
import com.sohu.middle.api.enums.behavior.OperaTypeEnum;
import com.sohu.middle.api.service.RemoteMiddleQuestionService;
import com.sohu.middle.api.service.RemoteMiddleService;
import com.sohu.middle.api.vo.SohuConentListStatVo;
import com.sohu.middle.api.vo.SohuQuestionVo;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * 问题
 * 前端访问路由地址为:/admin/question
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/question")
public class SohuQuestionController extends BaseController {

    @DubboReference
    private RemoteMiddleService<SohuQuestionBo, SohuQuestionVo> remoteMiddleService;
    @DubboReference
    private RemoteMiddleQuestionService remoteMiddleQuestionService;

    /**
     * 查询问题主体列表
     */
    @GetMapping("/list")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.QUESTION, operType = OperaTypeEnum.LIST)
    public TableDataInfo<SohuQuestionVo> list(SohuQuestionBo bo, PageQuery pageQuery) {
        return remoteMiddleService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询问题主体列表-统计
     */
    @GetMapping("/listStat")
    public R<SohuConentListStatVo> listStat(SohuQuestionBo bo) {
        return R.ok(remoteMiddleQuestionService.queryPageListStat(bo));
    }

    /**
     * 导出问题主体列表
     */
    @Hidden
    @SaCheckPermission("admin:question:export")
    @Log(title = "导出问题列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuQuestionBo bo, HttpServletResponse response) {
        List<SohuQuestionVo> list = remoteMiddleService.queryList(bo);
        ExcelUtil.exportExcel(list, "问题主体", SohuQuestionVo.class, response);
    }

    /**
     * 获取问题主体详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    @SaCheckPermission("admin:question:query")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.QUESTION, operType = OperaTypeEnum.INFO)
    public R<SohuQuestionVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteMiddleQuestionService.queryById(id, Boolean.FALSE));
    }

    /**
     * 新增问题主体
     */
    @Log(title = "新增问题", businessType = BusinessType.INSERT)
    @PostMapping()
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.QUESTION, operType = OperaTypeEnum.ADD)
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuQuestionBo bo) {
        return toAjax(remoteMiddleService.add(bo));
    }

    /**
     * 修改问题主体
     */
    @Log(title = "修改问题", businessType = BusinessType.UPDATE)
    @PutMapping()
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.QUESTION, operType = OperaTypeEnum.UPDATE)
    @SaCheckPermission("admin:question:edit")
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuQuestionBo bo) {
        return toAjax(remoteMiddleService.update(bo));
    }

    /**
     * 删除问题主体
     *
     * @param ids 主键串
     */
    @Log(title = "批量删除问题", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.QUESTION, operType = OperaTypeEnum.DELETE)
    @SaCheckPermission("admin:question:remove")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(remoteMiddleQuestionService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 删除问题主体
     *
     * @param id 主键ID
     */
    @Log(title = "删除问题", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{id}")
    @SaCheckPermission("admin:question:remove")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.QUESTION, operType = OperaTypeEnum.DELETE)
    public R<Boolean> delete(@PathVariable("id") Long id) {
        return toAjax(remoteMiddleService.delete(BusyType.Question, id));
    }

    /**
     * 问题草稿提交至审核
     *
     * @param id 问题ID
     * @return {@link R}
     */
    @PostMapping("/commit/{id}")
    @Operation(summary = "问题草稿提交至审核")
    @Parameter(name = "id", description = "问题id", example = "1", required = true)
    @Log(title = "问题草稿提交至审核", businessType = BusinessType.UPDATE)
    @SaCheckPermission("admin:question:commit")
    public R<Boolean> commit(@PathVariable("id") Long id) {
        remoteMiddleQuestionService.submitAudit(id);
        return R.ok(Boolean.TRUE);
    }

    /**
     * 批量修改用户作品状态
     */
    @PostMapping("/update/batch/state")
    @Operation(summary = "批量修改用户作品状态")
    public R<Boolean> updateBatchContentState(@Validated(AddGroup.class) @RequestBody SohuContentBatchBo bo) {
        return R.ok(remoteMiddleQuestionService.updateBatchContentState(bo));
    }

    /**
     * 用户申述
     */
    @PostMapping("/user/appeal")
    @Operation(summary = "用户申述")
    public R<Boolean> userAppeal(@Validated(AddGroup.class) @RequestBody SohuUserContentAppealBo bo) {
        return R.ok(remoteMiddleQuestionService.userAppeal(bo));
    }

    /**
     * 用户自主下架
     */
    @PostMapping("/update/offShelf/{id}")
    @Operation(summary = "用户自主下架")
    @Parameter(name = "id", description = "图文id", example = "1", required = true)
    @Log(title = "用户自主下架", businessType = BusinessType.UPDATE)
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.QUESTION, operType = OperaTypeEnum.OFFSHELF)
    public R<Boolean> updateOffShelfById(@PathVariable("id") Long id) {
        return R.ok(remoteMiddleQuestionService.updateOffShelfById(id));
    }

    /**
     * 审核强制下架
     */
    @PostMapping("/update/force/offShelf")
    @Operation(summary = "审核强制下架")
    @Log(title = "审核强制下架", businessType = BusinessType.UPDATE)
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.QUESTION, operType = OperaTypeEnum.OFFSHELF)
    public R<Boolean> updateForceOffShelfById(@RequestBody SohuContentRefuseBo bo) {
        return R.ok(remoteMiddleQuestionService.updateCompelOffById(bo));
    }

    /**
     * 回收站恢复
     */
    @PostMapping("/recovery/data/{id}")
    @Operation(summary = "回收站恢复")
    @Parameter(name = "id", description = "图文id", example = "1", required = true)
    @Log(title = "回收站恢复", businessType = BusinessType.UPDATE)
    public R<Boolean> recoveryData(@PathVariable("id") Long id) {
        return R.ok(remoteMiddleQuestionService.recoveryData(id));
    }

    /**
     * 回收站删除
     */
    @Log(title = "回收站删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/data/{id}")
    public R<Boolean> deleteDataById(@PathVariable("id") Long id) {
        return R.ok(remoteMiddleQuestionService.deleteDataById(id));
    }

    /**
     * 批量删除图文
     */
    @Log(title = "批量删除图文", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch/delete/{ids}")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.QUESTION, operType = OperaTypeEnum.DELETE)
    public R<Boolean> batchDelete(@NotEmpty(message = "主键不能为空") @PathVariable Collection<Long> ids) {
        return R.ok(remoteMiddleQuestionService.logicForceDeleteById(ids));
    }

    /**
     * 批量隐藏图文
     */
    @Log(title = "批量隐藏图文", businessType = BusinessType.UPDATE)
    @PostMapping("/batch/hide/{ids}")
    public R<Boolean> batchHide(@NotEmpty(message = "主键不能为空") @PathVariable Collection<Long> ids) {
        return R.ok(remoteMiddleQuestionService.hideDataBatch(ids));
    }

    /**
     * 查询视频列表-OnShelf
     */
    @GetMapping("/listOfOnShelf")
    public TableDataInfo<SohuQuestionVo> listOfOnShelf(SohuQuestionBo bo, PageQuery pageQuery) {
        return remoteMiddleQuestionService.queryPageListOfOnShelf(bo, pageQuery);
    }

    /**
     * 获取愿望列表(五养专用)
     */
    @GetMapping("/topic/list")
    public TableDataInfo<SohuQuestionVo> getTopicList(SohuTopicContentQueryBo bo, PageQuery pageQuery) {
        return remoteMiddleQuestionService.getTopicList(bo, pageQuery);
    }
}
