package com.sohu.admin.controller.airec;


import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuArticleBo;
import com.sohu.middle.api.bo.SohuQuestionBo;
import com.sohu.middle.api.bo.SohuVideoBo;
import com.sohu.middle.api.service.RemoteMiddleArticleService;
import com.sohu.middle.api.service.RemoteMiddlePlayletService;
import com.sohu.middle.api.service.RemoteMiddleQuestionService;
import com.sohu.middle.api.service.RemoteMiddleVideoService;
import com.sohu.middle.api.vo.SohuArticleVo;
import com.sohu.middle.api.vo.SohuPlayletVo;
import com.sohu.middle.api.vo.SohuQuestionVo;
import com.sohu.middle.api.vo.SohuVideoVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 前端访问路由地址为:/admin/airecContent
 *
 * <AUTHOR>
 * @date 2023-06-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/airecContent")
public class SohuAirecContentController extends BaseController {

    @DubboReference
    private RemoteMiddleArticleService remoteMiddleArticleService;
    @DubboReference
    private RemoteMiddleVideoService remoteMiddleVideoService;
    @DubboReference
    private RemoteMiddleQuestionService remoteMiddleQuestionService;
    @DubboReference
    private RemoteMiddlePlayletService remoteMiddlePlayletService;

    /**
     * 投流图文
     */
    @GetMapping("/article/page/center")
    public TableDataInfo<SohuArticleVo> articlePageContentCenter(SohuArticleBo bo, PageQuery pageQuery) {
        return remoteMiddleArticleService.articlePageCenterByType(bo, pageQuery);
    }

    /**
     * 投流视频
     */
    @GetMapping("/video/page/center")
    public TableDataInfo<SohuVideoVo> videoPageContentCenter(SohuVideoBo bo, PageQuery pageQuery) {
        return remoteMiddleVideoService.videoPageCenterByType(bo, pageQuery);
    }

    /**
     * 投流问答
     */
    @GetMapping("/question/page/center")
    public TableDataInfo<SohuQuestionVo> questionPageContentCenter(SohuQuestionBo bo, PageQuery pageQuery) {
        return remoteMiddleQuestionService.questionPageCenterByType(bo, pageQuery);
    }

    /**
     * 投流短剧
     */
    @GetMapping("/playlet/page/center")
    public TableDataInfo<SohuPlayletVo> playletPageContentCenter(String title, PageQuery pageQuery) {
        return remoteMiddlePlayletService.playletPageContentCenter(title, pageQuery);
    }
}
