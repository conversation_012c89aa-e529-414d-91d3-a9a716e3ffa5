package com.sohu.admin.controller.ai;

import cn.hutool.core.bean.BeanUtil;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.service.RemoteDialogService;
import com.sohu.middle.api.service.RemotePracticeService;
import com.sohu.middle.api.vo.ai.AiDialogRecordVo;
import com.sohu.middle.api.vo.ai.SohuDialogInfoVo;
import com.sohu.middle.api.vo.ai.SohuDialogRecordVo;
import com.sohu.middle.api.vo.ai.SohuDialogVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * ai对话记录
 *
 * @Author: leibo
 * @Date: 2025/3/5 14:33
 **/
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/dialog")
public class SohuDialogController extends BaseController {

    @DubboReference
    private RemoteDialogService remoteDialogService;
    @DubboReference
    private RemotePracticeService remotePracticeService;

    /**
     * 获取个人会话明细
     *
     * @return
     */
    @GetMapping("/info")
    public R<List<SohuDialogVo>> getDialogInfo(@RequestParam(value = "dialogName", required = false) String dialogName,
                                               @RequestParam(value = "busyType", required = false, defaultValue = "TASK") String busyType) {
        return R.ok(remoteDialogService.listByDialogName(dialogName, busyType));
    }

    /**
     * 获取单一会话记录明细
     *
     * @param dialogId 会话id
     * @return
     */
    @GetMapping("/record/{dialogId}/list")
    public R<List<SohuDialogRecordVo>> listRecord(@NotNull(message = "会话id不能为空") @PathVariable Long dialogId) {
        return R.ok(remoteDialogService.listRecord(dialogId));
    }

    /**
     * 获取个人历史会话
     *
     * @param busyType
     * @return
     */
    @GetMapping("/history/info")
    public R<SohuDialogInfoVo> getDialogHistoryInfo(@RequestParam(value = "busyType", required = false, defaultValue = "TASK") String busyType) {
        return R.ok(remoteDialogService.getDialogInfo(busyType));
    }

    /**
     * 获取最近的记录明细
     *
     * @param dialogId 会话id
     * @return
     */
    @GetMapping("/record/near")
    public R<AiDialogRecordVo> nearRecord(@RequestParam(value = "dialogId", required = false) Long dialogId) {
        SohuDialogRecordVo record = remoteDialogService.getNearDialogRecord(dialogId, LoginHelper.getUserId());
        return R.ok(BeanUtil.toBean(record, AiDialogRecordVo.class));
    }

    /**
     * 进行模型训练方法（测试）
     *
     * @return
     */
    @GetMapping
    public R<String> practice() {
        return R.ok(remotePracticeService.practice(null, null));
    }

}
