package com.sohu.admin.controller.airec;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.airec.SohuAirecTagRelationBo;
import com.sohu.middle.api.service.airec.RemoteMiddleAirecTagRelationService;
import com.sohu.middle.api.vo.airec.SohuAirecTagRelationVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * airec标签关联控制器
 * 前端访问路由地址为:/admin/airecTagRelation
 *
 * <AUTHOR>
 * @date 2024-06-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/airecTagRelation")
public class SohuAirecTagRelationController extends BaseController {

    @DubboReference
    private RemoteMiddleAirecTagRelationService remoteMiddleAirecTagRelationService;

    /**
     * 查询airec标签关联列表
     */
    @SaCheckPermission("system:airecTagRelation:list")
    @GetMapping("/list")
    public TableDataInfo<SohuAirecTagRelationVo> list(SohuAirecTagRelationBo bo, PageQuery pageQuery) {
        return remoteMiddleAirecTagRelationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出airec标签关联列表
     */
    @SaCheckPermission("system:airecTagRelation:export")
    @Log(title = "airec标签关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuAirecTagRelationBo bo, HttpServletResponse response) {
        List<SohuAirecTagRelationVo> list = remoteMiddleAirecTagRelationService.queryList(bo);
        ExcelUtil.exportExcel(list, "airec标签关联", SohuAirecTagRelationVo.class, response);
    }

    /**
     * 获取airec标签关联详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:airecTagRelation:query")
    @GetMapping("/{id}")
    public R<SohuAirecTagRelationVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteMiddleAirecTagRelationService.queryById(id));
    }

    /**
     * 新增airec标签关联
     */
    @SaCheckPermission("system:airecTagRelation:add")
    @Log(title = "airec标签关联", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuAirecTagRelationBo bo) {
        return toAjax(remoteMiddleAirecTagRelationService.insertByBo(bo));
    }

    /**
     * 修改airec标签关联
     */
    @SaCheckPermission("system:airecTagRelation:edit")
    @Log(title = "airec标签关联", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuAirecTagRelationBo bo) {
        return toAjax(remoteMiddleAirecTagRelationService.updateByBo(bo));
    }

    /**
     * 删除airec标签关联
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:airecTagRelation:remove")
    @Log(title = "airec标签关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(remoteMiddleAirecTagRelationService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
