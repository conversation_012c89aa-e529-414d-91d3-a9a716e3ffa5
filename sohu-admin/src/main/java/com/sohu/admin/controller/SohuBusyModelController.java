package com.sohu.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuBusyModelBo;
import com.sohu.middle.api.service.RemoteMiddleBusyModelService;
import com.sohu.middle.api.vo.SohuBusyModelVo;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 生意模式
 * 前端访问路由地址为:/admin/busy/model
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/busy/model")
public class SohuBusyModelController extends BaseController {

    @DubboReference
    private RemoteMiddleBusyModelService remoteMiddleBusyModelService;

    /**
     * 查询商单模式主体列表
     */
    @SaCheckPermission("admin:busyModel:list")
    @GetMapping("/list")
    public TableDataInfo<SohuBusyModelVo> list(SohuBusyModelBo bo, PageQuery pageQuery) {
        return remoteMiddleBusyModelService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出商单模式主体列表
     */
    @Hidden
    @SaCheckPermission("admin:busyModel:export")
    @Log(title = "商单模式主体", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuBusyModelBo bo, HttpServletResponse response) {
        List<SohuBusyModelVo> list = remoteMiddleBusyModelService.queryList(bo);
        ExcelUtil.exportExcel(list, "商单模式主体", SohuBusyModelVo.class, response);
    }

    /**
     * 获取商单模式主体详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("admin:busyModel:query")
    @GetMapping("/{id}")
    public R<SohuBusyModelVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteMiddleBusyModelService.queryById(id));
    }

    /**
     * 新增商单模式主体
     */
    @SaCheckPermission("admin:busyModel:add")
    @Log(title = "商单模式主体", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuBusyModelBo bo) {
        return toAjax(remoteMiddleBusyModelService.insertByBo(bo));
    }

    /**
     * 修改商单模式主体
     */
    @SaCheckPermission("admin:busyModel:edit")
    @Log(title = "商单模式主体", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuBusyModelBo bo) {
        return toAjax(remoteMiddleBusyModelService.updateByBo(bo));
    }

    /**
     * 删除商单模式主体
     *
     * @param ids 主键串
     */
    @SaCheckPermission("admin:busyModel:remove")
    @Log(title = "商单模式主体", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(remoteMiddleBusyModelService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
