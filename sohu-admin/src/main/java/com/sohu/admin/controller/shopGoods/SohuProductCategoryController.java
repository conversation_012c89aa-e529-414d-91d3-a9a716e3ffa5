package com.sohu.admin.controller.shopGoods;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.shopgoods.api.RemoteProductCategoryService;
import com.sohu.shopgoods.api.domain.SohuProductCategoryReqBo;
import com.sohu.shopgoods.api.model.SohuProductCategoryModel;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;

/**
 * 商户store-商品分类控制器
 *
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/shop-goods/productCategory")
public class SohuProductCategoryController extends BaseController {
    @DubboReference
    private RemoteProductCategoryService remoteProductCategoryService;

    /**
     * 查询商户-商品分类列表
     */
    @GetMapping("/list")
    public R<List<SohuProductCategoryModel>> list(SohuProductCategoryReqBo bo) {
        bo.setSysSource(getSysSource());
        return R.ok(remoteProductCategoryService.queryList(bo));
    }

    /**
     * 查询商户-商品分类列表-父级
     */
    @GetMapping("/list/pid")
    public R<List<SohuProductCategoryModel>> pidList() {
        return R.ok(remoteProductCategoryService.queryPidList(getSysSource()));
    }

    /**
     * 新增商户-商品分类
     */
    @Log(title = "商户-商品分类", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuProductCategoryReqBo bo) {
        bo.setSysSource(getSysSource());
        return toAjax(remoteProductCategoryService.insertByBo(bo));
    }

    /**
     * 修改商户-商品分类
     */
    @Log(title = "商户-商品分类", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuProductCategoryReqBo bo) {
        return toAjax(remoteProductCategoryService.updateByBo(bo));
    }

    /**
     * 删除商户-商品分类
     *
     * @param ids 主键串
     */
    @Log(title = "商户-商品分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(remoteProductCategoryService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 查询商品品牌分类关联列表
     */
    @GetMapping("/list/tree")
    public R<List<SohuProductCategoryModel>> listTree() {
        return R.ok(remoteProductCategoryService.storeTree(null,getSysSource()));
    }

}
