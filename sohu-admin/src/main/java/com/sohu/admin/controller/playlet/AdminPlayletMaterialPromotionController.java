package com.sohu.admin.controller.playlet;

import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.playlet.PlayletMaterialPromotionOrderBo;
import com.sohu.middle.api.service.playlet.RemoteMiddlePlayletMaterialPromotionOrderService;
import com.sohu.middle.api.vo.playlet.PlayletMaterialPromotionOrderVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 素材推广订单控制器
 *
 * <AUTHOR>
 * @date 2024/9/23 18:24
 **/
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/playlet/material/promotion")
public class AdminPlayletMaterialPromotionController extends BaseController {

    @DubboReference
    private RemoteMiddlePlayletMaterialPromotionOrderService materialPromotionOrderService;

    @Operation(summary = "分销订单列表", description = "负责人：zc，分销管理-分销订单列表")
    @GetMapping("/distribution/aggrList")
    public TableDataInfo<PlayletMaterialPromotionOrderVo> distributionAggrList(PlayletMaterialPromotionOrderBo bo, PageQuery pageQuery) {
        return materialPromotionOrderService.distributionAggrList(bo, pageQuery);
    }

    @Operation(summary = "分销订单详情列表", description = "负责人：zc，分销管理-分销订单详情列表")
    @GetMapping("/distribution/list")
    public TableDataInfo<PlayletMaterialPromotionOrderVo> distributionList(PlayletMaterialPromotionOrderBo bo, PageQuery pageQuery) {
        return materialPromotionOrderService.distributionList(bo, pageQuery);
    }
}
