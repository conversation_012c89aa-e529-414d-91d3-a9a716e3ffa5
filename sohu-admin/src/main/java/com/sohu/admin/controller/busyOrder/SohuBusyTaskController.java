package com.sohu.admin.controller.busyOrder;

import com.sohu.busyorder.api.RemoteBusyTaskService;
import com.sohu.busyorder.api.bo.SohuBusyTaskAllListBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskSiteBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskSortBo;
import com.sohu.busyorder.api.domain.SohuBusyTaskReqBo;
import com.sohu.busyorder.api.domain.SohuBusyTaskSiteReqBo;
import com.sohu.busyorder.api.domain.SohuMcnBusyTaskSiteReqBo;
import com.sohu.busyorder.api.model.SohuBusyTaskModel;
import com.sohu.busyorder.api.model.SohuBusyTaskSiteModel;
import com.sohu.busyorder.api.vo.SohuBusyTaskAllListVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * admin商单新版-主任务/子任务
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/busy-order/busy/task")
public class SohuBusyTaskController extends BaseController {

    @DubboReference
    private RemoteBusyTaskService remoteBusyTaskService;

    /**
     * 查询主任务分页列表
     */
    @GetMapping("/page")
    public TableDataInfo<SohuBusyTaskModel> listPage(SohuBusyTaskReqBo bo, PageQuery pageQuery) {
        return remoteBusyTaskService.queryPageLists(bo, pageQuery);
    }

    /**
     * 查询子任务分页列表
     */
    @GetMapping("/child/page")
    public TableDataInfo<SohuBusyTaskSiteModel> listChildPage(SohuBusyTaskSiteReqBo bo, PageQuery pageQuery) {
        return remoteBusyTaskService.queryPageChildLists(bo, pageQuery);
    }

    /**
     * 查询子任务分页列表-待接单
     */
    @GetMapping("/queryPageOfOnShelf")
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageOfOnShelf(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        return remoteBusyTaskService.queryPageOfOnShelf(bo, pageQuery);
    }

    /**
     * 查询子任务分页列表-任务广场
     */
    @GetMapping("/task/page")
    public TableDataInfo<SohuBusyTaskSiteModel> taskListPage(SohuBusyTaskSiteReqBo bo, PageQuery pageQuery) {
        return remoteBusyTaskService.queryPageTaskLists(bo, pageQuery);
    }

    /**
     * mcn挑选任务列表
     */
    @GetMapping("/taskMcn/page")
    public TableDataInfo<SohuBusyTaskSiteModel> taskMcnListPage(SohuMcnBusyTaskSiteReqBo bo, PageQuery pageQuery) {
        return remoteBusyTaskService.queryPageMcnTaskList(bo, pageQuery);
    }

    /**
     * 查询我的子任务订单分页列表-任务方
     */
    @GetMapping("/child/my/page")
    public TableDataInfo<SohuBusyTaskSiteModel> listMyChildPage(SohuBusyTaskSiteReqBo bo, PageQuery pageQuery) {
        return remoteBusyTaskService.queryPageMyChildLists(bo, pageQuery);
    }

    /**
     * 查询主任务的子任务详情列表
     */
    @GetMapping("/child/list/{masterTaskNumber}")
    public R<List<SohuBusyTaskSiteModel>> getChildList(@NotNull(message = "主任务编号不能为空") @PathVariable String masterTaskNumber) {
        return R.ok(remoteBusyTaskService.getChildList(masterTaskNumber));
    }

    /**
     * 获取子任务主体详细信息
     *
     * @param taskNumber 子任务编号
     */
    @GetMapping("/child/{taskNumber}")
    public R<SohuBusyTaskSiteModel> getChildInfo(@NotNull(message = "子任务编号不能为空") @PathVariable String taskNumber) {
        return R.ok(remoteBusyTaskService.getChildInfo(taskNumber));
    }

    /**
     * 审核--子任务：超管、国家站、城市站使用
     *
     * @return {@link R}
     */
    @PostMapping("/child/audit")
    @Log(title = "审核子任务", businessType = BusinessType.UPDATE)
    public R<Boolean> auditChild(@RequestBody SohuBusyTaskSiteReqBo bo) {
        return R.ok(remoteBusyTaskService.auditChild(bo));
    }

    /**
     * 获取主任务主体详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<SohuBusyTaskModel> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteBusyTaskService.queryById(id));
    }

    /**
     * 新增任务主体--任务方
     */
    @Log(title = "任务主体", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuBusyTaskReqBo bo) {
        return R.ok(remoteBusyTaskService.insertByBo(bo));
    }

    /**
     * 修改任务主体--校验
     */
    @GetMapping("/exit/{taskNumber}")
    public R<Boolean> exitTask(@NotNull(message = "任务编号不能为空") @PathVariable String taskNumber) {
        return R.ok(remoteBusyTaskService.exitChildTask(taskNumber));
    }

    /**
     * 审核任务主体--校验
     */
    @GetMapping("/audit/{taskNumber}")
    public R<Boolean> exitAuditTask(@NotNull(message = "任务编号不能为空") @PathVariable String taskNumber, @NotNull(message = "任务状态不能为空") @RequestParam(value = "state") String state) {
        return R.ok(remoteBusyTaskService.exitAuditTask(taskNumber, state));
    }

    /**
     * 修改任务主体
     */
    @Log(title = "任务主体", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Long> edit(@Validated(EditGroup.class) @RequestBody SohuBusyTaskReqBo bo) {
        return R.ok(remoteBusyTaskService.updateByBo(bo));
    }

    /**
     * 修改子任务主体
     */
    @PutMapping("/child")
    @Log(title = "子任务修改", businessType = BusinessType.UPDATE)
    public R<Boolean> exitChildTask(@Validated(EditGroup.class) @RequestBody SohuBusyTaskSiteReqBo bo) {
        return R.ok(remoteBusyTaskService.updateByChildBo(bo));
    }

    /**
     * 删除任务主体
     *
     * @param ids 主键串
     */
    @Log(title = "任务主体", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(remoteBusyTaskService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 审核--主任务：超管、国家站使用
     *
     * @return {@link R}
     */
    @PostMapping("/audit")
    @Log(title = "审核", businessType = BusinessType.UPDATE)
    public R<Boolean> audit(@RequestBody SohuBusyTaskReqBo bo) {
        return R.ok(remoteBusyTaskService.audit(bo));
    }

    /**
     * 上下架--主任务：超管、国家站使用
     *
     * @return {@link R}
     */
    @PostMapping("/shelf")
    @Log(title = "上下架", businessType = BusinessType.UPDATE)
    public R<Boolean> shelf(@RequestBody SohuBusyTaskReqBo bo) {
        return R.ok(remoteBusyTaskService.shelf(bo));
    }

    /**
     * 上下架--子任务：超管、国家站、城市站（三个可以强制下架）、个人上下架
     *
     * @return {@link R}
     */
    @PostMapping("/child/shelf")
    @Log(title = "上下架", businessType = BusinessType.UPDATE)
    public R<Boolean> childShelf(@RequestBody SohuBusyTaskSiteReqBo bo) {
        return R.ok(remoteBusyTaskService.childShelf(bo));
    }

    /**
     * 调整商单排序和生效时间
     */
    @PutMapping("/sort")
    @Log(title = "调整商单排序和生效时间", businessType = BusinessType.UPDATE)
    @Operation(summary = "调整商单排序和生效时间", description = "调整商单排序和生效时间,负责人:张良峰")
    public R<Boolean> updateSortAndEffectiveTimeById(@RequestBody SohuBusyTaskSortBo bo) {
        return R.ok(remoteBusyTaskService.updateSortAndEffectiveTimeById(bo));
    }

    /**
     * 根据国家站长获取全部商单列表
     */
    @GetMapping("/all/list")
    @Operation(summary = "根据国家站长获取全部商单列表", description = "根据国家站长获取全部商单列表,负责人:张良峰")
    public TableDataInfo<SohuBusyTaskAllListVo> getAllListWithRole(SohuBusyTaskAllListBo bo, PageQuery pageQuery) {
        return remoteBusyTaskService.getAllListWithRole(bo, pageQuery);
    }

}
