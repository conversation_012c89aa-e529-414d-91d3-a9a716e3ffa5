package com.sohu.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuSiteBo;
import com.sohu.middle.api.bo.SohuSiteV2QueryBo;
import com.sohu.middle.api.service.RemoteMiddleSiteService;
import com.sohu.middle.api.vo.SohuSiteVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 站点V2版
 * 前端访问路由地址为:/admin/site/v2
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/site/v2")
public class SohuSiteV2Controller extends BaseController {

    @DubboReference
    private RemoteMiddleSiteService remoteMiddleSiteService;

    @GetMapping("/list")
    @Operation(summary = "查询站点列表")
    public TableDataInfo<SohuSiteVo> list(SohuSiteV2QueryBo bo, PageQuery pageQuery) {
        return remoteMiddleSiteService.queryPageListV2(bo, pageQuery);
    }

    @SaCheckPermission(value = "admin:site:add", orRole = {"kefuchaoguan"})
    @Log(title = "新增站点且完善站长认证信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Operation(summary = "新增站点且完善站长认证信息")
    public R<Boolean> addV2(@Validated(AddGroup.class) @RequestBody SohuSiteBo bo) {
        return toAjax(remoteMiddleSiteService.addV2(bo));
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取站点详细信息")
    public R<SohuSiteVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteMiddleSiteService.getInfoV2(id));
    }

    @SaCheckPermission(value = "admin:site:edit", orRole = {"kefuchaoguan"})
    @Log(title = "修改站点", businessType = BusinessType.UPDATE)
    @PutMapping()
    @Operation(summary = "修改站点")
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuSiteBo bo) {
        return toAjax(remoteMiddleSiteService.updateV2(bo));
    }

    @GetMapping("/bindStationCheck")
    @Operation(summary = "绑定站长校验站长是否符合条件")
    public R<Boolean> bindStationCheck(@RequestParam(name = "phone") String stationmasterPhone) {
        return R.ok(remoteMiddleSiteService.bindStationCheck(stationmasterPhone));
    }

}
