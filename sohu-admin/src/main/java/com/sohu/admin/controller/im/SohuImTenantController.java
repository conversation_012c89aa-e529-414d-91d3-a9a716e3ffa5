package com.sohu.admin.controller.im;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.im.SohuImTenantApplyActiveBo;
import com.sohu.middle.api.bo.im.SohuImTenantBo;
import com.sohu.middle.api.bo.im.SohuImTenantUpdateCodeBo;
import com.sohu.middle.api.service.im.RemoteMiddleImTenantService;
import com.sohu.middle.api.vo.im.SohuImTenantConfigVo;
import com.sohu.middle.api.vo.im.SohuImTenantInfoModel;
import com.sohu.middle.api.vo.im.SohuImTenantVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * im租户控制器
 *
 * <AUTHOR>
 * @date 2024/12/12 15:31
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/im/tenant")
public class SohuImTenantController extends BaseController {
    @DubboReference
    private RemoteMiddleImTenantService sohuImTenantService;

    /**
     * 获取IM租户详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("admin:tenant:query")
    @GetMapping("/{id}")
    public R<SohuImTenantVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(sohuImTenantService.queryById(id));
    }

    @Log(title = "租户分页列表", businessType = BusinessType.OTHER)
    @Operation(summary = "租户分页列表", description = "负责人：汪伟，租户分页列表")
    @GetMapping("/page")
    public TableDataInfo<SohuImTenantVo> pageList(SohuImTenantBo bo, PageQuery pageQuery) {
        return sohuImTenantService.queryPageList(bo, pageQuery);
    }

    @Log(title = "租户禁用/启用", businessType = BusinessType.UPDATE)
    @Operation(summary = "租户禁用/启用", description = "负责人：汪伟，租户禁用/启用")
    @GetMapping("/enable/{id}")
    public R<Boolean> enable(@PathVariable("id") Long id) {
        return R.ok(sohuImTenantService.enable(id));
    }

    @Operation(summary = "根据用户ID查询im租户", description = "author：phc")
    @GetMapping("/queryByUserId")
    public R<SohuImTenantVo> queryByUserId() {
        return R.ok(sohuImTenantService.queryByUserId(LoginHelper.getUserId()));
    }

    /**
     * 修改服务器编码
     */
    @Log(title = "修改服务器编码", businessType = BusinessType.UPDATE)
    @PostMapping("/updateServerCode")
    public R<Boolean> applyActive(@Validated @RequestBody SohuImTenantUpdateCodeBo bo) {
        return toAjax(sohuImTenantService.updateServerCode(bo));
    }

    /**
     * 申请激活IM服务
     */
    @Log(title = "申请激活IM服务", businessType = BusinessType.INSERT)
    @PostMapping("/applyActive")
    public R<Boolean> applyActive(@Validated @RequestBody SohuImTenantApplyActiveBo bo) {
        return toAjax(sohuImTenantService.applyActive(bo));
    }

    @SaCheckPermission("admin:imTenant:refresh")
    @Operation(summary = "刷新Im租户信息缓存", description = "author：phc")
    @GetMapping("/refreshImTenantInfoCache")
    public R<SohuImTenantInfoModel> refreshImTenantInfoCache(@NotEmpty(message = "服务器编码不能为空") @RequestParam String serverCode) {
        return R.ok(sohuImTenantService.refreshImTenantInfoCache(serverCode));
    }
}
