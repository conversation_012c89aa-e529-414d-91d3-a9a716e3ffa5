package com.sohu.admin.controller.shopGoods;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.RemoteProductService;
import com.sohu.shopgoods.api.bo.SohuProductAdBo;
import com.sohu.shopgoods.api.bo.SohuProductBo;
import com.sohu.shopgoods.api.domain.ShopProductReqBo;
import com.sohu.shopgoods.api.domain.SohuOffOrPutReqBo;
import com.sohu.shopgoods.api.domain.SohuProductReqBo;
import com.sohu.shopgoods.api.model.SohuProductModel;
import com.sohu.shopgoods.api.vo.SohuProductVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 商品控制器-商户store
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/shop-goods/product/store")
public class SohuProductStoreController extends BaseController {
    @DubboReference
    private RemoteProductService remoteProductService;

    /**
     * 查询商户商品列表--商户端
     */
    @Operation(summary = "查询商户商品列表", description = "负责人:张良峰,查询商户商品列表")
    @GetMapping("/list")
    public TableDataInfo<SohuProductModel> list(ShopProductReqBo bo, PageQuery pageQuery) {
        bo.setSysSource(getSysSource());
        return remoteProductService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询商户商品列表--上架
     */
    @Operation(summary = "查询商户商品列表-上架", description = "author:phc")
    @GetMapping("/listOfOnShelf")
    public TableDataInfo<SohuProductVo> listOfOnShelf(SohuProductAdBo bo, PageQuery pageQuery) {
        return remoteProductService.queryPageListOfOnShelf(bo, pageQuery);
    }

    /**
     * 导出商品列表--商户端
     */
    @Log(title = "商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ShopProductReqBo bo, HttpServletResponse response) {
        bo.setSysSource(getSysSource());
        List<SohuProductModel> list = remoteProductService.queryList(bo);
        ExcelUtil.exportExcel(list, "商品", SohuProductModel.class, response);
    }

    /**
     * 获取商品详细信息--商户端
     *
     * @param id 主键
     */
    @Operation(summary = "获取商品详细信息", description = "负责人:张良峰,获取商品详细信息")
    @GetMapping("/{id}")
    public R<SohuProductModel> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteProductService.queryById(id));
    }

    /**
     * 新增商品--商户端
     */
    @Operation(summary = "新增商品", description = "负责人:张良峰,新增商品")
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuProductReqBo bo) {
        bo.setSysSource(getSysSource());
        return toAjax(remoteProductService.insertByBo(bo));
    }

    /**
     * 修改商品--商户端
     */
    @Operation(summary = "修改商品", description = "负责人:张良峰,修改商品")
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuProductReqBo bo) {
        return toAjax(remoteProductService.updateByBo(bo));
    }

    /**
     * 删除商品--商户端
     *
     * @param ids 主键串
     */
    @Log(title = "商品", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(remoteProductService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 删除商品表--商户端
     *
     * @param id
     * @param type 类型：recycle——回收站 delete——彻底删除
     */
    @Operation(summary = "删除商品表", description = "负责人:张良峰,删除商品表")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.GET)
    public R<Boolean> delete(@RequestBody @PathVariable Long id,
                          @RequestParam(value = "type", required = false, defaultValue = "recycle",
                                  name = "type") String type) {
        return toAjax(remoteProductService.deleteByIdAndType(id, type));
    }

    /**
     * 恢复已删除商品表--商户端
     *
     * @param id Integer
     */
    @Operation(summary = "恢复已删除商品表", description = "负责人:张良峰,恢复已删除商品表")
    @GetMapping("/restore/{id}")
    public R<Boolean> restore(@PathVariable Long id) {
        return toAjax(remoteProductService.reStoreProduct(id));
    }

    /**
     * 上下架商品--商户端
     */
    @Operation(summary = "上下架商品", description = "负责人:张良峰,上下架商品")
    @PostMapping("/offOrPutShell")
    public R<Boolean> offOrPutShell(@RequestBody SohuOffOrPutReqBo bo) {
        return toAjax(remoteProductService.offOrPutShelf(bo));
    }

    /**
     * 分佣开关商品--商户端
     */
    @PostMapping("/independent")
    public R<Boolean> independentIsShow(@RequestBody ShopProductReqBo bo) {
        return toAjax(remoteProductService.independentIsShow(bo));
    }
}
