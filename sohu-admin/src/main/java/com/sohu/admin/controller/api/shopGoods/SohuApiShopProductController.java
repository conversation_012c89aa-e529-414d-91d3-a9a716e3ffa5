package com.sohu.admin.controller.api.shopGoods;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.RemoteProductService;
import com.sohu.shopgoods.api.domain.McnShopProductReqBo;
import com.sohu.shopgoods.api.domain.SohuMcnShopProductBo;
import com.sohu.shopgoods.api.model.SohuIndexProductModel;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品
 *
 * @author: zc
 * @date: 2023/7/21 10:13
 * @version: 1.0.0
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/shop-goods/product")
public class SohuApiShopProductController extends BaseController {

    @DubboReference
    private RemoteProductService productService;

    /**
     * MCN选品批量添加
     */
    @PostMapping("mcn/window/independent")
    public R<Boolean> insertByIds(@RequestBody SohuMcnShopProductBo bo) {
        return toAjax(productService.insertByIds(bo));
    }

    /**
     * MCN带货库分页
     */
    @GetMapping("/mcn/window")
    public TableDataInfo<SohuIndexProductModel> mcnShopWindowPage(McnShopProductReqBo bo, PageQuery pageQuery) {
        return productService.mcnShopWindowPage(bo, pageQuery);
    }

    /**
     * MCN带货库修改
     */
    @PutMapping("mcn/window")
    public R<Boolean> mcnWindowUpdate(@RequestBody SohuMcnShopProductBo bo) {
        return R.ok(productService.mcnWindowUpdate(bo));
    }

    /**
     * MCN带货库删除
     */
    @DeleteMapping("mcn/window/{ids}")
    public R<Boolean> mcnWindowDeleteByIds(@PathVariable("ids") List<Long> ids) {
        return R.ok(productService.mcnShopWindowDeleteByIds(ids));
    }
}
