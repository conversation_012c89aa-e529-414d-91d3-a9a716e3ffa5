package com.sohu.admin.controller.entry;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.entry.api.RemoteEntryAuthService;
import com.sohu.entry.api.bo.SohuEntryAuthAddBo;
import com.sohu.entry.api.bo.SohuEntryAuthAuditBo;
import com.sohu.entry.api.bo.SohuEntryAuthBo;
import com.sohu.entry.api.bo.SohuEntryAuthEditBo;
import com.sohu.entry.api.model.SohuEntryAuthModel;
import com.sohu.entry.api.model.SohuEntryIndustryModel;
import com.sohu.entry.api.model.SohuEntryRoleAuthModel;
import com.sohu.entry.api.vo.SohuEntryAuthInfoVo;
import com.sohu.entry.api.vo.SohuEntryAuthVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 入驻认证
 * 前端访问路由地址为:/entry/entryAuth
 *
 * <AUTHOR>
 * @date 2023-08-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/entry/entryAuth")
public class SohuEntryAuthController extends BaseController {

    @DubboReference
    private RemoteEntryAuthService remoteEntryAuthService;

    /**
     * 查询用户入驻行业认证列表(审核)
     */
//    @SaCheckPermission("entry:entryAuth:list")
    @GetMapping("/list")
    public TableDataInfo<SohuEntryAuthVo> list(SohuEntryAuthBo bo, PageQuery pageQuery) {
        return remoteEntryAuthService.queryPageList(bo,pageQuery);
    }

    /**
     * 查询用户入驻行业认证列表(自己的)
     */
//    @SaCheckPermission("entry:entryAuth:list")
    @GetMapping("/myList")
    public TableDataInfo<SohuEntryAuthVo> myList(SohuEntryAuthBo bo, PageQuery pageQuery) {
        bo.setUserId(LoginHelper.getUserId());
        return remoteEntryAuthService.queryPageList(bo,pageQuery);
    }

    /**
     * 获取用户入驻行业认证资料详细信息
     *
     * @param id 主键
     */
    //@SaCheckPermission("entry:entryAuth:query")
    @GetMapping("/{id}")
    public R<SohuEntryAuthVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteEntryAuthService.queryById(id));
    }

    /**
     * 新增用户入驻行业认证资料
     */
    //@SaCheckPermission("entry:entryAuth:add")
    @Log(title = "用户入驻行业认证资料", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuEntryAuthAddBo bo) {
        remoteEntryAuthService.insertByBo(bo);
        return R.ok(Boolean.TRUE);
    }

    /**
     * 修改用户入驻行业认证资料
     */
    //@SaCheckPermission("entry:entryAuth:edit")
    @Log(title = "用户入驻行业认证资料", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuEntryAuthEditBo bo) {
        remoteEntryAuthService.updateByBo(bo);
        return R.ok(Boolean.TRUE);
    }

    /**
     * 查询当前用户已入驻的角色及已认证行业（作废）
     */
    @GetMapping("/selectEntryRole")
    public R<SohuEntryRoleAuthModel> selectEntryRole() {
        return R.ok();
    }

    /**
     * 获取当前用户最新的一条行业认证数据（作废）
     */
    @GetMapping("/selectVoOfLastByUserId")
    public R<SohuEntryAuthModel> selectVoOfLastByUserId() {
        return R.ok();
    }

    /**
     * 审核行业资质
     */
    @Log(title = "审核行业资质", businessType = BusinessType.UPDATE)
    @PostMapping("/audit")
    public R<Boolean> audit(@Validated @RequestBody SohuEntryAuthAuditBo bo) {
        remoteEntryAuthService.audit(bo);
        return R.ok(Boolean.TRUE);
    }

    /**
     * 查询当前用户已认证行业资质
     */
    @GetMapping("/selectEntryAuthOfPass")
    public R<List<SohuEntryAuthVo>> selectEntryAuthOfPass() {
        return R.ok(remoteEntryAuthService.selectEntryAuthOfPass());
    }

    /**
     * 查询当前用户已认证行业（树形结构）
     */
    @GetMapping("/selectEntryIndustryOfPass")
    public R<List<SohuEntryIndustryModel>> selectEntryIndustryOfPass() {
        return R.ok(remoteEntryAuthService.selectEntryIndustryOfPass());
    }

    /**
     * 查询当前用户未认证的行业（树形结构）
     */
    @GetMapping("/selectEntryIndustryOfWait")
    public R<List<SohuEntryIndustryModel>> selectEntryIndustryOfWait() {
        return R.ok(remoteEntryAuthService.selectEntryIndustryOfWait());
    }

    @GetMapping("/getUserEntryAuth")
    @Operation(summary = "查询用户已认证的行业(二级)")
    public R<List<String>> getUserEntryAuth(@RequestParam Long userId) {
        return R.ok(remoteEntryAuthService.getUserEntryAuth(userId));
    }

    @GetMapping("/getUserEntryAuthInfo")
    @Operation(summary = "查询用户认证详情")
    public R<List<SohuEntryAuthInfoVo>> getUserEntryAuthInfo(@RequestParam Long userId) {
        return R.ok(remoteEntryAuthService.getUserEntryAuthInfo(userId));
    }

}
