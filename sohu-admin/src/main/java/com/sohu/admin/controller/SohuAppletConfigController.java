package com.sohu.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuAppletConfigBo;
import com.sohu.middle.api.service.RemoteMiddleAppletConfigService;
import com.sohu.middle.api.vo.SohuAppletConfigVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 小程序菜单配置
 * 前端访问路由地址为:/admin/appletConfig
 *
 * <AUTHOR>
 * @date 2023-06-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/applet/config")
public class SohuAppletConfigController extends BaseController {

    @DubboReference
    private RemoteMiddleAppletConfigService remoteMiddleAppletConfigService;

    /**
     * 分页查询列表
     */
    @SaCheckPermission("admin:appletConfig:list")
    @GetMapping("/list")
    public TableDataInfo<SohuAppletConfigVo> list(SohuAppletConfigBo bo, PageQuery pageQuery) {
        return remoteMiddleAppletConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 详情
     *
     * @param id 主键
     */
    @SaCheckPermission("admin:appletConfig:query")
    @GetMapping("/{id}")
    public R<SohuAppletConfigVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteMiddleAppletConfigService.queryById(id));
    }

    /**
     * 新增
     */
    @SaCheckPermission("admin:appletConfig:add")
    @Log(title = "新增小程序菜单配置", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuAppletConfigBo bo) {
        return toAjax(remoteMiddleAppletConfigService.insertByBo(bo));
    }

    /**
     * 修改
     */
    @SaCheckPermission("admin:appletConfig:edit")
    @Log(title = "修改小程序菜单配置", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuAppletConfigBo bo) {
        return toAjax(remoteMiddleAppletConfigService.updateByBo(bo));
    }

    /**
     * 删除
     *
     * @param ids 主键串
     */
    @SaCheckPermission("admin:appletConfig:remove")
    @Log(title = "批量删除小程序菜单配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(remoteMiddleAppletConfigService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
