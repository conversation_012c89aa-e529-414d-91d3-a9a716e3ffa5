package com.sohu.admin.controller.busyOrder;

import com.sohu.busyorder.api.RemoteBusyOrderService;
import com.sohu.busyorder.api.domain.vo.SohuBusyOrderRespVo;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.log.annotation.Log;
import com.sohu.middle.api.bo.SohuAssTagBo;
import com.sohu.middle.api.service.RemoteMiddleAssTagService;
import com.sohu.middle.api.vo.SohuAssTagVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 商单
 *
 * @author:<PERSON>
 * @create:2024/3/20 15:05
 **/
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/busy-order/order")
public class SohuBusyOrderController extends BaseController {

    @DubboReference
    private final RemoteBusyOrderService remoteBusyOrderService;
    @DubboReference
    private RemoteMiddleAssTagService remoteMiddleAssTagService;

    /**
     * 商单角色权限控制查询
     */
    @GetMapping("/roleControl")
    @Log(title = "商单角色权限控制查询")
    public R<List<SohuAssTagVo>> roleControlQuery(SohuAssTagBo bo) {
        return R.ok(remoteMiddleAssTagService.roleControlQuery(bo));
    }

    /**
     * 商单详情
     */
    @GetMapping("/{id}")
    @Log(title = "商单详情")
    public R<SohuBusyOrderRespVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteBusyOrderService.queryById(id));
    }

}
