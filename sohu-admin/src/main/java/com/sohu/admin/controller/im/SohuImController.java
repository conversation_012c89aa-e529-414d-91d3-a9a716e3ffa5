package com.sohu.admin.controller.im;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.im.api.bo.SohuImGroupDisableBo;
import com.sohu.im.api.bo.SohuImPageQueryBo;
import com.sohu.im.api.service.RemoteImService;
import com.sohu.im.api.vo.SohuImGroupVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 后台IM
 *
 * <AUTHOR>
 * @date 2025/01/15 10:00
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/im")
public class SohuImController extends BaseController {

    @DubboReference
    private RemoteImService remoteImService;

    @Operation(summary = "群列表分页查询", description = "负责人：彭会闯，分页查询群列表")
    @GetMapping("/group/page")
    public TableDataInfo<SohuImGroupVo> groupPage(SohuImPageQueryBo bo) {
        return remoteImService.groupPage(bo);
    }

    @Log(title = "后台群禁用", businessType = BusinessType.UPDATE)
    @Operation(summary = "后台群禁用", description = "负责人：柯真，群禁用成功，会发送socket消息给前端")
    @PostMapping("/group/disable")
    public R<Boolean> groupDisable(@Validated @RequestBody SohuImGroupDisableBo bo) {
        return R.ok(remoteImService.groupDisable(bo));
    }

    @Log(title = "后台群启用", businessType = BusinessType.UPDATE)
    @Operation(summary = "后台群启用", description = "负责人：柯真，后台群启用成功，即关闭禁用，会发送socket消息给前端")
    @PostMapping("/group/enable/{groupId}")
    public R<Boolean> groupEnable(@PathVariable Long groupId) {
        return R.ok(remoteImService.groupEnable(groupId));
    }

}
