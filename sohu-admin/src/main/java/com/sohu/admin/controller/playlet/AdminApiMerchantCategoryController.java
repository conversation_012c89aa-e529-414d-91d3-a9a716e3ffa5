package com.sohu.admin.controller.playlet;

import com.sohu.admin.api.vo.playlet.PlayletMerchantCategoryVo;
import com.sohu.admin.service.ISohuMerchantCategoryService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.playlet.PlayletMerchantCategoryBo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;

/**
 * 商户分类
 * 前端访问路由地址为:/admin/api/merchantCategory
 *
 * <AUTHOR>
 * @date 2024-09-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/merchant/category")
public class AdminApiMerchantCategoryController {

    private final ISohuMerchantCategoryService iSohuMerchantCategoryService;

    /**
     * 查询商户分类列表 - 不分页
     */
    @Operation(summary = "查询商户分类列表 - 不分页", description = "负责人：张良峰，商户后台管理-查询商户分类列表 - 不分页")
    @GetMapping("")
    public R<List<PlayletMerchantCategoryVo>> getMerchantCategoryList() {
        return R.ok(iSohuMerchantCategoryService.getMerchantCategoryList());
    }

    /**
     * 查询商户分类列表--分页
     */
    @Operation(summary = "查询商户分类列表--分页", description = "负责人：张良峰，商户后台管理-查询商户分类列表--分页")
    @GetMapping("/list")
    public TableDataInfo<PlayletMerchantCategoryVo> getMerchantCategoryPageList(PageQuery pageQuery) {
        return iSohuMerchantCategoryService.getMerchantCategoryPageList(pageQuery);
    }

    /**
     * 新增商户分类
     */
    @Operation(summary = "新增商户分类", description = "负责人：张良峰，商户后台管理-新增商户分类")
    @PostMapping()
    public R<Boolean> addMerchantCategory(@Validated(AddGroup.class) @RequestBody PlayletMerchantCategoryBo bo) {
        return R.ok(iSohuMerchantCategoryService.addMerchantCategory(bo));
    }

    /**
     * 修改商户分类
     */
    @Operation(summary = "修改商户分类", description = "负责人：张良峰，商户后台管理-修改商户分类")
    @PutMapping()
    public R<Boolean> editMerchantCategory(@Validated(EditGroup.class) @RequestBody PlayletMerchantCategoryBo bo) {
        return R.ok(iSohuMerchantCategoryService.editMerchantCategory(bo));
    }

    /**
     * 删除商户分类
     *
     * @param ids 主键串
     */
    @Operation(summary = "删除商户分类", description = "负责人：张良峰，商户后台管理-删除商户分类")
    @DeleteMapping("/{ids}")
    public R<Boolean> removeMerchantCategory(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return R.ok(iSohuMerchantCategoryService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
