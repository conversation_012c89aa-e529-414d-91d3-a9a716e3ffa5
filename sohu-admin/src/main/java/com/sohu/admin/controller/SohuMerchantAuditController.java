package com.sohu.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.admin.api.bo.*;
import com.sohu.admin.api.vo.*;
import com.sohu.admin.service.ISohuMerchantBrandService;
import com.sohu.admin.service.ISohuMerchantClassificationService;
import com.sohu.admin.service.ISohuMerchantService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 商户-审核相关
 *
 * <AUTHOR>
 * @date 2025/5/09 15:10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/merchant/audit")
public class SohuMerchantAuditController extends BaseController {

    private final ISohuMerchantClassificationService sohuMerchantClassificationService;
    private final ISohuMerchantBrandService sohuMerchantBrandService;
    private final ISohuMerchantService sohuMerchantService;

    @SaCheckPermission(value = "admin:merchant:audit", orRole = {"admin", "kefuchaoguan"})
    @Operation(summary = "分页查询商家经营类目审核列表", description = "负责人：柯真")
    @GetMapping("/category/page")
    public TableDataInfo<SohuMerchantCategoryAuditVo> categoryPage(SohuMerchantCategoryQueryBo bo, PageQuery pageQuery) {
        return sohuMerchantClassificationService.categoryPage(bo, pageQuery);
    }

    @SaCheckPermission(value = "admin:merchant:audit", orRole = {"admin", "kefuchaoguan"})
    @Operation(summary = "分页查询商家经营品牌审核列表", description = "负责人：柯真")
    @GetMapping("/brand/page")
    public TableDataInfo<SohuMerchantBrandAuditVo> brandPage(SohuMerchantBrandQueryBo bo, PageQuery pageQuery) {
        return sohuMerchantBrandService.brandPage(bo, pageQuery);
    }

    @SaCheckPermission(value = "admin:merchant:audit", orRole = {"admin", "kefuchaoguan"})
    @Operation(summary = "分页查询闭店审核列表", description = "负责人：柯真")
    @GetMapping("/closeStore/page")
    public TableDataInfo<SohuMerchantCloseStoreAuditVo> closeStorePage(SohuMerchantCloseStoreQueryBo bo, PageQuery pageQuery) {
        return sohuMerchantService.closeStorePage(bo, pageQuery);
    }

    @SaCheckPermission(value = "admin:merchant:audit", orRole = {"admin", "kefuchaoguan"})
    @Operation(summary = "商家经营类目审核详情", description = "负责人：柯真")
    @Parameter(name = "id", description = "审核详情ID", example = "1", required = true)
    @GetMapping("/category/{id}")
    public R<SohuMerchantCategoryDetailAuditVo> categoryDetail(@NotNull(message = "审核详情ID不能为空") @PathVariable Long id) {
        return R.ok(sohuMerchantService.categoryDetail(id));
    }

    @SaCheckPermission(value = "admin:merchant:audit", orRole = {"admin", "kefuchaoguan"})
    @Operation(summary = "商家经营品牌审核详情", description = "负责人：柯真")
    @Parameter(name = "id", description = "审核详情ID", example = "1", required = true)
    @GetMapping("/brand/{id}")
    public R<SohuMerchantBrandDetailAuditVo> brandDetail(@NotNull(message = "审核详情ID不能为空") @PathVariable Long id) {
        return R.ok(sohuMerchantService.brandDetail(id));
    }

    @SaCheckPermission(value = "admin:merchant:audit", orRole = {"admin", "kefuchaoguan"})
    @Operation(summary = "商家闭店审核详情", description = "负责人：柯真")
    @Parameter(name = "id", description = "审核详情ID", example = "1", required = true)
    @GetMapping("/closeStore/{id}")
    public R<SohuMerchantCloseStoreDetailAuditVo> closeStoreDetail(@NotNull(message = "审核详情ID不能为空") @PathVariable Long id) {
        return R.ok(sohuMerchantService.closeStoreDetail(id));
    }

    @SaCheckPermission(value = "admin:merchant:audit", orRole = {"admin", "kefuchaoguan"})
    @Operation(summary = "商家经营类目审核", description = "负责人：柯真")
    @Log(title = "商家经营类目审核", businessType = BusinessType.UPDATE)
    @PostMapping("/category")
    public R<Boolean> auditCategory(@RequestBody SohuMerchantClassificationBo bo) {
        return R.ok(sohuMerchantClassificationService.updateByBo(bo));
    }

    @SaCheckPermission(value = "admin:merchant:audit", orRole = {"admin", "kefuchaoguan"})
    @Operation(summary = "商家经营品牌审核", description = "负责人：柯真")
    @Log(title = "商家经营品牌审核", businessType = BusinessType.UPDATE)
    @PostMapping("/brand")
    public R<Boolean> auditBrand(@RequestBody SohuMerchantBrandBo bo) {
        return R.ok(sohuMerchantBrandService.updateByBo(bo));
    }

    @SaCheckPermission(value = "admin:merchant:audit", orRole = {"admin", "kefuchaoguan"})
    @Operation(summary = "商家闭店审核", description = "负责人：柯真")
    @Log(title = "商家闭店审核", businessType = BusinessType.UPDATE)
    @PostMapping("/closeStore")
    public R<Boolean> closeStore(@RequestBody SohuMerchantAuditBo bo) {
        return R.ok(sohuMerchantService.closeStore(bo));
    }

}
