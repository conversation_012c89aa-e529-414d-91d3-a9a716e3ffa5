package com.sohu.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.sohu.admin.api.bo.playlet.PlayletMerchantSearchBo;
import com.sohu.admin.api.vo.playlet.PlayletMerchantApplyListVo;
import com.sohu.admin.service.ISohuMerchantService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuMerchantApplyBo;
import com.sohu.middle.api.bo.SohuMerchantBo;
import com.sohu.middle.api.bo.SohuMerchantSearchBo;
import com.sohu.middle.api.enums.AuditState;
import com.sohu.middle.api.vo.SohuMerchantVo;
import com.sohu.system.api.RemoteUserService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 商户-狐少少
 * 前端访问路由地址为:/admin/merchant
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/merchant")
public class SohuMerchantController extends BaseController {

    private final ISohuMerchantService iSohuMerchantService;
    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 查询商户-狐少少列表
     */
    //@SaCheckPermission("admin:merchant:list")
    //@SaCheckRole(value = {"admin", "sysPlaylet", "shop", "merchant", "sysSohu" }, mode = SaMode.OR)
    @SaCheckPermission(value = "admin:merchant:list",orRole = {"admin", "shop", "kefuchaoguan" })
    @GetMapping("/list")
    @Log(title = "查询商户-狐少少列表")
    public TableDataInfo<SohuMerchantVo> list(SohuMerchantBo bo, PageQuery pageQuery) {
        bo.setSysSource(getSysSource());
        return iSohuMerchantService.queryPageList(bo, pageQuery);
    }

    /**
     * 商户入驻分页列表
     */
    //@SaCheckPermission("admin:merchant:list")
    //@SaCheckRole(value = {"admin", "sysPlaylet", "shop", "merchant", "sysSohu" }, mode = SaMode.OR)
    @SaCheckPermission(value = "admin:merchant:list",orRole = {"admin", "shop", "kefuchaoguan" })
    @GetMapping("/apply/list")
    @Log(title = "商户入驻分页列表")
    public TableDataInfo<SohuMerchantVo> applyList(SohuMerchantSearchBo bo, PageQuery pageQuery) {
        bo.setSysSource(getSysSource());
        return iSohuMerchantService.getPageList(bo, pageQuery);
    }

    /**
     * 导出商户-狐少少列表
     */
    @Hidden
    //@SaCheckPermission("admin:merchant:export")
    //@SaCheckRole(value = {"admin", "sysPlaylet", "shop", "merchant", "sysSohu" }, mode = SaMode.OR)
    @SaCheckPermission(value = "admin:merchant:export",orRole = {"admin", "shop", "kefuchaoguan" })
    @Log(title = "导出商户-狐少少列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuMerchantBo bo, HttpServletResponse response) {
        bo.setSysSource(getSysSource());
        List<SohuMerchantVo> list = iSohuMerchantService.queryList(bo);
        ExcelUtil.exportExcel(list, "商户-狐少少", SohuMerchantVo.class, response);
    }

    /**
     * 狐少少店铺申请及重新申请
     */
    //@SaCheckPermission("admin:merchant:add")
    @Log(title = "狐少少店铺申请及重新申请", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuMerchantBo bo) {
        return toAjax(iSohuMerchantService.insertByBo(bo));
    }

    @Log(title = "狐少少店铺申请及重新申请", businessType = BusinessType.INSERT)
    @Operation(summary = "商户申请及重新申请", description = "负责人：汪伟，新增申请接口，不影响老接口的调用")
    @PostMapping("/apply")
    public R<Boolean> apply(@Validated(AddGroup.class) @RequestBody SohuMerchantApplyBo bo) {
        bo.setSysSource(getSysSource());
        return toAjax(iSohuMerchantService.applyByBo(bo));
    }

    /**
     * 狐少少入驻修改（修改备注）
     */
    //@SaCheckPermission("admin:merchant:edit")
    @Log(title = "狐少少入驻修改（修改备注）", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuMerchantBo bo) {
        bo.setSysSource(getSysSource());
        return toAjax(iSohuMerchantService.updateByBo(bo));
    }

    /**
     * 狐少少入驻审核
     */
    //@SaCheckPermission("admin:merchant:edit")
    //@SaCheckRole(value = {"admin", "sysPlaylet", "shop", "merchant", "sysSohu" }, mode = SaMode.OR)
    @SaCheckPermission(value = "admin:merchant:audit",orRole = {"admin", "kefuchaoguan" })
    @Log(title = "狐少少入驻审核", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public R<Boolean> audit(@RequestBody SohuMerchantBo bo) {
        bo.setSysSource(getSysSource());
        return toAjax(iSohuMerchantService.audit(bo));
    }

    /**
     * 删除商户-狐少少
     */
    //@SaCheckPermission("admin:merchant:remove")
    //@SaCheckRole(value = {"admin", "sysPlaylet", "shop", "merchant", "sysSohu" }, mode = SaMode.OR)
    @SaCheckPermission(value = "admin:merchant:remove",orRole = {"admin", "kefuchaoguan" })
    @Log(title = "删除商户-狐少少", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuMerchantService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 获取用户商户信息 - 商户列表
     */
    //@SaCheckPermission("admin:merchant:query")
    @GetMapping("/info/list")
    @Log(title = "获取用户商户信息 - 商户列表")
    public TableDataInfo<SohuMerchantVo> getInfoList(SohuMerchantSearchBo bo, PageQuery pageQuery) {
        bo.setSysSource(getSysSource());
        return iSohuMerchantService.getInfoList(bo, pageQuery);
    }

    /**
     * 获取用户通过商户信息
     */
    //@SaCheckPermission("admin:merchant:query")
    @GetMapping("/info/merchant")
    @Log(title = "获取用户商户信息 - 商户列表")
    public TableDataInfo<SohuMerchantVo> infoMerchant(SohuMerchantSearchBo bo, PageQuery pageQuery) {
        bo.setAuditStatus(AuditState.Pass.name());
        bo.setSysSource(getSysSource());
        return iSohuMerchantService.getInfoList(bo, pageQuery);
    }

    /**
     * 获取用户商户信息 - 具体某一个
     */
    //@SaCheckPermission("admin:merchant:query")
    @GetMapping("/info/{id}")
    @Log(title = "获取用户商户信息 - 具体某一个")
    public R<SohuMerchantVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuMerchantService.getInfo(id));
    }

    /**
     * 商户端商户信息修改
     */
    //@SaCheckPermission("admin:merchant:update")
    @PostMapping("/info/update")
    @Log(title = "商户端商户信息修改")
    public R<Boolean> updateInfo(@Validated(EditGroup.class) @RequestBody SohuMerchantBo bo) {
        return toAjax(iSohuMerchantService.updateInfo(bo));
    }

    /**
     * 商户密码修改
     */
    //@SaCheckPermission("admin:merchant:update")
    //@SaCheckRole(value = {"admin", "sysPlaylet", "shop", "merchant", "sysSohu" }, mode = SaMode.OR)
    @SaCheckPermission(value = "admin:merchant:update:pwd",orRole = {"admin", "shop","kefuchaoguan" })
    @PutMapping("/update/pwd")
    @Log(title = "商户密码修改")
    @Parameters({
        @Parameter(name = "userId", description = "用户id", example = "1"),
        @Parameter(name = "password", description = "密码", example = "123456")
    })
    public R<Boolean> updatePwd(Long userId, String password) {
        return toAjax(remoteUserService.updateUserPwd(userId, password));
    }


    /**
     * 修改复制商品数量
     */
    //@SaCheckPermission("admin:merchant:update")
    //@SaCheckRole(value = {"admin", "sysPlaylet", "shop", "merchant", "sysSohu" }, mode = SaMode.OR)
    @SaCheckPermission(value = "admin:merchant:update",orRole = {"admin", "shop","kefuchaoguan" })
    @PutMapping("/update/copy/product/num")
    @Log(title = "修改复制商品数量")
    @Parameters({
        @Parameter(name = "id", description = "商户id", example = "1"),
        @Parameter(name = "num", description = "数量", example = "5"),
        @Parameter(name = "type", description = "修改类型：add/sub", example = "5")
    })
    public R<Boolean> updateCopyProduct(Long id, String type, Long num) {
        return toAjax(iSohuMerchantService.updateCopyProduct(id, type, num));
    }

    /**
     * 商户开关切换事件
     */
    //@SaCheckPermission("admin:merchant:query")
    @PostMapping("/switch/{id}")
    @Log(title = "商户开关切换事件")
    public R<Boolean> updateSwitch(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(iSohuMerchantService.updateSwitch(id));
    }

    /**
     * 商户推荐切换事件
     */
    //@SaCheckPermission("admin:merchant:query")
    @PostMapping("/recommend/{id}")
    @Log(title = "商户推荐切换事件")
    public R<Boolean> recommend(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(iSohuMerchantService.recommend(id));
    }

    @Operation(summary = "获取商户申请记录", description = "负责人：汪伟 获取商户申请记录")
    @GetMapping("/applyByUser")
    public TableDataInfo<PlayletMerchantApplyListVo> getMerchantApplyList(PlayletMerchantSearchBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        bo.setUserId(loginUser.getUserId());
        bo.setSysSource(getSysSource());
        return iSohuMerchantService.getMerchantApplyList(bo, pageQuery,false);
    }

}
