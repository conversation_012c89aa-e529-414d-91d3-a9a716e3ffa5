package com.sohu.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.admin.api.bo.SohuAgentUserBo;
import com.sohu.admin.api.vo.SohuAgentChannelTopVo;
import com.sohu.admin.api.vo.SohuAgentStatVo;
import com.sohu.admin.api.vo.SohuAgentUserDetailVo;
import com.sohu.admin.api.vo.SohuAgentUserVo;
import com.sohu.admin.service.ISohuAgentUserService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.SohuIndependentObject;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.entry.api.RemoteEntryService;
import com.sohu.entry.api.vo.SohuAccountEnterVo;
import com.sohu.middle.api.service.RemoteMiddleTradeRecordService;
import com.sohu.middle.api.vo.SohuTradeRecordDetailIndependentVo;
import com.sohu.pay.api.RemoteIndependentOrderService;
import com.sohu.pay.api.vo.SohuIndependentOrderVo;
import com.sohu.report.api.RemoteIncomeStatisticsService;
import com.sohu.report.api.bo.SohuUserIncomeStatisticsInfoBo;
import com.sohu.report.api.vo.SohuAgentRetentionVo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsInfoVo;
import com.sohu.shoporder.api.RemoteShopOrderService;
import com.sohu.shoporder.api.model.SohuShopOrderModel;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 代理用户记录
 * 前端访问路由地址为:/admin/agentUser
 *
 * <AUTHOR>
 * @date 2024-01-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/agentUser")
public class SohuAgentUserController extends BaseController {

    private final ISohuAgentUserService iSohuAgentUserService;
    @DubboReference
    private RemoteIncomeStatisticsService remoteIncomeStatisticsService;
    @DubboReference
    private RemoteMiddleTradeRecordService remoteMiddleTradeRecordService;
    @DubboReference
    private RemoteEntryService remoteEntryService;
    @DubboReference
    private RemoteIndependentOrderService remoteIndependentOrderService;
    @DubboReference
    private RemoteShopOrderService remoteShopOrderService;

    /**
     * 查询代理用户记录列表
     */
//    @SaCheckPermission("system:agentUser:list")
    @GetMapping("/list")
    public TableDataInfo<SohuAgentUserVo> list(SohuAgentUserBo bo, PageQuery pageQuery) {
        return iSohuAgentUserService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出代理用户记录列表
     */
    @Hidden
    @SaCheckPermission("system:agentUser:export")
    @Log(title = "代理用户记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuAgentUserBo bo, HttpServletResponse response) {
        List<SohuAgentUserVo> list = iSohuAgentUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "代理用户记录", SohuAgentUserVo.class, response);
    }

    /**
     * 获取代理用户记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission(value = "system:agentUser:query", orRole = {"kefuchaoguan"})
    @GetMapping("/{id}")
    public R<SohuAgentUserVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuAgentUserService.queryById(id));
    }

    /**
     * 获取代理用户详细信息
     */
    @GetMapping("/getInfo")
    public R<SohuAgentUserVo> getInfo(@RequestParam(value = "siteType", required = false) Integer siteType,
                                      @RequestParam(value = "siteId", required = false) Long siteId,
                                      @RequestParam(value = "userRole", required = false) String userRole) {
        return R.ok(iSohuAgentUserService.getInfo(siteType, siteId, userRole));
    }

    /**
     * 获取商户结算信息
     *
     * @return
     */
    @GetMapping("/getMerInfo")
    public R<SohuAgentUserVo> getMerInfo() {
        return R.ok(iSohuAgentUserService.getMerInfo());
    }

    /**
     * 新增代理用户记录
     */
    @SaCheckPermission("system:agentUser:add")
    @Log(title = "代理用户记录", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuAgentUserBo bo) {
        return toAjax(iSohuAgentUserService.insertByBo(bo));
    }

    /**
     * 修改代理用户记录
     */
    @SaCheckPermission("system:agentUser:edit")
    @Log(title = "代理用户记录", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuAgentUserBo bo) {
        return toAjax(iSohuAgentUserService.updateByBo(bo));
    }

    /**
     * 删除代理用户记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:agentUser:remove")
    @Log(title = "代理用户记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuAgentUserService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 获取代理首页信息
     */
    @GetMapping("/getAgentStatInfo")
    public R<SohuAgentStatVo> getAgentStatInfo(@RequestParam(value = "startDate") String startDate,
                                               @RequestParam(value = "endDate") String endDate) {
        return R.ok(iSohuAgentUserService.getAgentStatInfo(startDate, endDate));
    }

    /**
     * 获取渠道客户数Top
     */
    @Operation(summary = "获取渠道客户数Top", description = "负责人：张良峰，代理商首页-渠道客户数Top")
    @GetMapping("/getChannelTop")
    public R<List<SohuAgentChannelTopVo>> getChannelTop(String startDate, String endDate) {
        return R.ok(iSohuAgentUserService.getChannelTop(startDate, endDate));
    }

    /**
     * 获取客户留存明细
     */
    @Operation(summary = "获取客户留存明细", description = "负责人：张良峰，代理商首页-客户留存明细")
    @GetMapping("/getRetentionDetail")
    public R<List<SohuAgentRetentionVo>> getRetentionDetail(String startDate, String endDate) {
        return R.ok(iSohuAgentUserService.getRetentionDetail(startDate, endDate));
    }

    /**
     * 查询用户是否入驻代理或MCN机构-作废
     */
    @Deprecated
    @Operation(summary = "查询用户是否入驻代理或MCN机构-作废", description = "作废。新接口参考：/admin/agentUser/queryUserEntry/{roleKey}")
    @GetMapping("/checkUserEntry/{roleKey}")
    public R<Map<String, String>> checkUserEntry(@PathVariable String roleKey) {
        return R.ok(iSohuAgentUserService.checkUserEntry(roleKey));
    }

    /**
     * 查询用户是否入驻代理或MCN机构
     */
    @Operation(summary = "查询用户入驻信息", description = "author:phc")
    @GetMapping("/queryUserEntry/{roleKey}")
    public R<SohuAccountEnterVo> queryUserEntry(@PathVariable String roleKey) {
        return R.ok(remoteEntryService.queryIncludeAccountByUserIdAndRoleKey(LoginHelper.getUserId(), roleKey));
    }

    @Operation(summary = "代理商邀请成功数", description = "负责人：张明明，代理商首页-邀请成功数")
    @GetMapping("/inviteSuccess")
    public R<Long> inviteSuccess() {
        return R.ok(iSohuAgentUserService.inviteSuccess());
    }

    @Operation(summary = "代理服务商详情信息", description = "负责人：汪伟，代理服务商详情信息")
    @GetMapping("/detail/{id}")
    public R<SohuAgentUserDetailVo> detailInfo(@PathVariable Long id) {
        return R.ok(iSohuAgentUserService.detailInfo(id));
    }

    @Operation(summary = "收益明细", description = "负责人：汪伟，代理服务商收益明细")
    @GetMapping("/income/list")
    public TableDataInfo<SohuUserIncomeStatisticsInfoVo> incomeList(SohuUserIncomeStatisticsInfoBo bo, PageQuery pageQuery) {
        bo.setUserId(LoginHelper.getUserId());
        return remoteIncomeStatisticsService.queryIncomePageList(bo, pageQuery);
    }

    @Operation(summary = "收益明细导出", description = "负责人：汪伟，收益明细导出")
    @GetMapping("/income/export")
    public void incomeExport(SohuUserIncomeStatisticsInfoBo bo, HttpServletResponse response) {
        bo.setUserId(LoginHelper.getUserId());
        List<SohuUserIncomeStatisticsInfoVo> list = remoteIncomeStatisticsService.queryIncomeList(bo);
        ExcelUtil.exportExcel(list, "收益明细", SohuUserIncomeStatisticsInfoVo.class, response);
    }

    @Operation(summary = "收益明细详情", description = "负责人：汪伟，收益明细详情")
    @GetMapping("/income/detail/{id}")
    public R<SohuTradeRecordDetailIndependentVo> incomeDetail(@PathVariable(value = "id") Long id) {
        SohuUserIncomeStatisticsInfoVo infoVo = remoteIncomeStatisticsService.queryIncomeById(id);
        if (Objects.nonNull(infoVo)) {
            SohuIndependentOrderVo sohuIndependentOrderVo = remoteIndependentOrderService.queryById(infoVo.getIndependentOrderId());
            String orderNo = sohuIndependentOrderVo.getOrderNo();
            if (BusyType.Goods.name().equals(sohuIndependentOrderVo.getTradeType())) {
                SohuShopOrderModel shopOrderModel = remoteShopOrderService.getByOrderNo(orderNo);
                orderNo = shopOrderModel.getMasterOrderNo();
            }
            //查询明细
            return R.ok(remoteMiddleTradeRecordService.taskDetail(null, orderNo, sohuIndependentOrderVo.getIndependentObject(), sohuIndependentOrderVo.getIndependentStatus()));
        }
        return R.ok();
    }

    @Operation(summary = "最近到账收益", description = "负责人：柯真，代理服务商最近到账收益")
    @GetMapping("/income/newest")
    public R<List<SohuUserIncomeStatisticsInfoVo>> incomeNewest() {
        return R.ok(remoteIncomeStatisticsService.incomeNewest());
    }

}
