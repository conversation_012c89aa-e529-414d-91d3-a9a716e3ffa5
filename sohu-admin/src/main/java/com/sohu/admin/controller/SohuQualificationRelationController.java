package com.sohu.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.admin.api.bo.SohuQualificationRelationBo;
import com.sohu.admin.api.vo.SohuQualificationRelationVo;
import com.sohu.admin.service.ISohuQualificationRelationService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 资质关联控制器
 * 前端访问路由地址为:/system/qualificationRelation
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/qualificationRelation")
public class SohuQualificationRelationController extends BaseController {

    private final ISohuQualificationRelationService iSohuQualificationRelationService;

    /**
     * 查询资质关联列表
     */
    @SaCheckPermission("system:qualificationRelation:list")
    @GetMapping("/list")
    public TableDataInfo<SohuQualificationRelationVo> list(SohuQualificationRelationBo bo, PageQuery pageQuery) {
        return iSohuQualificationRelationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出资质关联列表
     */
    @SaCheckPermission("system:qualificationRelation:export")
    @Log(title = "资质关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuQualificationRelationBo bo, HttpServletResponse response) {
        List<SohuQualificationRelationVo> list = iSohuQualificationRelationService.queryList(bo);
        ExcelUtil.exportExcel(list, "资质关联", SohuQualificationRelationVo.class, response);
    }

    /**
     * 获取资质关联详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:qualificationRelation:query")
    @GetMapping("/{id}")
    public R<SohuQualificationRelationVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuQualificationRelationService.queryById(id));
    }

    /**
     * 新增资质关联
     */
    @SaCheckPermission("system:qualificationRelation:add")
    @Log(title = "资质关联", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuQualificationRelationBo bo) {
        return toAjax(iSohuQualificationRelationService.insertByBo(bo));
    }

    /**
     * 修改资质关联
     */
    @SaCheckPermission("system:qualificationRelation:edit")
    @Log(title = "资质关联", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuQualificationRelationBo bo) {
        return toAjax(iSohuQualificationRelationService.updateByBo(bo));
    }

    /**
     * 删除资质关联
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:qualificationRelation:remove")
    @Log(title = "资质关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuQualificationRelationService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
