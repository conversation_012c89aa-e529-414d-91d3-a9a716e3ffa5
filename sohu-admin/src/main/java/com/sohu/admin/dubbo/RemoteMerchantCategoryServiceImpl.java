package com.sohu.admin.dubbo;

import com.sohu.admin.api.RemoteMerchantCategoryService;
import com.sohu.admin.api.model.SohuMerchantCategoryModel;
import com.sohu.admin.service.ISohuMerchantCategoryService;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.middle.api.vo.SohuMerchantCategoryVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商户分类服务
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteMerchantCategoryServiceImpl implements RemoteMerchantCategoryService {

    private final ISohuMerchantCategoryService merchantCategoryService;

    @Override
    public List<SohuMerchantCategoryModel> pageList() {
        List<SohuMerchantCategoryVo> vo = merchantCategoryService.pageList();
        return BeanCopyUtils.copyList(vo, SohuMerchantCategoryModel.class);
    }
}
