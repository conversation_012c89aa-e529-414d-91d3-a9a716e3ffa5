<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.admin.mapper.SohuMerchantMapper">

    <select id="getPageList" parameterType="com.sohu.admin.api.bo.SohuMerchantAdminBo"
            resultType="com.sohu.admin.api.vo.SohuMerchantAdminVo">
        SELECT
        sm.id,
        sm.`name`,
        sm.merchant_type,
        sm.user_id,
        sm.phone,
        sa.license_address,
        sa.license_province_name,
        sa.license_city_name,
        sa.license_area_name,
        sm.audit_status,
        sm.create_time
        FROM
        `sohu_merchant` sm
        LEFT JOIN sohu_account sa ON sm.user_id = sa.user_id
        WHERE
        1=1
        <if test="bo.name != null and bo.name != ''">
            AND sm.`name` LIKE CONCAT('%', #{bo.name}, '%')
        </if>
        <if test="bo.auditStatus != null and bo.auditStatus != ''">
            AND sm.audit_status = #{bo.auditStatus}
        </if>
        <if test="bo.merchantType != null and bo.merchantType != ''">
            AND sm.merchant_type = #{bo.merchantType}
        </if>
        <if test="bo.licenseProvinceCode != null and bo.licenseProvinceCode != ''">
            AND sa.license_province_code = #{bo.licenseProvinceCode}
        </if>
        <if test="bo.licenseCityCode != null and bo.licenseCityCode != ''">
            AND sa.license_city_code = #{bo.licenseCityCode}
        </if>
        <if test="bo.licenseAreaCode != null and bo.licenseAreaCode != ''">
            AND sa.license_area_code = #{bo.licenseAreaCode}
        </if>
        order by sm.create_time desc
    </select>

    <select id="selectCategoryList"
            resultType="com.sohu.admin.api.vo.SohuMerchantClassificationQualificationVo">
        select smcq.*,smc.state,smc.cate_id as cateId
        from sohu_merchant_classification smc
        LEFT JOIN sohu_merchant_classification_qualification smcq
        ON smc.mer_id = smcq.mer_id AND smc.cate_id = smcq.cate_id AND smc.relate_no = smcq.relate_no
        WHERE smc.mer_id = #{merId}
        <if test="relateNo != null and relateNo != ''">
            AND smc.relate_no= #{relateNo}
        </if>
        <if test="stateList != null">
            AND smc.state IN
            <foreach item="state" collection="stateList" open="(" separator="," close=")">
                #{state}
            </foreach>
        </if>
    </select>

    <select id="selectBrandList" resultType="com.sohu.admin.api.vo.SohuMerchantBrandQualificationVo">
        select smbq.*,smb.state,smb.brand_id as brandId from sohu_merchant_brand smb LEFT JOIN sohu_merchant_brand_qualification smbq ON
        smb.mer_id = smbq.mer_id AND smb.brand_id = smbq.brand_id AND smb.relate_no = smbq.relate_no
        WHERE smb.mer_id = #{merId}
        <if test="relateNo != null and relateNo != ''">
            AND smb.relate_no= #{relateNo}
        </if>
        <if test="stateList != null">
            AND smb.state IN
            <foreach item="state" collection="stateList" open="(" separator="," close=")">
                #{state}
            </foreach>
        </if>
    </select>

    <update id="freezeMerchant">
        update sohu_merchant
        set audit_status = 'FreezeUpgrade',
            is_switch    = 0
        where user_id = #{userId} and id != #{merchantId} and audit_status = 'Pass'
    </update>
</mapper>
