<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.admin.mapper.SohuMerchantBondMapper">

    <select id="selectListByStatus" resultType="com.sohu.admin.api.vo.SohuMerchantBondVo">
        SELECT
        smb.id,
        smb.mer_id,
        smb.cate_id,
        smb.gmv_amount,
        smb.bond_amount,
        smb.paid_amount,
        smb.pay_status,
        smb.gmv_interval
        FROM
        `sohu_merchant_bond` smb
        WHERE
        smb.mer_id = #{merchantId} and smb.pay_status in
        <foreach collection="payStatus" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>