<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.admin.mapper.SohuAiOrderMapper">

    <resultMap type="com.sohu.admin.domain.SohuAiOrder" id="SohuAiOrderResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="priceId" column="price_id"/>
        <result property="priceName" column="price_name"/>
        <result property="orderNo" column="order_no"/>
        <result property="payPrice" column="pay_price"/>
        <result property="totalNum" column="total_num"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="duration" column="duration"/>
        <result property="chatCount" column="chat_count"/>
        <result property="paid" column="paid"/>
        <result property="isCancel" column="is_cancel"/>
        <result property="orderNumber" column="order_number"/>
        <result property="payTime" column="pay_time"/>
        <result property="payType" column="pay_type"/>
        <result property="outTradeNo" column="out_trade_no"/>
        <result property="transactionId" column="transaction_id"/>
        <result property="status" column="status"/>
        <result property="rightCategory" column="right_category"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
