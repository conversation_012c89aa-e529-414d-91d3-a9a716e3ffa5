<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.system.mapper.SohuPolicyInterfaceMapper">

    <resultMap type="com.sohu.admin.domain.SohuPolicyInterface" id="SohuPolicyInterfaceResult">
        <result property="id" column="id"/>
        <result property="policyId" column="policy_id"/>
        <result property="interfaceId" column="interface_id"/>
        <result property="restrictionType" column="restriction_type"/>
        <result property="createTime" column="create_time"/>
    </resultMap>


</mapper>
