package com.sohu.pay.mapper;

import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.pay.api.vo.SohuIndependentOrderAggrVo;
import com.sohu.pay.api.vo.SohuIndependentOrderVo;
import com.sohu.pay.api.vo.SohuUserOrderModel;
import com.sohu.pay.domain.SohuIndependentOrder;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 第三方分账单Mapper接口
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
public interface SohuIndependentOrderMapper extends BaseMapperPlus<SohuIndependentOrderMapper, SohuIndependentOrder, SohuIndependentOrderVo> {

    /**
     * 更新分销单状态
     *
     * @param orderNo
     * @param tradeType
     * @param independentStatus
     */
    void updateIndependentStatus(@Param("orderNo") String orderNo, @Param("tradeType") String tradeType, @Param("independentStatus") int independentStatus);

    /**
     * 更新分销单状态
     *
     * @param orderNo
     * @param independentStatus
     */
    void updateIndependentStatusByOrderNo(@Param("orderNo") String orderNo, @Param("independentStatus") int independentStatus);

    /**
     * 查询商品分销统计或者商单分销统计 的金额总和
     *
     * @param userId            用户ID
     * @param tradeType         {@link com.sohu.common.core.enums.BusyType}
     * @param independentStatus
     * @param startTime
     * @param endTime
     * @return
     */
    BigDecimal selectSumDistributionAmount(@Param("userId") Long userId, @Param("tradeType") String tradeType,
                                           @Param("independentStatus") Integer independentStatus, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询商品分销统计或者商单分销统计 的金额总和
     *
     * @param userId            用户ID
     * @param tradeType         {@link com.sohu.common.core.enums.BusyType}
     * @param independentStatus
     * @param startTime
     * @param endTime
     * @return
     */
    List<String> listOrderNo(@Param("userId") Long userId, @Param("tradeType") String tradeType,
                             @Param("independentStatus") Integer independentStatus, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询商品分销统计或者商单分销统计 的分销单数
     *
     * @param userId    用户ID
     * @param tradeType {@link com.sohu.common.core.enums.BusyType}
     * @param startTime
     * @param endTime
     * @return
     */
    Long selectCountOrder(@Param("userId") Long userId, @Param("tradeType") String tradeType,
                          @Param("independentStatus") Integer independentStatus, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询商品分销统计或者商单分销聚合统计
     * @param userId
     * @param tradeType
     * @param independentStatus
     * @param startTime
     * @param endTime
     * @return
     */
    SohuIndependentOrderAggrVo selectAggrBySharePerson(@Param("userId") Long userId, @Param("tradeType") String tradeType,
                                                       @Param("independentStatus") Integer independentStatus, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 通过交易流水号更新分账状态
     * @param orderNo
     * @param independentStatus
     */
    void updateIndependentStatusByTradeNo(@Param("orderNo") String orderNo, @Param("independentStatus") int independentStatus);

    SohuUserOrderModel getUserOrderInfo(@Param("userId") Long userId, @Param("siteType") Integer siteType, @Param("siteId") Long siteId, @Param("independentObjects") List<String> independentObject, @Param("tradeType") String tradeType, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
