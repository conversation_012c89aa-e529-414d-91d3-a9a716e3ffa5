package com.sohu.pay.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.pay.api.bo.SohuServiceProviderBo;
import com.sohu.pay.api.vo.SohuServiceProviderRefundVo;
import com.sohu.pay.api.vo.SohuServiceProviderVo;
import com.sohu.pay.domain.SohuServiceProviderRefund;
import org.apache.ibatis.annotations.Param;

/**
 * 服务商退款Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
public interface SohuServiceProviderRefundMapper extends BaseMapperPlus<SohuServiceProviderRefundMapper, SohuServiceProviderRefund, SohuServiceProviderRefundVo> {

    IPage<SohuServiceProviderVo> refundList(@Param("page")Page page, @Param("bo") SohuServiceProviderBo bo);
}
