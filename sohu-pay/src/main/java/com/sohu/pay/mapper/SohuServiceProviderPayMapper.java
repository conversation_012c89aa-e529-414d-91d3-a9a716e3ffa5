package com.sohu.pay.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.pay.api.bo.SohuServiceProviderBo;
import com.sohu.pay.api.vo.SohuServiceProviderPayVo;
import com.sohu.pay.api.vo.SohuServiceProviderVo;
import com.sohu.pay.domain.SohuServiceProviderPay;
import org.apache.ibatis.annotations.Param;

/**
 * 服务商支付保证金Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
public interface SohuServiceProviderPayMapper extends BaseMapperPlus<SohuServiceProviderPayMapper, SohuServiceProviderPay, SohuServiceProviderPayVo> {

    IPage<SohuServiceProviderVo> payList(@Param("page")Page page,@Param("bo") SohuServiceProviderBo bo);
}
