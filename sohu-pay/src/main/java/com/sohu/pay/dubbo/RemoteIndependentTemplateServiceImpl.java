package com.sohu.pay.dubbo;

import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.pay.api.RemoteIndependentTemplateService;
import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import com.sohu.pay.api.vo.SohuIndependentTemplateVo;
import com.sohu.pay.service.ISohuIndependentTemplateService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 支付
 * <p>
 * 注意: 此Service内不允许调用标注`数据权限`注解的方法
 * 例如: deptMapper.selectList 此 selectList 方法标注了`数据权限`注解 会出现循环解析的问题
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteIndependentTemplateServiceImpl implements RemoteIndependentTemplateService {

    private final ISohuIndependentTemplateService independentTemplateService;

    @Override
    public SohuIndependentTemplateModel queryByIdAndType(Long siteId, Integer type) {
//        // 查询通用模板
//        if (siteId == null) {
//            SohuIndependentTemplateVo sohuIndependentTemplateVo = independentTemplateService.queryByIdAndType(type);
//            Objects.requireNonNull(sohuIndependentTemplateVo, "分账模板为空,请联系管理员");
//            return BeanCopyUtils.copy(sohuIndependentTemplateVo, SohuIndependentTemplateModel.class);
//        }
//        SohuIndependentTemplateVo independentTemplateVo = independentTemplateService.queryByIdAndType(siteId, type);
//        Objects.requireNonNull(independentTemplateVo, "当前站点的分账模版为空请联系管理员");
//        return BeanCopyUtils.copy(independentTemplateVo, SohuIndependentTemplateModel.class);
        return queryTemplateInfo(Constants.ONE, siteId, type);
    }

    @Override
    public SohuIndependentTemplateModel queryTemplateInfo(Integer siteType, Long siteId, Integer type) {
//        // 查询通用模板
//        if (siteId == null) {
//            SohuIndependentTemplateVo sohuIndependentTemplateVo = independentTemplateService.queryCommonTemp(type);
//            Objects.requireNonNull(sohuIndependentTemplateVo, "分账模板为空,请联系管理员");
//            return BeanCopyUtils.copy(sohuIndependentTemplateVo, SohuIndependentTemplateModel.class);
//        }
//        SohuIndependentTemplateVo independentTemplateVo = independentTemplateService.queryByIdAndType(siteId, type);
//        //如果站点模版不存在，取通用模版
//        if (Objects.isNull(independentTemplateVo)){
//            independentTemplateVo = independentTemplateService.queryCommonTemp(type);
//        }
//        Objects.requireNonNull(independentTemplateVo, "当前站点的分账模版为空请联系管理员");
//        return BeanCopyUtils.copy(independentTemplateVo, SohuIndependentTemplateModel.class);
        return independentTemplateService.queryTemplateInfo(siteType, siteId, type);
    }

    @Override
    public Map<Long, SohuIndependentTemplateModel> listByIdAndType(List<Long> addSiteIds, int i) {
        List<SohuIndependentTemplateVo> independentTemplateVo = independentTemplateService.listByIdAndType(addSiteIds, i);
        Objects.requireNonNull(independentTemplateVo, "当前站点的分账模版为空请联系管理员");
        List<SohuIndependentTemplateModel> templateModels = BeanCopyUtils.copyList(independentTemplateVo, SohuIndependentTemplateModel.class);
        return templateModels != null ? templateModels.stream().collect(Collectors.toMap(SohuIndependentTemplateModel::getId, u -> u)) : null;
    }

    @Override
    public Boolean queryByIndustryId(Long industryId) {
        return independentTemplateService.queryByIndustryId(industryId);
    }

}
