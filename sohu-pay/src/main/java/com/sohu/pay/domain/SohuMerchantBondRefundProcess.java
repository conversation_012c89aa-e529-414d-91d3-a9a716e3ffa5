package com.sohu.pay.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.math.BigDecimal;
import java.util.Date;
import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 商户分类保证金退款处理对象 sohu_merchant_bond_refund_process
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_merchant_bond_refund_process")
public class SohuMerchantBondRefundProcess extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 保证金记录id
     */
    private Long merchantBondId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 店铺id
     */
    private Long merId;
    /**
     * 类目id
     */
    private Long cateId;
    /**
     * 保证金金额
     */
    private BigDecimal bondAmount;
    /**
     * 待退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 已退款金额
     */
    private BigDecimal refundedAmount;
    /**
     * 扣款金额
     */
    private BigDecimal deductAmount;
    /**
     * 扣款原因
     */
    private String deductReason;
    /**
     * 状态：Processing  处理中  PartRefund  部分退款  Completed 已完成
     */
    private String status;
    /**
     * 支付来源
     */
    private String paySource;
    /**
     * 首次缴纳成功支付时间
     */
    private Date payTime;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 审核人
     */
    private String applyUser;
    /**
     * 财务处理时间
     */
    private Date handleTime;

}
