package com.sohu.pay.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * MCN退款单(预留业务)
 */
@Data
@TableName(value = "sohu_refund_pay_mcn")
public class SohuRefundPayMcn extends BaseEntity {
    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 第三方支付流水号
     */
    private String transactionId;

    /**
     * 第三方退款流水号
     */
    private String refundId;

    /**
     * 商户订单编码
     */
    private String shopOrderNo;

    /**
     * 商户退单编码
     */
    private String refundOrderNo;

    /**
     * 支付单号
     */
    private String payNumber;

    /**
     * 主支付单号
     */
    private String masterPayNumber;

    /**
     * 支付方式  wechat_jsapi-微信小程序支付、wechat-app-微信app支付、tiktok-抖音小程序支付、alipay-支付宝小程序支付、integral-积分支付、balance-余额支付
     */
    private String payType;

    /**
     * 退款状态 WAITAPPROVE-申请退款 REFUNDING-退款中 REFUNDSUCCESS-已退款 FALSE-未退款、REFUNDREFUSE-退款失败
     */
    private String payStatus;

    /**
     * 应退金额
     */
    private BigDecimal payableAmount;

    /**
     * 实退金额
     */
    private BigDecimal payAmount;

    /**
     * 支付补贴金额
     */
    private BigDecimal subsidyAmount;

    /**
     * 抵扣积分
     */
    private Integer payIntegral;

//    /**
//     * 创建时间(下单时间)
//     */
//    private Date createTime;
//
//    /**
//     * 创建人(下单用户)
//     */
//    private String createBy;
//
//    /**
//     * 修改时间
//     */
//    private Date updateTime;
//
//    /**
//     * 修改人
//     */
//    private String updateBy;

    private static final long serialVersionUID = 1L;

}
