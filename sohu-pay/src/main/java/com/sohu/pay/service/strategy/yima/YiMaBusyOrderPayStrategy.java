package com.sohu.pay.service.strategy.yima;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.sohu.busyorder.api.RemoteBusyOrderService;
import com.sohu.busyorder.api.model.SohuBusyOrderModel;
import com.sohu.busyorder.api.model.SohuBusyOrderPayModel;
import com.sohu.busyorder.api.domain.SohuBusyOrderSaveOrderPayReqBo;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.BusyOrderStatus;
import com.sohu.common.core.enums.PayObject;
import com.sohu.common.core.enums.PayStatus;
import com.sohu.common.core.enums.PayTypeEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.NumberUtil;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.domain.SohuRefundPayBo;
import com.wangcaio2o.ipossa.sdk.model.ExtendParams;
import com.wangcaio2o.ipossa.sdk.request.scanpay.Scanpay;
import com.wangcaio2o.ipossa.sdk.request.scanpay.ScanpayRequest;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.Unifiedorder;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.UnifiedorderRequest;
import com.wangcaio2o.ipossa.sdk.response.callback.CallbackRequest;
import com.wangcaio2o.ipossa.sdk.response.scanpay.ScanpayResponse;
import com.wangcaio2o.ipossa.sdk.response.unifiedorder.UnifiedorderResponse;
import com.wangcaio2o.ipossa.sdk.test.Client;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 商单预付款(旧版)
 */
@Component
@Slf4j
public class YiMaBusyOrderPayStrategy implements YiMaPayStrategy {

    @DubboReference
    private RemoteBusyOrderService remoteBusyOrderService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payment(SohuPrePayBo payBo) {
        log.info("翼码支付 - 商单预付款(旧版) - 支付来源：{}", JSONUtil.toJsonStr(payBo));
        return (StrUtil.equalsAnyIgnoreCase(payBo.getPayChannel(), Constants.CHANNEL_PC) ? getPcPay(payBo) : getMobilePay(payBo));
    }

    @Override
    public Boolean refund(SohuRefundPayBo refundPayBo) {
        log.info("翼码支付 - 商城商品退款 - 退款来源：{}", JSONUtil.toJsonStr(refundPayBo));
        return false;
    }

    @Override
    public String payCallback(String callbackResponse) {
        log.info("翼码支付 - 商单预付款(旧版)回调 ：{}", callbackResponse);
        CallbackRequest response = JSONObject.parseObject(callbackResponse, CallbackRequest.class);
        String outTradeNo = response.getPosSeq();
        // 校验支付状态
        checkPayStatus(response.getStatus());
        SohuBusyOrderPayModel busyOrderPayModel = remoteBusyOrderService.queryBusyOrderPayByOrderNo(outTradeNo);
        if (Objects.isNull(busyOrderPayModel)) {
            throw new ServiceException("商单预付款(旧版)记录不存在");
        }
        remoteBusyOrderService.updateBusyOrderSuccess(outTradeNo, response.getTradeNo());
        return "success";
    }

    @Transactional(rollbackFor = Exception.class)
    public String getPcPay(SohuPrePayBo payBo) {
        // 查询商单
        SohuBusyOrderModel sohuBusyOrder = remoteBusyOrderService.queryBusyOrder(Long.valueOf(payBo.getMasterOrderNo()));
        if (Objects.isNull(sohuBusyOrder)) {
            throw new ServiceException(MessageUtils.message("BUSY_ORDER_NOT_FOUNT"));
        }
        if (!Objects.equals(sohuBusyOrder.getUserId(), payBo.getUserId())) {
            throw new ServiceException(MessageUtils.message("BUSY_ORDER_OPERATE_NO_AUTH"));
        }
        if (!Objects.equals(sohuBusyOrder.getPrepayState(), PayStatus.WaitPay.name()) &&
                !Objects.equals(sohuBusyOrder.getPrepayState(), BusyOrderStatus.Prepay.name())) {
            throw new ServiceException(MessageUtils.message("BUSY_ORDER_WRONG_STATUS"));
        }
        String outTradeNo = NumberUtil.getOrderNo(OrderConstants.BUSY_ORDER_PREFIX);
        ScanpayRequest request = getScanpayRequest();
        request.setMemo("商单预付款支付");
        request.setPosSeq(outTradeNo);
        // 转换价格y-f
        int busyOrderTotalPrice = CalUtils.yuanToCent(sohuBusyOrder.getPrepayAmount()).intValue();
        // 商单价格对象
        Scanpay scanpay = getScanpay();
        scanpay.setTxAmt(busyOrderTotalPrice);
        // 是否分账配置
        ExtendParams extendParams = new ExtendParams();
        // 不分账 -- R实时分账 --D延时分账
        extendParams.setSplitFlag(ExtendParams.N);
        scanpay.setExtendParams(extendParams);
        // 设置请求参数
        request.setScanpayRequest(scanpay);
        log.info("翼码支付 - PC- 旧版商单预付款支付request：{}", JSONUtil.toJsonStr(request));
        ScanpayResponse response = Client.getClient().execute(request);
        log.info("翼码支付 - PC- 旧版商单预付款支付请求response返回：{}", JSONUtil.toJsonStr(response));
//        remoteBusyOrderService.saveBusyOrderPay(String.valueOf(sohuBusyOrder.getId()), outTradeNo, PayTypeEnum.PAY_TYPE_YI_MA.getStatus(),
//                PayStatus.WaitPay.name(), sohuBusyOrder.getPrepayAmount(), PayObject.BusyOrder.name(), 0L, payBo.getUserId(), 0L);
        SohuBusyOrderSaveOrderPayReqBo saveOrderPayModel = new SohuBusyOrderSaveOrderPayReqBo()
                .setOrderId(String.valueOf(sohuBusyOrder.getId()))
                .setOrderNo(outTradeNo)
                .setPayType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus())
                .setPayStatus(PayStatus.WaitPay.name())
                .setAmount(sohuBusyOrder.getPrepayAmount())
                .setBusyType(PayObject.BusyOrder.name())
                .setPayeeId(0L)
                .setUserId(payBo.getUserId())
                .setDeliveryId(0L)
                .setPayChannel(payBo.getPayChannel());
        remoteBusyOrderService.saveBusyOrderPay(saveOrderPayModel);
        return JSONUtil.toJsonStr(response);
    }

    @Transactional(rollbackFor = Exception.class)
    public String getMobilePay(SohuPrePayBo payBo) {
        // 查询商单
        SohuBusyOrderModel sohuBusyOrder = remoteBusyOrderService.queryBusyOrder(Long.valueOf(payBo.getMasterOrderNo()));
        if (Objects.isNull(sohuBusyOrder)) {
            throw new ServiceException(MessageUtils.message("BUSY_ORDER_NOT_FOUNT"));
        }
        if (!Objects.equals(sohuBusyOrder.getUserId(), payBo.getUserId())) {
            throw new ServiceException(MessageUtils.message("BUSY_ORDER_OPERATE_NO_AUTH"));
        }
        if (!Objects.equals(sohuBusyOrder.getPrepayState(), PayStatus.WaitPay.name()) &&
                !Objects.equals(sohuBusyOrder.getPrepayState(), BusyOrderStatus.Prepay.name())) {
            throw new ServiceException(MessageUtils.message("BUSY_ORDER_WRONG_STATUS"));
        }
        String outTradeNo = NumberUtil.getOrderNo(OrderConstants.BUSY_ORDER_PREFIX);
        UnifiedorderRequest request = getUnifiedorderRequest();
        request.setMemo("商单预付款支付");
        request.setPosSeq(outTradeNo);
        // 转换价格y-f
        int busyOrderTotalPrice = CalUtils.yuanToCent(sohuBusyOrder.getPrepayAmount()).intValue();
        // 商单价格对象
        Unifiedorder unifiedorder = getUnifiedorder(payBo.getUserId());
        unifiedorder.setTxAmt(busyOrderTotalPrice);
        // 是否分账配置
        ExtendParams extendParams = new ExtendParams();
        // 不分账 -- R实时分账 --D延时分账
        extendParams.setSplitFlag(ExtendParams.N);
        unifiedorder.setExtendParams(extendParams);
        // 设置请求参数
        request.setUnifiedorderRequest(unifiedorder);
        log.info("翼码支付 -MOBILE- 旧版商单预付款支付请求request：{}", JSONUtil.toJsonStr(request));
        UnifiedorderResponse response = Client.getClient().execute(request);
        log.info("翼码支付 -MOBILE- 旧版商单预付款支付请求response返回：{}", JSONUtil.toJsonStr(response));
        // 保存待付款记录
        SohuBusyOrderSaveOrderPayReqBo saveOrderPayModel = new SohuBusyOrderSaveOrderPayReqBo()
                .setOrderId(String.valueOf(sohuBusyOrder.getId()))
                .setOrderNo(outTradeNo)
                .setPayType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus())
                .setPayStatus(PayStatus.WaitPay.name())
                .setAmount(sohuBusyOrder.getPrepayAmount())
                .setBusyType(PayObject.BusyOrder.name())
                .setPayeeId(0L)
                .setUserId(payBo.getUserId())
                .setDeliveryId(0L)
                .setPayChannel(payBo.getPayChannel());
        remoteBusyOrderService.saveBusyOrderPay(saveOrderPayModel);

//        remoteBusyOrderService.saveBusyOrderPay(String.valueOf(sohuBusyOrder.getId()), outTradeNo,
//                PayTypeEnum.PAY_TYPE_YI_MA.getStatus(),
//                PayStatus.WaitPay.name(),
//                sohuBusyOrder.getPrepayAmount(),
//                PayObject.BusyOrder.name(),
//                0L, payBo.getUserId(), 0L);
        return JSONUtil.toJsonStr(response);
    }
}
