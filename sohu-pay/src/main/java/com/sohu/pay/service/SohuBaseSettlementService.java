package com.sohu.pay.service;

import com.sohu.busyorder.api.bo.SohuPayBusyBo;
import com.sohu.busyorder.api.bo.SohuRefundBusyBo;
import com.sohu.busyorder.api.vo.SohuPayBusyVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.pay.api.bo.DelayConfirmqueryBo;
import com.sohu.pay.api.bo.SohuPayBatchSettlementBo;
import com.sohu.pay.api.bo.SohuPaySettlementBo;

/**
 * <AUTHOR>
 * @date 2025/3/6 12:05
 */
public interface SohuBaseSettlementService {

    /**
     * 单笔结算
     * @param bo
     * @return
     */
    Boolean settle(SohuPaySettlementBo bo);

    /**
     * 单笔结算(密码加密)
     * @param bo
     * @return
     */
    Boolean settleV2(SohuPaySettlementBo bo);

    /**
     * 批量结算
     * @param bo
     * @return
     */
    Boolean batchSettle(SohuPayBatchSettlementBo bo);

    /**
     * 批量结算 密码加密
     * @param bo
     * @return
     */
    Boolean batchSettleV2(SohuPayBatchSettlementBo bo);

    /**
     * 分账查询，提现
     * @param delayConfirmqueryBo
     * @return
     */
    Boolean delayConfirmquery(DelayConfirmqueryBo delayConfirmqueryBo);


    /**
     * 获取支付/退款流水列表
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuPayBusyVo> payList(SohuPayBusyBo bo, PageQuery pageQuery);

    /**
     * 退款
     * @param bo
     * @return
     */
    Boolean refund(SohuRefundBusyBo bo);



}
