package com.sohu.pay.service.strategy.yima;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Lists;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.PayStatus;
import com.sohu.common.core.enums.PayTypeEnum;
import com.sohu.common.core.enums.SohuTradeRecordEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.BigDecimalUtils;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.service.airec.RemoteMiddleAirecOrderService;
import com.sohu.middle.api.vo.YiMaPayConfig;
import com.sohu.middle.api.vo.airec.SohuAirecMasterOrderVo;
import com.sohu.middle.api.vo.airec.SohuAirecOrderVo;
import com.sohu.pay.api.bo.SohuPayOrderBo;
import com.sohu.pay.api.bo.SohuRefundPayOrderBo;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.domain.SohuRefundPayBo;
import com.sohu.pay.domain.SohuPayOrder;
import com.sohu.pay.service.ISohuRefundPayOrderService;
import com.sohu.pay.service.strategy.AbsTradeRecordStrategy;
import com.sohu.pay.service.strategy.PaymentStrategy;
import com.wangcaio2o.ipossa.sdk.model.ExtendParams;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverse;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverseRequest;
import com.wangcaio2o.ipossa.sdk.request.scanpay.Scanpay;
import com.wangcaio2o.ipossa.sdk.request.scanpay.ScanpayRequest;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.Unifiedorder;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.UnifiedorderRequest;
import com.wangcaio2o.ipossa.sdk.response.barcodereverse.BarcodeReverseResponse;
import com.wangcaio2o.ipossa.sdk.response.callback.CallbackRequest;
import com.wangcaio2o.ipossa.sdk.response.scanpay.ScanpayResponse;
import com.wangcaio2o.ipossa.sdk.response.unifiedorder.UnifiedorderResponse;
import com.wangcaio2o.ipossa.sdk.test.Client;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;
import java.util.Objects;

import static com.sohu.common.core.constant.OrderConstants.ORDER_REFUND_STATUS_NO_REFUSE;
import static com.sohu.common.core.constant.OrderConstants.ORDER_STATUS_H5_REFUNDED;

/**
 * 智能推荐投流订单支付
 */
@Component
@Slf4j
public class YiMaAirecOrderPayStrategy extends AbsTradeRecordStrategy implements YiMaPayStrategy {

    @DubboReference
    private RemoteMiddleAirecOrderService remoteMiddleAirecOrderService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private ISohuRefundPayOrderService sohuRefundPayOrderService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payment(SohuPrePayBo payBo) {
        log.info("翼码支付 - MCN订单支付 - 支付来源：{}", JSONUtil.toJsonStr(payBo));
        return getPay(payBo);
    }

    @Override
    public Boolean refund(SohuRefundPayBo refundPayBo) {
        log.info("翼码支付 - MCN订单退款 - 退款来源：{}", JSONUtil.toJsonStr(refundPayBo));
        SohuPayOrder sohuPayOrder = sohuPayOrderService.queryByPayNumber(refundPayBo.getOrderNo());
        if (sohuPayOrder.getPayAmount().compareTo(sohuPayOrder.getRefundAmount()) < 1) {
            throw new RuntimeException("子订单已退款金额不能超过实际付款金额");
        }

        SohuAirecOrderVo sohuAirecOrderModel = remoteMiddleAirecOrderService.selectByOrderNo(refundPayBo.getOrderNo());
        if (Objects.isNull(sohuAirecOrderModel)) {
            throw new RuntimeException("投流订单不存在");
        }
        //退款单
        SohuRefundPayOrderBo refundPayOrder = new SohuRefundPayOrderBo();
        refundPayOrder.setUserId(sohuAirecOrderModel.getUserId());

        refundPayOrder.setTransactionId(sohuPayOrder.getPayNumber());
        refundPayOrder.setMasterOrderNo(sohuPayOrder.getMasterOrderNo());
        refundPayOrder.setOrderNo(sohuPayOrder.getOrderNo());
        refundPayOrder.setPayNumber(sohuPayOrder.getPayNumber());
        refundPayOrder.setPayType(sohuPayOrder.getPayType());
        refundPayOrder.setPayableAmount(sohuPayOrder.getPayAmount());
        refundPayOrder.setRefundAmount(sohuPayOrder.getPayAmount());

        YiMaPayConfig yiMaPayConfig = getPayConfig();
        // 组装微信小程序退款请求参数
        BarcodeReverseRequest request = getBarcodeReverseRequest();
        request.setPosId(yiMaPayConfig.getPosId());
        request.setIsspid(yiMaPayConfig.getIssPid());
        request.setSystemId(yiMaPayConfig.getSystemId());
        request.setStoreId(yiMaPayConfig.getStoreId());
        // 退款参数封装
        BarcodeReverse reverse = new BarcodeReverse();
        String payType = getPayType(sohuPayOrder.getSourceType());
        reverse.setPayType(payType);
        // 请求退款单号拼接
        request.setPosSeq("R" + System.nanoTime());
        // 支付请求流水号
        reverse.setOrgPosSeq(sohuPayOrder.getPayNumber());
        reverse.setTxAmt(BigDecimalUtils.yuanToFen(sohuPayOrder.getPayAmount()));
        request.setBarcodeReverseRequest(reverse);
        log.info("Airec退款请求:{}", JSONUtil.toJsonStr(request));

        refundPayOrder.setRefundOrderNo(request.getPosSeq());

        BarcodeReverseResponse response = Client.getClient().execute(request);
        List<String> resultList = Lists.newArrayList("9998", "0000");
        if (ObjectUtils.isNull(response) || !resultList.contains(response.getResult().getId())) {
            log.error("Airec退款异常：{}", JSONUtil.toJsonStr(response));
            //没看到枚举值，待后期调整
            refundPayOrder.setRefundStatus(ORDER_REFUND_STATUS_NO_REFUSE);
            //throw new RuntimeException(response.getResult().getComment());
        } else {
            //没看到枚举值，待后期调整
            refundPayOrder.setRefundStatus(ORDER_STATUS_H5_REFUNDED);
        }
        //保存退款单
        this.sohuRefundPayOrderService.insertByBo(refundPayOrder);
        //修改支付单状态
        SohuPayOrderBo sohuPayOrderBo = SohuPayOrderBo.builder()
                .id(sohuPayOrder.getId())
                .refundAmount(sohuPayOrder.getPayAmount())
                .build();
        sohuPayOrderService.updateByBo(sohuPayOrderBo);

        SohuTradeRecordBo.SohuTradeRecordBoBuilder recordBo = SohuTradeRecordBo.builder();
        recordBo.userId(sohuAirecOrderModel.getUserId());
        recordBo.amount(sohuPayOrder.getPayAmount());
        recordBo.payType(payType);
        recordBo.payStatus(PayStatus.Refund.name());
        recordBo.operateChannel(sohuPayOrder.getSourceType());
        recordBo.type(SohuTradeRecordEnum.Type.AIREC.getCode());
        recordBo.consumeType(SohuTradeRecordEnum.Type.AIREC.getCode());
        recordBo.consumeCode(sohuAirecOrderModel.getMasterOrderNo());
        recordBo.msg(SohuTradeRecordEnum.Type.AIREC.getMsg());
        recordBo.amountType(SohuTradeRecordEnum.AmountType.InCome.getCode());
        recordBo.payNumber(refundPayOrder.getRefundOrderNo());
        recordBo.accountType(SohuTradeRecordEnum.AccountType.Amount.name());
        // 保存流水记录 - 钱 - 收入
        saveTradeRecord(recordBo.build());

        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payCallback(String callbackResponse) {
        log.info("翼码支付 - MCN订单支付回调 ：{}", callbackResponse);
        CallbackRequest response = JSONObject.parseObject(callbackResponse, CallbackRequest.class);
        String masterOrderNo = response.getPosSeq();
        // 校验支付状态
        checkPayStatus(response.getStatus());
        remoteMiddleAirecOrderService.updatePaidByMasterOrderNo(masterOrderNo);
        // 更新流水记录成功
        remoteMiddleTradeRecordService.updatePayStatus(masterOrderNo, response.getTradeNo(), PayStatus.Paid, null);
        updatePayOrder(masterOrderNo, CalUtils.centToYuan(new BigDecimal(response.getChargeAmount())), PayStatus.Paid.name(), response.getTradeNo());
        return "success";
    }

    /**
     * 订单支付
     */
    @Transactional(rollbackFor = Exception.class)
    public String getPay(SohuPrePayBo payBo) {
        String key = CacheConstants.ORDER_PAY_TWO + PayTypeEnum.PAY_TYPE_YI_MA.getStatus() + StrPool.COLON +
                payBo.getMasterOrderNo() + StrPool.COLON + getOperateChannel(payBo.getPayChannel()) + StrPool.COLON + payBo.getUserId();
        boolean exists = RedisUtils.isExistsObject(key);
        // 如果存在直接唤醒
        if (exists) {
            log.info("Airec订单支付订单存在，直接唤醒支付，订单号：{}，缓存值：{}", payBo.getMasterOrderNo(), RedisUtils.getCacheObject(key));
            return RedisUtils.getCacheObject(key);
        }
        SohuAirecMasterOrderVo masterOrder = remoteMiddleAirecOrderService.selectOfUnpaiByMasterOrderNo(payBo.getMasterOrderNo());
        // 状态判断
        if (ObjectUtil.isNull(masterOrder)) {
            throw new ServiceException("order does not exist");
        }

        UnifiedorderRequest unifiedorderRequest = getUnifiedorderRequest();
        ScanpayRequest scanpayRequest = getScanpayRequest();
        // 备注
        unifiedorderRequest.setMemo("Airec功能支付");
        scanpayRequest.setMemo("Airec功能支付");
        // 唯一订单号
        unifiedorderRequest.setPosSeq(masterOrder.getOrderNo());
        scanpayRequest.setPosSeq(masterOrder.getOrderNo());
        // 总金额
        Unifiedorder unifiedOrder = getUnifiedorder(payBo.getUserId());
        Scanpay scanpay = getScanpay();
        scanpay.setTxAmt(BigDecimalUtils.yuanToFen(masterOrder.getTotalPrice()));
        unifiedOrder.setTxAmt(BigDecimalUtils.yuanToFen(masterOrder.getTotalPrice()));
        // 不分账 -- R实时分账 --D延时分账
        ExtendParams extendParams = new ExtendParams();
        extendParams.setSplitFlag(ExtendParams.N);
        scanpay.setExtendParams(extendParams);
        // 设置请求参数
        unifiedorderRequest.setUnifiedorderRequest(unifiedOrder);
        scanpayRequest.setScanpayRequest(scanpay);

        payBo.setAmount(masterOrder.getTotalPrice());

        SohuTradeRecordBo.SohuTradeRecordBoBuilder recordBo = buildRecord(payBo);
        recordBo.type(SohuTradeRecordEnum.Type.AIREC.getCode());
        recordBo.consumeType(SohuTradeRecordEnum.Type.AIREC.getCode());
        recordBo.consumeCode(payBo.getMasterOrderNo());
        recordBo.msg(SohuTradeRecordEnum.Type.AIREC.getMsg());
        recordBo.amountType(SohuTradeRecordEnum.AmountType.Expend.getCode());
        recordBo.payNumber(masterOrder.getOrderNo());
        recordBo.accountType(SohuTradeRecordEnum.AccountType.Amount.name());
        // 保存流水记录 - 钱 - 支出
        saveTradeRecord(recordBo.build());

        // 保存主支付单
        String payMasterOrderNo = saveMasterPayOrder(payBo, masterOrder.getOrderNo(), PayStatus.WaitPay.name());
        List<SohuAirecOrderVo> orderList = remoteMiddleAirecOrderService.getListByMasterNo(masterOrder.getOrderNo());
        for (SohuAirecOrderVo order : orderList) {
            payBo.setAmount(order.getPrice());
            // 保存子支付单
            savePayOrder(payBo, payMasterOrderNo, order.getOrderNo(), PayStatus.WaitPay.name());
        }

        if (StrUtil.equalsAnyIgnoreCase(payBo.getPayChannel(), Constants.CHANNEL_PC)) {
            log.info("翼码支付 - PC-Airec订单支付请求request：{}", JSONUtil.toJsonStr(scanpayRequest));
            ScanpayResponse response = Client.getClient().execute(scanpayRequest);
            log.info("翼码支付 - PC-Airec订单支付请求response返回：{}", JSONUtil.toJsonStr(response));
            RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
            return JSONUtil.toJsonStr(response);
        }
        log.info("翼码支付 -MOBILE- Airec订单支付请求request：{}", JSONUtil.toJsonStr(unifiedorderRequest));
        UnifiedorderResponse response = Client.getClient().execute(unifiedorderRequest);
        log.info("翼码支付 -MOBILE- Airec订单支付请求response返回：{}", JSONUtil.toJsonStr(response));
        RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
        return JSONUtil.toJsonStr(response);
    }

}
