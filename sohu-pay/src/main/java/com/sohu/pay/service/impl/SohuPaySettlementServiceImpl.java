package com.sohu.pay.service.impl;

import cn.hutool.core.util.StrUtil;
import com.sohu.busyorder.api.bo.SohuPayBusyBo;
import com.sohu.busyorder.api.bo.SohuRefundBusyBo;
import com.sohu.busyorder.api.vo.SohuPayBusyVo;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.exception.user.CaptchaExpireException;
import com.sohu.common.core.exception.user.UserException;
import com.sohu.common.core.utils.SecretUtil;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.pay.api.bo.DelayConfirmqueryBo;
import com.sohu.pay.api.bo.SohuPayBatchSettlementBo;
import com.sohu.pay.api.bo.SohuPayPasswordBo;
import com.sohu.pay.api.bo.SohuPaySettlementBo;
import com.sohu.pay.service.SohuPaySettlementService;
import com.sohu.pay.service.strategy.SettlementProcessor;
import com.sohu.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025/1/14 15:51
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuPaySettlementServiceImpl implements SohuPaySettlementService {
    private final AsyncConfig asyncConfig;
    private final SettlementProcessor settlementProcessor;
    @DubboReference
    protected RemoteUserService remoteUserService;

    @Override
    public Boolean settle(SohuPaySettlementBo bo) {
        return settlementProcessor.getStrategy(bo.getBusyTaskType()).settle(bo);
    }

    @Override
    public Boolean settleV2(SohuPaySettlementBo bo) {
        return settlementProcessor.getStrategy(bo.getBusyTaskType()).settleV2(bo);
    }

    @Override
    public Boolean batchSettle(SohuPayBatchSettlementBo bo) {
        return settlementProcessor.getStrategy(bo.getBusyTaskType()).batchSettle(bo);
    }

    @Override
    public Boolean batchSettleV2(SohuPayBatchSettlementBo bo) {
        return settlementProcessor.getStrategy(bo.getBusyTaskType()).batchSettleV2(bo);
    }

    @Override
    public Boolean payPassword(SohuPayPasswordBo bo) {
        Long userId = LoginHelper.getUserId();
        Objects.requireNonNull(userId, "用户未登录");
        LoginUser user = remoteUserService.queryById(userId);
        Objects.requireNonNull(user, "用户不存在");
        if (!StringUtils.validNumberPassword(bo.getPayPassword())) {
            throw new RuntimeException("支付密码必须为6位纯数字");
        }
        String oldPassword = user.getPayPassword();
        // 校验验证码
        String code = RedisUtils.getCacheObject(CacheConstants.CAPTCHA_CODE_KEY + user.getPhoneNumber());
        if (StringUtils.isBlank(code)) {
            throw new CaptchaExpireException();
        }
        if (!StrUtil.equals(code, bo.getCode())) {
            throw new UserException("code.error");
        }
        Integer result = remoteUserService.updatePayPwd(userId, bo.getPayPassword());
        // 修改支付密码发送消息通知
        if (StringUtils.isNotBlank(oldPassword)) {
            CompletableFuture.runAsync(() -> remoteUserService.sendMsgOfUpdatePayPwd(userId), asyncConfig.getAsyncExecutor());
        }
        // 验证码只能使用一次
        RedisUtils.deleteObject(CacheConstants.CAPTCHA_CODE_KEY + user.getPhoneNumber());
        return result > 0;
    }

    @Override
    public Boolean payPasswordV2(SohuPayPasswordBo bo) {
        String newPassword = SecretUtil.desEncrypt(bo.getPayPassword(), SecretUtil.KEY);
        if (StrUtil.isEmpty(newPassword)) {
            throw new RuntimeException("密码解析失败");
        }
        Long userId = LoginHelper.getUserId();
        Objects.requireNonNull(userId, "用户未登录");
        LoginUser user = remoteUserService.queryById(userId);
        Objects.requireNonNull(user, "用户不存在");
        if (!StringUtils.validNumberPassword(newPassword)) {
            throw new RuntimeException("支付密码必须为6位纯数字");
        }
        String oldPassword = user.getPayPassword();
        // 校验验证码
        String code = RedisUtils.getCacheObject(CacheConstants.CAPTCHA_CODE_KEY + user.getPhoneNumber());
        if (StringUtils.isBlank(code)) {
            throw new CaptchaExpireException();
        }
        if (!StrUtil.equals(code, bo.getCode())) {
            throw new UserException("code.error");
        }
        Integer result = remoteUserService.updatePayPwd(userId, newPassword);
        // 修改支付密码发送消息通知
        if (StringUtils.isNotBlank(oldPassword)) {
            CompletableFuture.runAsync(() -> remoteUserService.sendMsgOfUpdatePayPwd(userId), asyncConfig.getAsyncExecutor());
        }
        // 验证码只能使用一次
        RedisUtils.deleteObject(CacheConstants.CAPTCHA_CODE_KEY + user.getPhoneNumber());
        return result > 0;
    }

    @Override
    public Boolean delayConfirmquery(DelayConfirmqueryBo delayConfirmqueryBo) {
        return settlementProcessor.getStrategy(delayConfirmqueryBo.getBusyType()).delayConfirmquery(delayConfirmqueryBo);
    }

    @Override
    public TableDataInfo<SohuPayBusyVo> payList(SohuPayBusyBo bo, PageQuery pageQuery) {
        return settlementProcessor.getStrategy(bo.getBusyTaskType()).payList(bo, pageQuery);
    }

    @Override
    public Boolean refund(SohuRefundBusyBo bo) {
        return settlementProcessor.getStrategy(bo.getBusyTaskType()).refund(bo);
    }
}
