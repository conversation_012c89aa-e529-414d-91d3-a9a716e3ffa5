package com.sohu.pay.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sohu.pay.api.bo.SohuOnlineBankLogBo;
import com.sohu.pay.domain.SohuOnlineBankLog;
import com.sohu.pay.mapper.SohuOnlineBankLogMapper;
import com.sohu.pay.service.ISohuOnlineBankLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/16 19:04
 */
@RequiredArgsConstructor
@Service
public class SohuOnlineBankLogServiceImpl implements ISohuOnlineBankLogService {

    private final SohuOnlineBankLogMapper baseMapper;
    @Override
    public Boolean insertByBo(SohuOnlineBankLogBo bo) {
        SohuOnlineBankLog add = BeanUtil.toBean(bo, SohuOnlineBankLog.class);
        return baseMapper.insert(add) > 0;
    }

    @Override
    public String getResponse(String tradeNo) {
        LambdaQueryWrapper<SohuOnlineBankLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SohuOnlineBankLog::getPosSeq,tradeNo);
        queryWrapper.orderByDesc(SohuOnlineBankLog::getCreateTime);
        queryWrapper.last("limit 1");
        SohuOnlineBankLog sohuOnlineBankLog = baseMapper.selectOne(queryWrapper);
        return Objects.nonNull(sohuOnlineBankLog)? sohuOnlineBankLog.getResponseStr() : "";
    }
}
