package com.sohu.pay.service.strategy.paypal;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.paypal.orders.*;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.PayStatus;
import com.sohu.common.core.enums.PayTypeEnum;
import com.sohu.common.core.enums.SohuTradeRecordEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.NumberUtil;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.service.RemoteMiddleRechargeListService;
import com.sohu.middle.api.vo.SohuRechargeListVo;
import com.sohu.middle.api.vo.SohuTradeRecordVo;
import com.sohu.pay.api.domain.SohuPayQueryBo;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.domain.SohuRefundPayBo;
import com.sohu.pay.api.model.SohuPayResultModel;
import com.sohu.pay.api.vo.SohuMasterPayOrderVo;
import com.wangcaio2o.ipossa.sdk.response.callback.CallbackRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * PayPal虚拟币充值
 */
@Component
@Slf4j
public class PayPalVirtualPayStrategy extends PayPalAbs {
    @DubboReference
    private RemoteMiddleRechargeListService remoteMiddleRechargeListService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payment(SohuPrePayBo payBo) {
        log.info("PalPay支付 - 虚拟币充值支付 - 支付来源：{}", JSONUtil.toJsonStr(payBo));
        return getPay(payBo);
    }

    @Override
    public SohuPayResultModel paySuccess(SohuPayQueryBo payQueryBo) {
        return null;
    }

    @Override
    public Object paySuccessQuery(SohuPayQueryBo payQueryBo) {
        SohuMasterPayOrderVo orderVo = sohuMasterPayOrderService.queryByPayTypeAndTransactionId(payQueryBo.getPayType(), payQueryBo.getOrderNo());
        if (Objects.isNull(orderVo)) {
            return "fail";
        }
        return StrUtil.equalsAnyIgnoreCase(orderVo.getPayStatus(), PayStatus.Paid.name()) ? "success" : "fail";
    }

    @Override
    public Boolean refund(SohuRefundPayBo refundPayBo) {
        log.info("PalPay支付 - 虚拟币充值退款 - 退款来源：{}", JSONUtil.toJsonStr(refundPayBo));
        return false;
    }

    @Override
    public Boolean refundSuccess(String refundOrderNo) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payCallback(String callbackResponse) {
        log.info("PalPay支付 - 虚拟币充值回调 ：{}", callbackResponse);
        CallbackRequest response = JSONUtil.toBean(callbackResponse, CallbackRequest.class);
        String outTradeNo = response.getPosSeq();

        SohuTradeRecordBo recordBo = SohuTradeRecordBo.builder().build();
        recordBo.setPayType(PayTypeEnum.PAY_TYPE_PAYPAL.getStatus());
        recordBo.setPayNumber(outTradeNo);
        SohuTradeRecordVo recordVo = remoteMiddleTradeRecordService.queryOne(recordBo);
        recordBo.setId(recordVo.getId());
        recordBo.setPayStatus(PayStatus.Paid.name());
        recordBo.setTransactionId(response.getTradeNo());
        recordBo.setCaptureId(response.getCaptureId());

        // 更新流水明细状态
        //Boolean successTrade = remoteMiddleTradeRecordService.updatePayStatus(outTradeNo, response.getTradeNo(), PayStatus.Paid);
        Boolean successTrade = remoteMiddleTradeRecordService.updateByBo(recordBo);
        remoteMiddleTradeRecordService.updatePayStatus(outTradeNo, response.getTradeNo(), PayStatus.Paid, null);
        if (successTrade) {
            // 修改支付订单状态
            BigDecimal chargeAmount = StrUtil.isBlankIfStr(response.getChargeAmount()) ? BigDecimal.ZERO : CalUtils.centToYuan(new BigDecimal(response.getChargeAmount()));
            updatePayOrder(outTradeNo, chargeAmount, PayStatus.Paid.name(), response.getTradeNo());
        }
        return successTrade ? "success" : "fail";
    }

    @Override
    public Boolean close(String outTradeNo, String payType) {
        return null;
    }

    /**
     * 狐币充值
     *
     * @param payBo 预支付请求对象
     * @return {@link String}
     */
    @Transactional(rollbackFor = Exception.class)
    public String getPay(SohuPrePayBo payBo) {
        if (payBo.getUserId() == null || payBo.getUserId() <= 0L) {
            payBo.setUserId(0L);
        }
        log.info("发起狐币充值：{}", JSONUtil.toJsonStr(payBo));
        Long masterId = payBo.getMasterId();

        String type = SohuTradeRecordEnum.Type.VirtualRecharge.getCode();
        String msg = SohuTradeRecordEnum.Type.VirtualRecharge.getMsg();
        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal coin = BigDecimal.ZERO;
        if ((masterId == null || masterId == 0) && payBo.getAmount() != null && CalUtils.isGreatZero(payBo.getAmount())) {
            amount = payBo.getAmount();
            log.info("发起自定义充值");
            type = SohuTradeRecordEnum.Type.VirtualDiyRecharge.getCode();
            msg = SohuTradeRecordEnum.Type.VirtualDiyRecharge.getMsg();
            coin = CalUtils.multiply(amount, Constants.VIRTUAL_AMOUNT_RATIO);
        } else {
            SohuRechargeListVo rechargeListVo = remoteMiddleRechargeListService.queryById(masterId);
            if (ObjectUtil.isNull(rechargeListVo)) {
                throw new ServiceException("recharge does not exist");
            }
            if (!StrUtil.equalsAnyIgnoreCase(rechargeListVo.getState(), CommonState.OnShelf.name())) {
                throw new ServiceException("playlet status is incorrect");
            }
            amount = rechargeListVo.getAmount();
            coin = (rechargeListVo.getCoin() != null && rechargeListVo.getCoin() > 0L) ? new BigDecimal(rechargeListVo.getCoin())
                    : CalUtils.multiply(amount, Constants.VIRTUAL_AMOUNT_RATIO);
        }

        //第三方支付单号
        String posSeq = NumberUtil.getOrderNo(OrderConstants.VIRTUAL_CURRENCY_PREFIX);

        OrderRequest orderRequest = getInitOrderRequest();

        List<PurchaseUnitRequest> purchaseUnitRequests = new ArrayList<PurchaseUnitRequest>();
        PurchaseUnitRequest purchaseUnitRequest = new PurchaseUnitRequest();
        purchaseUnitRequest.description(MessageUtils.message("coin.recharge")).customId(posSeq).invoiceId(posSeq);
        AmountWithBreakdown breakdown = new AmountWithBreakdown()
                .currencyCode(DEFAULT_CURRENCY_CODE)
                .value(String.valueOf(amount))
                .amountBreakdown(new AmountBreakdown().
                        itemTotal(new Money().currencyCode(DEFAULT_CURRENCY_CODE).value(String.valueOf(amount)))

                );
        purchaseUnitRequest.amountWithBreakdown(breakdown);
        BigDecimal finalAmount = amount;
        purchaseUnitRequest.items(new ArrayList<Item>() {{
            add(new Item().name("Recharge Coin").description("Recharge Coin").unitAmount(new Money().currencyCode(DEFAULT_CURRENCY_CODE).value(String.valueOf(finalAmount))).quantity("1"));
        }});
        purchaseUnitRequest.shippingDetail(new ShippingDetail()
                .name(new Name().fullName("Recharge Coin"))
                .addressPortable(new AddressPortable()
                        .addressLine1("minglereels")
                        .addressLine2("minglereels")
                        .adminArea2("minglereels")
                        .adminArea1("minglereels")
                        .postalCode("minglereels")
                        .countryCode("CN")));
        purchaseUnitRequests.add(purchaseUnitRequest);
        orderRequest.purchaseUnits(purchaseUnitRequests);

        Order payOrder = payPalService.createPayOrder(orderRequest);
        requireNotNullOrder(payOrder);

        String paymentId = payOrder.id();

        payBo.setAmount(amount);

        SohuTradeRecordBo.SohuTradeRecordBoBuilder recordAmountBo = buildRecord(payBo);
        recordAmountBo.payNumber(posSeq);
        recordAmountBo.type(type);
        recordAmountBo.consumeType(type);
        recordAmountBo.consumeCode(payBo.getMasterOrderNo());
        recordAmountBo.msg(msg);
        recordAmountBo.amountType(SohuTradeRecordEnum.AmountType.Expend.getCode());
        recordAmountBo.accountType(SohuTradeRecordEnum.AccountType.Amount.getCode());
        recordAmountBo.transactionId(paymentId);
        recordAmountBo.virtualCoin(coin);
        // 保存充值流水记录-购买者 - 钱 - 支出 , 对虚拟币来说是收入，在成功支付之后，才会插入 虚拟币收入的一条数据
        saveTradeRecord(recordAmountBo.build());

        // 保存主支付单
        saveMasterPayOrderSpecial(payBo, posSeq, PayStatus.WaitPay.name(), paymentId, payBo.getUuid());
        // 保存子支付单
        savePayOrderSpecial(payBo, OrderConstants.ORDER_PREFIX_PLATFORM + posSeq, posSeq, PayStatus.WaitPay.name(), paymentId, payBo.getUuid());
        // 获取付款单跳转链接
        String href = getPayOrderHref(payOrder);
        // 校验跳转链接
        requireNotPayOrderHref(href);
        RedisUtils.setCacheObject(PayTypeEnum.PAY_TYPE_PAYPAL.getStatus() + StrPool.COLON + paymentId, JSONUtil.toJsonStr(payBo));
        return JSONUtil.toJsonStr(buildPayResponse(paymentId, href));

    }

}
