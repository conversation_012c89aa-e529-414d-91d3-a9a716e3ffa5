package com.sohu.pay.service.strategy.yima;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.sohu.busyorder.api.RemoteBusyOrderService;
import com.sohu.busyorder.api.RemoteBusyTaskReceiveService;
import com.sohu.busyorder.api.model.SohuBusyTaskModel;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.BigDecimalUtils;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.NumberUtil;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.im.api.service.RemoteImService;
import com.sohu.middle.api.service.RemoteMiddleBillRecordService;
import com.sohu.middle.api.service.RemoteMiddleCategoryService;
import com.sohu.middle.api.service.RemoteMiddleTradeRecordService;
import com.sohu.middle.api.vo.SohuBillRecordVo;
import com.sohu.pay.api.bo.SohuOnlineBankLogBo;
import com.sohu.pay.api.bo.SohuWithdrawPayBo;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.domain.SohuRefundPayBo;
import com.sohu.pay.api.vo.SohuAccountBankVo;
import com.sohu.pay.service.*;
import com.sohu.pay.service.strategy.AbsTradeRecordStrategy;
import com.sohu.pay.service.strategy.PaymentStrategy;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.wangcaio2o.ipossa.sdk.model.ExtendParams;
import com.wangcaio2o.ipossa.sdk.model.SplitInfo;
import com.wangcaio2o.ipossa.sdk.model.SplitList;
import com.wangcaio2o.ipossa.sdk.request.onlinebankpay.OnlineBankpay;
import com.wangcaio2o.ipossa.sdk.request.onlinebankpay.OnlineBankpayRequest;
import com.wangcaio2o.ipossa.sdk.request.scanpay.Scanpay;
import com.wangcaio2o.ipossa.sdk.request.scanpay.ScanpayRequest;
import com.wangcaio2o.ipossa.sdk.response.callback.CallbackRequest;
import com.wangcaio2o.ipossa.sdk.response.onlinebankpay.OnlineBankpayResponse;
import com.wangcaio2o.ipossa.sdk.response.scanpay.ScanpayResponse;
import com.wangcaio2o.ipossa.sdk.test.Client;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 在线网银支付
 * zc
 * 12.16
 */
@Component
@Slf4j
public class YiMaOnlineBankPayStrategy extends AbsTradeRecordStrategy implements YiMaPayStrategy {

    @DubboReference
    private RemoteBusyOrderService remoteBusyOrderService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;
    @DubboReference
    private RemoteImService remoteImService;
    @DubboReference
    private RemoteMiddleTradeRecordService remoteMiddleTradeRecordService;
    @DubboReference
    private RemoteMiddleCategoryService remoteMiddleCategoryService;
    @Resource
    private ISohuMasterPayOrderService sohuMasterPayOrderService;
    @DubboReference
    private RemoteBusyTaskReceiveService remoteBusyTaskReceiveService;
    @DubboReference
    private RemoteMiddleBillRecordService remoteBillRecordService;
    @Resource
    private ISohuAccountBankService accountBankService;
    @Resource
    private ISohuOnlineBankLogService onlineBankLogService;

    @Resource
    private ISohuWithdrawPayService withdrawPayService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payment(SohuPrePayBo payBo) {
        log.info("翼码支付 - 网银下单支付 - 支付来源：{}", JSONUtil.toJsonStr(payBo));
        return getPay(payBo);
    }

    @Override
    public Boolean refund(SohuRefundPayBo refundPayBo) {
        log.info("翼码网银支付 -网银支付退款 - 退款来源：{}", JSONUtil.toJsonStr(refundPayBo));
        // todo 12月24号这一期没有退款
//        String busyOrder = refundPayBo.getMasterOrderNo();
//        SohuBusyTaskReceiveModel taskReceiveModel = remoteBusyOrderService.queryReceiveByTaskNoAndBackup(busyOrder, Boolean.TRUE);
//        SohuBusyOrderPayModel orderPayModel = remoteBusyOrderService.queryBusyOrderPay(busyOrder, PayObject.BusyTaskPromise.name(), PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.Paid.name());
//        if (orderPayModel.getPayAmount() != null && CalUtils.isZero(taskReceiveModel.getAmount()) &&
//                StrUtil.equalsAnyIgnoreCase(orderPayModel.getPayStatus(), PayStatus.Paid.name())) {
//            remoteBusyOrderService.updateBusyOrderAndRece(orderPayModel.getBusyOrderReceiveId(), false);
//            return Boolean.TRUE;
//        }
//        try {
//            SohuBusyTaskSiteModel sohuBusyTaskSiteModel = remoteBusyOrderService.querySiteByTaskNo(taskReceiveModel.getTaskNumber());
//            if (Objects.nonNull(sohuBusyTaskSiteModel)) {
//                remoteImService.deleteGroupTaskNoLogin(sohuBusyTaskSiteModel.getUserId(), taskReceiveModel.getTaskNumber());
//                log.info("任务群解散,taskNumber：{}", taskReceiveModel.getTaskNumber());
//            }
//        } catch (Exception e) {
//            log.error("任务群解散失败,taskNumber：{}", taskReceiveModel.getTaskNumber());
//        }
//
//        SohuMasterPayOrderVo masterPayOrderVo = sohuMasterPayOrderService.queryByMasterPayNumberAndPayType("M" + orderPayModel.getOrderNo(), orderPayModel.getPayType());
//        YiMaPayConfig yiMaPayConfig = getPayConfig();
//        // 组装微信小程序退款请求参数
//        BarcodeReverseRequest request = getBarcodeReverseRequest();
//        request.setPosId(yiMaPayConfig.getPosId());
//        request.setIsspid(yiMaPayConfig.getIssPid());
//        request.setSystemId(yiMaPayConfig.getSystemId());
//        request.setStoreId(yiMaPayConfig.getStoreId());
//        // 退款参数封装
//        BarcodeReverse reverse = new BarcodeReverse();
//        reverse.setPayType(masterPayOrderVo.getChildPayType());
//        // 请求退款单号拼接
//        request.setPosSeq("R" + System.nanoTime());
//        // 支付请求流水号
//        reverse.setOrgPosSeq(orderPayModel.getOrderNo());
//        reverse.setTxAmt(BigDecimalUtils.yuanToFen(orderPayModel.getPayAmount()));
//        request.setBarcodeReverseRequest(reverse);
//        log.info("商单预付款退款请求:{}", JSONUtil.toJsonStr(request));
//
//        BarcodeReverseResponse response = Client.getClient().execute(request);
//        List<String> resultList = Lists.newArrayList("9998", "0000");
//        if (ObjectUtils.isNull(response) || !resultList.contains(response.getResult().getId())) {
//            log.error("商单预付款退款异常：{}", JSONUtil.toJsonStr(response));
//            throw new RuntimeException(response.getResult().getComment());
//        }
//        orderPayModel.setPayableAmount(orderPayModel.getPayAmount());
//        // 保存商单预付款退款记录
//        remoteBusyOrderService.savePrepaymentRefundBillRecord(orderPayModel);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payCallback(String callbackResponse) {
        log.info("翼码支付 - 网银下单回调 ：{}", callbackResponse);
        CallbackRequest response = JSONObject.parseObject(callbackResponse, CallbackRequest.class);
        String outTradeNo = response.getPosSeq();
        String transactionId = response.getTradeNo();
        // 更新提现记录状态 待处理->处理中
        remoteBillRecordService.updateStatusByPayNumber(outTradeNo,PayStatus.Processing.name());
        // 更改提现支付单状态
        withdrawPayService.updateByPayNumber(outTradeNo,transactionId,PayStatus.Paid.name());
        // 延时消息队列（5min） 发起客户提现
        MqMessaging mqMessaging = new MqMessaging(outTradeNo, MqKeyEnum.YI_MA_WITHDRAWAL.getKey());
        remoteStreamMqService.sendDelayMsg(mqMessaging, 9L);
        return "success";
    }


    /**
     * 财务支付总金额逻辑
     * x=财务支付总金额
     * n= 用户数
     * a= 每个用户提现金额数
     * y=提现总金额（n*a）
     * 1%自留金额规定最小值比例
     * 0.3%手续费
     * x=自留金额+提现总金额+手续费
     * x=x*1%+y+x*0.3%
     * 例如提现总金额为y=1000
     * x=1.3%x+1000
     * x=1013.***************
     * 向上取整x=1014
     * 表存储：提现金额1000、手续费由支付回调具体返回、请求自留金额=1014-1000=14、实际自留金额=14-手续费
     */
    @Transactional(rollbackFor = Exception.class)
    public String getPay(SohuPrePayBo payBo) {
//        return onlineBankPay(payBo);
        return scanPay(payBo);
    }

    protected String onlineBankPay(SohuPrePayBo payBo) {
        String key = CacheConstants.ORDER_PAY_TWO + PayTypeEnum.PAY_TYPE_YI_MA.getStatus() + StrPool.COLON +
                payBo.getMasterOrderNo() + StrPool.COLON + getOperateChannel(payBo.getPayChannel()) + StrPool.COLON + payBo.getUserId();
        if (StrUtil.isEmpty(payBo.getBillIds())){
            throw new ServiceException("请选择要提现的订单");
        }
        List<Long> billIds = StrUtil.split(payBo.getBillIds(), StrPool.COMMA).stream().map(Long::valueOf).collect(Collectors.toList());
        //查询体现的订单
        List<SohuBillRecordVo> billRecordVos = remoteBillRecordService.queryWithdrawalByIds(billIds);
        if (CollUtil.isEmpty(billRecordVos)){
            throw new ServiceException("无可提现的订单");
        }
        //提现的总金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        List<Long> userIds = new ArrayList<>();
        Map<Long,BigDecimal> userAmountMap = new HashMap<>();
        for (SohuBillRecordVo billRecordVo : billRecordVos) {
            totalAmount = totalAmount.add(billRecordVo.getAmount());
            userIds.add(billRecordVo.getUserId());
            if (userAmountMap.get(billRecordVo.getUserId())!=null){
                userAmountMap.put(billRecordVo.getUserId(),userAmountMap.get(billRecordVo.getUserId()).add(billRecordVo.getAmount()));
            }else {
                userAmountMap.put(billRecordVo.getUserId(),billRecordVo.getAmount());
            }
        }
        // 查询所有分账人的翼码帐户信息
        List<SohuAccountBankVo> accountBankVos = accountBankService.queryListByUserId(userIds);
        //获取用户id与翼码映射
        Map<Long,String> bankMap = accountBankVos.stream().collect(Collectors.toMap(SohuAccountBankVo::getUserId, SohuAccountBankVo::getMerchantId));
        //财务需支付的金额 向上取整
        BigDecimal txAmt = CalUtils.divide(totalAmount, new BigDecimal("0.987")).setScale(0, RoundingMode.CEILING);
        //第三方支付单号
        String posSeq = NumberUtil.getOrderNo(OrderConstants.ONLINE_BANK_PREFIX);
        //网银支付封装参数
        OnlineBankpayRequest onlineRequest = getOnlinePayRequest();
        onlineRequest.setMemo("网银支付酬金");
        onlineRequest.setPosSeq(posSeq);

        OnlineBankpay onlineBankpay = new OnlineBankpay();
        //支付金额
        onlineBankpay.setTxAmt(BigDecimalUtils.yuanToFen(txAmt));
        onlineBankpay.setOrderType("P");
        onlineBankpay.setCardType("D");
        onlineBankpay.setTimeExpire(DateUtil.format(DateUtil.offsetDay(new Date(), 1), "yyyyMMddHHmmss"));
        onlineBankpay.setGoodsDesc("个人电脑");
        onlineBankpay.setGateType("01");
        onlineBankpay.setGoodsShortName("个人电脑");
        onlineBankpay.setGwChnnlTp("01");
        onlineBankpay.setBizTp("130004");

        //R实时分账
        ExtendParams extendParams = new ExtendParams();
        extendParams.setSplitFlag(ExtendParams.R);
        // 分账信息
        SplitInfo splitInfo = new SplitInfo();
        //（支付金额-总分账金额）-手续费
        BigDecimal keepAmt = CalUtils.sub(txAmt,totalAmount).subtract(CalUtils.multiply(txAmt,new BigDecimal("0.003")));
        splitInfo.setKeepAmt(BigDecimalUtils.yuanToFen(keepAmt).toString());

        List<SplitList> mergedList = Lists.newArrayList();
        userAmountMap.forEach((k, v) -> {
            SplitList newSplitList = new SplitList();
            newSplitList.setMerchantId(bankMap.get(k));
            newSplitList.setDivAmt(BigDecimalUtils.yuanToFen(v).toString());
            mergedList.add(newSplitList);
        });
        splitInfo.setSplitList(mergedList);
        extendParams.setSplitInfo(splitInfo);
        onlineBankpay.setExtendParams(extendParams);
        onlineRequest.setOnlineBankpayRequest(onlineBankpay);

        log.info("财务提现 -PC- 网银支付酬金请求request：{}", JSONUtil.toJsonStr(onlineRequest));
        OnlineBankpayResponse response = Client.getClient().execute(onlineRequest);
        log.info("财务提现 -PC- 网银支付酬金请求response返回：{}", JSONUtil.toJsonStr(response));
        //判断状态
        List<String> resultList = Lists.newArrayList();
        resultList.add("9998");
        resultList.add("0000");
        if (ObjectUtils.isNull(response) || !resultList.contains(response.getResult().getId())) {
            throw new RuntimeException(response.getResult().getComment());
        }
        //新增提现支付记录
        SohuWithdrawPayBo withdrawPayBo = new SohuWithdrawPayBo();
        withdrawPayBo.setUserId(payBo.getUserId());
        withdrawPayBo.setPayNumber(posSeq);
        withdrawPayBo.setPayAmount(txAmt);
        withdrawPayBo.setWithdrawAmount(totalAmount);
        withdrawPayService.insertByBo(withdrawPayBo);
        //同步提现记录流水号
        remoteBillRecordService.updatePayNumberByIds(billIds,posSeq);
        //记录日志
        SohuOnlineBankLogBo onlineBankLogBo = new SohuOnlineBankLogBo();
        onlineBankLogBo.setRequestType(onlineRequest.getRequestType());
        onlineBankLogBo.setRequestStr(JSONUtil.toJsonStr(onlineRequest));
        onlineBankLogBo.setResponseStr(JSONUtil.toJsonStr(response));
        onlineBankLogBo.setPosSeq(posSeq);
        onlineBankLogService.insertByBo(onlineBankLogBo);

        RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
        return JSONUtil.toJsonStr(response);
    }

    protected String scanPay(SohuPrePayBo payBo) {
        String key = CacheConstants.ORDER_PAY_TWO + PayTypeEnum.PAY_TYPE_YI_MA.getStatus() + StrPool.COLON +
                payBo.getMasterOrderNo() + StrPool.COLON + getOperateChannel(payBo.getPayChannel()) + StrPool.COLON + payBo.getUserId();
        if (StrUtil.isEmpty(payBo.getBillIds())){
            throw new ServiceException("请选择要提现的订单");
        }
        List<Long> billIds = StrUtil.split(payBo.getBillIds(), StrPool.COMMA).stream().map(Long::valueOf).collect(Collectors.toList());
        //查询体现的订单
        List<SohuBillRecordVo> billRecordVos = remoteBillRecordService.queryWithdrawalByIds(billIds);
        if (CollUtil.isEmpty(billRecordVos)){
            throw new ServiceException("无可提现的订单");
        }
        //提现的总金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        List<Long> userIds = new ArrayList<>();
        Map<Long,BigDecimal> userAmountMap = new HashMap<>();
        for (SohuBillRecordVo billRecordVo : billRecordVos) {
            totalAmount = totalAmount.add(billRecordVo.getAmount());
            userIds.add(billRecordVo.getUserId());
            if (userAmountMap.get(billRecordVo.getUserId())!=null){
                userAmountMap.put(billRecordVo.getUserId(),userAmountMap.get(billRecordVo.getUserId()).add(billRecordVo.getAmount()));
            }else {
                userAmountMap.put(billRecordVo.getUserId(),billRecordVo.getAmount());
            }
        }
        // 查询所有分账人的翼码帐户信息
        List<SohuAccountBankVo> accountBankVos = accountBankService.queryListByUserId(userIds);
        //获取用户id与翼码映射
        Map<Long,String> bankMap = accountBankVos.stream().collect(Collectors.toMap(SohuAccountBankVo::getUserId, SohuAccountBankVo::getMerchantId));
        //财务需支付的金额 向上取整
        BigDecimal txAmt = CalUtils.divide(totalAmount, new BigDecimal("0.987")).setScale(0, RoundingMode.CEILING);
        //第三方支付单号
        String posSeq = NumberUtil.getOrderNo(OrderConstants.ONLINE_BANK_PREFIX);
        //网银支付封装参数
        ScanpayRequest scanpayRequest = getScanpayRequest();
        scanpayRequest.setMemo("扫码支付酬金");
        scanpayRequest.setPosSeq(posSeq);

        Scanpay scanpay = getScanpay();
        //支付金额
        scanpay.setTxAmt(BigDecimalUtils.yuanToFen(txAmt));

        //R实时分账
        ExtendParams extendParams = new ExtendParams();
        extendParams.setSplitFlag(ExtendParams.R);
        // 分账信息
        SplitInfo splitInfo = new SplitInfo();
        //（支付金额-总分账金额）-手续费
//        BigDecimal keepAmt = CalUtils.sub(txAmt,totalAmount).subtract(CalUtils.multiply(txAmt,new BigDecimal("0.003")));
        BigDecimal keepAmt = CalUtils.sub(txAmt,totalAmount);
        splitInfo.setKeepAmt(BigDecimalUtils.yuanToFen(keepAmt).toString());

        List<SplitList> mergedList = Lists.newArrayList();
        userAmountMap.forEach((k, v) -> {
            SplitList newSplitList = new SplitList();
            newSplitList.setMerchantId(bankMap.get(k));
            newSplitList.setDivAmt(BigDecimalUtils.yuanToFen(v).toString());
            mergedList.add(newSplitList);
        });
        splitInfo.setSplitList(mergedList);
        extendParams.setSplitInfo(splitInfo);
        scanpay.setExtendParams(extendParams);
        scanpayRequest.setScanpayRequest(scanpay);

        log.info("财务提现 -PC- 扫码支付酬金请求request：{}", JSONUtil.toJsonStr(scanpayRequest));
        ScanpayResponse response = Client.getClient().execute(scanpayRequest);
        log.info("财务提现 -PC- 扫码支付酬金请求response返回：{}", JSONUtil.toJsonStr(response));
        //判断状态
        List<String> resultList = Lists.newArrayList();
        resultList.add("9998");
        resultList.add("0000");
        if (ObjectUtils.isNull(response) || !resultList.contains(response.getResult().getId())) {
            throw new RuntimeException(response.getResult().getComment());
        }
        //新增提现支付记录
        SohuWithdrawPayBo withdrawPayBo = new SohuWithdrawPayBo();
        withdrawPayBo.setUserId(payBo.getUserId());
        withdrawPayBo.setPayNumber(posSeq);
        withdrawPayBo.setPayAmount(txAmt);
        withdrawPayBo.setWithdrawAmount(totalAmount);
        withdrawPayService.insertByBo(withdrawPayBo);
        //同步提现记录流水号
        remoteBillRecordService.updatePayNumberByIds(billIds,posSeq);
        //记录日志
        SohuOnlineBankLogBo onlineBankLogBo = new SohuOnlineBankLogBo();
        onlineBankLogBo.setRequestType(scanpayRequest.getRequestType());
        onlineBankLogBo.setRequestStr(JSONUtil.toJsonStr(scanpayRequest));
        onlineBankLogBo.setResponseStr(JSONUtil.toJsonStr(response));
        onlineBankLogBo.setPosSeq(posSeq);
        onlineBankLogService.insertByBo(onlineBankLogBo);

        RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
        return JSONUtil.toJsonStr(response);
    }
}
