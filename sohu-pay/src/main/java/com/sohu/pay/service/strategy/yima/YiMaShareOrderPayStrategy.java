package com.sohu.pay.service.strategy.yima;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.core.utils.BigDecimalUtils;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.NumberUtil;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.bo.finance.SohuShareMasterOrderBo;
import com.sohu.middle.api.bo.finance.SohuShareOrderBo;
import com.sohu.middle.api.service.finance.RemoteMiddleShareMasterOrderService;
import com.sohu.middle.api.service.finance.RemoteMiddleShareOrderService;
import com.sohu.middle.api.vo.finance.SohuShareMasterOrderVo;
import com.sohu.middle.api.vo.finance.SohuShareOrderVo;
import com.sohu.pay.api.bo.SohuIndependentOrderBo;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.domain.SohuRefundPayBo;
import com.sohu.pay.api.vo.SohuAccountBankVo;
import com.sohu.pay.service.ISohuAccountBankService;
import com.sohu.pay.service.ISohuIndependentOrderService;
import com.sohu.pay.service.strategy.AbsTradeRecordStrategy;
import com.sohu.pay.service.strategy.PaymentStrategy;
import com.wangcaio2o.ipossa.sdk.model.ExtendParams;
import com.wangcaio2o.ipossa.sdk.model.SplitInfo;
import com.wangcaio2o.ipossa.sdk.model.SplitList;
import com.wangcaio2o.ipossa.sdk.request.scanpay.Scanpay;
import com.wangcaio2o.ipossa.sdk.request.scanpay.ScanpayRequest;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.Unifiedorder;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.UnifiedorderRequest;
import com.wangcaio2o.ipossa.sdk.response.callback.CallbackRequest;
import com.wangcaio2o.ipossa.sdk.response.scanpay.ScanpayResponse;
import com.wangcaio2o.ipossa.sdk.response.unifiedorder.UnifiedorderResponse;
import com.wangcaio2o.ipossa.sdk.test.Client;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;

/**
 * 金融分享订单支付
 */
@Component
@Slf4j
public class YiMaShareOrderPayStrategy extends AbsTradeRecordStrategy implements YiMaPayStrategy {

    /**
     * 分销人分账比例
     */
    private final static BigDecimal CON_INDEPENDENT_DISTRIBUTOR_RATIO = new BigDecimal("0.99");

    @DubboReference
    private RemoteMiddleShareOrderService remoteShareOrderService;
    @DubboReference
    private RemoteMiddleShareMasterOrderService remoteMiddleShareMasterOrderService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private ISohuAccountBankService sohuAccountBankService;
    @Resource
    private ISohuIndependentOrderService sohuIndependentOrderService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payment(SohuPrePayBo payBo) {
        log.info("翼码支付 - Share订单支付 - 支付来源：{}", JSONUtil.toJsonStr(payBo));
        return getPay(payBo);
    }

    @Override
    public Boolean refund(SohuRefundPayBo refundPayBo) {
        log.info("翼码支付 - Share订单退款 - 退款来源：{}", JSONUtil.toJsonStr(refundPayBo));
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payCallback(String callbackResponse) {
        log.info("翼码支付 - Share订单支付回调 ：{}", callbackResponse);
        CallbackRequest response = JSONObject.parseObject(callbackResponse, CallbackRequest.class);
        String outTradeNo = response.getPosSeq();
        // 校验支付状态
        checkPayStatus(response.getStatus());
        SohuShareMasterOrderVo masterOrder = remoteMiddleShareMasterOrderService.selectByMasterOutTradeNo(outTradeNo);
        SohuShareMasterOrderBo masterOrderBo = BeanUtil.copyProperties(masterOrder, SohuShareMasterOrderBo.class);
        // 添加:系统内部的订单号、支付流水号、修改支付状态
        masterOrderBo.setTransactionId(response.getTradeNo());
        masterOrderBo.setPaid(true);
        masterOrderBo.setChargePrice(CalUtils.centToYuan(new BigDecimal(response.getChargeAmount())));

        List<SohuShareOrderVo> shareOrderList = remoteShareOrderService.getListByMasterNo(masterOrderBo.getOrderNo());
        List<SohuShareOrderBo> shareOrderBoList = BeanCopyUtils.copyList(shareOrderList, SohuShareOrderBo.class);
        // 添加支付成功修改订单状态  --弃用redis队列
        Boolean execute = transactionTemplate.execute(e -> {
            for (SohuShareOrderBo storeOrder : shareOrderBoList) {
                storeOrder.setPaid(true);
                storeOrder.setPayTime(new Date());
                storeOrder.setPayType(masterOrderBo.getPayType());
                storeOrder.setStatus(OrderConstants.ORDER_STATUS_OVER);
            }
            remoteShareOrderService.updateBatchById(shareOrderBoList);
            //remoteShareOrderService.updateShareMasterOrderByMasterId(masterOrderBo);
            remoteMiddleShareMasterOrderService.updateByBo(masterOrderBo);
            return Boolean.TRUE;
        });
        if (Boolean.FALSE.equals(execute)) {
            log.error("wechat pay error : 订单更新失败==》" + masterOrderBo.getOutTradeNo());
            throw new ServiceException("订单回调修改数据失败");
        }
        // 更新流水记录成功
        remoteMiddleTradeRecordService.updatePayStatus(outTradeNo, response.getTradeNo(), PayStatus.Paid, null);
        updatePayOrder(outTradeNo, masterOrderBo.getChargePrice(), PayStatus.Paid.name(), response.getTradeNo());
        return "success";
    }

    /**
     * Share订单支付-开通一键分发功能
     */
    @Transactional(rollbackFor = Exception.class)
    public String getPay(SohuPrePayBo payBo) {
        String key = CacheConstants.ORDER_PAY_TWO + PayTypeEnum.PAY_TYPE_YI_MA.getStatus() + StrPool.COLON +
                payBo.getMasterOrderNo() + StrPool.COLON + getOperateChannel(payBo.getPayChannel()) + StrPool.COLON + payBo.getUserId();
        boolean exists = RedisUtils.isExistsObject(key);
        // 如果存在直接唤醒
        if (exists) {
            log.info("Share订单支付订单存在，直接唤醒支付，订单号：{}，缓存值：{}", payBo.getMasterOrderNo(), RedisUtils.getCacheObject(key));
            return RedisUtils.getCacheObject(key);
        }
        //主订单
        SohuShareMasterOrderVo masterOrder = remoteMiddleShareMasterOrderService.selectByMasterOrderNo(payBo.getMasterOrderNo());
        // 状态判断
        if (ObjectUtil.isNull(masterOrder)) {
            throw new ServiceException("order does not exist");
        }
        if (masterOrder.getIsCancel()) {
            throw new ServiceException("order is cancelled");
        }
        if (masterOrder.getPaid()) {
            throw new ServiceException("order not paid");
        }
        //订单详情
        List<SohuShareOrderVo> orderList = remoteShareOrderService.getListByMasterNo(payBo.getMasterOrderNo());
        if (CollUtil.isEmpty(orderList)) {
            throw new ServiceException("order does not exist");
        }
        //清理旧数据
        if (StrUtil.isNotBlank(masterOrder.getOutTradeNo())) {
            //清理旧的分账信息
            this.sohuIndependentOrderService.deleteByByOrderNo(masterOrder.getOutTradeNo());
            //清理旧的流水记录
            this.remoteMiddleTradeRecordService.deleteByByPayNumber(masterOrder.getOutTradeNo());
        }

        //第三方支付单号
        String posSeq = NumberUtil.getOrderNo(OrderConstants.FINANCE_SHARE_PREFIX_MASTER);
        // 分账请求流水号
        String delayOrderNo = NumberUtil.getOrderNo(OrderConstants.YI_MA_INDEPENDENT_NO);
        //分账
        Map<Long, SohuIndependentOrderBo> independentOrderMap = new HashMap<>();
        for (SohuShareOrderVo orderModel : orderList) {
            SohuIndependentOrderBo independentOrderBo = independentOrderMap.get(orderModel.getShareUserId());
            if (independentOrderBo == null) {
                independentOrderBo = new SohuIndependentOrderBo();
                independentOrderBo.setOrderNo(posSeq);
                independentOrderBo.setTradeNo(delayOrderNo);
                independentOrderBo.setTradeType(BusyType.FINANCE_SHARE.name());
                independentOrderBo.setIndependentStatus(2);
                independentOrderBo.setUserId(orderModel.getShareUserId());
                independentOrderBo.setIndependentObject(SohuIndependentObject.distribution.getKey());
                independentOrderBo.setIndependentPrice(orderModel.getPayPrice());
                independentOrderBo.setMerId(0L);
                independentOrderBo.setSiteId(0L);
            } else {
                independentOrderBo.setIndependentPrice(CalUtils.add(independentOrderBo.getIndependentPrice(), orderModel.getPayPrice()));
            }
            independentOrderMap.put(orderModel.getShareUserId(), independentOrderBo);
        }

        // 查询所有参与分账人员的翼码账户信息
        Map<Long, SohuAccountBankVo> accountBankVoMap = sohuAccountBankService.queryMapByUserIds(new ArrayList<>(independentOrderMap.keySet()));
        log.warn("accountBankVoMap : {}", JSONUtil.toJsonStr(accountBankVoMap));

        // 分账账号信息参数
        List<SplitList> splitLists = new ArrayList<>();

        //分账接收总金额（不含平台）
        BigDecimal shareTotalPrice = BigDecimal.ZERO;
        // 累计的没有分出去的金额
        BigDecimal newPlatePrice = BigDecimal.ZERO;
        //参与分账的
        List<SohuIndependentOrderBo> newIndependentOrderList = new ArrayList<>();
        for (Long userId : independentOrderMap.keySet()) {
            SohuIndependentOrderBo independentOrderBo = independentOrderMap.get(userId);
            //分销人做多不能超过99%
            BigDecimal independentPrice = CalUtils.multiplyOfFloor(independentOrderBo.getIndependentPrice(), CON_INDEPENDENT_DISTRIBUTOR_RATIO);
            independentOrderBo.setIndependentPrice(independentPrice);
            SohuAccountBankVo sohuAccountBankVo = accountBankVoMap.get(userId);
            if (sohuAccountBankVo != null) {
                SplitList item = new SplitList();
                item.setDivAmt(BigDecimalUtils.yuanToFen(independentOrderBo.getIndependentPrice()).toString());
                item.setMerchantId(sohuAccountBankVo.getMerchantId());
                splitLists.add(item);
                newIndependentOrderList.add(independentOrderBo);
                shareTotalPrice = CalUtils.add(shareTotalPrice, independentOrderBo.getIndependentPrice());
            } else {
                newPlatePrice = CalUtils.add(newPlatePrice, independentOrderBo.getIndependentPrice());
            }
        }
        // 平台最少1%的留存
        //BigDecimal adminPrice = CalUtils.multiply(masterOrder.getPayPrice(), CON_INDEPENDENT_ADMIN_RATIO);
        BigDecimal adminPrice = CalUtils.sub(masterOrder.getPayPrice(), shareTotalPrice, newPlatePrice);
        //没分出去的钱给文化传媒
        if (newPlatePrice.compareTo(BigDecimal.ZERO) > 0) {
            SplitList item = new SplitList();
            item.setDivAmt(BigDecimalUtils.yuanToFen(newPlatePrice).toString());
            item.setMerchantId("2290110");
            splitLists.add(item);
        }

        // 分账信息
        SplitInfo splitInfo = new SplitInfo();
        // 设置分账参数
        splitInfo.setSplitList(splitLists);
        //平台留存金额
        splitInfo.setKeepAmt(BigDecimalUtils.yuanToFen(adminPrice).toString());

        UnifiedorderRequest unifiedorderRequest = getUnifiedorderRequest();
        ScanpayRequest scanpayRequest = getScanpayRequest();
        // 备注
        unifiedorderRequest.setMemo("金融分享功能支付");
        scanpayRequest.setMemo("金融分享功能支付");

        // 唯一订单号
        unifiedorderRequest.setPosSeq(posSeq);
        scanpayRequest.setPosSeq(posSeq);
        // 总金额
        Unifiedorder unifiedOrder = getUnifiedorder(payBo.getUserId());
        Scanpay scanpay = getScanpay();
        scanpay.setTxAmt(BigDecimalUtils.yuanToFen(masterOrder.getPayPrice()));
        unifiedOrder.setTxAmt(BigDecimalUtils.yuanToFen(masterOrder.getPayPrice()));
        // 不分账 -- R实时分账 --D延时分账
        ExtendParams extendParams = new ExtendParams();
        extendParams.setSplitInfo(splitInfo);
        extendParams.setSplitFlag(ExtendParams.R);
        scanpay.setExtendParams(extendParams);
        // 设置请求参数
        unifiedorderRequest.setUnifiedorderRequest(unifiedOrder);
        scanpayRequest.setScanpayRequest(scanpay);

        //更新第三方支付单号
        SohuShareMasterOrderBo masterOrderBo = BeanUtil.copyProperties(masterOrder, SohuShareMasterOrderBo.class);
        masterOrderBo.setOutTradeNo(posSeq);
        masterOrderBo.setAdminPrice(adminPrice);
        //remoteShareOrderService.updateShareMasterOrderByMasterId(masterOrderBo);
        remoteMiddleShareMasterOrderService.updateByBo(masterOrderBo);
        //保存分账信息
        sohuIndependentOrderService.insertByBoList(newIndependentOrderList);

        // 保存流水记录
        List<SohuTradeRecordBo> tradeRecordList = new ArrayList<>();
        for (SohuIndependentOrderBo independentOrderBo : newIndependentOrderList) {
            tradeRecordList.add(this.buildSohuTradeRecord(payBo.getPayChannel(), posSeq,
                    independentOrderBo.getIndependentPrice(), posSeq, independentOrderBo.getUserId()));
        }
        remoteMiddleTradeRecordService.insertBatch(tradeRecordList);
        payBo.setAmount(masterOrder.getPayPrice());
        // 保存主支付单
        saveMasterPayOrder(payBo, posSeq, PayStatus.WaitPay.name());
        // 保存子支付单
        savePayOrder(payBo, OrderConstants.ORDER_PREFIX_PLATFORM + posSeq, posSeq, PayStatus.WaitPay.name());

        if (StrUtil.equalsAnyIgnoreCase(payBo.getPayChannel(), Constants.CHANNEL_PC)) {
            log.info("翼码支付 - PC-Share订单支付请求request：{}", JSONUtil.toJsonStr(scanpayRequest));
            ScanpayResponse response = Client.getClient().execute(scanpayRequest);
            log.info("翼码支付 - PC-Share订单支付请求response返回：{}", JSONUtil.toJsonStr(response));
            RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
            return JSONUtil.toJsonStr(response);
        }
        log.info("翼码支付 -MOBILE- Share订单支付请求request：{}", JSONUtil.toJsonStr(unifiedorderRequest));
        UnifiedorderResponse response = Client.getClient().execute(unifiedorderRequest);
        log.info("翼码支付 -MOBILE- Share订单支付请求response返回：{}", JSONUtil.toJsonStr(response));
        RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
        return JSONUtil.toJsonStr(response);
    }

    /**
     * 构建支付流水记录
     *
     * @param payChannel  支付渠道：pc、mobile
     * @param consumeCode 流水消费对象ID
     * @param amount      流水金额
     * @param payNumber   狐少少系统单号
     * @param userId      用户id
     * @return {@link SohuTradeRecordBo}
     */
    private SohuTradeRecordBo buildSohuTradeRecord(String payChannel, String consumeCode, BigDecimal amount, String payNumber, Long userId) {
        SohuTradeRecordBo recordBo = SohuTradeRecordBo.builder().build();
        recordBo.setUserId(userId);
        recordBo.setType(SohuTradeRecordEnum.Type.FINANCE_SHARE.getCode());
        recordBo.setConsumeType(SohuTradeRecordEnum.Type.FINANCE_SHARE.getCode());
        recordBo.setConsumeCode(consumeCode);
        recordBo.setAmount(amount);
        recordBo.setMsg(SohuTradeRecordEnum.Type.FINANCE_SHARE.getMsg());
        recordBo.setAmountType(SohuTradeRecordEnum.AmountType.InCome.getCode());
        recordBo.setPayType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus());
        recordBo.setOperateChannel(getOperateChannel(payChannel));
        recordBo.setPayNumber(payNumber);
        recordBo.setPayStatus(PayStatus.WaitPay.name());
        recordBo.setAccountType(SohuTradeRecordEnum.AccountType.Amount.name());
        recordBo.setUnq(getUnq());
        return recordBo;
    }

}
