package com.sohu.pay.service.strategy.wechat;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.DictEnum;
import com.sohu.common.core.enums.PayTypeEnum;
import com.sohu.common.core.utils.BigDecimalUtils;
import com.sohu.common.core.utils.SpringUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.pay.api.domain.SohuPayQueryBo;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.domain.SohuRefundPayBo;
import com.sohu.pay.api.model.SohuPayResultModel;
import com.sohu.pay.service.strategy.PaymentStrategy;
import com.sohu.system.api.RemoteDictService;
import com.sohu.system.api.domain.SysDictData;
import com.sohu.third.wechat.pay.bean.WechatPayConfig;
import com.sohu.third.wechat.pay.constant.WechatPayConstants;
import com.sohu.third.wechat.pay.request.WechatPayOrderQueryRequest;
import com.sohu.third.wechat.pay.request.WechatPayRefundQueryRequest;
import com.sohu.third.wechat.pay.response.WechatPayQueryResponse;
import com.sohu.third.wechat.pay.response.WechatPayRefundQueryResponse;
import com.sohu.third.wechat.pay.service.WechatPayService;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.Objects;

public interface WechatPayStrategy extends PaymentStrategy {

    /**
     * 支付类型：渠道-PC
     */
    String PAY_TYPE_CHANNEL_PC = "503";

    /**
     * 支付类型：渠道-Mobile（小程序）
     */
    String PAY_TYPE_CHANNEL_MOBILE = "515";

    @Override
    String payment(SohuPrePayBo payBo);

    /**
     * 查询支付结果
     *
     * @param payQueryBo 预支付请求对象
     */
    @Override
    default SohuPayResultModel paySuccess(SohuPayQueryBo payQueryBo) {
        WechatPayOrderQueryRequest queryRequest = new WechatPayOrderQueryRequest();
        queryRequest.setOutTradeNo(payQueryBo.getOrderNo());
        // 回调时不是支付成功状态先主动查询，如果主动查询状态也是不是SUCCESS就返回false
        WechatPayQueryResponse wechatPayQueryResponse;
        SohuPayResultModel payPalResultResponse = new SohuPayResultModel();
        try {
            wechatPayQueryResponse = WechatPayService.queryOrder(queryRequest, getWechatPayConfig(payQueryBo.getPayType()));
            if (!wechatPayQueryResponse.getTradeState().equals("SUCCESS") || ObjectUtil.isNull(wechatPayQueryResponse) ||
                    wechatPayQueryResponse.getCode().equals("ORDER_NOT_EXIST")) {
                payPalResultResponse.setStatus(Boolean.FALSE).setOrderNo(payQueryBo.getOrderNo());
                return payPalResultResponse;
            }
            BigDecimal centToYuanTotal = BigDecimalUtils.centToYuan(new BigDecimal(wechatPayQueryResponse.getAmount().getPayerTotal()));
            // 返回支付成功相关结果
            payPalResultResponse.setPayPrice(centToYuanTotal).setOrderNo(wechatPayQueryResponse.getOutTradeNo()).setStatus(Boolean.TRUE);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return payPalResultResponse;
    }

    /**
     * 根据支付渠道获取支付类型
     * PC:503；Mobile:515
     *
     * @param payChannel 支付渠道
     */
    default String getPayType(String payChannel) {
        if (StringUtils.isEmpty(payChannel)) {
            return PAY_TYPE_CHANNEL_PC;
        }
        if (payChannel.equals(Constants.CHANNEL_MOBILE)) {
            return PAY_TYPE_CHANNEL_MOBILE;
        }
        return PAY_TYPE_CHANNEL_PC;
    }

    ;


    @Override
    Boolean refund(SohuRefundPayBo refundPayBo);


    /**
     * 退款查询结果
     *
     * @param refundOrderNo
     * @return
     */
    @Override
    default Boolean refundSuccess(String refundOrderNo) {
        try {
            // 退款查询 商家退款单号--微信outRefundNo
            WechatPayRefundQueryRequest refundRequest = new WechatPayRefundQueryRequest();
            // 微信退款查询参数--退款单号
            refundRequest.setOutRefundNo(refundOrderNo);
            WechatPayRefundQueryResponse queryResponse = WechatPayService.refundQuery(refundRequest, getWechatPayConfig(PayTypeEnum.PAY_TYPE_WECHAT_JSAPI.getStatus()));
            if (!queryResponse.isSuccess() && !queryResponse.getStatus().equals("SUCCESS")) {
                return Boolean.FALSE;
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override

    default Object paySuccessQuery(SohuPayQueryBo payQueryBo) {
        return null;
    }

    /**
     * 付款成功后的回调
     *
     * @param callbackResponse 三方回调信息
     */
    @Override
    String payCallback(String callbackResponse);

    /**
     * 根据支付方式查询支付配置
     *
     * @param payType
     * @return WechatPayConfig
     */
    default WechatPayConfig getWechatPayConfig(String payType) {
        RemoteDictService remoteDictService = SpringUtils.getBean(RemoteDictService.class);
        SysDictData dictData = remoteDictService.getDictData(DictEnum.payConfig.getKey(), payType);
        if (Objects.isNull(dictData)) {
            System.out.println(PayTypeEnum.PAY_TYPE_WECHAT_JSAPI.getDescription() + "配置为空");
            return null;
        }
        WechatPayConfig wechatPayConfig = JSONObject.parseObject(dictData.getDictValue(), WechatPayConfig.class);
        wechatPayConfig.setTradeType(WechatPayConstants.TradeType.JSAPI);
        wechatPayConfig.setSignType("MD5");
        return wechatPayConfig;
    }

    /**
     * 解析微信notifyXml
     *
     * @param request
     * @return notifyXml
     */
    default String getWxNotifyXml(HttpServletRequest request) {
        try {
            BufferedReader br = new BufferedReader(new InputStreamReader(request.getInputStream()));
            String line;
            StringBuilder sb = new StringBuilder();
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
            br.close();
            String notifyXml = sb.toString();
            return notifyXml;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 关单接口
     * @param outTradeNo
     * @return
     */
    @Override
    default Boolean close(String outTradeNo, String payType) {
        return Boolean.TRUE;
    }

}
