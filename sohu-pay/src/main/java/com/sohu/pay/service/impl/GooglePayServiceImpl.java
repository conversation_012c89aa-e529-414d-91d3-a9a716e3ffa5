package com.sohu.pay.service.impl;

import cn.hutool.json.JSONUtil;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.AndroidPublisherScopes;
import com.google.api.services.androidpublisher.model.ProductPurchase;
import com.google.api.services.androidpublisher.model.SubscriptionPurchase;
import com.google.api.services.androidpublisher.model.SubscriptionPurchaseV2;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.GoogleCredentials;
import com.sohu.busyorder.api.model.SohuBusyOrderPayModel;
import com.sohu.pay.api.domain.SohuPayQueryBo;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.model.SohuOrderRefundModel;
import com.sohu.pay.api.model.SohuPayResultModel;
import com.sohu.pay.service.AbstractPayService;
import com.sohu.third.wechat.pay.util.ResourcesUtils;
import com.wangcaio2o.ipossa.sdk.response.callback.CallbackRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.GeneralSecurityException;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
public class GooglePayServiceImpl extends AbstractPayService {
    @Override
    public String payment(SohuPrePayBo payModel) {
        return null;
    }

    @Override
    public void payCallback(HttpServletRequest request) {

    }

    @Override
    public String payCallback(CallbackRequest request) {
        return null;
    }

    @Override
    public SohuPayResultModel paySuccess(SohuPayQueryBo payQueryBo) {
        return null;
    }

    @Override
    public Boolean refund(SohuOrderRefundModel refundModel) {
        return null;
    }

    @Override
    public Boolean refund(SohuBusyOrderPayModel sohuBusyOrderPayModel) {
        return null;
    }

    @Override
    public Boolean refundCallback(HttpServletRequest request) {
        return null;
    }

    @Override
    public Boolean refundSuccess(String refundOrderNo) {
        return null;
    }


    //存放多个实例的 AndroidPublisher Map
    public static Map<String, AndroidPublisher> ANDROID_PUBLISHER_MAP = new ConcurrentHashMap<>();

    /**
     * 必须实现对象创建逻辑,可能会存在空
     *
     * @param appName 应用唯一标识（单例模式创建）
     * @return
     */
    public synchronized AndroidPublisher getAuthInstance(String appName) throws Exception {
        AndroidPublisher androidPublisher = ANDROID_PUBLISHER_MAP.get(appName);
        if (Objects.nonNull(androidPublisher)) {
            return androidPublisher;
        }
        InputStream input = null;
        try {
            //获取用户凭证 json文件流
            input = ResourcesUtils.getResourceAsStream("api秘钥json文件");
            //生成Credentials对象
            GoogleCredentials credentials = GoogleCredentials.fromStream(input);
            HttpTransport transport = GoogleNetHttpTransport.newTrustedTransport();
            HttpCredentialsAdapter httpCredentialsAdapter = new HttpCredentialsAdapter(credentials);
            //创建 AndroidPublisher对象
            androidPublisher = new AndroidPublisher.Builder(
                    transport,
                    GsonFactory.getDefaultInstance(),
                    httpCredentialsAdapter).build();
            //放入对象容器
            ANDROID_PUBLISHER_MAP.put(appName, androidPublisher);
        } catch (Exception e) {
            throw new Exception("[Google] client instance create exception: " + e.getMessage());
        } finally {
        }
        return androidPublisher;
    }

    /**
     * 一次性（单次）购买订单对象获取
     *
     * @param appName       应用唯一标识
     * @param packageName   包名称
     * @param productId     商品Id
     * @param purchaseToken 谷歌购买凭据
     * @return
     */
    public ProductPurchase getOneTimeGoogleOrder(String appName, String packageName, String productId, String purchaseToken) throws Exception {
        AndroidPublisher authInstance = this.getAuthInstance(appName);
        if (authInstance == null) {
            throw new Exception("[Google] AndroidPublisher instance is empty");
        }
        ProductPurchase products = null;
        try {
            products = authInstance.purchases().products().get(packageName, productId, purchaseToken).execute();
            log.info("Products order info: {}", JSONUtil.toJsonStr(products));
        } catch (IOException e) {
            throw new Exception("[Google] Get google products order info exception: " + e.getMessage());
        }
        return products;
    }

    /**
     * 订阅购买订单获取（V2版本），最新订阅请使用V2，官方对V1已经不维护了，但是可以用哈！
     *
     * @param appName       应用唯一标识
     * @param packageName   包名称
     * @param purchaseToken 购买凭据
     * @return
     */
    public SubscriptionPurchaseV2 getSubscriptionGoogleOrderV2(String appName, String packageName, String purchaseToken) throws Exception {
        AndroidPublisher authInstance = this.getAuthInstance(appName);
        if (authInstance == null) {
            throw new Exception("[Google] AndroidPublisher instance is empty");
        }
        SubscriptionPurchaseV2 subscriptionsV2 = null;
        try {
            subscriptionsV2 = authInstance.purchases().subscriptionsv2().get(packageName, purchaseToken).execute();
            log.info("SubscriptionsV2 order info: {}", JSONUtil.toJsonStr(subscriptionsV2));
        } catch (IOException e) {
            throw new Exception("[Google] Get google subscriptions v2 order info exception: " + e.getMessage());
        }
        return subscriptionsV2;
    }

    /**
     * 取消订阅方法
     *
     * @param appName        应用唯一标识
     * @param packageName    包名称
     * @param subscriptionId 订阅Id，其实就是商品Id
     * @param purchaseToken  购买凭据
     * @return
     */
    public void cancelSubscription(String appName, String packageName, String subscriptionId, String purchaseToken) throws Exception {
        AndroidPublisher authInstance = this.getAuthInstance(appName);
        if (authInstance == null) {
            throw new Exception("[Google] AndroidPublisher instance is empty");
        }
        try {
            authInstance.purchases().subscriptions().cancel(packageName, subscriptionId, purchaseToken).execute();
            log.info("Cancel subscription success");
        } catch (IOException e) {
            throw new Exception("[Google] Cancel google subscriptions exception: " + e.getMessage());
        }
    }

    /**
     * 订阅退款方法
     *
     * @param appName        应用唯一标识
     * @param packageName    包名称
     * @param subscriptionId 订阅Id，其实就是商品Id
     * @param purchaseToken  购买凭据
     * @return
     */
    public void refundSubscription(String appName, String packageName, String subscriptionId, String purchaseToken) throws Exception {
        AndroidPublisher authInstance = this.getAuthInstance(appName);
        if (authInstance == null) {
            throw new Exception("[Google] AndroidPublisher instance is empty");
        }
        try {
            authInstance.purchases().subscriptions().refund(packageName, subscriptionId, purchaseToken).execute();
            log.info("Refund subscription success");
        } catch (IOException e) {
            throw new Exception("[Google] Refund google subscriptions exception: " + e.getMessage());
        }
    }

    /**
     * 订阅撤销方法，但是不建议我们实现，让客户自己在Google play商店操作订阅的状态
     *
     * @param appName        应用唯一标识
     * @param packageName    包名称
     * @param subscriptionId 订阅Id，其实就是商品Id
     * @param purchaseToken  购买凭据
     * @return
     */
    public void revokeSubscription(String appName, String packageName, String subscriptionId, String purchaseToken) throws Exception {
        AndroidPublisher authInstance = this.getAuthInstance(appName);
        if (authInstance == null) {
            throw new Exception("[Google] AndroidPublisher instance is empty");
        }
        try {
            authInstance.purchases().subscriptions().revoke(packageName, subscriptionId, purchaseToken).execute();
            log.info("Revoke subscription success");
        } catch (IOException e) {
            throw new Exception("[Google] Revoke google subscriptions exception: " + e.getMessage());
        }
        return;
    }

    /**
     * 订阅购买订单获取（旧版本）,跟V2一样的逻辑, 但是google官网说明方法已经废弃，不维护
     *
     * @param appName        应用唯一标识
     * @param packageName    包名称
     * @param subscriptionId 订阅Id，其实就是商品Id
     * @param purchaseToken  购买凭据
     * @return
     */
    public SubscriptionPurchase getSubscriptionGoogleOrder(String appName, String packageName, String subscriptionId, String purchaseToken) throws Exception {
        AndroidPublisher authInstance = this.getAuthInstance(appName);
        if (authInstance == null) {
            throw new Exception("[Google] AndroidPublisher instance is empty");
        }
        SubscriptionPurchase subscriptions = null;
        try {
            subscriptions = authInstance.purchases().subscriptions().get(packageName, subscriptionId, purchaseToken).execute();
            log.info("Subscriptions order info: {}", JSONUtil.toJsonStr(subscriptions));
        } catch (IOException e) {
            throw new Exception("[Google] Get google subscriptions order info exception: " + e.getMessage());
        }
        return subscriptions;
    }

    public static void main(String[] args) throws GeneralSecurityException, IOException {
        // 你的Google Play开发者帐户的服务账户密钥文件路径
        String serviceAccountKeyFile = "/path/to/your/service-account-key.json";

        // 你的应用的包名
        String packageName = "your.package.name";

        // 要验证的订阅ID
        String subscriptionId = "your_subscription_id";

        // 初始化Google凭据
        GoogleCredential credential = GoogleCredential
                .fromStream(new FileInputStream(serviceAccountKeyFile))
                .createScoped(Collections.singleton(AndroidPublisherScopes.ANDROIDPUBLISHER));

        // 初始化Android Publisher客户端
        AndroidPublisher androidPublisher = new AndroidPublisher.Builder(
                credential.getTransport(),
                credential.getJsonFactory(),
                credential)
                .setApplicationName("YourAppName")
                .build();

        // 获取订阅详细信息
        SubscriptionPurchase subscriptionPurchase = androidPublisher
                .purchases()
                .subscriptions()
                .get(packageName, subscriptionId, "token-from-purchase")
                .execute();

        // 处理订阅详细信息
        System.out.println("Subscription Status: " + subscriptionPurchase.getPaymentState());
    }


}
