package com.sohu.pay.service.strategy;

import com.sohu.common.core.enums.PaySourceEnum;
import com.sohu.common.core.enums.PayTypeEnum;
import com.sohu.common.core.enums.VirtutalPaySourceEnum;
import com.sohu.common.core.utils.SpringUtils;
import com.sohu.pay.service.strategy.airwallex.AirWallexGoodsStrategy;
import com.sohu.pay.service.strategy.airwallex.AirWallexVirtualPayStrategy;
import com.sohu.pay.service.strategy.ali.AliBondPayStrategy;
import com.sohu.pay.service.strategy.apple.ApplePayVirtualStrategy;
import com.sohu.pay.service.strategy.offline.BondOfflinePayStrategy;
import com.sohu.pay.service.strategy.paypal.PayPalGoodsStrategy;
import com.sohu.pay.service.strategy.paypal.PayPalPlayletPayStrategy;
import com.sohu.pay.service.strategy.paypal.PayPalVideoPayStrategy;
import com.sohu.pay.service.strategy.paypal.PayPalVirtualPayStrategy;
import com.sohu.pay.service.strategy.virtual.VirtualPlayletPayStrategy;
import com.sohu.pay.service.strategy.virtual.VirtualVIPayStrategy;
import com.sohu.pay.service.strategy.virtual.VirtualVideoPayStrategy;
import com.sohu.pay.service.strategy.wechat.WechatAppPayStrategy;
import com.sohu.pay.service.strategy.wechat.WechatBondPayStrategy;
import com.sohu.pay.service.strategy.wechat.WechatJsApiPayStrategy;
import com.sohu.pay.service.strategy.yima.*;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

import static com.sohu.common.core.enums.PayTypeEnum.*;

public class PaymentProcessor {
    private final Map<PayTypeEnum, Map<Integer, PaymentStrategy>> strategyMap = new HashMap<>();

    public PaymentProcessor() {
        // 初始化策略
        initializeStrategies();
    }

    /**
     * 初始化策略
     */
    @PostConstruct
    private void initializeStrategies() {
        for (PayTypeEnum payType : PayTypeEnum.values()) {
            if (payType == PayTypeEnum.PAY_TYPE_YI_MA) {
                Map<Integer, PaymentStrategy> sourceStrategyMap = new HashMap<>();
                for (PaySourceEnum paySource : PaySourceEnum.values()) {
                    PaymentStrategy strategy = createStrategy(payType, paySource);
                    sourceStrategyMap.put(paySource.getCode(), strategy);
                }
                strategyMap.put(payType, sourceStrategyMap);
            } else if (payType == PayTypeEnum.PAY_TYPE_VIRTUAL_PAY) {
                // 虚拟币
                Map<Integer, PaymentStrategy> sourceStrategyMap = new HashMap<>();
                for (VirtutalPaySourceEnum paySource : VirtutalPaySourceEnum.values()) {
                    PaymentStrategy strategy = createStrategy(paySource);
                    sourceStrategyMap.put(paySource.getCode(), strategy);
                }
                strategyMap.put(payType, sourceStrategyMap);
            }
            if (payType == PayTypeEnum.PAY_TYPE_APPLE) {
                // 苹果支付
                Map<Integer, PaymentStrategy> sourceStrategyMap = new HashMap<>();
                for (PaySourceEnum paySource : PaySourceEnum.values()) {
                    if (PaySourceEnum.SOHU_VIRTUAL_CURRENCY_RECHARGE != paySource) {
                        continue;
                    }
                    PaymentStrategy strategy = createStrategy(payType, paySource);
                    sourceStrategyMap.put(paySource.getCode(), strategy);
                }
                strategyMap.put(payType, sourceStrategyMap);
            }
            if (payType == PAY_TYPE_WECHAT_APP) {
                PaymentStrategy strategy = createStrategy(payType, PaySourceEnum.SHOP_ORDER_PAY);
                Map<Integer, PaymentStrategy> sourceStrategyMap = new HashMap<>();
                sourceStrategyMap.put(PaySourceEnum.SHOP_ORDER_PAY.getCode(), strategy);
                sourceStrategyMap.put(PaySourceEnum.BOND_ON_LINE.getCode(), SpringUtils.getBean(WechatBondPayStrategy.class));
                strategyMap.put(payType, sourceStrategyMap);
            }
            // paypal支付
            if (payType == PAY_TYPE_PAYPAL) {
                Map<Integer, PaymentStrategy> sourceStrategyMap = new HashMap<>();
                sourceStrategyMap.put(PaySourceEnum.VIDEO_PAY.getCode(), SpringUtils.getBean(PayPalVideoPayStrategy.class));
                sourceStrategyMap.put(PaySourceEnum.PLAYLET_PAY.getCode(), SpringUtils.getBean(PayPalPlayletPayStrategy.class));
                sourceStrategyMap.put(PaySourceEnum.SOHU_VIRTUAL_CURRENCY_RECHARGE.getCode(), SpringUtils.getBean(PayPalVirtualPayStrategy.class));
                sourceStrategyMap.put(PaySourceEnum.SHOP_ORDER_PAY.getCode(), SpringUtils.getBean(PayPalGoodsStrategy.class));
                strategyMap.put(payType, sourceStrategyMap);
            }
            // 空中云汇
            if (payType == PAY_TYPE_AIR_WALLEX) {
                Map<Integer, PaymentStrategy> sourceStrategyMap = new HashMap<>();
                sourceStrategyMap.put(PaySourceEnum.SOHU_VIRTUAL_CURRENCY_RECHARGE.getCode(), SpringUtils.getBean(AirWallexVirtualPayStrategy.class));
                sourceStrategyMap.put(PaySourceEnum.SHOP_ORDER_PAY.getCode(), SpringUtils.getBean(AirWallexGoodsStrategy.class));
                strategyMap.put(payType, sourceStrategyMap);
            }
            // 线下支付
            if (payType == PAY_TYPE_OFFLINE_PAY) {
                Map<Integer, PaymentStrategy> sourceStrategyMap = new HashMap<>();
                sourceStrategyMap.put(PaySourceEnum.BOND_OFF_LINE.getCode(), SpringUtils.getBean(BondOfflinePayStrategy.class));
                strategyMap.put(payType, sourceStrategyMap);
            }
            // 阿里云支付
            if (payType == PAY_TYPE_ALI_PAY) {
                Map<Integer, PaymentStrategy> sourceStrategyMap = new HashMap<>();
                sourceStrategyMap.put(PaySourceEnum.BOND_ON_LINE.getCode(), SpringUtils.getBean(AliBondPayStrategy.class));
                strategyMap.put(payType, sourceStrategyMap);
            }
        }
    }

    private PaymentStrategy createStrategy(PayTypeEnum payType, PaySourceEnum paySource) {
        // 根据支付类型和支付来源创建相应的支付策略
        switch (payType) {
            case PAY_TYPE_WECHAT_NATIVE:
            case PAY_TYPE_WECHAT_H5:
            case PAY_TYPE_APPLE:
                switch (paySource) {
                    case SOHU_VIRTUAL_CURRENCY_RECHARGE:
                        return SpringUtils.getBean(ApplePayVirtualStrategy.class);
                    default:
                        throw new IllegalArgumentException("Unsupported pay source for payment type " + payType + ": " + paySource);
                }
            case PAY_TYPE_WECHAT_JSAPI:
                switch (paySource) {
                    case SHOP_ORDER_PAY:
                        return SpringUtils.getBean(WechatJsApiPayStrategy.class);
                    case BOND_ON_LINE:
                        return SpringUtils.getBean(WechatBondPayStrategy.class);
                    default:
                }
            case PAY_TYPE_WECHAT_APP:
                switch (paySource) {
                    case SHOP_ORDER_PAY:
                        return SpringUtils.getBean(WechatAppPayStrategy.class);
                    case BOND_ON_LINE:
                        return SpringUtils.getBean(WechatBondPayStrategy.class);
                    default:
                }
                // 翼码支付
            case PAY_TYPE_YI_MA:
                switch (paySource) {
                    case SHOP_ORDER_PAY:
                        return SpringUtils.getBean(YiMaShopOrderPayStrategy.class);
                    case BUSY_ORDER_PREPAY:
                        return SpringUtils.getBean(YiMaBusyOrderPayStrategy.class);
                    case MERCHANT_CODE_PAY:
                        return SpringUtils.getBean(YiMaMerchantCodePayStrategy.class);
                    case TASK_PARTY_PAY:
                        return SpringUtils.getBean(YiMaTaskPartyPayStrategy.class);
                    case MCN_ORDER_PAY:
                        return SpringUtils.getBean(YiMaMcnOrderPayStrategy.class);
                    case BUSY_TASK_PROMISE_PAY:
                        return SpringUtils.getBean(YiMaBusyTaskPromisePayStrategy.class);
                    case VIDEO_PAY:
                        return SpringUtils.getBean(YiMaVideoPayStrategy.class);
                    case PLAYLET_PAY:
                        return SpringUtils.getBean(YiMaPlayletPayStrategy.class);
                    case SHORT_PAY:
                        return SpringUtils.getBean(YiMaAllShortPayStrategy.class);
                    case SOHU_VIRTUAL_CURRENCY_RECHARGE:
                        return SpringUtils.getBean(YiMaVirtualPayStrategy.class);
                    case TASK_AFTER_SALES_PAY:
                        return SpringUtils.getBean(YiMaTaskSalePayStrategy.class);
                    case AI_PAY:
                        return SpringUtils.getBean(YiMaAIPayStrategy.class);
                    case FINANCE_SHARE_PAY:
                        return SpringUtils.getBean(YiMaShareOrderPayStrategy.class);
                    case AIREC_PAY:
                        return SpringUtils.getBean(YiMaAirecOrderPayStrategy.class);
                    case BUY_WHOLE_NOVEL:
                        return SpringUtils.getBean(YiMaWholeNovelPayStrategy.class);
                    case BUY_SURA_NOVEL:
                        return SpringUtils.getBean(YiMaSuraNovelPayStrategy.class);
                    case REWARD_NOVEL:
                        return SpringUtils.getBean(YiMaRewardNovelPayStrategy.class);
                    case REWARD_LITERATURE:
                        return SpringUtils.getBean(YiMaRewardLiteraturePayStrategy.class);
                    case ONLINE_BANK_PAY:
                        return SpringUtils.getBean(YiMaOnlineBankPayStrategy.class);
                    case BUY_NOVEL_VIP:
                        return SpringUtils.getBean(YiMaBuyNovelVipPayStrategy.class);
                    case BUSY_TASK_FLOW:
                        return SpringUtils.getBean(YiMaBusyTaskFlowStrategy.class);
                    case SERVICE_PROVIDER_PAY:
                        return SpringUtils.getBean(YiMaServiceProviderStrategy.class);
                    case BUSY_TASK_COMMON:
                        return SpringUtils.getBean(YiMaBusyTaskCommonStrategy.class);
                    case APPLET_COMMON:
                        return SpringUtils.getBean(YiMaAppletPayStrategy.class);
                    default:
                        return null;
                }
                // PayPal
            case PAY_TYPE_PAYPAL:
                switch (paySource) {
                    case VIDEO_PAY:
                        return SpringUtils.getBean(PayPalVideoPayStrategy.class);
                    case PLAYLET_PAY:
                        return SpringUtils.getBean(PayPalPlayletPayStrategy.class);
                    case SOHU_VIRTUAL_CURRENCY_RECHARGE:
                        return SpringUtils.getBean(PayPalVirtualPayStrategy.class);
                    case SHOP_ORDER_PAY:
                        return SpringUtils.getBean(PayPalGoodsStrategy.class);
                }
                // 空中云汇
            case PAY_TYPE_AIR_WALLEX:
                switch (paySource) {
                    case SOHU_VIRTUAL_CURRENCY_RECHARGE:
                        return SpringUtils.getBean(AirWallexVirtualPayStrategy.class);
                    case SHOP_ORDER_PAY:
                        return SpringUtils.getBean(AirWallexGoodsStrategy.class);
                }
                // 虚拟币支付
            case PAY_TYPE_VIRTUAL_PAY:
                switch (paySource) {
                    case VIDEO_PAY:
                        return SpringUtils.getBean(VirtualVideoPayStrategy.class);
                    case PLAYLET_PAY:
                        return SpringUtils.getBean(VirtualPlayletPayStrategy.class);
                    case AI_PAY:
                        return SpringUtils.getBean(VirtualVIPayStrategy.class);
                }
                // 线下支付
            case PAY_TYPE_OFFLINE_PAY:
                switch (paySource) {
                    case BOND_OFF_LINE:
                        return SpringUtils.getBean(BondOfflinePayStrategy.class);
                }
                // 支付宝支付
            case PAY_TYPE_ALI_PAY:
                switch (paySource) {
                    case BOND_ON_LINE:
                        return SpringUtils.getBean(AliBondPayStrategy.class);
                }
            default:
                throw new IllegalArgumentException("Unsupported payment type: " + payType);
        }
    }

    private PaymentStrategy createStrategy(VirtutalPaySourceEnum paySource) {
        // 根据支付类型和支付来源创建相应的支付策略
        switch (paySource) {
            case VIRTUAL_CURRENCY_VIDEO_PAY:
                return SpringUtils.getBean(VirtualVideoPayStrategy.class);
            case VIRTUAL_CURRENCY_PLAYLET_PAY:
                return SpringUtils.getBean(VirtualPlayletPayStrategy.class);
            case VIRTUAL_CURRENCY_AI_PAY:
                return SpringUtils.getBean(VirtualVIPayStrategy.class);
            default:
                throw new IllegalArgumentException("Unsupported payment type: " + paySource);
        }
    }

    public PaymentStrategy getPaymentStrategy(PayTypeEnum payType, Integer paySource) {
        Map<Integer, PaymentStrategy> sourceStrategyMap = strategyMap.get(payType);
        if (sourceStrategyMap == null) {
            throw new IllegalArgumentException("Unsupported payment type: " + payType);
        }
        PaymentStrategy strategy = sourceStrategyMap.get(paySource);
        if (strategy == null) {
            throw new IllegalArgumentException("Unsupported pay source for payment type " + payType + ": " + paySource);
        }
        return strategy;
    }

}


