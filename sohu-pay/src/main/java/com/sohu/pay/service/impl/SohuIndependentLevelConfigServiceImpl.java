package com.sohu.pay.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.pay.api.bo.SohuIndependentLevelConfigBo;
import com.sohu.pay.api.vo.SohuIndependentLevelConfigVo;
import com.sohu.pay.domain.SohuIndependentLevelConfig;
import com.sohu.pay.mapper.SohuIndependentLevelConfigMapper;
import com.sohu.pay.service.ISohuIndependentLevelConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 分账等级设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-11
 */
@RequiredArgsConstructor
@Service
public class SohuIndependentLevelConfigServiceImpl implements ISohuIndependentLevelConfigService {

    private final SohuIndependentLevelConfigMapper baseMapper;

    /**
     * 查询分账等级设置
     */
    @Override
    public SohuIndependentLevelConfigVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询分账等级设置列表
     */
    @Override
    public TableDataInfo<SohuIndependentLevelConfigVo> queryPageList(SohuIndependentLevelConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuIndependentLevelConfig> lqw = buildQueryWrapper(bo);
        Page<SohuIndependentLevelConfigVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询分账等级设置列表
     */
    @Override
    public List<SohuIndependentLevelConfigVo> queryList(SohuIndependentLevelConfigBo bo) {
        LambdaQueryWrapper<SohuIndependentLevelConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuIndependentLevelConfig> buildQueryWrapper(SohuIndependentLevelConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuIndependentLevelConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getLevel() != null, SohuIndependentLevelConfig::getLevel, bo.getLevel());
        lqw.eq(bo.getLevelType() != null, SohuIndependentLevelConfig::getLevelType, bo.getLevelType());
        lqw.eq(bo.getLevelStatus() != null, SohuIndependentLevelConfig::getLevelStatus, bo.getLevelStatus());
        return lqw;
    }

    /**
     * 新增分账等级设置
     */
    @Override
    public Boolean insertByBo(SohuIndependentLevelConfigBo bo) {
        SohuIndependentLevelConfig add = BeanUtil.toBean(bo, SohuIndependentLevelConfig.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改分账等级设置
     */
    @Override
    public Boolean updateByBo(SohuIndependentLevelConfigBo bo) {
        SohuIndependentLevelConfig update = BeanUtil.toBean(bo, SohuIndependentLevelConfig.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuIndependentLevelConfig entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除分账等级设置
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public SohuIndependentLevelConfig queryByStatusAndSiteId(Integer type, Long siteId) {
        LambdaQueryWrapper<SohuIndependentLevelConfig> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuIndependentLevelConfig::getLevelStatus, true);
        if (siteId != null) {
            lqw.eq(SohuIndependentLevelConfig::getSiteId, siteId);
        }else {
            lqw.eq(SohuIndependentLevelConfig::getLevelCommon, Constants.ONE);
        }
        lqw.eq(SohuIndependentLevelConfig::getLevelType, type);
        return this.baseMapper.selectOne(lqw);
    }

}
