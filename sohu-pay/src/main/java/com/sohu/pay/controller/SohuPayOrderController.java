package com.sohu.pay.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.busyorder.api.RemoteBusyTaskService;
import com.sohu.busyorder.api.bo.SohuPayBusyBo;
import com.sohu.busyorder.api.vo.SohuPayBusyVo;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.pay.api.bo.SohuPayOrderBo;
import com.sohu.pay.api.vo.SohuPayOrderVo;
import com.sohu.pay.service.ISohuPayOrderService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 支付订单控制器
 * 前端访问路由地址为:/pay/payOrder
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/payOrder")
public class SohuPayOrderController extends BaseController {

    private final ISohuPayOrderService iSohuPayOrderService;

    @DubboReference
    private RemoteBusyTaskService remoteBusyTaskService;

    /**
     * 查询支付订单列表
     */
    @SaCheckPermission("pay:payOrder:list")
    @GetMapping("/list")
    public TableDataInfo<SohuPayOrderVo> list(SohuPayOrderBo bo, PageQuery pageQuery) {
        return iSohuPayOrderService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出支付订单列表
     */
    @SaCheckPermission("pay:payOrder:export")
    @Log(title = "支付订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuPayOrderBo bo, HttpServletResponse response) {
        List<SohuPayOrderVo> list = iSohuPayOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "支付订单", SohuPayOrderVo.class, response);
    }

    /**
     * 获取支付订单详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pay:payOrder:query")
    @GetMapping("/{id}")
    public R<SohuPayOrderVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuPayOrderService.queryById(id));
    }

    /**
     * 新增支付订单
     */
    @SaCheckPermission("pay:payOrder:add")
    @Log(title = "支付订单", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuPayOrderBo bo) {
        return toAjax(iSohuPayOrderService.insertByBo(bo));
    }

    /**
     * 修改支付订单
     */
    @SaCheckPermission("pay:payOrder:edit")
    @Log(title = "支付订单", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuPayOrderBo bo) {
        return toAjax(iSohuPayOrderService.updateByBo(bo));
    }

    /**
     * 删除支付订单
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pay:payOrder:remove")
    @Log(title = "支付订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuPayOrderService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    @Operation(summary = "流量商单支付流水", description = "负责人：汪伟 流量商单支付流水")
    @GetMapping("/busyFlow/list")
    public TableDataInfo<SohuPayBusyVo> busyFlowList(SohuPayBusyBo bo, PageQuery pageQuery) {
        return remoteBusyTaskService.busyFlowList(bo, pageQuery);
    }

}
