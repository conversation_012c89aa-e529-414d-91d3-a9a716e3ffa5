<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.pay.mapper.SohuServiceProviderRefundMapper">
    <select id="refundList" parameterType="com.sohu.pay.api.bo.SohuServiceProviderBo"
            resultType="com.sohu.pay.api.vo.SohuServiceProviderVo">
        SELECT
        IFNULL(su.user_name,su.nick_name) user_name,
        refund_order_no order_no,
        transaction_id,
        refund_type pay_type,
        refund_amount amount,
        voucher_url voucher_url,
        ssp.create_time oper_time
        FROM `sohu_service_provider_refund` ssp
        join sys_user su on ssp.user_id = su.user_id
        where refund_status = 'SUCCESS'
        <if test="bo.orderNo!=null and bo.orderNo!=''">
            AND refund_order_no = #{bo.orderNo}
        </if>
        <if test="bo.startTime!=null and bo.startTime!=''">
            AND ssp.create_time &gt;= #{bo.startTime}
        </if>
        <if test="bo.endTime!=null and bo.endTime!=''">
            AND ssp.create_time &lt;= #{bo.endTime}
        </if>
        order by ssp.create_time desc

    </select>
</mapper>