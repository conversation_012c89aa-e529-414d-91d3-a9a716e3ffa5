<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.pay.mapper.SohuAccountBankMapper">

    <resultMap type="com.sohu.pay.domain.SohuAccountBank" id="SohuAccountBankResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="settleType" column="settle_type"/>
        <result property="settlementWay" column="settlement_way"/>
        <result property="cardNo" column="card_no"/>
        <result property="parentBankName" column="parent_bank_name"/>
        <result property="parentBankCode" column="parent_bank_code"/>
        <result property="branchBankName" column="branch_bank_name"/>
        <result property="branchBankCode" column="branch_bank_code"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="settlePhotoA" column="settle_photo_a"/>
        <result property="settlePhotoB" column="settle_photo_b"/>
        <result property="settlePhotoFront" column="settle_photo_front"/>
        <result property="settlePhotoBack" column="settle_photo_back"/>
        <result property="state" column="state"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
