<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.pay.mapper.SohuPayOrderMapper">

    <resultMap type="com.sohu.pay.domain.SohuPayOrder" id="SohuPayOrderResult">
        <result property="id" column="id"/>
        <result property="masterOrderNo" column="master_order_no"/>
        <result property="orderNo" column="order_no"/>
        <result property="merId" column="mer_id"/>
        <result property="userId" column="user_id"/>
        <result property="sourceType" column="source_type"/>
        <result property="terminalId" column="terminal_id"/>
        <result property="transactionId" column="transaction_id"/>
        <result property="refundId" column="refund_id"/>
        <result property="payNumber" column="pay_number"/>
        <result property="payType" column="pay_type"/>
        <result property="childPayType" column="child_pay_type"/>
        <result property="payStatus" column="pay_status"/>
        <result property="payAmount" column="pay_amount"/>
        <result property="payableAmount" column="payable_amount"/>
        <result property="subsidyAmount" column="subsidy_amount"/>
        <result property="payIntegral" column="pay_integral"/>
        <result property="couponId" column="coupon_id"/>
        <result property="refundAmount" column="refund_amount"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="payTime" column="pay_time"/>
        <result property="chargeAmount" column="charge_amount"/>
    </resultMap>


</mapper>
