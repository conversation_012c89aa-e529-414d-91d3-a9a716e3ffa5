package com.sohu.middleService.mapper.focus;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.middle.api.vo.focus.SohuFocusSiteVo;
import com.sohu.middleService.domain.focus.SohuFocusSite;

/**
 * 站点Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
@DS(value = "focus")
public interface SohuFocusSiteMapper extends BaseMapperPlus<SohuFocusSiteMapper, SohuFocusSite, SohuFocusSiteVo> {

}
