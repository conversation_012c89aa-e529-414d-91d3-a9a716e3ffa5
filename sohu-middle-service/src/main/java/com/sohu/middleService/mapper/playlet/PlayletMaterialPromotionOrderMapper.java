package com.sohu.middleService.mapper.playlet;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.middle.api.bo.playlet.PlayletMaterialPromotionOrderBo;
import com.sohu.middle.api.vo.SohuMaterialPromotionOrderAggrVo;
import com.sohu.middle.api.vo.playlet.PlayletMaterialPromotionOrderVo;
import com.sohu.middleService.domain.playlet.PlayletMaterialPromotionOrder;
import org.apache.ibatis.annotations.Param;

/**
 * 短剧素材推广订单流水Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
public interface PlayletMaterialPromotionOrderMapper extends BaseMapperPlus<PlayletMaterialPromotionOrderMapper, PlayletMaterialPromotionOrder, PlayletMaterialPromotionOrderVo> {

    Page<PlayletMaterialPromotionOrderVo> queryPageList(@Param("page") Page page, @Param("bo") PlayletMaterialPromotionOrderBo bo);

    Page<PlayletMaterialPromotionOrderVo> queryPageAggrList(@Param("page") Page page, @Param("bo") PlayletMaterialPromotionOrderBo bo);

    SohuMaterialPromotionOrderAggrVo selectAggrBySharePerson(@Param("sharePerson") Long sharePerson, @Param("tradeType") String tradeType, @Param("startTime") String startTime, @Param("endTime") String endTime);

    Page<PlayletMaterialPromotionOrderVo> playletDistributionList(@Param("page") Page page, @Param("sharePerson") Long sharePerson, @Param("startTime") String startTime, @Param("endTime") String endTime);

}
