package com.sohu.middleService.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.middle.api.bo.SohuIndependentMaterialBo;
import com.sohu.middle.api.vo.SohuIndependentMaterialVo;
import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.middleService.domain.SohuIndependentMaterial;
import org.apache.ibatis.annotations.Param;

/**
 * 分销素材库Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-24
 */
public interface SohuIndependentMaterialMapper extends BaseMapperPlus<SohuIndependentMaterialMapper, SohuIndependentMaterial, SohuIndependentMaterialVo> {

    /**
     *
     * 公共素材库列表
     *
     * @param page 分页参数
     * @param bo SohuIndependentMaterialBo
     * @return  Page<SohuIndependentMaterialVo>
     */
    Page<SohuIndependentMaterialVo> queryPageList(@Param("page") Page<SohuIndependentMaterial> page, @Param("bo") SohuIndependentMaterialBo bo, @Param("userId") Long userId);

    /**
     * 我的分销素材列表
     *
     * @param page 分页参数
     * @return Page<SohuIndependentMaterialVo>
     */
    Page<SohuIndependentMaterialVo> myMaterialList(@Param("bo") SohuIndependentMaterialBo bo, @Param("page") Page<SohuIndependentMaterial> page, @Param("userId") Long userId);

    /**
     * 获取分销素材库详情
     * @param id 主键id
     * @return SohuIndependentMaterialVo
     */
    SohuIndependentMaterialVo queryById(@Param("id") Long id, @Param("userId") Long userId,@Param("isMe") Integer isMe);
}
