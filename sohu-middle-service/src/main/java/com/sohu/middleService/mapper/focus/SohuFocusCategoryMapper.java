package com.sohu.middleService.mapper.focus;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.middle.api.vo.focus.SohuFocusCategoryVo;
import com.sohu.middleService.domain.focus.SohuFocusCategory;

/**
 * 分类Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-06
 */
@DS(value = "focus")
public interface SohuFocusCategoryMapper extends BaseMapperPlus<SohuFocusCategoryMapper, SohuFocusCategory, SohuFocusCategoryVo> {

}
