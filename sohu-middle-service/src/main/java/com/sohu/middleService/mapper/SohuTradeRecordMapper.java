package com.sohu.middleService.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.vo.SohuStatVo;
import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.vo.SohuStatAmountVo;
import com.sohu.middle.api.vo.SohuTradeRecordVo;
import com.sohu.middleService.domain.SohuTradeRecord;
import com.sohu.system.api.domain.SysUser;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 用户流水明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-07
 */
public interface SohuTradeRecordMapper extends BaseMapperPlus<SohuTradeRecordMapper, SohuTradeRecord, SohuTradeRecordVo> {


    List<SohuStatAmountVo> stat(@Param("userId") Long userId, @Param("month") String month);

    /**
     * 获取当日收入统计
     *
     * @param userId 用户id
     * @return BigDecimal
     */
    BigDecimal getTodayIncomeStat(@Param("userId") Long userId);

    /**
     * 统计用户短剧收入
     *
     * @param userId 用户id
     * @return BigDecimal
     */
    BigDecimal getPlayletIncomeStat(@Param("userId") Long userId);

    /**
     * 任务收益统计
     *
     * @param userId 用户id
     * @return BigDecimal
     */
    BigDecimal getTaskIncomeStat(@Param("userId") Long userId);

    /**
     * 统计用户推广佣金
     *
     * @param userId 用户id
     * @return BigDecimal
     */
    BigDecimal getShareIncomeStat(@Param("userId") Long userId);

    /**
     * 统计用户拉新佣金
     *
     * @param userId 用户id
     * @return BigDecimal
     */
    BigDecimal getInviteIncomeStat(@Param("userId") Long userId);

    /**
     * 虚拟币账单明细列表
     *
     * @param page Page<Object>
     * @param bo   SohuTradeRecordBo
     * @return Page<SohuTradeRecordVo>
     */
    Page<SohuTradeRecordVo> getVirtualPayRecord(@Param("page") Page<Object> page, @Param("bo") SohuTradeRecordBo bo);


    /**
     * 获取用户最早收支明细
     *
     * @param userId 用户id
     * @return SohuTradeRecordVo
     */
    SohuTradeRecordVo queryFirstRecord(@Param("userId") Long userId, @Param("accountType") String accountType);

    BigDecimal sumGuestAmount(@Param("uuid") String uuid, @Param("amountType") String amountType, @Param("payStatus") String payStatus);

    BigDecimal sumUserAmount(@Param("userId") Long userId, @Param("amountType") String amountType, @Param("payStatus") String payStatus, @Param("typeList") List<String> typeList);

    /**
     * 小说购买支付结果分页查询
     */
    Page<SohuTradeRecordVo> queryNovelPageList(@Param("page") Page<Object> page, @Param("bo") SohuTradeRecordBo bo);

    /**
     * 诗文购买支付结果分页查询
     * @param page
     * @param bo
     * @return
     */
    Page<SohuTradeRecordVo> queryLiteraturePageList(@Param("page")Page<Object> page, @Param("bo")SohuTradeRecordBo bo);

    /**
     * 获取付费的人数
     * @param queryWrapper
     * @return
     */
    Long getNewPayUserNum(@Param(Constants.WRAPPER) Wrapper<SohuTradeRecord> queryWrapper);

    /**
     * 获取用户收入统计
     * @param endTime
     * @return
     */
    SohuStatVo getStat(@Param("endTime") Date endTime);
}
