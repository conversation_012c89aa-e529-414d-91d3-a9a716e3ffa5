package com.sohu.middleService.mapper;

import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.middleService.domain.SohuViewRecord;
import com.sohu.middle.api.vo.SohuViewRecordVo;
import org.apache.ibatis.annotations.Param;

/**
 * 浏览记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-17
 */
public interface SohuViewRecordMapper extends BaseMapperPlus<SohuViewRecordMapper, SohuViewRecord, SohuViewRecordVo> {

    /**
     * 前一天用户项目数据浏览总数
     * @param userId
     * @return
     */
    Long getCountByUser(@Param("userId") Long userId);
}
