package com.sohu.middleService.service.finance.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.finance.SohuShareMasterOrderBo;
import com.sohu.middle.api.bo.finance.SohuShareMasterQueryBo;
import com.sohu.middle.api.vo.finance.SohuShareMasterOrderVo;
import com.sohu.middleService.domain.finance.SohuShareMasterOrder;
import com.sohu.middleService.mapper.finance.SohuShareMasterOrderMapper;
import com.sohu.middleService.service.finance.ISohuShareMasterOrderService;
import com.sohu.middleService.service.impl.SohuBaseServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 金融分享主订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@RequiredArgsConstructor
@Service
public class SohuShareMasterOrderServiceImpl extends SohuBaseServiceImpl<SohuShareMasterOrderMapper,SohuShareMasterOrder,SohuShareMasterOrderVo> implements ISohuShareMasterOrderService {

//    private final SohuShareMasterOrderMapper baseMapper;

//    /**
//     * 查询金融分享主订单
//     */
//    @Override
//    public SohuShareMasterOrderVo queryById(String id) {
//        return baseMapper.selectVoById(id);
//    }

    /**
     * 查询金融分享主订单列表
     */
    @Override
    public TableDataInfo<SohuShareMasterOrderVo> queryPageList(SohuShareMasterQueryBo bo, PageQuery pageQuery) {
        //LambdaQueryWrapper<SohuShareMasterOrder> lqw = buildQueryWrapper(bo);
        LambdaQueryWrapper<SohuShareMasterOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(bo.getPaid() != null, SohuShareMasterOrder::getPaid, bo.getPaid());
        lqw.ge(StringUtils.isNotBlank(bo.getStartPayTimeStr()),SohuShareMasterOrder::getPayTime, DateUtils.beginOfTime(bo.getStartPayTimeStr()));
        lqw.ge(StringUtils.isNotBlank(bo.getEndPayTimeStr()),SohuShareMasterOrder::getPayTime, DateUtils.endOfTime(bo.getEndPayTimeStr()));
        lqw.orderByDesc(SohuShareMasterOrder::getCreateTime);
        Page<SohuShareMasterOrderVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询金融分享主订单列表
     */
    @Override
    public List<SohuShareMasterOrderVo> queryList(SohuShareMasterOrderBo bo) {
        LambdaQueryWrapper<SohuShareMasterOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuShareMasterOrder> buildQueryWrapper(SohuShareMasterOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuShareMasterOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), SohuShareMasterOrder::getOrderNo, bo.getOrderNo());
        lqw.eq(Objects.nonNull(bo.getUserId()), SohuShareMasterOrder::getUserId, bo.getUserId());
        lqw.eq(bo.getProductTotalPrice() != null, SohuShareMasterOrder::getProductTotalPrice, bo.getProductTotalPrice());
        lqw.eq(bo.getTotalPrice() != null, SohuShareMasterOrder::getTotalPrice, bo.getTotalPrice());
        lqw.eq(bo.getPayPrice() != null, SohuShareMasterOrder::getPayPrice, bo.getPayPrice());
        lqw.eq(bo.getChargePrice() != null, SohuShareMasterOrder::getChargePrice, bo.getChargePrice());
        lqw.eq(Objects.nonNull(bo.getPaid()), SohuShareMasterOrder::getPaid, bo.getPaid());
        lqw.eq(bo.getPayTime() != null, SohuShareMasterOrder::getPayTime, bo.getPayTime());
        lqw.eq(StringUtils.isNotBlank(bo.getPayType()), SohuShareMasterOrder::getPayType, bo.getPayType());
        lqw.eq(StringUtils.isNotBlank(bo.getPayChannel()), SohuShareMasterOrder::getPayChannel, bo.getPayChannel());
        lqw.eq(StringUtils.isNotBlank(bo.getMark()), SohuShareMasterOrder::getMark, bo.getMark());
//        lqw.eq(StringUtils.isNotBlank(bo.getIsCancel()), SohuShareMasterOrder::getIsCancel, bo.getIsCancel());
        lqw.eq(StringUtils.isNotBlank(bo.getPayMasterOrderNo()), SohuShareMasterOrder::getPayMasterOrderNo, bo.getPayMasterOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getOutTradeNo()), SohuShareMasterOrder::getOutTradeNo, bo.getOutTradeNo());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionId()), SohuShareMasterOrder::getTransactionId, bo.getTransactionId());
        lqw.eq(StringUtils.isNotBlank(bo.getRedirect()), SohuShareMasterOrder::getRedirect, bo.getRedirect());
        //lqw.eq(StringUtils.isNotBlank(bo.getTotalNum()), SohuShareMasterOrder::getTotalNum, bo.getTotalNum());
        lqw.eq(bo.getAdminPrice() != null, SohuShareMasterOrder::getAdminPrice, bo.getAdminPrice());
        lqw.like(StringUtils.isNotBlank(bo.getNickName()), SohuShareMasterOrder::getNickName, bo.getNickName());
        return lqw;
    }

    /**
     * 新增金融分享主订单
     */
    @Override
    public Boolean insertByBo(SohuShareMasterOrderBo bo) {
        SohuShareMasterOrder add = BeanUtil.toBean(bo, SohuShareMasterOrder.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改金融分享主订单
     */
    @Override
    public Boolean updateByBo(SohuShareMasterOrderBo bo) {
        SohuShareMasterOrder update = BeanUtil.toBean(bo, SohuShareMasterOrder.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuShareMasterOrder entity) {
        //TODO 做一些数据校验,如唯一约束
    }

//    /**
//     * 批量删除金融分享主订单
//     */
//    @Override
//    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
//        return baseMapper.deleteBatchIds(ids) > 0;
//    }

    @Override
    public SohuShareMasterOrderVo selectByMasterOrderNo(String masterOrderNo) {
        LambdaQueryWrapper<SohuShareMasterOrder> lqw=new LambdaQueryWrapper<>();
        lqw.eq(SohuShareMasterOrder::getOrderNo, masterOrderNo);
        return this.baseMapper.selectVoOne(lqw);
    }

//    @Override
//    public SohuShareMasterOrderVo selectVoByMasterOrderNo(String masterOrderNo) {
//        SohuShareMasterOrder entity = this.selectByMasterOrderNo(masterOrderNo);
//        return BeanUtil.copyProperties(entity, SohuShareMasterOrderVo.class);
//    }

    @Override
    public SohuShareMasterOrderVo selectByMasterOutTradeNo(String outTradeNo) {
        LambdaQueryWrapper<SohuShareMasterOrder> lqw=new LambdaQueryWrapper<>();
        lqw.eq(SohuShareMasterOrder::getOutTradeNo, outTradeNo);
        return this.baseMapper.selectVoOne(lqw);
    }

}
