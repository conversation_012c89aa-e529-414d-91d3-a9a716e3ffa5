package com.sohu.middleService.service.im;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.im.SohuImTenantApplyActiveBo;
import com.sohu.middle.api.bo.im.SohuImTenantBo;
import com.sohu.middle.api.bo.im.SohuImTenantUpdateCodeBo;
import com.sohu.middle.api.vo.im.SohuImTenantInfoModel;
import com.sohu.middle.api.vo.im.SohuImTenantVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/12 15:05
 */
public interface ISohuImTenantService {

    /**
     * 查询im租户
     */
    SohuImTenantVo queryById(Long id);

    /**
     * 查询im租户列表
     */
    TableDataInfo<SohuImTenantVo> queryPageList(SohuImTenantBo bo, PageQuery pageQuery);

    /**
     * 查询im租户列表
     */
    List<SohuImTenantVo> queryList(SohuImTenantBo bo);

    /**
     * 修改im租户
     */
    Boolean insertByBo(SohuImTenantBo bo);

    /**
     * 修改im租户
     */
    Boolean updateByBo(SohuImTenantBo bo);

    /**
     * 启用/禁用
     * @param id
     * @return
     */
    Boolean enable(Long id);

    /**
     * 根据用户ID查询im租户
     * @param userId
     * @return
     */
    SohuImTenantVo queryByUserId(Long userId);

    /**
     * 申请激活服务
     * @return
     */
    Boolean applyActive(SohuImTenantApplyActiveBo bo);

    /**
     * 修改服务器编码
     * @param bo
     * @return
     */
    Boolean updateServerCode(SohuImTenantUpdateCodeBo bo);

    /**
     * 初始化Im租户
     * @return
     */
    Boolean initImTenant();

    /**
     * 刷新Im租户信息缓存
     * @param serverCode 服务器编码
     * @return
     */
    SohuImTenantInfoModel refreshImTenantInfoCache(String serverCode);

    /**
     * 根据服务编码查询im租户
     * @param serverCode 服务器编码
     * @return
     */
    SohuImTenantVo queryByServerCode(String serverCode);

    /**
     * 根据服务器编码获取租户信息
     * @param serverCode
     * @return
     */
    SohuImTenantInfoModel getTenantInfoByServerCode(String serverCode);
    /**
     * 根据服务器编码获取租户信息
     * @param serverCode
     * @param forceRefresh 强制刷新
     * @return
     */
    SohuImTenantInfoModel getTenantInfoByServerCode(String serverCode,boolean forceRefresh);

}
