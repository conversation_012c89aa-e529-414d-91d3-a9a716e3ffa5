package com.sohu.middleService.service.gameNovel;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.gameNovel.SohuGameNovelInfoBo;
import com.sohu.middle.api.vo.gameNovel.SohuGameNovelInfoVo;
import com.sohu.middleService.domain.gameNovel.SohuGameNovelInfo;
import com.sohu.middleService.service.ISohuBaseService;

import java.util.Collection;
import java.util.List;

/**
 * 游戏网页拓展Service接口
 *
 * <AUTHOR>
 * @date 2024-09-22
 */
public interface ISohuGameNovelInfoService extends ISohuBaseService<SohuGameNovelInfo, SohuGameNovelInfoVo> {

    /**
     * 查询游戏网页拓展
     */
    SohuGameNovelInfoVo queryById(Long id);

    /**
     * 查询游戏网页拓展列表
     */
    TableDataInfo<SohuGameNovelInfoVo> queryPageList(SohuGameNovelInfoBo bo, PageQuery pageQuery);

    /**
     * 查询游戏网页拓展列表
     */
    List<SohuGameNovelInfoVo> queryList(SohuGameNovelInfoBo bo);

    /**
     * 修改游戏网页拓展
     */
    Boolean insertByBo(SohuGameNovelInfoBo bo);

    /**
     * 修改游戏网页拓展
     */
    Boolean updateByBo(SohuGameNovelInfoBo bo);

    /**
     * 校验并批量删除游戏网页拓展信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
