package com.sohu.middleService.service.diy.impl;

import cn.hutool.core.bean.BeanUtil;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.diy.SohuUniversalsConfigBo;
import com.sohu.middle.api.vo.diy.SohuUniversalsConfigVo;
import com.sohu.middleService.domain.diy.SohuUniversalsConfig;
import com.sohu.middleService.mapper.diy.SohuUniversalsConfigMapper;
import com.sohu.middleService.service.diy.ISohuUniversalsConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Collection;

/**
 * 装修通用配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@RequiredArgsConstructor
@Service
public class SohuUniversalsConfigServiceImpl implements ISohuUniversalsConfigService {

    private final SohuUniversalsConfigMapper baseMapper;

    /**
     * 查询装修通用配置
     */
    @Override
    public SohuUniversalsConfigVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询装修通用配置列表
     */
    @Override
    public TableDataInfo<SohuUniversalsConfigVo> queryPageList(SohuUniversalsConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuUniversalsConfig> lqw = buildQueryWrapper(bo);
        Page<SohuUniversalsConfigVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    private LambdaQueryWrapper<SohuUniversalsConfig> buildQueryWrapper(SohuUniversalsConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuUniversalsConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getConfigKey()), SohuUniversalsConfig::getConfigKey, bo.getConfigKey());
        lqw.eq(StringUtils.isNotBlank(bo.getConfig()), SohuUniversalsConfig::getConfig, bo.getConfig());
        lqw.eq(StringUtils.isNotBlank(bo.getConfigDesc()), SohuUniversalsConfig::getConfigDesc, bo.getConfigDesc());
        lqw.eq(bo.getStatus() != null, SohuUniversalsConfig::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增装修通用配置
     */
    @Override
    public Boolean insertByBo(SohuUniversalsConfigBo bo) {
        SohuUniversalsConfig add = BeanUtil.toBean(bo, SohuUniversalsConfig.class);
        validEntityBeforeSave(add);
        add.setUserId(LoginHelper.getUserId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改装修通用配置
     */
    @Override
    public Boolean updateByBo(SohuUniversalsConfigBo bo) {
        SohuUniversalsConfig update = BeanUtil.toBean(bo, SohuUniversalsConfig.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuUniversalsConfig entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除装修通用配置
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
