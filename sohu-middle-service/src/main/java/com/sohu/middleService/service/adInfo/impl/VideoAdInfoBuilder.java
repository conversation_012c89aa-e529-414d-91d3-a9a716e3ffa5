package com.sohu.middleService.service.adInfo.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.vo.SohuAdInfoVo;
import com.sohu.middle.api.vo.SohuVideoVo;
import com.sohu.middleService.domain.SohuVideoInfo;
import com.sohu.middleService.mapper.SohuVideoInfoMapper;
import com.sohu.middleService.service.ISohuUserLikeService;
import com.sohu.middleService.service.ISohuUserService;
import com.sohu.middleService.service.ISohuVideoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 视频广告构建器
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VideoAdInfoBuilder extends AbstractAdInfoBuilder {

    @Resource
    private SohuVideoInfoMapper sohuVideoInfoMapper;

    public VideoAdInfoBuilder(ISohuVideoService iSohuVideoService, ISohuUserService iSohuUserService, ISohuUserLikeService iSohuUserLikeService) {
        super(iSohuVideoService, iSohuUserService, iSohuUserLikeService);
    }

    @Override
    public boolean supports(String objType) {
        return BusyType.Video.getType().equals(objType);
    }

    @Override
    @DS(value = "master")
    public SohuAdInfoVo build(SohuAdInfoVo adInfoVo) {
        SohuVideoVo videoVo = iSohuVideoService.queryById(adInfoVo.getObjId());
        if (Objects.isNull(videoVo)) {
            return adInfoVo;
        }

        LoginUser loginUser = iSohuUserService.selectById(videoVo.getUserId());
        if (Objects.nonNull(loginUser)) {
            adInfoVo.setNickName(loginUser.getNickname());
            adInfoVo.setAvatar(loginUser.getAvatar());
        }

        Long likeCount = iSohuUserLikeService.countStatById(BusyType.Video.name(), videoVo.getId());
        adInfoVo.setObjPraiseCount(likeCount == null ? 0 : likeCount.intValue());
        adInfoVo.setVideoPraiseCount(likeCount == null ? 0 : likeCount.intValue());

        if (Objects.nonNull(LoginHelper.getUserId())) {
            adInfoVo.setObjPraiseObj(iSohuUserLikeService.queryByUserId(LoginHelper.getUserId(), BusyType.Video.name(), videoVo.getId()));
            adInfoVo.setVideoPraiseObj(iSohuUserLikeService.queryByUserId(LoginHelper.getUserId(), BusyType.Video.name(), videoVo.getId()));
        }

        adInfoVo.setObjCoverImage(videoVo.getCoverImage());
        adInfoVo.setObjUrl(videoVo.getVideoUrl());
        adInfoVo.setObjUserId(videoVo.getUserId());
        adInfoVo.setVideoCoverImage(videoVo.getCoverImage());
        adInfoVo.setVideoUrl(videoVo.getVideoUrl());
        adInfoVo.setVideoUserId(videoVo.getUserId());
        adInfoVo.setVideoForwardCount(videoVo.getForwardCount());
        adInfoVo.setVideoCollectCount(videoVo.getCollectCount());
        adInfoVo.setVideoCollectObj(videoVo.getCollectObj());
        adInfoVo.setVideoFollow0bj(videoVo.getFollowObj());
        adInfoVo.setVideoNickName(loginUser.getNickname());
        adInfoVo.setVideoAvatar(loginUser.getAvatar());
        adInfoVo.setAspectRatio(videoVo.getAspectRatio());
        SohuVideoInfo info = sohuVideoInfoMapper.selectOne(new LambdaQueryWrapper<SohuVideoInfo>().eq(SohuVideoInfo::getVideoId, videoVo.getId()));
        if (Objects.nonNull(info)) {
            adInfoVo.setVideoCommentCount(info.getCommentCount());
        }

        return adInfoVo;
    }

}