package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuWhiteListBo;
import com.sohu.middle.api.vo.SohuWhiteListVo;
import com.sohu.middleService.domain.SohuWhiteList;
import com.sohu.middleService.mapper.SohuWhiteListMapper;
import com.sohu.middleService.service.ISohuWhiteListService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 外部链接白名单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@RequiredArgsConstructor
@Service
public class SohuWhiteListServiceImpl implements ISohuWhiteListService {

    private final SohuWhiteListMapper baseMapper;

    /**
     * 查询外部链接白名单
     */
    @Override
    public SohuWhiteListVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询外部链接白名单列表
     */
    @Override
    public TableDataInfo<SohuWhiteListVo> queryPageList(SohuWhiteListBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuWhiteList> lqw = buildQueryWrapper(bo);
        Page<SohuWhiteListVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询外部链接白名单列表
     */
    @Override
    public List<SohuWhiteListVo> queryList(SohuWhiteListBo bo) {
        LambdaQueryWrapper<SohuWhiteList> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuWhiteList> buildQueryWrapper(SohuWhiteListBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuWhiteList> lqw = Wrappers.lambdaQuery();
        lqw.like(bo.getUrl() != null, SohuWhiteList::getUrl, bo.getUrl());
        lqw.eq(bo.getUserId() != null, SohuWhiteList::getUserId, bo.getUserId());
        lqw.eq(bo.getIsUse() != null, SohuWhiteList::getIsUse, bo.getIsUse());
        return lqw;
    }

    /**
     * 新增外部链接白名单
     */
    @Override
    public Boolean insertByBo(SohuWhiteListBo bo) {
        SohuWhiteList add = BeanUtil.toBean(bo, SohuWhiteList.class);
        validEntityBeforeSave(add);
        SohuWhiteListVo whiteListVo = queryByUrl(bo.getUrl());
        if (!Objects.nonNull(whiteListVo)) {
            boolean flag = baseMapper.insert(add) > 0;
            if (flag) {
                bo.setId(add.getId());
            }
            return flag;
        }
        return true;
    }

    /**
     * 取消外部链接白名单
     */
    @Override
    public Boolean updateByBo(SohuWhiteListBo bo) {
        SohuWhiteList update = BeanUtil.toBean(bo, SohuWhiteList.class);
        SohuWhiteListVo whiteListVo = queryByUrl(bo.getUrl());
        update.setId(whiteListVo.getId());
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuWhiteList entity) {
        //TODO 做一些数据校验,如唯一约束
        String url = splitUrl(entity.getUrl());
        if (StrUtil.isNotBlank(url)) {
            entity.setUrl(url);
        }
    }

    /**
     * 批量删除外部链接白名单
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public SohuWhiteListVo queryByUrl(String url) {
        SohuWhiteListBo bo = new SohuWhiteListBo();
        if (StrUtil.isNotBlank(splitUrl(url))) {
            bo.setUrl(splitUrl(url));
        }
        bo.setIsUse(1);
        return baseMapper.selectVoOne(buildQueryWrapper(bo));
    }

    @Override
    public SohuWhiteListVo getByUrl(String url) {
        SohuWhiteListBo bo = new SohuWhiteListBo();
        bo.setUrl(StringUtils.isNotEmpty(splitUrl(url)) ? splitUrl(url) : url);
        bo.setIsUse(1);
        return baseMapper.selectVoOne(buildQueryWrapper(bo));
    }

    /**
     * 截取网址中的域名
     *
     * @param url
     * @return
     */
    private String splitUrl(String url) {
        String host = "";
        try {
            URL Url = new URL(url);
            host = Url.getHost();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return host;
    }

}
