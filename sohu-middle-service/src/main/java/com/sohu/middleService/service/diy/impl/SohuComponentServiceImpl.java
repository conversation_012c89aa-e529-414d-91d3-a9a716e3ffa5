package com.sohu.middleService.service.diy.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.middle.api.bo.diy.SohuComponentBo;
import com.sohu.middle.api.vo.diy.SohuComponentVo;
import com.sohu.middleService.domain.diy.SohuComponent;
import com.sohu.middleService.mapper.diy.SohuComponentMapper;
import com.sohu.middleService.service.diy.ISohuComponentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 装修组件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@RequiredArgsConstructor
@Service
public class SohuComponentServiceImpl implements ISohuComponentService {

    private final SohuComponentMapper baseMapper;

    /**
     * 查询装修组件
     */
    @Override
    public SohuComponentVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询装修组件列表
     */
    @Override
    public List<SohuComponentVo> queryList(SohuComponentBo bo) {
        LambdaQueryWrapper<SohuComponent> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuComponent> buildQueryWrapper(SohuComponentBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuComponent> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStatus() != null, SohuComponent::getStatus, bo.getStatus());
        return lqw;
    }
}
