package com.sohu.middleService.service;

import com.sohu.common.core.domain.MsgContent;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.bo.playlet.PlayletOpenAdsQueryBo;
import com.sohu.middle.api.bo.playlet.PlayletPatchesAdsQueryBo;
import com.sohu.middle.api.vo.*;
import com.sohu.middle.api.vo.playlet.PlayletOpenAdsListVo;
import com.sohu.middle.api.vo.playlet.PlayletOpenAdsVo;
import com.sohu.middle.api.vo.playlet.PlayletPatchesAdsListVo;
import com.sohu.middle.api.vo.playlet.PlayletPatchesAdsVo;
import com.sohu.middleService.domain.SohuAdInfo;

import java.util.Collection;
import java.util.List;

/**
 * 站点广告主体Service接口
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
public interface ISohuAdInfoService extends ISohuBaseService<SohuAdInfo, SohuAdInfoVo> {

    /**
     * 获取对象的作者id
     *
     * @param id 对象主键id
     * @return 返回对象的作者id，不存在就返回0
     */
    Long getAuthorId(Long id);

    /**
     * 查询站点广告主体
     */
    SohuAdInfoVo queryById(Long id);

    /**
     * 查询站点广告主体列表
     */
    TableDataInfo<SohuAdInfoVo> queryPageList(SohuAdInfoBo bo, PageQuery pageQuery);

    /**
     * 查询站点广告主体列表-管理
     */
    TableDataInfo<SohuAdInfoVo> queryPageListV2(SohuAdInfoQueryBo bo, PageQuery pageQuery);

    /**
     * 查询站点广告主体列表
     */
    List<SohuAdInfoVo> queryList(SohuAdInfoBo bo);

    /**
     * 修改站点广告主体
     */
    Boolean insertByBo(SohuAdInfoBo bo);

    /**
     * 修改站点广告主体
     */
    Boolean updateByBo(SohuAdInfoBo bo);

    /**
     * 校验并批量删除站点广告主体信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 校验并批量删除站点广告主体信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, String type);

    /**
     * 广告上下架
     *
     * @param bo
     * @return
     */
    Boolean operate(SohuAdInfoBo bo);

    /**
     * 开屏广告分页查询
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<PlayletOpenAdsListVo> queryOpenAdsList(PlayletOpenAdsQueryBo bo, PageQuery pageQuery);

    /**
     * 定时任务处理广告状态
     *
     * @return
     */
    Boolean updateAdInfoState();

    /**
     * 分片广告分页查询
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<PlayletPatchesAdsListVo> queryPatchesAdsList(PlayletPatchesAdsQueryBo bo, PageQuery pageQuery);

    /**
     * 查询贴片广告详情
     *
     * @param id
     * @return
     */
    PlayletPatchesAdsVo queryPatchesById(Long id);

    /**
     * 开屏广告查询
     */
    PlayletOpenAdsVo queryOpenDetail();

    /**
     * 贴片广告查询
     */
    PlayletPatchesAdsVo queryPatchesAdsByVideoId(Long videoId);

    /**
     * 广告定时任务
     *
     * @return
     */
    Boolean advertisementJobHandler();

    /**
     * 刷新广告缓存
     * 
     * @param msgContent
     * @return
     */
    Boolean refreshAdvertisementCache(MsgContent msgContent);

    /**
     * 我的-猜你喜欢列表
     */
    TableDataInfo<SohuGuessYouLikeVo> queryPageListOfAirec(SohuGuessYouLikeQueryBo bo, PageQuery pageQuery);

    /**
     * 视频-广告
     */
    TableDataInfo<SohuVideoAdInfoVo> queryPageListOfVideo(SohuVideoAdInfoQueryBo bo, PageQuery pageQuery);

    /**
     * 图文-广告
     */
    TableDataInfo<SohuArticleAdInfoVo> queryPageListOfArticle(SohuArticleAdInfoQueryBo bo, PageQuery pageQuery);

    /**
     * 商单-广告
     */
    TableDataInfo<SohuTaskSiteAdInfoVo> queryPageListOfTaskSite(SohuTaskSiteAdInfoQueryBo bo, PageQuery pageQuery);

    /**
     * 根据广告位编号刷新广告主体信息-广告位发送修改时调用
     * 
     * @param adPlace
     * @return
     */
    Boolean refreshAdInfoByAdPlace(String adPlace);

    /**
     * 刷新数据-用于发版时处理兼容处理旧数据
     * 
     * @return
     */
    Boolean refreshAdInfoTest();

    /**
     * 随机获取一个广告
     * 
     * @param excludeIds 需要排除的广告ID列表
     * @param adPlaceCode 广告位编号
     * @return 随机广告信息
     */
    SohuAdInfoVo getRandomAd(List<Long> excludeIds, String adPlaceCode);

    /**
     * 根据广告位编号获取广告信息
     *
     * @param adPlaceCode 广告位编号
     * @return 广告信息列表
     */
    List<SohuAdInfoVo> getAdListByAdPlaceCode(String adPlaceCode);

    /**
     * 处理机审结果
     * @param busyCode
     * @param isPass
     * @param reason
     */
    void handleRobot(String busyCode, Boolean isPass, String reason);

    /**
     * 根据群聊id下架广告
     *
     * @param groupId 群聊id
     */
    void offShelfAdInfoByGroupId(Long groupId);

    /**
     * 图文-广告详情
     */
    SohuArticleAdInfoVo queryArticleAdInfoDetail(SohuArticleAdInfoQueryBo bo);
}
