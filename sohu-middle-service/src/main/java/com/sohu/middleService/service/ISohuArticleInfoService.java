package com.sohu.middleService.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuArticleInfoBo;
import com.sohu.middle.api.vo.SohuArticleInfoVo;
import com.sohu.middleService.domain.SohuArticleInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 图文拓展Service接口
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
public interface ISohuArticleInfoService extends ISohuBaseService<SohuArticleInfo,SohuArticleInfoVo> {

    /**
     * 查询图文拓展
     */
    SohuArticleInfoVo queryById(Long id);

    /**
     * 查询图文拓展列表
     */
    TableDataInfo<SohuArticleInfoVo> queryPageList(SohuArticleInfoBo bo, PageQuery pageQuery);

    /**
     * 查询图文拓展列表
     */
    List<SohuArticleInfoVo> queryList(SohuArticleInfoBo bo);

    /**
     * 修改图文拓展
     */
    Boolean insertByBo(SohuArticleInfoBo bo);

    /**
     * 修改图文拓展
     */
    Boolean updateByBo(SohuArticleInfoBo bo);

    /**
     * 校验并批量删除图文拓展信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询图文拓展
     */
    SohuArticleInfoVo queryByArticleId(Long articleId);

    /**
     * 查询图文拓展
     */
    SohuArticleInfoVo selectByArticleId(Long articleId);

    /**
     * 查询图文拓展
     */
    SohuArticleInfo queryEntityByArticleId(Long articleId);

    /**
     * 更新内容
     *
     * @param articleId 图文id
     * @param content   内容
     * @param learnNum
     */
    void updateByArticleId(Long articleId, String content, Integer learnNum);

    void deleteByArticleIds(Collection<Long> ids);

    /**
     * 返回map
     */
    Map<Long, SohuArticleInfoVo> queryMap(List<Long> articleIds);

    void updateByArticleId(Long articleId, SohuArticleInfoBo bo);

    /**
     * 删除缓存
     */
    void evictArticleId(Long articleId);

    /**
     * 查询文章统计信息
     * @param articleId
     * @return
     */
    SohuArticleInfoVo queryArticleForCount(Long articleId);
}
