package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.UserConstants;
import com.sohu.common.core.enums.DictEnum;
import com.sohu.common.core.enums.NoticeOuterEnum;
import com.sohu.common.core.enums.SystemNoticeEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.SohuImUtil;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.web.domain.SohuEntity;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.mybatis.db.CacheMgr;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.bo.ImChatRequestBo;
import com.sohu.im.api.bo.SohuImGroupCreateBo;
import com.sohu.im.api.bo.SohuImGroupUserBo;
import com.sohu.im.api.enums.ImGroupPermissionType;
import com.sohu.im.api.enums.ImGroupType;
import com.sohu.im.api.enums.ImMessageTypeEnum;
import com.sohu.im.api.service.RemoteImService;
import com.sohu.im.api.vo.SohuImGroupVo;
import com.sohu.middle.api.bo.notice.SohuOuterNoticeBo;
import com.sohu.middle.api.bo.notice.SohuSystemNoticeBo;
import com.sohu.middle.api.vo.notice.SohuSystemNoticeVo;
import com.sohu.middleService.domain.SohuSystemNotice;
import com.sohu.middleService.mapper.notice.SohuSystemNoticeMapper;
import com.sohu.middleService.service.ISohuOuterNoticeService;
import com.sohu.middleService.service.ISohuSystemNoticeService;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.system.api.RemoteDictService;
import com.sohu.system.api.domain.SysDictData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 系统通知消息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-10
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuSystemNoticeServiceImpl implements ISohuSystemNoticeService {

    private final SohuSystemNoticeMapper baseMapper;
    private final ISohuOuterNoticeService sohuOuterNoticeService;
    @DubboReference
    private RemoteImService remoteImService;
    @DubboReference
    private RemoteAccountService remoteAccountService;
    @DubboReference
    private RemoteDictService remoteDictService;

    /**
     * 查询系统通知消息
     */
    @Override
    public SohuSystemNoticeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询系统通知消息列表
     */
    @Override
    public TableDataInfo<SohuSystemNoticeVo> queryPageList(SohuSystemNoticeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuSystemNotice> lqw = buildQueryWrapper(bo);
        Page<SohuSystemNoticeVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuSystemNoticeVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            for (SohuSystemNoticeVo record : records) {
                record.setTime(record.getCreateTime().getTime());
            }
            result.setRecords(records);
        }
        // 外层系统消息未读数
        Integer outerSystemUnReadCount = CacheMgr.getInt(CacheConstants.NOTICE_UN_READ, SohuImUtil.noticeRedisKey(NoticeOuterEnum.OuterEnum.outerSystem.name(), null, LoginHelper.getUserId()));
        Integer systemUnReadCount = CacheMgr.getInt(CacheConstants.NOTICE_UN_READ, SohuImUtil.noticeRedisKey(NoticeOuterEnum.OuterEnum.outerSystem.name(), NoticeOuterEnum.SystemChildEnum.system.name(), LoginHelper.getUserId()));
        int unReadCount = outerSystemUnReadCount - systemUnReadCount;
        CacheMgr.set(CacheConstants.NOTICE_UN_READ, SohuImUtil.noticeRedisKey(NoticeOuterEnum.OuterEnum.outerSystem.name(), null, LoginHelper.getUserId()), unReadCount);
        // 清除系统通知的的未读消息
        CacheMgr.evict(CacheConstants.NOTICE_UN_READ, SohuImUtil.noticeRedisKey(NoticeOuterEnum.OuterEnum.outerSystem.name(), NoticeOuterEnum.SystemChildEnum.system.name(), LoginHelper.getUserId()));
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询系统通知消息列表
     */
    @Override
    public List<SohuSystemNoticeVo> queryList(SohuSystemNoticeBo bo) {
        LambdaQueryWrapper<SohuSystemNotice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuSystemNotice> buildQueryWrapper(SohuSystemNoticeBo bo) {
        LambdaQueryWrapper<SohuSystemNotice> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getSenderId() != null, SohuSystemNotice::getSenderId, bo.getSenderId());
        lqw.eq(bo.getReceiverId() != null, SohuSystemNotice::getReceiverId, bo.getReceiverId());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), SohuSystemNotice::getContent, bo.getContent());
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), SohuSystemNotice::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), SohuSystemNotice::getType, bo.getType());
        lqw.eq(bo.getReadType() != null, SohuSystemNotice::getReadType, bo.getReadType());
        lqw.eq(StringUtils.isNotBlank(bo.getShareType()), SohuSystemNotice::getShareType, bo.getShareType());
        lqw.eq(bo.getShareId() != null, SohuSystemNotice::getShareId, bo.getShareId());
        lqw.orderByDesc(SohuEntity::getCreateTime);
        return lqw;
    }

    /**
     * 新增系统通知消息
     */
    @Override
    public Boolean insertByBo(SohuSystemNoticeBo bo) {
        SohuSystemNotice add = BeanUtil.toBean(bo, SohuSystemNotice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改系统通知消息
     */
    @Override
    public Boolean updateByBo(SohuSystemNoticeBo bo) {
        SohuSystemNotice update = BeanUtil.toBean(bo, SohuSystemNotice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuSystemNotice entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除系统通知消息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<SohuSystemNoticeVo> noticeList() {
        Long userId = LoginHelper.getUserId();
        if (userId == null) {
            return Collections.emptyList();
        }
        List<SohuSystemNoticeVo> list = this.baseMapper.noticeList(userId);
        if (CollUtil.isNotEmpty(list)) {
            for (SohuSystemNoticeVo vo : list) {
                vo.setTime(vo.getCreateTime().getTime());
                String content = vo.getContent();
                if (JSONUtil.isTypeJSONObject(content)) {
                    JSONObject parseObj = JSONUtil.parseObj(content);
                    vo.setContent(parseObj.getStr("content"));
                }
            }
        }
        return list;
    }

    @Override
    public void sendNotice(Long senderId, Long receiverId, String title, String content, String type) {
        SohuSystemNotice notice = new SohuSystemNotice();
        notice.setSenderId(senderId == null ? UserConstants.ADMIN_ID : senderId);
        notice.setReceiverId(receiverId);
        notice.setTitle(title);
        notice.setContent(content);
        notice.setType(type);
        notice.setCreateTime(new Date());
        notice.setUpdateTime(new Date());
        this.baseMapper.insert(notice);
        // 系统消息未读数
        CacheMgr.incr(CacheConstants.NOTICE_UN_READ, SohuImUtil.noticeRedisKey(NoticeOuterEnum.OuterEnum.outerSystem.name(), NoticeOuterEnum.SystemChildEnum.system.name(), receiverId));
        // 更新外层通知
        SohuOuterNoticeBo noticeBo = SohuOuterNoticeBo.builder().userId(receiverId).type(NoticeOuterEnum.OuterEnum.outerSystem.name()).title(title).build();
        sohuOuterNoticeService.updateLastContent(noticeBo);
    }

    @Override
    public SohuImGroupVo agentDetail(Long id) {
        SohuSystemNotice systemNotice = this.baseMapper.selectById(id);
        if (Objects.isNull(systemNotice)) {
            log.warn("agentDetail -系统通知不存在，id:{}", id);
            return new SohuImGroupVo();
        }
        if (!StrUtil.equalsAnyIgnoreCase(systemNotice.getType(), SystemNoticeEnum.Type.createAgentGroup.name())) {
            return new SohuImGroupVo();
        }
        SohuImGroupVo imGroupVo = remoteImService.query(systemNotice.getReceiverId(), ImGroupType.groupAgent.name());
        if (Objects.nonNull(imGroupVo)) {
            return imGroupVo;
        } else {
            if (Objects.equals(systemNotice.getReadType(), 1L)) {
                throw new ServiceException("当前群聊已经解散");
            }
        }
        SohuAccountVo accountVo = remoteAccountService.queryByUserId(systemNotice.getReceiverId());
        if (Objects.isNull(accountVo)) {
            return new SohuImGroupVo();
        }
        SohuImGroupCreateBo imGroupCreateBo = new SohuImGroupCreateBo();

        imGroupCreateBo.setUnq(System.nanoTime());
        imGroupCreateBo.setUserId(systemNotice.getReceiverId());
        imGroupCreateBo.setGroupNotice(SohuImGroupCreateBo.ANONYMOUS_NOTICE);
        imGroupCreateBo.setName(accountVo.getMerchantName() + "客户群");
        imGroupCreateBo.setGroupType(ImGroupType.groupAgent.name());

        List<SysDictData> dictDataList = remoteDictService.selectDictDataByType(DictEnum.CustomerService.getKey());
        if (CollUtil.isNotEmpty(dictDataList)) {
            List<SohuImGroupUserBo> groupUsers = new ArrayList<>();
            for (SysDictData dictData : dictDataList) {
                if (NumberUtil.isNumber(dictData.getDictValue()) && Long.parseLong(dictData.getDictValue()) <= 0) {
                    continue; // 跳过无效的用户ID
                }
                SohuImGroupUserBo groupUserPull = SohuImGroupUserBo.builder().userId(Long.parseLong(dictData.getDictValue())).permissionType(ImGroupPermissionType.group_user.name()).build();
                groupUsers.add(groupUserPull);
            }
            imGroupCreateBo.setGroupUsers(groupUsers);
        }
        Boolean aBoolean = remoteImService.createGroup(imGroupCreateBo);
        if (BooleanUtil.isFalse(aBoolean)) {
            return new SohuImGroupVo();
        }

        imGroupVo = remoteImService.query(systemNotice.getReceiverId(), ImGroupType.groupAgent.name());
        if (Objects.isNull(imGroupVo)) {
            return new SohuImGroupVo();
        }
        String content = "【许愿狐行业群】欢迎你！\n" +
                "亲爱的伙伴：\n" +
                "这里专注帮你用许愿狐接更多订单！群主教你：\n" +
                "1.发布高曝光商单\n" +
                "2.玩转App生意技巧\n" +
                "必做\n" +
                "改群昵称：地区+业务+名字\n" +
                "直接提问：直接发你的业务难题\n" +
                "禁广告，急事@群主";

        ImChatRequestBo requestBo = new ImChatRequestBo();
        requestBo.setRealSenderId(systemNotice.getReceiverId());
        requestBo.setReceiverId(imGroupVo.getId());
        requestBo.setRealReceiverId(imGroupVo.getId());
        requestBo.setSessionType(imGroupVo.getGroupType());
        requestBo.setMessageType(ImMessageTypeEnum.Text.getCode());
        requestBo.setContent(content);
        requestBo.setChatId(System.nanoTime());
        requestBo.setLocalId(RandomUtil.randomString(16));
        remoteImService.serverActiveSend(systemNotice.getReceiverId(), requestBo, true);
        systemNotice.setReadType(1L);
        this.baseMapper.updateById(systemNotice);
        return imGroupVo;
    }
}
