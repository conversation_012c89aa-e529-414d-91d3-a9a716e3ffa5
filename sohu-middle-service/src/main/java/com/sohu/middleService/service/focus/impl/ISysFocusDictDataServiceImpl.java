package com.sohu.middleService.service.focus.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.sohu.middleService.domain.focus.SysFocusDictData;
import com.sohu.middleService.mapper.focus.SysFocusDictDataMapper;
import com.sohu.middleService.service.focus.ISysFocusDictDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@RequiredArgsConstructor
@Service
@DS(value = "focus")
public class ISysFocusDictDataServiceImpl implements ISysFocusDictDataService {

    private final SysFocusDictDataMapper sysFocusDictDataMapper;

    @Override
    public List<SysFocusDictData> queryList(Collection<Long> ids) {
        return sysFocusDictDataMapper.selectBatchIds(ids);
    }
}
