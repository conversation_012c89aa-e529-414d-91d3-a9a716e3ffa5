package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuIndependentMaterialUserBo;
import com.sohu.middle.api.vo.SohuIndependentMaterialUserVo;
import com.sohu.middleService.domain.SohuIndependentMaterialUser;
import com.sohu.middleService.mapper.SohuIndependentMaterialUserMapper;
import com.sohu.middleService.service.ISohuIndependentMaterialUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 用户分销素材库Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@RequiredArgsConstructor
@Service
public class SohuIndependentMaterialUserServiceImpl implements ISohuIndependentMaterialUserService {

    private final SohuIndependentMaterialUserMapper baseMapper;

    /**
     * 查询用户分销素材库
     */
    @Override
    public SohuIndependentMaterialUserVo queryById(Long materialId) {
        return baseMapper.selectVoById(materialId);
    }

    /**
     * 查询用户分销素材库列表
     */
    @Override
    public TableDataInfo<SohuIndependentMaterialUserVo> queryPageList(SohuIndependentMaterialUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuIndependentMaterialUser> lqw = buildQueryWrapper(bo);
        Page<SohuIndependentMaterialUserVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询用户分销素材库列表
     */
    @Override
    public List<SohuIndependentMaterialUserVo> queryList(SohuIndependentMaterialUserBo bo) {
        LambdaQueryWrapper<SohuIndependentMaterialUser> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuIndependentMaterialUser> buildQueryWrapper(SohuIndependentMaterialUserBo bo) {
        LambdaQueryWrapper<SohuIndependentMaterialUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getMaterialShareUserId() != null, SohuIndependentMaterialUser::getMaterialShareUserId, bo.getMaterialShareUserId());
        lqw.like(StringUtils.isNotBlank(bo.getMaterialShareUserName()), SohuIndependentMaterialUser::getMaterialShareUserName, bo.getMaterialShareUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getMaterialShareUrl()), SohuIndependentMaterialUser::getMaterialShareUrl, bo.getMaterialShareUrl());
        return lqw;
    }

    /**
     * 新增用户分销素材库
     */
    @Override
    public Boolean insertByBo(SohuIndependentMaterialUserBo bo) {
        SohuIndependentMaterialUser add = BeanUtil.toBean(bo, SohuIndependentMaterialUser.class);
        validEntityBeforeSave(add);
        // 判断是否存在记录
        LambdaQueryWrapper<SohuIndependentMaterialUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuIndependentMaterialUser::getMaterialId, bo.getMaterialId());
        lqw.eq(SohuIndependentMaterialUser::getMaterialShareUserId, bo.getMaterialShareUserId());
        if (baseMapper.exists(lqw)) {
            return Boolean.TRUE;
        }
        if (add.getMaterialShareUserId() == null) {
            add.setMaterialShareUserId(LoginHelper.getUserId());
        }
        if (add.getMaterialShareUserName() == null) {
            add.setMaterialShareUserName(LoginHelper.getUsername());
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMaterialId(add.getMaterialId());
        }
        return flag;
    }

    /**
     * 修改用户分销素材库
     */
    @Override
    public Boolean updateByBo(SohuIndependentMaterialUserBo bo) {
        SohuIndependentMaterialUser update = BeanUtil.toBean(bo, SohuIndependentMaterialUser.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuIndependentMaterialUser entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除用户分销素材库
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 查询用户分销素材库
     */
    @Override
    public SohuIndependentMaterialUserVo queryMaterialUser(Long materialId, Long materialShareUserId) {
        LambdaQueryWrapper<SohuIndependentMaterialUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuIndependentMaterialUser::getMaterialId, materialId);
        lqw.eq(SohuIndependentMaterialUser::getMaterialShareUserId, materialShareUserId);
        return baseMapper.selectVoOne(lqw);
    }

}
