package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuLessonLabelBo;
import com.sohu.middle.api.vo.SohuLessonLabelVo;
import com.sohu.middleService.domain.SohuLessonLabel;
import com.sohu.middleService.mapper.SohuLessonLabelMapper;
import com.sohu.middleService.service.ISohuLessonLabelService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 狐少少课堂标签Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
@RequiredArgsConstructor
@Service
public class SohuLessonLabelServiceImpl extends SohuBaseServiceImpl<SohuLessonLabelMapper, SohuLessonLabel, SohuLessonLabelVo> implements ISohuLessonLabelService {

//    /**
//     * 查询狐少少课堂标签
//     */
//    @Override
//    public SohuLessonLabelVo queryById(Long id) {
//        return baseMapper.selectVoById(id);
//    }

    /**
     * 查询狐少少课堂标签列表
     */
    @Override
    public TableDataInfo<SohuLessonLabelVo> queryPageList(SohuLessonLabelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuLessonLabel> lqw = buildQueryWrapper(bo);
        Page<SohuLessonLabelVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询狐少少课堂标签列表(不分页 已上架)
     */
    @Override
    public List<SohuLessonLabelVo> queryList(SohuLessonLabelBo bo) {
        bo.setState(CommonState.OnShelf.getCode());
        LambdaQueryWrapper<SohuLessonLabel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuLessonLabel> buildQueryWrapper(SohuLessonLabelBo bo) {
        LambdaQueryWrapper<SohuLessonLabel> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getType()), SohuLessonLabel::getType, bo.getType());
        lqw.like(StringUtils.isNotBlank(bo.getName()), SohuLessonLabel::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuLessonLabel::getState, bo.getState());
        lqw.eq(bo.getSortIndex() != null, SohuLessonLabel::getSortIndex, bo.getSortIndex());
        lqw.eq(bo.getUserId() != null, SohuLessonLabel::getUserId, bo.getUserId());
        lqw.orderByAsc(SohuLessonLabel::getSortIndex);
        lqw.orderByDesc(SohuLessonLabel::getCreateTime);
        return lqw;
    }

    /**
     * 新增狐少少课堂标签
     */
    @Override
    public Boolean insertByBo(SohuLessonLabelBo bo) {
        SohuLessonLabel add = BeanUtil.toBean(bo, SohuLessonLabel.class);
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        add.setUserId(userId);
        add.setCreateTime(new Date());
        add.setUpdateTime(new Date());
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }


    /**
     * 修改狐少少课堂标签
     */
    @Override
    public Boolean updateByBo(SohuLessonLabelBo bo) {
        SohuLessonLabel update = BeanUtil.toBean(bo, SohuLessonLabel.class);
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
//        if (!Objects.equals(LoginHelper.getUserId(), bo.getUserId())) {
//            throw new ServiceException(MessageUtils.message("no.power"));
//        }
        update.setUpdateTime(new Date());
        update.setUserId(userId);
        SohuLessonLabel label = baseMapper.selectById(bo.getId());
        if (!bo.getName().equals(label.getName()) || !bo.getType().equals(label.getType())) {
            validEntityBeforeSave(update);
        }
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private boolean validEntityBeforeSave(SohuLessonLabel entity) {
        //同名称同类型已存在不给修改
        QueryWrapper<SohuLessonLabel> wrapper = new QueryWrapper<>();
        wrapper.eq("type", entity.getType());
        wrapper.eq("name", entity.getName());
        SohuLessonLabel sohuLessonLabel = baseMapper.selectOne(wrapper);
        if (ObjectUtil.isNotNull(sohuLessonLabel)) {
            throw new RuntimeException("该标签名称已存在");
        } else {
            return true;
        }
        //TODO 做一些数据校验,如唯一约束
    }
//
//    /**
//     * 批量删除狐少少课堂标签
//     */
//    @Override
//    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        if (isValid) {
//            //TODO 做一些业务上的校验,判断是否需要校验
//        }
//        return baseMapper.deleteBatchIds(ids) > 0;
//    }
}
