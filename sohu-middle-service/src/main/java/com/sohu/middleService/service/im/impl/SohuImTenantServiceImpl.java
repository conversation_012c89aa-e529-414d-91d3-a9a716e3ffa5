package com.sohu.middleService.service.im.impl;

/**
 * <AUTHOR>
 * @date 2024/12/12 15:07
 */

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.im.SohuImTenantApplyActiveBo;
import com.sohu.middle.api.bo.im.SohuImTenantBo;
import com.sohu.middle.api.bo.im.SohuImTenantUpdateCodeBo;
import com.sohu.middle.api.vo.im.SohuImTenantConfigVo;
import com.sohu.middle.api.vo.im.SohuImTenantInfoModel;
import com.sohu.middle.api.vo.im.SohuImTenantVo;
import com.sohu.middleService.domain.im.SohuImTenant;
import com.sohu.middleService.mapper.im.SohuImTenantMapper;
import com.sohu.middleService.service.im.ISohuImActivationCodeService;
import com.sohu.middleService.service.im.ISohuImTenantConfigService;
import com.sohu.middleService.service.im.ISohuImTenantService;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class SohuImTenantServiceImpl implements ISohuImTenantService {

    private final SohuImTenantMapper baseMapper;

    @DubboReference
    private RemoteUserService remoteUserService;

    private final ISohuImTenantConfigService sohuImTenantConfigService;
    private final ISohuImActivationCodeService sohuImActivationCodeService;

    @DubboReference
    private RemoteAccountService remoteAccountService;

    /**
     * 修改服务器ID最大提交次数
     */
    private final static Integer SUBMIT_NUM_MAX_VALUE = 1;
    /**
     * 默认服务缓存秒数，单位秒，缓存7天
     */
    private final static Long DEFAULT_CACHE_TENANT_CODE_SECONDS = 604800L;

    /**
     * 查询im租户
     */
    @Override
    public SohuImTenantVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询im租户列表
     */
    @Override
    public TableDataInfo<SohuImTenantVo> queryPageList(SohuImTenantBo bo, PageQuery pageQuery) {
        Page<SohuImTenantVo> result = baseMapper.queryPageList(PageQueryUtils.build(pageQuery), bo);
        //封装租户注册量
        if (CollUtil.isNotEmpty(result.getRecords())) {
            List<Long> tenantIds = result.getRecords().stream().map(SohuImTenantVo::getUserId).collect(Collectors.toList());
            //查询租户注册量
            Map<Long, Integer> tenantRegisterNumMap = remoteUserService.getTenantRegisterNum(tenantIds);
            result.getRecords().forEach(item -> {
                item.setRegisterNum(tenantRegisterNumMap.get(item.getUserId()) == null ? 0 : tenantRegisterNumMap.get(item.getUserId()));
                if (item.getValidEndTime() != null) {
                    long days = item.getValidEndTime().after(new Date()) ? DateUtil.between(DateUtil.beginOfDay(new Date()), item.getValidEndTime(), DateUnit.DAY) : 0;
                    item.setDays(days);
                }
            });
        }
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询im租户列表
     */
    @Override
    public List<SohuImTenantVo> queryList(SohuImTenantBo bo) {
        return null;
    }


    /**
     * 新增im租户
     */
    @Override
    public Boolean insertByBo(SohuImTenantBo bo) {
        SohuImTenant add = BeanUtil.toBean(bo, SohuImTenant.class);
        return baseMapper.insert(add) > 0;
    }

    /**
     * 修改im租户
     */
    @Override
    public Boolean updateByBo(SohuImTenantBo bo) {
        SohuImTenant update = BeanUtil.toBean(bo, SohuImTenant.class);
        return baseMapper.updateById(update) > 0;
    }


    @Override
    public Boolean enable(Long id) {
        SohuImTenant sohuImTenant = baseMapper.selectById(id);
        if (Objects.isNull(sohuImTenant)) {
            throw new RuntimeException("租户不存在");
        }
        int isEnable = sohuImTenant.getIsEnable();
        if (isEnable == Constants.ZERO) {
            sohuImTenant.setIsEnable(Constants.ONE);
        }
        if (isEnable == Constants.ONE) {
            sohuImTenant.setIsEnable(Constants.ZERO);
        }
        baseMapper.updateById(sohuImTenant);
        refreshImTenantInfoCache(sohuImTenant.getServerCode());
        return true;
    }

    @Override
    public SohuImTenantVo queryByUserId(Long userId) {
        LambdaQueryWrapper<SohuImTenant> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImTenant::getUserId, userId);
        lqw.last("limit 1");
        return this.baseMapper.selectVoOne(lqw);
    }

    @Override
    public Boolean applyActive(SohuImTenantApplyActiveBo bo) {
        SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserIdOfPass(LoginHelper.getUserId());
        if (Objects.isNull(sohuAccountVo)) {
            throw new RuntimeException("请实名认证通过后再激活");
        }
        SohuImTenantConfigVo sohuImTenantConfigVo = sohuImTenantConfigService.queryByUserId(LoginHelper.getUserId());
        if (Objects.isNull(sohuImTenantConfigVo)) {
            throw new RuntimeException("请填写配置项再激活");
        }
        sohuImActivationCodeService.bindTenant(LoginHelper.getUserId(), bo.getActivationCode());
        SohuImTenantVo sohuImTenantVo = this.queryByUserId(LoginHelper.getUserId());
        if (Objects.nonNull(sohuImTenantVo)) {
            SohuImTenantInfoModel model = BeanUtil.copyProperties(sohuImTenantVo, SohuImTenantInfoModel.class);
            model.setImServerUrl(sohuImTenantConfigVo.getImServerUrl());
            model.setImSocketUrl(sohuImTenantConfigVo.getImSocketUrl());
            model.setMerchantName(sohuAccountVo.getMerchantName());
            /**
             * 剩余缓存秒数
             */
            long seconds = DateUtil.between(new Date(), sohuImTenantVo.getValidEndTime(), DateUnit.SECOND);
            RedisUtils.setCacheObject(CacheConstants.IM_TENANT_CODE + sohuImTenantVo.getServerCode(), model, Duration.ofSeconds(seconds));
        }
        return true;
    }

    @Override
    public Boolean updateServerCode(SohuImTenantUpdateCodeBo bo) {
        SohuImTenant sohuImTenant = this.baseMapper.selectById(bo.getId());
        if (Objects.isNull(sohuImTenant)) {
            throw new RuntimeException("数据不存在");
        }
        if (!Objects.equals(LoginHelper.getUserId(), sohuImTenant.getUserId())) {
            throw new RuntimeException("非法操作,无数据权限");
        }
        if (sohuImTenant.getSubmitNum() >= SUBMIT_NUM_MAX_VALUE) {
            throw new RuntimeException("修改次数已达上限");
        }
        LambdaQueryWrapper<SohuImTenant> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImTenant::getServerCode, bo.getServerCode());
        if (this.baseMapper.exists(lqw)) {
            throw new RuntimeException("服务器ID已存在，请重新命名");
        }
        String lastServerCode = sohuImTenant.getServerCode();
        sohuImTenant.setSubmitNum(sohuImTenant.getSubmitNum() + 1);
        sohuImTenant.setServerCode(bo.getServerCode());
        this.baseMapper.updateById(sohuImTenant);
        this.refreshImTenantInfoCache(sohuImTenant.getServerCode());
        RedisUtils.deleteObject(CacheConstants.IM_TENANT_CODE + lastServerCode);
        return true;
    }

    @Override
    public Boolean initImTenant() {
        SohuImTenantVo sohuImTenantVo = this.queryByUserId(LoginHelper.getUserId());
        if (Objects.isNull(sohuImTenantVo)) {
            SohuImTenant sohuImTenant = new SohuImTenant();
            sohuImTenant.setUserId(LoginHelper.getUserId());
            sohuImTenant.setServerCode(generateServerCode());
            sohuImTenant.setIsEnable(1);
            sohuImTenant.setStatus(0);
            sohuImTenant.setNickName(LoginHelper.getLoginUser().getNickname());
            this.baseMapper.insert(sohuImTenant);
        }
        return true;
    }

    private String generateServerCode() {
        String serverCode = RandomUtil.randomString(10);
        LambdaQueryWrapper<SohuImTenant> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImTenant::getServerCode, serverCode);
        if (this.baseMapper.exists(lqw)) {
            return generateServerCode();
        }
        return serverCode;
    }

    @Override
    public SohuImTenantInfoModel refreshImTenantInfoCache(String serverCode) {
        /**
         * 剩余缓存秒数
         */
        long seconds = DEFAULT_CACHE_TENANT_CODE_SECONDS;
        SohuImTenantVo sohuImTenantVo = this.queryByServerCode(serverCode);
        SohuImTenantInfoModel model = null;
        if (Objects.nonNull(sohuImTenantVo)) {
            model = BeanUtil.copyProperties(sohuImTenantVo, SohuImTenantInfoModel.class);
            SohuImTenantConfigVo sohuImTenantConfigVo = sohuImTenantConfigService.queryByUserId(sohuImTenantVo.getUserId());
            if (Objects.nonNull(sohuImTenantConfigVo)) {
                model.setImServerUrl(sohuImTenantConfigVo.getImServerUrl());
                model.setImSocketUrl(sohuImTenantConfigVo.getImSocketUrl());
            }
            SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserIdOfPass(sohuImTenantVo.getUserId());
            if (Objects.nonNull(sohuAccountVo)) {
                model.setMerchantName(sohuAccountVo.getMerchantName());
            }
            if (Objects.nonNull(sohuImTenantVo.getValidEndTime()) && sohuImTenantVo.getValidEndTime().compareTo(new Date()) == 1) {
                seconds = DateUtil.between(new Date(), sohuImTenantVo.getValidEndTime(), DateUnit.SECOND);
            }
        } else {
            //兜底
            model = new SohuImTenantInfoModel();
            model.setServerCode(serverCode);
            model.setStatus(0);
            model.setIsEnable(0);
        }
        RedisUtils.setCacheObject(CacheConstants.IM_TENANT_CODE + serverCode, model, Duration.ofSeconds(seconds));
        return model;
    }

    @Override
    public SohuImTenantVo queryByServerCode(String serverCode) {
        LambdaQueryWrapper<SohuImTenant> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImTenant::getServerCode, serverCode);
        lqw.last("limit 1");
        return this.baseMapper.selectVoOne(lqw);
    }

    @Override
    public SohuImTenantInfoModel getTenantInfoByServerCode(String serverCode) {
        return this.getTenantInfoByServerCode(serverCode, false);
    }

    @Override
    public SohuImTenantInfoModel getTenantInfoByServerCode(String serverCode, boolean forceRefresh) {
        SohuImTenantInfoModel model = (SohuImTenantInfoModel) RedisUtils.getCacheObject(CacheConstants.IM_TENANT_CODE + serverCode);
        if (Objects.nonNull(model)) {
            return model;
        }
        RLock rLock = RedisUtils.getLock(CacheConstants.IM_TENANT_CODE_LOCK_KEY);
        if (Optional.ofNullable(RedisUtils.tryLockAndWaitAndNeedUnlock(rLock, 2000)).orElse(true)) {
            try {
                model = (SohuImTenantInfoModel) RedisUtils.getCacheObject(CacheConstants.IM_TENANT_CODE + serverCode);
                if (Objects.nonNull(model)) {
                    return model;
                }
                return refreshImTenantInfoCache(serverCode);
            } finally {
                RedisUtils.unLock(rLock);
                return null;
            }
        } else {
            //请稍后再试
            return null;
        }
    }
}
