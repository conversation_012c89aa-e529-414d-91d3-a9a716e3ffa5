package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.domain.BaseCommonBo;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuUpdateStateBo;
import com.sohu.middle.api.bo.gameNovel.SohuGameNovelAppPageBo;
import com.sohu.middle.api.bo.gameNovel.SohuGameNovelBo;
import com.sohu.middle.api.bo.gameNovel.SohuGameNovelFormBo;
import com.sohu.middle.api.bo.gameNovel.SohuGameNovelInfoBo;
import com.sohu.middle.api.vo.SohuCategoryVo;
import com.sohu.middle.api.vo.SohuViewRecordVo;
import com.sohu.middle.api.vo.gameNovel.SohuGameNovelAppListVo;
import com.sohu.middle.api.vo.gameNovel.SohuGameNovelInfoVo;
import com.sohu.middle.api.vo.gameNovel.SohuGameNovelRecordVo;
import com.sohu.middle.api.vo.gameNovel.SohuGameNovelVo;
import com.sohu.middleService.domain.SohuViewRecord;
import com.sohu.middleService.domain.gameNovel.SohuGameNovel;
import com.sohu.middleService.domain.gameNovel.SohuGameNovelInfo;
import com.sohu.middleService.mapper.SohuGameNovelInfoMapper;
import com.sohu.middleService.mapper.SohuGameNovelMapper;
import com.sohu.middleService.mapper.SohuViewRecordMapper;
import com.sohu.middleService.service.ISohuCategoryService;
import com.sohu.middleService.service.ISohuUserService;
import com.sohu.middleService.service.gameNovel.ISohuGameNovelInfoService;
import com.sohu.middleService.service.gameNovel.ISohuGameNovelService;
import com.sohu.middleService.utils.FormalTool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 游戏主体Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-22
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuGameNovelServiceImpl extends SohuBaseServiceImpl<SohuGameNovelMapper, SohuGameNovel, SohuGameNovelVo> implements ISohuGameNovelService {

    private final SohuGameNovelInfoMapper sohuGameNovelInfoMapper;
    private final SohuViewRecordMapper sohuViewRecordMapper;

    private final ISohuGameNovelInfoService sohuGameNovelInfoService;
    private final ISohuCategoryService sohuCategoryService;
    private final ISohuUserService sohuUserService;

    /**
     * 查询游戏主体
     */
    @Override
    public SohuGameNovelVo queryById(Long id, BaseCommonBo commonBo) {
        SohuGameNovelVo novelVo = baseMapper.selectVoById(id);
        saveClickRecord(novelVo, commonBo);
        if (Objects.nonNull(novelVo)) {
            LambdaQueryWrapper<SohuGameNovelInfo> infoLqw = Wrappers.lambdaQuery();
            infoLqw.eq(SohuGameNovelInfo::getGameId, id);
            infoLqw.last("limit 1");
            novelVo.setInfo(sohuGameNovelInfoMapper.selectVoOne(infoLqw));
        }
        return novelVo;
    }

    private void saveClickRecord(SohuGameNovelVo novelVo, BaseCommonBo commonBo) {
        if (Objects.isNull(novelVo)) {
            return;
        }
        CompletableFuture.runAsync(() -> {
            Long loginId = LoginHelper.getUserId();
            SohuViewRecord record = new SohuViewRecord();
            record.setBusyType(novelVo.getBusyType());
            record.setBusyCode(novelVo.getId());
            record.setUuid(commonBo.getUuid());
            record.setUserId(loginId == null ? 0L : loginId);
            record.setBusyBelonger(novelVo.getUserId() == null ? 0L : novelVo.getUserId());
            record.setViewTime(new Date());
            if (record.getUserId() == 0L && StrUtil.isBlankIfStr(record.getUuid())) {
                return;
            }
            sohuViewRecordMapper.insert(record);
            sohuGameNovelInfoMapper.inrClickCount(record.getBusyCode());
        });
    }

    /**
     * 查询游戏主体列表
     */
    @Override
    public TableDataInfo<SohuGameNovelVo> queryPageList(SohuGameNovelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuGameNovel> lqw = buildQueryWrapper(bo);
        Page<SohuGameNovelVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        if (CollUtil.isEmpty(result.getRecords())) {
            return TableDataInfoUtils.build();
        }
        List<SohuGameNovelVo> records = result.getRecords();
        List<Long> ids = records.stream().map(SohuGameNovelVo::getId).collect(Collectors.toList());
        LambdaQueryWrapper<SohuGameNovelInfo> infoLqw = Wrappers.lambdaQuery();
        infoLqw.in(SohuGameNovelInfo::getGameId, ids);
        List<SohuGameNovelInfoVo> novelInfos = sohuGameNovelInfoMapper.selectVoList(infoLqw);
        Map<Long, SohuGameNovelInfoVo> infoMap = novelInfos.stream().collect(Collectors.toMap(SohuGameNovelInfoVo::getGameId, u -> u));
        for (SohuGameNovelVo record : records) {
            record.setInfo(infoMap.get(record.getId()));
        }
        result.setRecords(records);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询游戏主体列表
     */
    @Override
    public List<SohuGameNovelVo> queryList(SohuGameNovelBo bo) {
        LambdaQueryWrapper<SohuGameNovel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuGameNovel> buildQueryWrapper(SohuGameNovelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuGameNovel> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuGameNovel::getUserId, bo.getUserId());
        lqw.like(StrUtil.isNotBlank(bo.getUserName()), SohuGameNovel::getUserName, bo.getUserName());
        lqw.eq(StrUtil.isNotBlank(bo.getPublishCompany()), SohuGameNovel::getPublishCompany, bo.getPublishCompany());
        lqw.eq(StrUtil.isNotBlank(bo.getCompanyUrl()), SohuGameNovel::getCompanyUrl, bo.getCompanyUrl());
        lqw.eq(StrUtil.isNotBlank(bo.getPublishNumber()), SohuGameNovel::getPublishNumber, bo.getPublishNumber());
        lqw.eq(StrUtil.isNotBlank(bo.getIdent()), SohuGameNovel::getIdent, bo.getIdent());
        lqw.eq(StrUtil.isNotBlank(bo.getLang()), SohuGameNovel::getLang, bo.getLang());
        lqw.eq(StrUtil.isNotBlank(bo.getTitle()), SohuGameNovel::getTitle, bo.getTitle());
        lqw.eq(StrUtil.isNotBlank(bo.getSummary()), SohuGameNovel::getSummary, bo.getSummary());
        lqw.eq(StrUtil.isNotBlank(bo.getIcon()), SohuGameNovel::getIcon, bo.getIcon());
        lqw.eq(StrUtil.isNotBlank(bo.getCoverBanner()), SohuGameNovel::getCoverBanner, bo.getCoverBanner());
        lqw.eq(bo.getCategoryId() != null, SohuGameNovel::getCategoryId, bo.getCategoryId());
        lqw.eq(StrUtil.isNotBlank(bo.getLabel()), SohuGameNovel::getLabel, bo.getLabel());
        lqw.eq(StrUtil.isNotBlank(bo.getRenew()), SohuGameNovel::getRenew, bo.getRenew());
        lqw.eq(bo.getPublish() != null, SohuGameNovel::getPublish, bo.getPublish());
        lqw.eq(StrUtil.isNotBlank(bo.getType()), SohuGameNovel::getType, bo.getType());
        lqw.eq(StrUtil.isNotBlank(bo.getBusyType()), SohuGameNovel::getBusyType, bo.getBusyType());
        lqw.eq(StrUtil.isNotBlank(bo.getJumpLink()), SohuGameNovel::getJumpLink, bo.getJumpLink());
        lqw.eq(StrUtil.isNotBlank(bo.getDownloadLink()), SohuGameNovel::getDownloadLink, bo.getDownloadLink());
        lqw.eq(StrUtil.isNotBlank(bo.getQrcodeUrl()), SohuGameNovel::getQrcodeUrl, bo.getQrcodeUrl());
        lqw.eq(bo.getSortIndex() != null, SohuGameNovel::getSortIndex, bo.getSortIndex());
        lqw.eq(bo.getRecomm() != null, SohuGameNovel::getRecomm, bo.getRecomm());
        lqw.eq(bo.getTop() != null, SohuGameNovel::getTop, bo.getTop());
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            lqw.ge(StrUtil.isNotBlank(bo.getStartTime()), SohuGameNovel::getCreateTime, DateUtil.beginOfDay(DateUtil.parseDate(bo.getStartTime())));
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            lqw.le(StrUtil.isNotBlank(bo.getEndTime()), SohuGameNovel::getCreateTime, DateUtil.endOfDay(DateUtil.parseDate(bo.getEndTime())));
        }
        return lqw;
    }

    /**
     * 新增游戏主体
     */
    @Override
    public Boolean insertByBo(SohuGameNovelBo bo) {
        SohuGameNovel add = BeanUtil.toBean(bo, SohuGameNovel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改游戏主体
     */
    @Override
    public Boolean updateByBo(SohuGameNovelBo bo) {
        SohuGameNovel update = BeanUtil.toBean(bo, SohuGameNovel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuGameNovel entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除游戏主体
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addGameNovel(SohuGameNovelFormBo addBo) {
        SohuGameNovel sohuGameNovel = new SohuGameNovel();
        sohuGameNovel.setIcon(addBo.getIcon());
        sohuGameNovel.setTitle(addBo.getTitle());
        sohuGameNovel.setPopularizeTitle(addBo.getPopularizeTitle());
        sohuGameNovel.setCompanyUrl(addBo.getCompanyUrl());
        sohuGameNovel.setCategoryId(addBo.getCategoryId());
        sohuGameNovel.setPublishCompany(addBo.getPublishCompany());
        sohuGameNovel.setCoverBanner(addBo.getCoverBanner());
        sohuGameNovel.setQrcodeUrl(addBo.getQrcodeUrl());
        sohuGameNovel.setTerminal(addBo.getTerminal());
        sohuGameNovel.setBusyType(BusyType.GameNovel.getType());
        sohuGameNovel.setSummary(FormalTool.getPlainText(addBo.getIntro()));
        sohuGameNovel.setRecomm(addBo.getRecomm());
        sohuGameNovel.setType(addBo.getType());
        sohuGameNovel.setTitleBandUrl(addBo.getTitleBandUrl());
        sohuGameNovel.setDownloadLink(addBo.getDownloadLink());
        sohuGameNovel.setCreateTime(new Date());
        baseMapper.insert(sohuGameNovel);

        SohuGameNovelInfoBo infoBo = new SohuGameNovelInfoBo();
        infoBo.setGameId(sohuGameNovel.getId());
        infoBo.setGiftIcon(addBo.getGiftIcon());
        infoBo.setGiftLink(addBo.getGiftLink());
        infoBo.setIntro(addBo.getIntro());
        infoBo.setImages(addBo.getImages());
        infoBo.setState(CommonState.OffShelf.getCode());
        sohuGameNovelInfoService.insertByBo(infoBo);
        return true;
    }

    @Override
    public Boolean updateState(SohuUpdateStateBo stateBo) {
        SohuGameNovelInfo gameNovelInfo = sohuGameNovelInfoMapper.selectOne(SohuGameNovelInfo::getGameId, stateBo.getId());
        if (Objects.isNull(gameNovelInfo)) {
            throw new ServiceException(MessageUtils.message("wrong_info"));
        }
        gameNovelInfo.setState(stateBo.getState());
        gameNovelInfo.setRejectReason(stateBo.getRejectReason());
        return sohuGameNovelInfoMapper.updateById(gameNovelInfo) > 0;
    }

    @Override
    public TableDataInfo<SohuGameNovelAppListVo> queryAppPageList(SohuGameNovelAppPageBo bo) {
        log.info("game novel queryAppPageList request:{}", JSONUtil.toJsonStr(bo));
        SohuCategoryVo sohuCategoryVo = sohuCategoryService.queryOne(BusyType.GameNovel.getType(), bo.getCategoryIdent());
        IPage<SohuGameNovel> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<SohuGameNovelVo> pageList = baseMapper.queryAppPageList(Objects.isNull(sohuCategoryVo) ? null : sohuCategoryVo.getId(), CommonState.OnShelf.getCode(), bo, page);
        if (CollUtil.isEmpty(pageList.getRecords())) {
            return TableDataInfoUtils.build();
        }
        List<SohuGameNovelVo> records = pageList.getRecords();
        Set<Long> categoryIds = records.stream().map(SohuGameNovelVo::getCategoryId).collect(Collectors.toSet());
        Map<Long, SohuCategoryVo> categoryVoMap = sohuCategoryService.queryCategoryMap(categoryIds, bo.getCommonBo().getLang());
        for (SohuGameNovelVo record : records) {
            SohuCategoryVo categoryVo = categoryVoMap.get(record.getCategoryId());
            record.setCategoryName(Objects.isNull(categoryVo) ? "" : categoryVo.getName());
        }
        pageList.setRecords(records);
        TableDataInfo<SohuGameNovelVo> build = TableDataInfoUtils.build(pageList);
        TableDataInfo<SohuGameNovelAppListVo> result = TableDataInfoUtils.copyInfo(build, SohuGameNovelAppListVo.class);
        List<SohuGameNovelAppListVo> list = result.getData();
        for (SohuGameNovelAppListVo vo : list) {
            if (StrUtil.isBlankIfStr(vo.getTitleBandUrl())) {
                continue;
            }
            vo.setJumpLink(StrUtil.equalsAnyIgnoreCase("platformUrl", vo.getTitleBandUrl()) ? vo.getCompanyUrl() : vo.getGiftLink());
        }
        result.setData(list);
        return result;
    }

    @Override
    public TableDataInfo<SohuGameNovelVo> queryAdminPageList(SohuGameNovelBo bo, PageQuery pageQuery) {
        IPage<SohuGameNovel> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        Page<SohuGameNovelVo> pageList = baseMapper.queryAdminPageList(bo, page);
        if (CollUtil.isEmpty(pageList.getRecords())) {
            return TableDataInfoUtils.build();
        }
        List<SohuGameNovelVo> records = pageList.getRecords();
        Set<Long> categoryIds = records.stream().map(SohuGameNovelVo::getCategoryId).collect(Collectors.toSet());
        Map<Long, SohuCategoryVo> categoryVoMap = sohuCategoryService.queryCategoryMap(categoryIds, bo.getCommonBo().getLang());
        List<Long> ids = records.stream().map(SohuGameNovelVo::getId).collect(Collectors.toList());
        LambdaQueryWrapper<SohuGameNovelInfo> infoLqw = Wrappers.lambdaQuery();
        infoLqw.in(SohuGameNovelInfo::getGameId, ids);
        List<SohuGameNovelInfoVo> novelInfos = sohuGameNovelInfoMapper.selectVoList(infoLqw);
        Map<Long, SohuGameNovelInfoVo> infoMap = novelInfos.stream().collect(Collectors.toMap(SohuGameNovelInfoVo::getGameId, u -> u));
        for (SohuGameNovelVo record : records) {
            SohuCategoryVo categoryVo = categoryVoMap.get(record.getCategoryId());
            record.setCategoryName(Objects.isNull(categoryVo) ? "" : categoryVo.getName());
            record.setInfo(infoMap.get(record.getId()));
        }
        pageList.setRecords(records);
        return TableDataInfoUtils.build(pageList);
    }

    @Override
    public TableDataInfo<SohuGameNovelRecordVo> clickRecordList(Long id, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuViewRecord> lqw = new LambdaQueryWrapper<>();
        if (id != null && id > 0L) {
            lqw.eq(SohuViewRecord::getBusyCode, id);
        }
        lqw.eq(SohuViewRecord::getBusyType, BusyType.GameNovel.getType());
        lqw.orderByDesc(SohuViewRecord::getViewTime);
        IPage<SohuViewRecordVo> recordVoIPage = sohuViewRecordMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        if (CollUtil.isEmpty(recordVoIPage.getRecords())) {
            return TableDataInfoUtils.build();
        }
        List<SohuViewRecordVo> records = recordVoIPage.getRecords();
        Set<Long> clickUserIds = new HashSet<>();
        Set<Long> busyCodeList = new HashSet<>();
        for (SohuViewRecordVo record : records) {
            if (record.getUserId() != null && record.getUserId() > 0L) {
                clickUserIds.add(record.getUserId());
            }
            busyCodeList.add(record.getBusyCode());

        }
        List<SohuGameNovelVo> sohuGameNovels = baseMapper.selectVoBatchIds(busyCodeList);
        if (CollUtil.isEmpty(sohuGameNovels)) {
            return TableDataInfoUtils.build();
        }
        Map<Long, SohuGameNovelVo> map = sohuGameNovels.stream().collect(Collectors.toMap(SohuGameNovelVo::getId, u -> u));
        Map<Long, LoginUser> userMap = sohuUserService.queryMap(clickUserIds);
        TableDataInfo<SohuGameNovelRecordVo> result = new TableDataInfo<>();
        result.setTotal(recordVoIPage.getTotal());
        result.setData(records.stream().map(u -> {
            SohuGameNovelRecordVo recordVo = new SohuGameNovelRecordVo();
            SohuGameNovelVo novelVo = map.get(u.getBusyCode());
            BeanUtil.copyProperties(novelVo, recordVo);
            recordVo.setClickTime(u.getViewTime());
            if (u.getUserId() != null && u.getUserId() > 0L) {
                LoginUser user = userMap.get(u.getUserId());
                recordVo.setClickUserId(user.getUserId().toString());
                recordVo.setClickUserName(StrUtil.isBlankIfStr(user.getNickname()) ? user.getUsername() : user.getNickname());
            } else {
                recordVo.setClickUserId(u.getUuid());
                recordVo.setClickUserName("游客");
            }
            return recordVo;
        }).collect(Collectors.toList()));
        result.setCode(HttpStatus.HTTP_OK);
        return result;
    }

    @Override
    public Boolean updateGameNovel(SohuGameNovelFormBo addBo) {
        Long id = addBo.getId();
        SohuGameNovel gameNovel = baseMapper.selectById(id);
        if (Objects.isNull(gameNovel)) {
            throw new ServiceException(MessageUtils.message("wrong_info"));
        }
        gameNovel.setIcon(addBo.getIcon());
        gameNovel.setTitle(addBo.getTitle());
        gameNovel.setPopularizeTitle(addBo.getPopularizeTitle());
        gameNovel.setCompanyUrl(addBo.getCompanyUrl());
        gameNovel.setCategoryId(addBo.getCategoryId());
        gameNovel.setPublishCompany(addBo.getPublishCompany());
        gameNovel.setCoverBanner(addBo.getCoverBanner());
        gameNovel.setQrcodeUrl(addBo.getQrcodeUrl());
        gameNovel.setTerminal(addBo.getTerminal());
        gameNovel.setBusyType(BusyType.GameNovel.getType());
        gameNovel.setSummary(FormalTool.getPlainText(addBo.getIntro()));
        gameNovel.setRecomm(addBo.getRecomm());
        gameNovel.setType(addBo.getType());
        gameNovel.setTitleBandUrl(addBo.getTitleBandUrl());
        gameNovel.setDownloadLink(addBo.getDownloadLink());
        gameNovel.setUpdateTime(new Date());
        baseMapper.updateById(gameNovel);

        SohuGameNovelInfo gameNovelInfo = sohuGameNovelInfoMapper.selectOne(SohuGameNovelInfo::getGameId, id);
        gameNovelInfo.setGiftIcon(addBo.getGiftIcon());
        gameNovelInfo.setGiftLink(addBo.getGiftLink());
        gameNovelInfo.setIntro(addBo.getIntro());
        gameNovelInfo.setImages(addBo.getImages());
        gameNovelInfo.setState(CommonState.OffShelf.getCode());
        sohuGameNovelInfoMapper.updateById(gameNovelInfo);
        return true;
    }

}
