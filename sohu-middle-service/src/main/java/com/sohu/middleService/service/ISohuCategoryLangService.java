package com.sohu.middleService.service;


import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuCategoryLangBo;
import com.sohu.middle.api.vo.SohuCategoryLangVo;
import com.sohu.middleService.domain.SohuCategoryLang;

import java.util.Collection;
import java.util.List;

/**
 * 分类语种Service接口
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
public interface ISohuCategoryLangService extends ISohuBaseService<SohuCategoryLang, SohuCategoryLangVo> {

    /**
     * 查询分类语种
     */
    SohuCategoryLangVo queryById(Long id);

    /**
     * 查询分类语种列表
     */
    TableDataInfo<SohuCategoryLangVo> queryPageList(SohuCategoryLangBo bo, PageQuery pageQuery);

    /**
     * 查询分类语种列表
     */
    List<SohuCategoryLangVo> queryList(SohuCategoryLangBo bo);

    /**
     * 修改分类语种
     */
    Boolean insertByBo(SohuCategoryLangBo bo);

    /**
     * 修改分类语种
     */
    Boolean updateByBo(SohuCategoryLangBo bo);

    /**
     * 校验并批量删除分类语种信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
