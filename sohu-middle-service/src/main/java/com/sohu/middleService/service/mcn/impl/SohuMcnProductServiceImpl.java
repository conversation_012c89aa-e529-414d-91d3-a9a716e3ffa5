package com.sohu.middleService.service.mcn.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.ProductConstants;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.mcn.SohuMcnProductBo;
import com.sohu.middle.api.vo.mcn.SohuMcnProductVo;
import com.sohu.middleService.domain.mcn.SohuMcnProduct;
import com.sohu.middleService.mapper.mcn.SohuMcnProductMapper;
import com.sohu.middleService.service.impl.SohuBaseServiceImpl;
import com.sohu.middleService.service.mcn.ISohuMcnProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * MCN商品
 */
@RequiredArgsConstructor
@Service
public class SohuMcnProductServiceImpl extends SohuBaseServiceImpl<SohuMcnProductMapper,SohuMcnProduct,SohuMcnProductVo> implements ISohuMcnProductService {


    @Override
    public List<SohuMcnProductVo> getListOfShow() {
        LambdaQueryWrapper<SohuMcnProduct> wrapper = new LambdaQueryWrapper<SohuMcnProduct>();
        wrapper.eq(SohuMcnProduct::getIsRecycle, Boolean.FALSE)
            .eq(SohuMcnProduct::getIsForced, Boolean.FALSE)
            .eq(SohuMcnProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_SUCCESS)
            .eq(SohuMcnProduct::getIsDel, Boolean.FALSE)
            .orderByAsc(SohuMcnProduct::getSort);
        return this.baseMapper.selectVoList(wrapper);
    }

//    /**
//     * 查询MCN会员套餐
//     */
//    @Override
//    public SohuMcnProductVo queryById(Long id){
//        return baseMapper.selectVoById(id);
//    }

    /**
     * 查询MCN会员套餐列表
     */
    @Override
    public TableDataInfo<SohuMcnProductVo> queryPageList(SohuMcnProductBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuMcnProduct> lqw = buildQueryWrapper(bo);
        Page<SohuMcnProductVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询MCN会员套餐列表
     */
    @Override
    public List<SohuMcnProductVo> queryList(SohuMcnProductBo bo) {
        LambdaQueryWrapper<SohuMcnProduct> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuMcnProduct> buildQueryWrapper(SohuMcnProductBo bo) {
        LambdaQueryWrapper<SohuMcnProduct> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), SohuMcnProduct::getProductName, bo.getProductName());
        lqw.eq(StringUtils.isNotBlank(bo.getProductInfo()), SohuMcnProduct::getProductInfo, bo.getProductInfo());
        lqw.eq(bo.getPrice() != null, SohuMcnProduct::getPrice, bo.getPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrency()), SohuMcnProduct::getCurrency, bo.getCurrency());
        lqw.eq(StringUtils.isNotBlank(bo.getMemberType()), SohuMcnProduct::getMemberType, bo.getMemberType());
        lqw.eq(StringUtils.isNotBlank(bo.getUseType()), SohuMcnProduct::getUseType, bo.getUseType());
        lqw.eq(bo.getUseDays() != null, SohuMcnProduct::getUseDays, bo.getUseDays());
        lqw.eq(bo.getGiveIntegral() != null, SohuMcnProduct::getGiveIntegral, bo.getGiveIntegral());
        lqw.eq(bo.getAccountNum() != null, SohuMcnProduct::getAccountNum, bo.getAccountNum());
        lqw.eq(bo.getSort() != null, SohuMcnProduct::getSort, bo.getSort());
        lqw.eq(bo.getIsRecycle() != null, SohuMcnProduct::getIsRecycle, bo.getIsRecycle());
        lqw.eq(StringUtils.isNotBlank(bo.getIsForced()), SohuMcnProduct::getIsForced, bo.getIsForced());
        lqw.eq(StringUtils.isNotBlank(bo.getAuditStatus()), SohuMcnProduct::getAuditStatus, bo.getAuditStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), SohuMcnProduct::getReason, bo.getReason());
        lqw.eq(bo.getIsShow() != null, SohuMcnProduct::getIsShow, bo.getIsShow());
        lqw.eq(StringUtils.isNotBlank(bo.getIsDel()), SohuMcnProduct::getIsDel, bo.getIsDel());
        return lqw;
    }

    /**
     * 新增MCN会员套餐
     */
    @Override
    public Boolean insertByBo(SohuMcnProductBo bo) {
        SohuMcnProduct add = BeanUtil.toBean(bo, SohuMcnProduct.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改MCN会员套餐
     */
    @Override
    public Boolean updateByBo(SohuMcnProductBo bo) {
        SohuMcnProduct update = BeanUtil.toBean(bo, SohuMcnProduct.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuMcnProduct entity){
        //TODO 做一些数据校验,如唯一约束
    }

//    /**
//     * 批量删除MCN会员套餐
//     */
//    @Override
//    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        if(isValid){
//            //TODO 做一些业务上的校验,判断是否需要校验
//        }
//        return baseMapper.deleteBatchIds(ids) > 0;
//    }
}
