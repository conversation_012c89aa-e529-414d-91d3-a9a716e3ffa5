package com.sohu.middleService.service.focus.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.RemoteAdminService;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuAdInfoBo;
import com.sohu.middle.api.bo.focus.SohuFocusArticleBo;
import com.sohu.middle.api.bo.focus.SohuFocusCategoryBo;
import com.sohu.middle.api.vo.SohuAdInfoVo;
import com.sohu.middle.api.vo.SohuVideoVo;
import com.sohu.middle.api.vo.focus.SohuFocusArticleVo;
import com.sohu.middle.api.vo.focus.SohuFocusCategoryVo;
import com.sohu.middle.api.vo.focus.SohuFocusSiteVo;
import com.sohu.middle.api.vo.focus.SysFocusOssVo;
import com.sohu.middleService.config.MiddleDynamicsProperties;
import com.sohu.middleService.domain.focus.SohuFocusArticle;
import com.sohu.middleService.domain.focus.SysFocusUser;
import com.sohu.middleService.mapper.focus.SohuFocusArticleMapper;
import com.sohu.middleService.service.ISohuAdInfoService;
import com.sohu.middleService.service.adInfo.impl.AdInfoBuilderFactory;
import com.sohu.middleService.service.focus.*;
import com.sohu.system.api.RemoteDictService;
import com.sohu.system.api.domain.SysDictData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 内容主体Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-03
 */
@RequiredArgsConstructor
@Service
@DS(value = "focus")
@Slf4j
public class SohuFocusArticleServiceImpl implements ISohuFocusArticleService {

    private final SohuFocusArticleMapper baseMapper;
    private final ISohuFocusSiteService sohuFocusSiteService;
    private final ISohuFocusCategoryService sohuFocusCategoryService;
    private final ISysFocusUserService sysFocusUserService;
    private final ISysFocusOssService sysFocusOssService;
    private final ISohuAdInfoService sohuAdInfoService;
    private final AdInfoBuilderFactory adInfoBuilderFactory;

    private final MiddleDynamicsProperties middleDynamicsProperties;
    @DubboReference
    private RemoteDictService remoteDictService;
    @DubboReference
    private RemoteAdminService remoteAdminService;

    /**
     * 查询内容主体列表
     */
    @Override
    @DS(value = "focus")
    public TableDataInfo<SohuFocusArticleVo> queryPageList(SohuFocusArticleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuFocusArticle> lqw = buildQueryWrapper(bo);
        Page<SohuFocusArticleVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuFocusArticleVo> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfoUtils.build();
        }

        // 数据收集部分保持不变
        Set<Long> siteIds = new HashSet<>();
        Set<Long> categoryIds = new HashSet<>();
        Set<Long> userIds = new HashSet<>();
        Set<Long> ossIds = new HashSet<>();
        for (SohuFocusArticleVo record : records) {
            Long siteId = record.getSiteId();
            Long category = record.getCategory();
            Long userId = record.getUserId();
            siteIds.add(siteId);
            categoryIds.add(category);
            userIds.add(userId);
            if (StrUtil.isNotBlank(record.getCoverImage()) && NumberUtil.isNumber(record.getCoverImage())) {
                ossIds.add(Long.valueOf(record.getCoverImage()));
            }
        }

        List<SohuFocusSiteVo> sohuSiteVos = sohuFocusSiteService.queryList(siteIds);
        List<SohuFocusCategoryVo> sohuCategoryVos = sohuFocusCategoryService.queryList(categoryIds);
        List<SysFocusUser> sysUsers = sysFocusUserService.queryList(userIds);
        List<SysFocusOssVo> sysOssVos = sysFocusOssService.queryList(ossIds);

        // 获取广告列表
        SohuAdInfoBo adInfoBo = new SohuAdInfoBo();
        adInfoBo.setAdPlace("APP-ZXWZ-XXL");
        adInfoBo.setState(CommonState.OnShelf.getCode());
        List<SohuAdInfoVo> infoVos = sohuAdInfoService.queryList(adInfoBo);
        infoVos.forEach(adInfoBuilderFactory::buildAdInfo);

        // 获取广告推荐间隔（从字典表）
        SysDictData adInsertMin = remoteDictService.getDictData("system_config", DictEnum.focus_ad_insert_interval_min.getKey());
        SysDictData adInsertMax = remoteDictService.getDictData("system_config", DictEnum.focus_ad_insert_interval_max.getKey());
        int adMinInterval = Integer.parseInt(Objects.nonNull(adInsertMin) && StrUtil.isNotBlank(adInsertMin.getDictValue()) ? adInsertMin.getDictValue() : "5"); // 默认5
        int adMaxInterval = Integer.parseInt(Objects.nonNull(adInsertMax) && StrUtil.isNotBlank(adInsertMax.getDictValue()) ? adInsertMax.getDictValue() : "10"); // 默认10

        // 获取视频推荐间隔（从字典表）
        SysDictData videoInsertMin = remoteDictService.getDictData("system_config", DictEnum.focus_video_insert_interval_min.getKey());
        SysDictData videoInsertMax = remoteDictService.getDictData("system_config", DictEnum.focus_video_insert_interval_max.getKey());
        int videoMinInterval = Integer.parseInt(Objects.nonNull(videoInsertMin) && StrUtil.isNotBlank(videoInsertMin.getDictValue()) ? videoInsertMin.getDictValue() : "5"); // 默认5
        int videoMaxInterval = Integer.parseInt(Objects.nonNull(videoInsertMax) && StrUtil.isNotBlank(videoInsertMax.getDictValue()) ? videoInsertMax.getDictValue() : "10"); // 默认10
        Random random = new Random();
        int videoInterval = random.nextInt(videoMaxInterval - videoMinInterval + 1) + videoMinInterval; // 随机生成5~10
        int adInterval = random.nextInt(adMaxInterval - adMinInterval + 1) + adMinInterval; // 随机生成5~10

        // 获取推荐视频列表（已保证不重复）
        PageQuery focusQuery = pageQuery;
        focusQuery.setPageSize(4);
        List<SohuVideoVo> videoVoList = remoteAdminService.queryVideoListByTopicId(bo.getIdent(), focusQuery);

        Map<Long, SohuFocusSiteVo> siteMap = sohuSiteVos.stream().collect(Collectors.toMap(SohuFocusSiteVo::getId, u -> u));
        Map<Long, SohuFocusCategoryVo> categoryMap = sohuCategoryVos.stream().collect(Collectors.toMap(SohuFocusCategoryVo::getId, u -> u));
        Map<Long, SysFocusUser> userMap = sysUsers.stream().collect(Collectors.toMap(SysFocusUser::getUserId, u -> u));
        Map<Long, SysFocusOssVo> ossMap = sysOssVos.stream().collect(Collectors.toMap(SysFocusOssVo::getOssId, u -> u));

        // 处理广告和视频插入逻辑
        List<SohuFocusArticleVo> finalRecords = new ArrayList<>();
        Integer pageSize = pageQuery.getPageSize(); // 每页大小，默认10
        Integer pageNum = pageQuery.getPageNum();   // 当前页码，从1开始

        int maxAdInsertions = infoVos.size(); // 最大广告插入次数

        for (int i = 0; i < records.size(); i++) {
            int localIndex = i + 1; // 当前页索引，从1开始
            finalRecords.add(records.get(i)); // 默认添加当前记录

            // 广告插入：当前页第adInterval条后
            if (localIndex == adInterval && CollUtil.isNotEmpty(infoVos)) {
                int adInsertCount = pageNum - 1; // 每页插入一次广告，按页码计数
                if (adInsertCount < maxAdInsertions) {
                    SohuFocusArticleVo adVo = convertAdToArticleVo(infoVos.get(adInsertCount));
                    finalRecords.add(adVo); // 添加广告
                }
            }

            // 视频插入：当前页第videoInterval条后，特定ident下
            if (StrUtil.equalsAnyIgnoreCase(bo.getIdent(), FocusArticleIdent.SINOLOGY_CULTURE.getCode(),
                    FocusArticleIdent.FOOD_HEALTH.getCode(), FocusArticleIdent.MEDICANT_HEALTH.getCode(),
                    FocusArticleIdent.WORK_HEALTH.getCode(), FocusArticleIdent.PROSPECT_HEALTH.getCode(),
                    FocusArticleIdent.HEART_HEALTH.getCode())) {
                if (localIndex == videoInterval && CollUtil.isNotEmpty(videoVoList)) {
                    log.info("videoInterval:{}", videoInterval);
                    SohuFocusArticleVo videoTemp = new SohuFocusArticleVo();
                    videoTemp.setType("video");
                    // 取前4条视频，或全部如果不足4条
                    int videoCount = Math.min(4, videoVoList.size());
                    List<SohuVideoVo> videoVoListFinal = videoVoList.subList(0, videoCount);
                    if (CollUtil.isNotEmpty(videoVoListFinal) && videoVoListFinal.size() >= 4) {
                        videoTemp.setVideoList(videoVoListFinal);
                        finalRecords.add(videoTemp); // 添加视频
                    }
                }
            }
        }

        // 字段填充（无论是否有广告或视频都执行）
        for (SohuFocusArticleVo record : finalRecords) {
            if (StrUtil.equalsAnyIgnoreCase(record.getType(), "ad", "video")) {
                continue;
            }
            if (StrUtil.isNotBlank(record.getCoverImage()) && NumberUtil.isNumber(record.getCoverImage())) {
                SysFocusOssVo sysOssVo = ossMap.get(Long.valueOf(record.getCoverImage()));
                record.setCoverImage(Objects.nonNull(sysOssVo) ? sysOssVo.getUrl() : "");
            }
            SohuFocusSiteVo sohuFocusSiteVo = siteMap.get(record.getSiteId());
            record.setSiteName(sohuFocusSiteVo.getChineseName());
            SohuFocusCategoryVo sohuCategoryVo = categoryMap.get(record.getCategory());
            if (ObjectUtil.isNotNull(sohuCategoryVo)) {
                record.setCategoryName(sohuCategoryVo.getTitle());
                if (sohuCategoryVo.getPid() == 0) {
                    record.setIdent(sohuCategoryVo.getIdent());
                } else {
                    SohuFocusCategoryVo vo = sohuFocusCategoryService.queryById(sohuCategoryVo.getPid());
                    record.setIdent(Objects.nonNull(vo) ? vo.getIdent() : null);
                }
            }
            SysFocusUser sysUser = userMap.get(record.getUserId());
            if (ObjectUtil.isNotNull(sysUser)) {
                record.setAuthorName(sysUser.getNickName());
                record.setAuthorAvatar(sysUser.getAvatar());
            }
            record.setStateName(FocusAuditState.map.get(record.getState()));
            record.setH5Url(middleDynamicsProperties.getFocusH5Url() + "/#/pages/detail/detail?id=" + record.getId() + "&ident=" + record.getIdent());
            record.setUpdateTimeFormat(DateUtil.format(record.getUpdateTime(), DatePattern.NORM_DATE_PATTERN));
        }

        result.setRecords(finalRecords);
        return TableDataInfoUtils.build(result);
    }

    @Override
    @DS(value = "focus")
    public List<SohuFocusArticleVo> touTiaoList(SohuFocusArticleBo bo) {
        LambdaQueryWrapper<SohuFocusArticle> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuFocusArticle::getState, FocusAuditState.OnShelf.getCode());
        if (StrUtil.isNotBlank(bo.getIdent())) {
            SohuFocusCategoryVo sohuCategory = sohuFocusCategoryService.selectByIdent(bo.getIdent());
            if (Objects.nonNull(sohuCategory)) {
                Long id = sohuCategory.getId();
                SohuFocusCategoryBo categoryBo = new SohuFocusCategoryBo();
                categoryBo.setPid(id);
                List<SohuFocusCategoryVo> categoryVoList = sohuFocusCategoryService.queryList(categoryBo);
                List<Long> catgenoryIds = new ArrayList<>();
                catgenoryIds.add(id);
                if (CollUtil.isNotEmpty(categoryVoList)) {
                    catgenoryIds.addAll(categoryVoList.stream().map(SohuFocusCategoryVo::getId).collect(Collectors.toList()));
                }
                List<Long> queryCategoryVol = new ArrayList<>();
                for (Long catgenoryId : catgenoryIds) {
                    if (catgenoryId == null || catgenoryId <= 0) {
                        continue;
                    }
                    queryCategoryVol.add(catgenoryId);
                }
                lqw.in(CollUtil.isNotEmpty(queryCategoryVol), SohuFocusArticle::getCategory, queryCategoryVol);
                if (CalUtils.isNullOrZero(sohuCategory.getPid())) {
                    lqw.eq(SohuFocusArticle::isColumnHeadlinesEnable, true);
                    lqw.last("ORDER BY column_headlines_sort ASC LIMIT 5 ");
                } else {
                    lqw.eq(SohuFocusArticle::isSubcolumnHeadlinesEnable, true);
                    lqw.last("ORDER BY subcolumn_headlines_sort ASC LIMIT 5 ");
                }
            }
        }
        List<SohuFocusArticleVo> list = baseMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        for (SohuFocusArticleVo record : list) {
            record.setH5Url(middleDynamicsProperties.getFocusH5Url() + "/#/pages/detail/detail?id=" + record.getId() + "&ident=" + record.getIdent());
        }
        return list;
    }

    /**
     * 将广告VO转换为文章VO
     */
    private SohuFocusArticleVo convertAdToArticleVo(SohuAdInfoVo adInfoVo) {
        SohuFocusArticleVo articleVo = new SohuFocusArticleVo();
        // 设置广告标识
        articleVo.setType("Ad");
        articleVo.setAdInfo(adInfoVo);
        return articleVo;
    }

    private LambdaQueryWrapper<SohuFocusArticle> buildQueryWrapper(SohuFocusArticleBo bo) {
        Map<String, Object> params = bo.getParams();
        String ident = bo.getIdent();
        LambdaQueryWrapper<SohuFocusArticle> lqw = Wrappers.lambdaQuery();
        lqw.like(StrUtil.isNotBlank(bo.getTitle()), SohuFocusArticle::getTitle, bo.getTitle());
        lqw.eq(StrUtil.isNotBlank(bo.getAbstracts()), SohuFocusArticle::getAbstracts, bo.getAbstracts());
        lqw.eq(bo.getOriginal() != null, SohuFocusArticle::getOriginal, bo.getOriginal());
        lqw.eq(StrUtil.isNotBlank(bo.getOriginUrl()), SohuFocusArticle::getOriginUrl, bo.getOriginUrl());
        lqw.eq(StrUtil.isNotBlank(bo.getCoverImage()), SohuFocusArticle::getCoverImage, bo.getCoverImage());
        lqw.eq(StrUtil.isNotBlank(bo.getImage()), SohuFocusArticle::getImage, bo.getImage());
        lqw.eq(StrUtil.isNotBlank(bo.getState()), SohuFocusArticle::getState, bo.getState());
        lqw.eq(StrUtil.isNotBlank(bo.getRejectReason()), SohuFocusArticle::getRejectReason, bo.getRejectReason());
        lqw.eq(StrUtil.isNotBlank(bo.getDisplayReason()), SohuFocusArticle::getDisplayReason, bo.getDisplayReason());
        lqw.eq(StrUtil.isNotBlank(bo.getType()), SohuFocusArticle::getType, bo.getType());
        lqw.eq(StrUtil.isNotBlank(bo.getVideoUrl()), SohuFocusArticle::getVideoUrl, bo.getVideoUrl());
        lqw.eq(bo.getRecomm() != null, SohuFocusArticle::getRecomm, bo.getRecomm());
        lqw.eq(StrUtil.isNotBlank(bo.getCreateIp()), SohuFocusArticle::getCreateIp, bo.getCreateIp());
        lqw.eq(StrUtil.isNotBlank(bo.getUpdateIp()), SohuFocusArticle::getUpdateIp, bo.getUpdateIp());
        //lqw.eq(bo.getSortIndex() != null, SohuFocusArticle::getSortIndex, bo.getSortIndex());
        lqw.eq(bo.getCategory() != null, SohuFocusArticle::getCategory, bo.getCategory());
        lqw.eq(StrUtil.isNotBlank(bo.getExtend()), SohuFocusArticle::getExtend, bo.getExtend());
        lqw.eq(bo.getSiteId() != null, SohuFocusArticle::getSiteId, bo.getSiteId());
        lqw.eq(StrUtil.isNotBlank(bo.getContent()), SohuFocusArticle::getContent, bo.getContent());
        lqw.eq(StrUtil.isNotBlank(bo.getCreateBy()), SohuFocusArticle::getCreateBy, bo.getCreateBy());
        lqw.eq(StrUtil.isNotBlank(bo.getUpdateBy()), SohuFocusArticle::getUpdateBy, bo.getUpdateBy());
        lqw.eq(StrUtil.isNotBlank(bo.getHomeRecommend()), SohuFocusArticle::getHomeRecommend, bo.getHomeRecommend());
        lqw.eq(SohuFocusArticle::isColumnHeadlinesEnable, false);
        lqw.eq(SohuFocusArticle::isSubcolumnHeadlinesEnable, false);
        if (StrUtil.isNotBlank(ident)) {
            SohuFocusCategoryVo sohuCategory = sohuFocusCategoryService.selectByIdent(ident);
            if (Objects.nonNull(sohuCategory)) {
                Long id = sohuCategory.getId();
                SohuFocusCategoryBo categoryBo = new SohuFocusCategoryBo();
                categoryBo.setPid(id);
                List<SohuFocusCategoryVo> categoryVoList = sohuFocusCategoryService.queryList(categoryBo);
                List<Long> catgenoryIds = new ArrayList<>();
                catgenoryIds.add(id);
                if (CollUtil.isNotEmpty(categoryVoList)) {
                    catgenoryIds.addAll(categoryVoList.stream().map(SohuFocusCategoryVo::getId).collect(Collectors.toList()));
                }
                List<Long> queryCategoryVol = new ArrayList<>();
                for (Long catgenoryId : catgenoryIds) {
                    if (catgenoryId == null || catgenoryId <= 0) {
                        continue;
                    }
                    queryCategoryVol.add(catgenoryId);
                }
                lqw.in(CollUtil.isNotEmpty(queryCategoryVol), SohuFocusArticle::getCategory, queryCategoryVol);
            }
        }
        if (bo.getHot() == 1) {
            lqw.orderByDesc(SohuFocusArticle::getUpdateTime, SohuFocusArticle::getViewCount);
        } else if (StrUtil.isNotBlank(bo.getTitle())) {
            lqw.orderByDesc(SohuFocusArticle::getViewCount);
        } else {
            if (StrUtil.equalsAnyIgnoreCase(bo.getSource(), "index")) {
                lqw.last("ORDER BY FIELD(home_recommend, '" + FocusHomeRecommendType.COLUMN_HEADLINES.getCode() + "', '" + FocusHomeRecommendType.COLUMN_RECOMMEND.getCode() + "', '" + FocusHomeRecommendType.ALL_HEADLINES.getCode() + "', '" + FocusHomeRecommendType.ALL_RECOMMEND.getCode() + "', '" + "none" + "'), update_time DESC ");
            } else {
                lqw.orderByDesc(SohuFocusArticle::getUpdateTime);
            }
        }

        if (bo.getIsCoverImage() != null && bo.getIsCoverImage() > 0L) {
            lqw.isNotNull(SohuFocusArticle::getCoverImage);
        }
        return lqw;
    }
}
