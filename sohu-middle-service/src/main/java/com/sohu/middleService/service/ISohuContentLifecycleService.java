package com.sohu.middleService.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuContentLifecycleBo;
import com.sohu.middle.api.vo.SohuContentLifecycleVo;

import java.util.Collection;
import java.util.List;

/**
 * 内容作品生命周期Service接口
 *
 * <AUTHOR>
 * @date 2024-11-29
 */
public interface ISohuContentLifecycleService {

    /**
     * 查询内容作品生命周期
     */
    SohuContentLifecycleVo queryById(Long id);

    /**
     * 查询内容作品生命周期列表
     */
    TableDataInfo<SohuContentLifecycleVo> queryPageList(SohuContentLifecycleBo bo, PageQuery pageQuery);

    /**
     * 查询内容作品生命周期列表
     */
    List<SohuContentLifecycleVo> queryList(SohuContentLifecycleBo bo);

    /**
     * 修改内容作品生命周期
     */
    Boolean insertByBo(SohuContentLifecycleBo bo);

    /**
     * 修改内容作品生命周期
     */
    Boolean updateByBo(SohuContentLifecycleBo bo);

    /**
     * 校验并批量删除内容作品生命周期信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取最新的一条数据
     * @param busyCode
     * @param busyType {@link com.sohu.common.core.enums.BusyType}
     * @return
     */
    SohuContentLifecycleVo selectOfLast(Long busyCode,String busyType);

}
