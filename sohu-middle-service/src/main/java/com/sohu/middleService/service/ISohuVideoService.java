package com.sohu.middleService.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sohu.middle.api.bo.SohuTopicContentQueryBo;
import com.sohu.common.core.domain.BaseCommonBo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.bo.mcn.SohuMcnVideoReqBo;
import com.sohu.middle.api.bo.playlet.PlayletAdsQueryBo;
import com.sohu.middle.api.vo.SohuConentListStatVo;
import com.sohu.middle.api.vo.SohuTopVideoVo;
import com.sohu.middle.api.vo.SohuVideoVo;
import com.sohu.middle.api.vo.playlet.PlayletListVo;
import com.sohu.middle.api.vo.playlet.SohuPlayetEpisodeListVo;
import com.sohu.middleService.domain.SohuVideo;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * 视频Service接口
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
public interface ISohuVideoService extends ISohuBaseService<SohuVideo, SohuVideoVo> {

    /**
     * 获取对象的作者id
     *
     * @param id 对象主键id
     * @return 返回对象的作者id，不存在就返回0
     */
    Long getAuthorId(Long id);

    /**
     * 查询视频
     */
    SohuVideoVo get(Long id);

    /**
     * 查询视频
     */
    @Override
    SohuVideoVo queryById(Long id);

    /**
     * x
     * 查询视频主体
     */
    SohuVideoVo selectVoById(Long id);

    /**
     * 视频查看记录
     */
    Boolean videoView(Long id);

    /**
     * 查询视频列表
     */
    TableDataInfo<SohuVideoVo> queryPageList(SohuVideoBo bo, PageQuery pageQuery);

    /**
     * 查询视频列表-统计
     */
    SohuConentListStatVo queryPageListStat(SohuVideoBo bo);

    /**
     * 查询视频列表
     */
    TableDataInfo<SohuVideoVo> queryPageListOfOnShelf(SohuVideoBo bo, PageQuery pageQuery);

    /**
     * 查询视频列表
     */
    List<SohuVideoVo> queryList(SohuVideoBo bo);

    /**
     * 新增视频
     */
    Boolean insertByBo(SohuVideoBo bo);

    /**
     * 修改视频
     */
    Boolean updateByBo(SohuVideoBo bo);

    /**
     * 短剧剧集列表
     *
     * @param episodeRelevance
     * @return
     */
    List<SohuVideoVo> listByEp(String episodeRelevance, BaseCommonBo commonBo);

//    /**
//     * 校验并批量删除视频信息
//     */
//    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 视频草稿提交至审核
     *
     * @param videoId 视频ID
     */
    @Deprecated
    void commit(Long videoId);

    /**
     * 视频列表（新）
     */
    TableDataInfo<SohuVideoVo> queryPage(SohuVideoBo bo, PageQuery pageQuery);

    /**
     * 视频列表（新）-智能推荐
     */
    TableDataInfo<SohuVideoVo> queryPageOfAirec(SohuVideoBo bo, PageQuery pageQuery);

    /**
     * 个人中心视频
     */
    TableDataInfo<SohuVideoVo> videoPageCenter(Long userId, PageQuery pageQuery);

    /**
     * 关注视频列表
     */
    TableDataInfo<SohuVideoVo> followPage(PageQuery pageQuery);

    /**
     * MCN视频分页查询
     */
    TableDataInfo<SohuVideoVo> queryMCNVideoList(SohuMcnVideoReqBo bo, PageQuery pageQuery);

    /**
     * MCN视频逻辑删除
     */
    Boolean logicDeleteById(Long id);

    /**
     * 逻辑删除-自主删除-批量
     *
     * @param ids
     * @return
     */
    boolean logicDeleteById(Collection<Long> ids);

    /**
     * 图文逻辑删除-强制删除
     *
     * @param id
     * @return
     */
    boolean logicForceDeleteById(Long id);

    /**
     * 图文逻辑删除-强制删除-批量
     *
     * @param ids
     * @return
     */
    boolean logicForceDeleteById(Collection<Long> ids);

    /**
     * 获取MCN视频点赞总数
     */
    Long getMcnVideoPraiseStat(Long userId, Long mcnId);

    /**
     * 获取MCN视频评论总数
     */
    Long getMcnVideoCommentStat(Long userId, Long mcnId);

    /**
     * 获取MCN视频浏览量
     */
    Long getMcnVideoViewStat(Long userId, Long mcnId);

    /**
     * 赚钱视频列表
     *
     * @param bo
     * @return {@link List}
     */
    IPage<SohuVideoVo> businessVideoList(SohuBusinessVideoBo bo);

    /**
     * 赚钱视频列表-智能推荐
     *
     * @param bo
     * @return {@link List}
     */
    TableDataInfo<SohuVideoVo> businessVideoListOfAirec(SohuBusinessVideoBo bo);

    /**
     * 智能推荐视频物料
     */
    void updateSohuAirecContentVideoItem(SohuVideo sohuVideo);

    /**
     * 短剧集数列表
     *
     * @param bo 短剧唯一标识，如DJ909170625936838982559
     * @return {@link List}
     */
    List<SohuPlayetEpisodeListVo> listEpisodes(PlayletAdsQueryBo bo);

//    /**
//     * 推荐视频列表
//     *
//     * @return
//     */
//    TableDataInfo<SohuVideoVo> getSohuAirecContentVideo(SohuVideoBo sohuVideoBo);

    /**
     * 初始化智能推荐物料
     */
    Boolean initAirecContentItems();

    /**
     * 短剧首页视频列表
     *
     * @param bo
     * @return {@link TableDataInfo}
     */
    TableDataInfo<SohuVideoVo> playletVideoList(SohuPlayletVideoBo bo);

    /**
     * 短剧首页视频列表-智能推荐
     *
     * @param bo
     * @return {@link TableDataInfo}
     */
    TableDataInfo<SohuVideoVo> playletVideoListOfAirec(SohuPlayletVideoBo bo);

//    /**
//     * 推荐短剧列表
//     */
//    TableDataInfo<SohuVideoVo> getSohuAirecContentShortVideo(SohuPlayletVideoBo bo, Long userId);


    /**
     * 返回每个标签下前五的视频
     * 双层数组结构返回
     */
    List<SohuTopVideoVo> labelTopFive();


    /**
     * 投流视频
     */
    TableDataInfo<SohuVideoVo> videoPageCenterByType(SohuVideoBo bo, PageQuery pageQuery);

    /**
     * 草稿重发
     *
     * @param busyBO
     * @return {@link Boolean}
     */
    @Deprecated
    Boolean draftRetry(SohuBusyBO busyBO);

    /**
     * 转发视频
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean forward(SohuBusyBO bo);

    /**
     * 评论视频
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean comment(SohuCommentBo bo, Boolean commentCountAdd);

    /**
     * 点赞视频
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean like(SohuBusyBO bo);

    /**
     * 收藏视频
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean collect(SohuBusyBO bo);

    /**
     * 修改用户所有视频的状态，删除的话是假删除
     *
     * @param userId 用户ID
     * @param state  {@link com.sohu.common.core.enums.CommonState}
     * @return {@link Boolean}
     */
    @Deprecated
    Boolean updateUserVideoState(Long userId, String state);

    /**
     * 修改视频状态
     *
     * @param bo
     * @return {@link Boolean}
     */
    @Deprecated
    Boolean updateState(SohuBusyUpdateStateBo bo);

    /**
     * 站点内容数
     */
    Long queryVideoNumBySite(Long siteId, Long userId);

    /**
     * 转发短剧
     *
     * @param playletId 短剧id
     * @return {@link Boolean}
     */
    CompletableFuture<Boolean> playletForward(Long playletId);

    /**
     * 整部短剧的所有集
     *
     * @param episodeRelevance 短剧唯一标识
     * @param commonBo
     * @return List<PlayletListVo>
     */
    List<PlayletListVo> getPlayletList(String episodeRelevance, BaseCommonBo commonBo);

    /**
     * 返回map
     *
     * @param ids 主键集合
     * @return {@link Map}
     */
    Map<Long, SohuVideoVo> map(Set<Long> ids);

    /**
     * 批量修改作品状态
     *
     * @param bo SohuContentBatchBo
     * @return Boolean
     */
    @Deprecated
    Boolean updateBatchContentState(SohuContentBatchBo bo);

    /**
     * 基于时间统计视频数量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    Long countByTime(String startTime, String endTime);

    /**
     * 提交草稿至审核
     * @param id
     * @return
     */
    Boolean submitAudit(Long id);

    /**
     * 图文下架-自主下架
     *
     * @param id
     * @return
     */
    boolean updateOffShelfById(Long id);

    /**
     * 图文下架-强制下架
     *
     * @param bo
     * @return
     */
    boolean updateCompelOffById(SohuContentRefuseBo bo);

    /**
     * 从回收站恢复数据
     * @param id
     * @return
     */
    Boolean recoveryData(Long id);

    /**
     * 审核上架
     * @param id
     * @return
     */
    Boolean auditOnShelf(Long id);

    /**
     * 审核拒绝
     * @param id
     * @param rejectReason
     * @return
     */
    Boolean auditRefuse(Long id,String rejectReason);

    /**
     * 用户申述
     * @return
     */
    Boolean userAppeal(SohuUserContentAppealBo bo);

    /**
     * 隐藏数据
     * @param id
     * @return
     */
    Boolean hideData(Long id);

    /**
     * 更新图文只能推荐物料
     *
     * @param video
     */
    void updateSohuAirecContentArticleItem(SohuVideo video);

    /**
     * 从回收站删除数据
     * @param id
     * @return
     */
    Boolean deleteDataById(Long id);

    /**
     * 清空回收站数据-过期
     * @return
     */
    Boolean clearRecycleDataOfTimeOut();

    /**
     * 隐藏数据
     * @param ids
     * @return
     */
    Boolean hideDataBatch(Collection<Long> ids);

    /**
     * 查询视频
     */
    SohuVideoVo queryById(Long id, Boolean isIndependent, Long userId);

    /**
     * 通过id与状态查询视频
     * @param ids
     * @param state
     * @return
     */
    List<SohuVideoVo> queryVideoByIds(List<Long> ids,String state);

    /**
     * 构建视频返回参数
     *
     * @param records
     */
    void buildVideoVo(List<SohuVideoVo> records);

    /**
     * 查询专题内容列表
     */
    TableDataInfo<SohuVideoVo> getTopicList(SohuTopicContentQueryBo bo, PageQuery pageQuery);

    /**
     * 更改视频的宽高比例
     *
     * @param videoId
     * @param ratioEnum
     */
    void updateRation(Long videoId, Integer ratioEnum);

    /**
     * 处理机审结果
     *
     * @param busyCode
     * @param isPass
     * @param reason
     */
    void handleRobot(String busyCode, Boolean isPass, String reason);
}
