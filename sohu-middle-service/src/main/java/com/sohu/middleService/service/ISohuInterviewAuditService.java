package com.sohu.middleService.service;


import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuInterviewAuditBo;
import com.sohu.middle.api.vo.SohuInterviewAuditVo;

import java.util.Collection;
import java.util.List;

/**
 * 外部链接访问审核Service接口
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
public interface ISohuInterviewAuditService {

    /**
     * 查询外部链接访问审核
     */
    SohuInterviewAuditVo queryById(Long id);

    /**
     * 查询外部链接访问审核列表(分页)
     */
    TableDataInfo<SohuInterviewAuditVo> queryPageList(SohuInterviewAuditBo bo, PageQuery pageQuery);

    /**
     * 查询外部链接访问审核列表
     */
    List<SohuInterviewAuditVo> queryList(SohuInterviewAuditBo bo);

    /**
     * 新增外部链接访问审核
     */
    Boolean insertByBo(SohuInterviewAuditBo bo);

    /**
     * 修改外部链接访问审核
     */
    Boolean audit(SohuInterviewAuditBo bo);

    /**
     * 校验并批量删除外部链接访问审核信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询外部链接访问审核详情（通过url）
     */
    SohuInterviewAuditVo queryByUrl(String url);
}
