package com.sohu.middleService.service.im.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.im.*;
import com.sohu.middle.api.enums.ReportBizTypeEnum;
import com.sohu.middle.api.vo.im.SohuImReportHssVo;
import com.sohu.middle.api.vo.im.SohuImReportVo;
import com.sohu.middleService.domain.im.SohuImReport;
import com.sohu.middleService.domain.im.SohuImReportHss;
import com.sohu.middleService.mapper.im.SohuImReportHssMapper;
import com.sohu.middleService.mapper.im.SohuImReportMapper;
import com.sohu.middleService.service.im.ISohuImReportHssService;
import com.sohu.middleService.service.im.ISohuImReportService;
import com.sohu.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * IM举报信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-23
 */
@RequiredArgsConstructor
@Service
public class SohuImReportHssServiceImpl implements ISohuImReportHssService {

    private final SohuImReportHssMapper baseMapper;

    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 查询IM举报信息
     */
    @Override
    public SohuImReportHssVo queryById(Long id) {
        SohuImReportHssVo vo = baseMapper.selectVoById(id);
        this.buildModel(vo);
        return vo;
    }

    /**
     * 查询IM举报信息列表
     */
    @Override
    public TableDataInfo<SohuImReportHssVo> queryPageList(SohuImReportHssBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuImReportHss> lqw = buildQueryWrapper(bo);
        Page<SohuImReportHssVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    @Override
    public TableDataInfo<SohuImReportHssVo> queryPageListOfAudit(SohuImReportHssQueryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuImReportHss> lqw = new LambdaQueryWrapper<>();
        lqw.like(bo.getId() != null, SohuImReportHss::getId, bo.getId());
        lqw.eq(StrUtil.isNotBlank(bo.getReportType()), SohuImReportHss::getReportType, bo.getReportType());
        lqw.like(bo.getReportUserId() != null, SohuImReportHss::getReportUserId, bo.getReportUserId());
        lqw.like(StrUtil.isNotBlank(bo.getReportNickname()), SohuImReportHss::getReportNickname, bo.getReportNickname());
        lqw.like(StrUtil.isNotBlank(bo.getReportUserName()), SohuImReportHss::getReportUserName, bo.getReportUserName());
        lqw.like(bo.getObjId() != null, SohuImReportHss::getObjId, bo.getObjId());
        lqw.eq(bo.getObjType() != null, SohuImReportHss::getObjType, bo.getObjType());
        lqw.like(StrUtil.isNotBlank(bo.getObjCode()), SohuImReportHss::getObjCode, bo.getObjCode());
        lqw.like(StrUtil.isNotBlank(bo.getTitle()), SohuImReportHss::getTitle, bo.getTitle());
        lqw.eq(StrUtil.isNotBlank(bo.getState()), SohuImReportHss::getState, bo.getState());
        lqw.ge(bo.getCreateStartTime() != null, SohuImReportHss::getCreateTime, bo.getCreateStartTime());
        lqw.le(bo.getCreateEndTime() != null, SohuImReportHss::getCreateTime, bo.getCreateEndTime());
        lqw.ge(bo.getAuditStartTime() != null, SohuImReportHss::getAuditTime, bo.getAuditStartTime());
        lqw.le(bo.getAuditEndTime() != null, SohuImReportHss::getAuditTime, bo.getAuditEndTime());
        lqw.orderByDesc(SohuImReportHss::getCreateTime);
        Page<SohuImReportHssVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询IM举报信息列表
     */
    @Override
    public List<SohuImReportHssVo> queryList(SohuImReportHssBo bo) {
        LambdaQueryWrapper<SohuImReportHss> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuImReportHss> buildQueryWrapper(SohuImReportHssBo bo) {
        LambdaQueryWrapper<SohuImReportHss> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getReportUserId() != null, SohuImReportHss::getReportUserId, bo.getReportUserId());
        lqw.like(StringUtils.isNotBlank(bo.getReportNickname()), SohuImReportHss::getReportNickname, bo.getReportNickname());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuImReportHss::getState, bo.getState());
        lqw.eq(StringUtils.isNotBlank(bo.getReportType()), SohuImReportHss::getReportType, bo.getReportType());
        lqw.eq(StringUtils.isNotBlank(bo.getReportInformation()), SohuImReportHss::getReportInformation, bo.getReportInformation());
        lqw.eq(bo.getObjId() != null, SohuImReportHss::getObjId, bo.getObjId());
        lqw.eq(StringUtils.isNotBlank(bo.getObjType()), SohuImReportHss::getObjType, bo.getObjType());
        lqw.like(StringUtils.isNotBlank(bo.getObjName()), SohuImReportHss::getObjName, bo.getObjName());
        lqw.eq(StringUtils.isNotBlank(bo.getImageUrl()), SohuImReportHss::getImageUrl, bo.getImageUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getRejectReason()), SohuImReportHss::getRejectReason, bo.getRejectReason());
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), SohuImReportHss::getTitle, bo.getTitle());
        lqw.eq(bo.getAuthorId() != null, SohuImReportHss::getAuthorId, bo.getAuthorId());
        lqw.eq(bo.getAuditUserId() != null, SohuImReportHss::getAuditUserId, bo.getAuditUserId());
        lqw.eq(bo.getAuditTime() != null, SohuImReportHss::getAuditTime, bo.getAuditTime());
        lqw.eq(StringUtils.isNotBlank(bo.getChatEvidence()), SohuImReportHss::getChatEvidence, bo.getChatEvidence());
        lqw.eq(bo.getId() != null, SohuImReportHss::getId, bo.getId());
        lqw.eq(bo.getCreateTime() != null, SohuImReportHss::getCreateTime, bo.getCreateTime());
        return lqw;
    }

    /**
     * 新增IM举报信息
     */
    @Override
    public Boolean insertByBo(SohuImReportHssBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        bo.setReportUserId(loginUser.getUserId());
        bo.setReportNickname(loginUser.getNickname());
        bo.setReportUserName(loginUser.getUsername());
        bo.setState(CommonState.WaitApprove.getCode());
        bo.setCreateTime(new Date());
        //同一个作品不可重复举报
        if (ReportBizTypeEnum.SINGLE_CHAT.getCode().equals(bo.getObjType())) {
            LoginUser userVo = remoteUserService.queryById(bo.getObjId());
            if (Objects.nonNull(userVo)) {
                bo.setObjCode(userVo.getUsername());
            }
        } else {
            bo.setObjCode(bo.getObjId().toString());
        }
        this.buildJson(bo);
        SohuImReportHss add = BeanUtil.toBean(bo, SohuImReportHss.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改IM举报信息
     */
    @Override
    public Boolean updateByBo(SohuImReportHssBo bo) {
        this.buildJson(bo);
        SohuImReportHss update = BeanUtil.toBean(bo, SohuImReportHss.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuImReportHss entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除IM举报信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean addReport(SohuImReportAppBo bo) {
        SohuImReportHssBo imReportBo = new SohuImReportHssBo();
        imReportBo.setReportInformation(bo.getReportInformation());
        imReportBo.setReportType(bo.getReportType());
        imReportBo.setImageUrl(bo.getImageUrl());
        imReportBo.setObjId(bo.getObjId());
        imReportBo.setObjType(bo.getObjType());
        imReportBo.setChatEvidenceModel(bo.getChatEvidenceModel());
        imReportBo.setTitle(bo.getTitle());
        this.insertByBo(imReportBo);
        return true;
    }

    @Override
    public Boolean audit(SohuImReportAuditBo bo) {
        SohuImReportHss sohuImReport = this.baseMapper.selectById(bo.getId());
        if (Objects.isNull(sohuImReport)) {
            throw new RuntimeException("数据不存在");
        }
        if (Objects.equals(CommonState.FINISH.getCode(), sohuImReport.getState())) {
            throw new RuntimeException("已处理，请勿重复操作");
        }
        SohuImReportHss updateEntity = new SohuImReportHss();
        updateEntity.setId(bo.getId());
        updateEntity.setAuditUrl(bo.getAuditUrl());
        updateEntity.setRejectReason(bo.getRejectReason());
        updateEntity.setAuditTime(new Date());
        updateEntity.setAuditUserId(LoginHelper.getUserId());
        updateEntity.setState(CommonState.FINISH.getCode());
        this.baseMapper.updateById(updateEntity);
        return true;
    }

    private void buildJson(SohuImReportHssBo bo) {
        if (CollUtil.isNotEmpty(bo.getChatEvidenceModel())) {
            bo.setChatEvidence(JSONUtil.toJsonStr(bo.getChatEvidenceModel()));
        }
    }

    private void buildModel(SohuImReportHssVo vo) {
        if (Objects.isNull(vo)) {
            return;
        }
        if (StrUtil.isNotBlank(vo.getChatEvidence())) {
            vo.setChatEvidenceModel(JSONUtil.toList(vo.getChatEvidence(), ImChatReportEvidenceModel.class));
        }
    }
}
