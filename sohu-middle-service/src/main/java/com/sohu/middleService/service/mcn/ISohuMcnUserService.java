package com.sohu.middleService.service.mcn;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.mcn.SohuMcnUserBo;
import com.sohu.middle.api.vo.mcn.SohuMcnUserImportVo;
import com.sohu.middle.api.vo.mcn.SohuMcnUserVo;
import com.sohu.middleService.domain.mcn.SohuMcnUser;
import com.sohu.middleService.service.ISohuBaseService;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * MCN机构成员
 */
public interface ISohuMcnUserService extends ISohuBaseService<SohuMcnUser,SohuMcnUserVo> {

    /**
     * 查询列表
     */
    TableDataInfo<SohuMcnUserVo> queryPageList(SohuMcnUserBo bo, PageQuery pageQuery);

    /**
     * 获取当前MNC机构所有已加入成员
     *
     * @return
     */
    List<SohuMcnUserVo> getListOfSelf();

    /**
     * 获取当前MNC机构所有已加入正常状态成员-包含分组信息
     *
     * @return
     */
    List<SohuMcnUserVo> getListOfNormal();

    /**
     * 新增
     *
     * @param bo
     * @return
     */
    Boolean insertByBo(SohuMcnUserBo bo);

    /**
     * 修改
     *
     * @param bo
     * @return
     */
    Boolean updateByBo(SohuMcnUserBo bo);

    /**
     * 查询详情-自己的数据
     *
     * @param id
     * @return
     */
    SohuMcnUserVo getInfo(Long id);

    /**
     * 获取邀请信息-含MCN机构信息
     *
     * @param id
     * @return
     */
    SohuMcnUserVo getInfoOfMcn(Long id);

    /**
     * 查询MCN机构成员-含MCN机构信息
     *
     * @param userId
     * @return
     */
    SohuMcnUserVo getInfoByUserId(Long userId);

//    /**
//     * 删除
//     *
//     * @param ids
//     * @param isValid
//     * @return
//     */
//    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 刷新邀请链接时效
     *
     * @param id
     */
    void updateInviteDate(Long id);

    /**
     * 更新超时状态
     */
    void updataStateOfTimeout();

    /**
     * 发送手机短信邀请
     *
     * @param entity
     */
    void sendInviteOfPhone(SohuMcnUser entity);

    /**
     * 发送邮件邀请
     *
     * @param entity
     */
    void sendInviteOfEmail(SohuMcnUser entity);

    /**
     * 绑定MCN机构成员
     */
    @Deprecated
    void bindMcnUser(Long id);

    /**
     * 绑定MCN机构成员
     */
    void bindMcnUser(Long id,Long userId);

//    /**
//     * MCN达人带货列表
//     *
//     * @param bo
//     * @param pageQuery
//     * @return
//     */
//    TableDataInfo<McnUserShopProductVo> queryShopProductPageList(McnUserShopProductReqBo bo, PageQuery pageQuery);

    /**
     * 获取当前MNC机构所有成员昨日新增粉丝数
     *
     * @return
     */
    Long getMcnFansCount();

    /**
     * 获取当前MNC机构所有成员粉丝总数
     *
     * @return
     */
    Long getMcnFansStat();

    /**
     * 获取mcn机构总浏览量
     *
     * @return
     */
    Long getMcnViewCount();

    /**
     * 获取MCN机构所有成员点赞数(/视频/图文/问答)
     */
    Long getMcnPraiseCount();

    /**
     * 获取MCN机构所有成员评论数(视频/图文/问答)
     */
    Long getMcnCommentCount();

    /**
     * 获取MCN机构所有成员互动率
     */
    BigDecimal getMcnInteractionRate();

    /**
     * 通过mcnId获取当前MNC机构所有成员
     */
    List<SohuMcnUserVo> getListByMcnId(Long mcnId);

    /**
     * 获取达人粉丝数
     */
    Long getFansCount(Long userId);

    /**
     * 获取机构达人数
     * @return
     */
    Long getMcnUserCount();

    /**
     * 批量添加达人（导入excel表）
     *
     * @return
     */
    SohuMcnUserImportVo importData(MultipartFile file);

    /**
     * 批量绑定合同
     */
    Boolean updateContracts(List<SohuMcnUserBo> mcnUsers);


    /**
     * 通过登录查询mcn信息
     */
    SohuMcnUserVo findMcnIdByLoginId(List<String> states);

    /**
     * 根据id集合查询mcn机构
     *
     * @return
     */
    List<SohuMcnUserVo> selectBatchIds(List<String> ids);
}
