package com.sohu.middleService.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuProjectInfoBo;
import com.sohu.middle.api.vo.SohuProjectInfoVo;
import com.sohu.middleService.domain.SohuProjectInfo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 项目记录Service接口
 *
 * <AUTHOR>
 * @date 2023-06-29
 */
public interface ISohuProjectInfoService extends ISohuBaseService<SohuProjectInfo,SohuProjectInfoVo>{

//    /**
//     * 查询项目记录
//     */
//    SohuProjectInfoVo queryById(Long id);
//
    /**
     * 查询项目记录列表
     */
    TableDataInfo<SohuProjectInfoVo> queryPageList(SohuProjectInfoBo bo, PageQuery pageQuery);

    /**
     * 查询项目记录列表
     */
    List<SohuProjectInfoVo> queryList(SohuProjectInfoBo bo);

    /**
     * 修改项目记录
     */
    Boolean insertByBo(SohuProjectInfoBo bo);

    /**
     * 修改项目记录
     */
    Boolean updateByBo(SohuProjectInfoBo bo);

    /**
     * 根据项目ID查询数据
     *
     * @param projectId 项目ID
     * @return
     */
    SohuProjectInfoVo queryByProjectId(Long projectId);

//    /**
//     * 校验并批量删除项目记录信息
//     */
//    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Map<Long, SohuProjectInfo> queryMap(Set<Long> projectIds);

    void deleteByProjectId(Long id);
}
