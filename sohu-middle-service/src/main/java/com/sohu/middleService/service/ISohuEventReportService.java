package com.sohu.middleService.service;


import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuEventReportBo;
import com.sohu.middle.api.bo.SohuEventReportListBo;
import com.sohu.middle.api.vo.AdReportVo;
import com.sohu.middle.api.vo.SohuEventReportVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 事件统计Service接口
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
public interface ISohuEventReportService {

    /**
     * 获取统计数据
     *
     * @param reportBo
     * @return
     */
    String getEventId(SohuEventReportBo reportBo);

    /**
     * 新增时间
     *
     * @param eventId
     * @param time
     * @return
     */
    Boolean addStayDuration(String eventId, Long time);

    /**
     * 查询事件统计列表-分页
     * @param bo SohuEventReportBo
     * @param pageQuery PageQuery
     * @return TableDataInfo<SohuEventReportVo>
     */
    TableDataInfo<SohuEventReportVo> queryPageList(SohuEventReportListBo bo, PageQuery pageQuery);

    /**
     * 查询事件统计列表--不分页
     *
     * @param bo SohuEventReportBo
     * @return List<SohuEventReportVo>
     */
    List<SohuEventReportVo> queryList(SohuEventReportListBo bo);

    /**
     * 新增用户事件
     *
     * @param userId
     * @return
     */
    String addUserEvent(Long userId);

    /**
     * 统计
     *
     * @param busyType
     * @param startTime
     * @param endTime
     * @return
     */
    Long getNumByTypeAndTime(String busyType, Date startTime, Date endTime);

    /**
     * 广告事件统计
     * @param busyCodeList
     * @return
     */
    List<AdReportVo> selectAdReportList(Collection<String> busyCodeList);

    /**
     * 广告事件统计
     * @param busyCodeList
     * @return
     */
    Map<String,AdReportVo> selectAdReportMap(Collection<String> busyCodeList);
}
