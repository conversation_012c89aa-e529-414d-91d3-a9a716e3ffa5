package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.NoticeInteractEnum;
import com.sohu.common.core.enums.RoleCodeEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.StreamUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuBusyBO;
import com.sohu.middle.api.bo.SohuCommentBo;
import com.sohu.middle.api.bo.SohuEventReportBo;
import com.sohu.middle.api.bo.SohuUserLabelRelationBo;
import com.sohu.middle.api.bo.notice.SohuInteractNoticeBo;
import com.sohu.middle.api.enums.LabelEnum;
import com.sohu.middle.api.enums.report.ActionTypeEnum;
import com.sohu.middle.api.enums.report.RecreationReportEnum;
import com.sohu.middle.api.service.RemoteMiddleCommonLabelService;
import com.sohu.middle.api.service.RemoteMiddleEventReportService;
import com.sohu.middle.api.vo.*;
import com.sohu.middleService.domain.*;
import com.sohu.middleService.mapper.SohuArticleInfoMapper;
import com.sohu.middleService.mapper.SohuCommentMapper;
import com.sohu.middleService.mapper.SohuQoraInfoMapper;
import com.sohu.middleService.mapper.SohuVideoInfoMapper;
import com.sohu.middleService.service.*;
import com.sohu.middleService.strategy.MiddleProcessor;
import com.sohu.third.aliyun.audit.constants.AliyunAuditLabelEnum;
import com.sohu.third.aliyun.audit.service.AliyunAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 评论记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuCommentServiceImpl extends SohuBaseServiceImpl<SohuCommentMapper, SohuComment, SohuCommentVo> implements ISohuCommentService {


    private final SohuQoraInfoMapper sohuQoraInfoMapper;
    private final SohuArticleInfoMapper sohuArticleInfoMapper;
    private final SohuVideoInfoMapper sohuVideoInfoMapper;

    private final ISohuUserService sohuUserService;
    private final ISohuUserLikeService sohuUserLikeService;
    private final ISohuUserSiteRelationService sohuUserSiteRelationService;
    private final ISohuArticleService sohuArticleService;
    private final ISohuVideoService sohuVideoService;
    private final ISohuContentMainService sohuContentMainService;
    private final ISohuInteractNoticeService sohuInteractNoticeService;
    @DubboReference
    private RemoteMiddleEventReportService remoteMiddleEventReportService;
    @DubboReference
    private RemoteMiddleCommonLabelService remoteMiddleCommonLabelService;
    @Resource
    private AliyunAuditService aliyunAuditService;

    @Override
    public Long getAuthorId(Long id) {
        if (CalUtils.isNullOrZero(id)) {
            return 0L;
        }
        SohuCommentVo commentVo = this.get(id);
        return Objects.isNull(commentVo) ? 0L : commentVo.getCommentUser();
    }

    /**
     * 查询评论记录
     */
    @Override
    public SohuCommentVo get(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询评论记录
     */
    @Override
    public SohuCommentVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询评论记录列表
     */
    @Override
    public TableDataInfo<SohuCommentVo> queryPageList(SohuCommentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuComment> lqw = buildQueryWrapper(bo);
        Page<SohuCommentVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuCommentVo> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfoUtils.build();
        }
        Set<Long> userIds = new HashSet<>();
        List<Long> commentIds = new ArrayList<>();
        for (SohuCommentVo commentVo : records) {
            userIds.add(commentVo.getCommentUser());
            userIds.add(commentVo.getReplyUser());
            commentIds.add(commentVo.getId());
        }
        Long loginId = LoginHelper.getUserId();
        Map<Long, SohuUserLikeVo> likeVoMap = new HashMap<>();
        if (loginId != null && loginId > 0L) {
            likeVoMap = sohuUserLikeService.queryMap(loginId, BusyType.Comment.name(), commentIds);
        }
        Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
        Map<String, String> coverImageMap = coverImageMap(records);

        for (SohuCommentVo commentVo : records) {
            LoginUser loginUser = userMap.get(commentVo.getCommentUser());
            if (Objects.isNull(loginUser)) {
                continue;
            }
            commentVo.setCommentUserName(loginUser.getNickname());
            commentVo.setCommentUserAvatar(loginUser.getAvatar());
            commentVo.setPraiseObj(likeVoMap.get(commentVo.getId()) != null);

            //设置封面图
            commentVo.setCoverImage(coverImageMap.get(commentVo.getBusyType() + StrPool.DASHED + commentVo.getBusyCode()));

            if (commentVo.getReplyUser() != null && commentVo.getReplyUser() > 0L) {
                LoginUser replyUser = userMap.get(commentVo.getReplyUser());
                if (Objects.isNull(replyUser)) {
                    continue;
                }
                commentVo.setReplyUserName(replyUser.getNickname());
                commentVo.setReplyUserAvatar(replyUser.getAvatar());
            }
            //SohuContentMainVo contentMain = sohuContentMainService.getByObj(commentVo.getBusyCode(), commentVo.getBusyType());
            //commentVo.setBusyTitle(Objects.nonNull(contentMain) ? contentMain.getObjTitle() : "");

        }
        result.setRecords(records);
        return TableDataInfoUtils.build(result);
    }

    private void setCoverImage(SohuCommentVo vo) {
        if (vo.getBusyType().equals(BusyType.Article.name())) {
            SohuArticleVo sohuArticleVo = sohuArticleService.selectVoById(vo.getBusyCode());
            if (StrUtil.isNotBlank(sohuArticleVo.getCoverImage())) {
                vo.setCoverImage(sohuArticleVo.getCoverImage());
            }
        } else if (vo.getBusyType().equals(BusyType.Video.name())) {
            SohuVideoVo sohuVideoVo = sohuVideoService.selectVoById(vo.getBusyCode());
            if (StrUtil.isNotBlank(sohuVideoVo.getCoverImage())) {
                vo.setCoverImage(sohuVideoVo.getCoverImage());
            }
        }
    }

    private Map<String, String> coverImageMap(List<SohuCommentVo> records) {
        if (CollUtil.isEmpty(records)) {
            return new HashMap<>();
        }
        // 按照type进行分组归类
        Map<String, List<SohuCommentVo>> busyMap = StreamUtils.groupByKey(records, SohuCommentVo::getBusyType);
        Map<String, Set<Long>> busyIdListMap = new HashMap<>();
        Map<String, String> busyCoverMap = new HashMap<>();
        for (Map.Entry<String, List<SohuCommentVo>> entry : busyMap.entrySet()) {
            List<SohuCommentVo> sohuCommentVos = entry.getValue();
            if (CollUtil.isEmpty(sohuCommentVos)) {
                continue;
            }
            Set<Long> busyCodeIds = sohuCommentVos.stream().map(SohuCommentVo::getBusyCode).collect(Collectors.toSet());
            busyIdListMap.put(entry.getKey(), busyCodeIds);
        }
        for (Map.Entry<String, Set<Long>> entry : busyIdListMap.entrySet()) {
            String key = entry.getKey();
            if (StrUtil.equalsAnyIgnoreCase(key, BusyType.Article.name())) {
                Map<Long, SohuArticleVo> articleVoMap = sohuArticleService.map(entry.getValue());
                if (articleVoMap.isEmpty()) {
                    continue;
                }
                for (Map.Entry<Long, SohuArticleVo> articleVoEntry : articleVoMap.entrySet()) {
                    SohuArticleVo articleVo = articleVoEntry.getValue();
                    busyCoverMap.put(entry.getKey() + StrPool.DASHED + articleVo.getId(), articleVo.getCoverImage());
                }
            } else if (StrUtil.equalsAnyIgnoreCase(key, BusyType.Video.name())) {
                Map<Long, SohuVideoVo> videoVoMap = sohuVideoService.map(entry.getValue());
                if (videoVoMap.isEmpty()) {
                    continue;
                }
                for (Map.Entry<Long, SohuVideoVo> videoVoEntry : videoVoMap.entrySet()) {
                    SohuVideoVo videoVo = videoVoEntry.getValue();
                    busyCoverMap.put(entry.getKey() + StrPool.DASHED + videoVo.getId(), videoVo.getCoverImage());
                }
            }
        }
        return busyCoverMap;
    }

    @Override
    public TableDataInfo<SohuCommentVo> queryListBySiteId(SohuCommentBo bo, PageQuery pageQuery) {
        SohuUserSiteRelationVo userSiteRelation = sohuUserSiteRelationService.getByUserId(LoginHelper.getUserId());
        // 评论审核列表根据当前登录用户站点id展示
        if (ObjectUtil.isNotNull(userSiteRelation)) {
            bo.setSiteId(userSiteRelation.getSiteId());
        }
        LambdaQueryWrapper<SohuComment> lqw = buildQueryWrapper(bo);
        Page<SohuCommentVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuCommentVo> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfoUtils.build();
        }
        Set<Long> userIds = new HashSet<>();
        List<Long> commentIds = new ArrayList<>();
        for (SohuCommentVo commentVo : records) {
            userIds.add(commentVo.getCommentUser());
            userIds.add(commentVo.getReplyUser());
            commentIds.add(commentVo.getId());
        }
        Long userId = LoginHelper.getUserId();
        Map<Long, SohuUserLikeVo> likeVoMap = new HashMap<>();
        if (userId != null && userId > 0L) {
            likeVoMap = sohuUserLikeService.queryMap(userId, BusyType.Comment.name(), commentIds);
        }
        Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
        for (SohuCommentVo commentVo : records) {
            LoginUser loginUser = userMap.get(commentVo.getCommentUser());
            if (Objects.isNull(loginUser)) {
                continue;
            }
            commentVo.setCommentUserName(loginUser.getNickname());
            commentVo.setCommentUserAvatar(loginUser.getAvatar());
            commentVo.setPraiseObj(likeVoMap.get(commentVo.getId()) != null);
            if (commentVo.getReplyUser() != null && commentVo.getReplyUser() > 0L) {
                LoginUser replyUser = userMap.get(commentVo.getReplyUser());
                if (Objects.isNull(replyUser)) {
                    continue;
                }
                commentVo.setReplyUserName(replyUser.getNickname());
                commentVo.setReplyUserAvatar(replyUser.getAvatar());
            }
        }
        result.setRecords(records);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 点赞
     *
     * @param bo
     * @return
     */
    @Override
    public Boolean like(SohuBusyBO bo) {
        SohuComment sohuComment = baseMapper.selectById(bo.getBusyCode());
        if (Objects.isNull(sohuComment)) {
            return Boolean.FALSE;
        }
        Long oldCount = sohuComment.getPraiseCount();
        Long count = bo.getIsAdd() ? oldCount + 1 : Math.max((oldCount - 1), 0);
        sohuComment.setPraiseCount(count);

        String busyType = sohuComment.getBusyType();
        Long busyCode = sohuComment.getBusyCode();
        SohuContentMain sohuContentMain = sohuContentMainService.getEntityByObj(busyCode, busyType);

        bo.setTopCode(busyCode);
        bo.setTopType(busyType);
        bo.setSourceUser(sohuComment.getCommentUser());
        bo.setSourceId(sohuComment.getId());
        bo.setSourceType(BusyType.Comment.getType());
        bo.setOperateUser(bo.getOperateUser());
        bo.setBusyCoverImage(sohuContentMain.getCoverImage());
        bo.setBusyUser(bo.getBusyUser());
        SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildBusy(bo, sohuComment.getCommentUser(), NoticeInteractEnum.like.name());
        interactNoticeReceive.setContent(String.format(NoticeInteractEnum.likeContent, "评论"));
        sohuInteractNoticeService.insertByBo(interactNoticeReceive);

        return baseMapper.updateById(sohuComment) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SohuCommentVo insertBo(SohuCommentBo bo) {
        validEntityBeforeSave(bo);
        SohuComment add = BeanUtil.toBean(bo, SohuComment.class);

        // 阿里云内容审核识别结果
        String scanText = aliyunAuditService.scanText(HtmlUtil.cleanHtmlTag(bo.getContent()), AliyunAuditLabelEnum.textCheck);
        if (StrUtil.isEmptyIfStr(scanText)) {
            add.setState(CommonState.OnShelf.getCode());
        } else {
            add.setState(CommonState.WaitApprove.getCode());
        }
        // 顶级评论的ID
        Long pid = bo.getPid();
        SohuComment parentComment = null;
        if (pid != null && pid > 0L) {
            parentComment = this.baseMapper.selectById(pid);
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            if (Objects.nonNull(parentComment)) {
                // 更新顶级评论的最后一次评论id
                parentComment.setLastCommentId(add.getId());
                this.baseMapper.updateById(parentComment);
            }
            bo.setId(add.getId());
            if (StrUtil.isEmptyIfStr(scanText)) {
                syncComment(add);
            }
        }
        SohuCommentVo vo = this.baseMapper.selectVoById(add.getId());
        LoginUser user = sohuUserService.queryById(add.getCommentUser());
        LoginUser replyUser = sohuUserService.queryById(add.getReplyUser());
        vo.setCommentUserName(StrUtil.isBlankIfStr(user.getNickname()) ? user.getUsername() : user.getNickname());
        vo.setCommentUserAvatar(user.getAvatar());
        if (Objects.nonNull(replyUser)) {
            vo.setReplyUserName(StrUtil.isBlankIfStr(replyUser.getNickname()) ? replyUser.getUsername() : replyUser.getNickname());
            vo.setReplyUserAvatar(replyUser.getAvatar());
        }
        // 绑定用户标签
        if (StringUtils.equalsAnyIgnoreCase(bo.getBusyType(), BusyType.Article.getType(), BusyType.Video.getType())) {
            Long categoryId = 0L;
            if (StrUtil.equalsAnyIgnoreCase(bo.getBusyType(), BusyType.Article.getType())) {
                categoryId = sohuArticleService.get(add.getBusyCode()).getCategoryId();
            }
            if (StrUtil.equalsAnyIgnoreCase(bo.getBusyType(), BusyType.Video.getType())) {
                categoryId = sohuVideoService.get(add.getBusyCode()).getCategoryId();
            }
            bindUserLabel(add.getCommentUser(), categoryId);
        }
        // 评论埋点
        if (StrUtil.equalsAnyIgnoreCase(bo.getBusyType(), BusyType.Article.getType())) {
            buildCommentEventRecord(RecreationReportEnum.TWPL, bo.getCommentUser());
        } else if (StrUtil.equalsAnyIgnoreCase(bo.getBusyType(), BusyType.Video.getType())) {
            buildCommentEventRecord(RecreationReportEnum.SPPL, bo.getCommentUser());
        }
        return vo;
    }

    /**
     * 绑定用户标签
     *
     * @param loginId 用户id
     * @param categoryId 行业标签id
     */
    private void bindUserLabel(Long loginId, Long categoryId) {
        SohuUserLabelRelationBo sohuUserLabelRelationBo = new SohuUserLabelRelationBo();
        sohuUserLabelRelationBo.setUserId(loginId);
        sohuUserLabelRelationBo.setLabelId(categoryId);
        sohuUserLabelRelationBo.setLabelType(LabelEnum.CONTENT.getCode());
        List<SohuUserLabelRelationBo> userLabelList = new ArrayList<>();
        userLabelList.add(sohuUserLabelRelationBo);
        remoteMiddleCommonLabelService.insertBatch(userLabelList);
    }

    /**
     * 关注埋点
     *
     * @param reportEnum 埋点枚举
     * @param userId     用户id
     * @return 事件id
     */
    public String buildCommentEventRecord(RecreationReportEnum reportEnum, Long userId) {
        SohuEventReportBo bo = new SohuEventReportBo(
                RecreationReportEnum.getCode(reportEnum.getType()),
                reportEnum.getDesc(),
                ActionTypeEnum.Recreation.name(),
                reportEnum.getType(),
                userId);

        return remoteMiddleEventReportService.getEventId(bo);
    }

    @Async("asyncExecutor")
    public void syncComment(SohuComment add) {
        SohuCommentBo busyBO = new SohuCommentBo();
        busyBO.setReplyUser(add.getReplyUser());
        busyBO.setTopReplyUser(add.getTopReplyUser());
        busyBO.setBusyType(add.getBusyType());
        busyBO.setBusyCode(add.getBusyCode());
        busyBO.setSourceId((add.getId()));
        busyBO.setSourceType(BusyType.Comment.getType());
        busyBO.setOperateUser(add.getCommentUser());
        busyBO.setOperateContent(add.getContent());
        MiddleProcessor processor = new MiddleProcessor();
        log.info("评论-互动通知操作:{}", JSONUtil.toJsonStr(busyBO));
        // 接收方
        processor.getStrategy(BusyType.valueOf(add.getBusyType())).comment(busyBO, Boolean.TRUE);

        if (add.getTopReplyUser() != null && add.getTopReplyUser() > 0L) {
            // 评论的评论，对象作者收到
            busyBO.setReplyUser(add.getTopReplyUser());
            processor.getStrategy(BusyType.valueOf(add.getBusyType())).comment(busyBO, Boolean.FALSE);
        }
    }

    /**
     * 查询评论记录列表
     */
    @Override
    public List<SohuCommentVo> queryList(SohuCommentBo bo) {
        LambdaQueryWrapper<SohuComment> lqw = buildQueryWrapper(bo);
        List<SohuCommentVo> commentVoList = baseMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(commentVoList)) {
            Set<Long> userIds = new HashSet<>();
            List<Long> commentIds = new ArrayList<>();
            for (SohuCommentVo commentVo : commentVoList) {
                userIds.add(commentVo.getCommentUser());
                commentIds.add(commentVo.getId());
            }
            Long userId = LoginHelper.getUserId();
            Map<Long, SohuUserLikeVo> likeVoMap;
            if (userId != null && userId > 0L) {
                likeVoMap = sohuUserLikeService.queryMap(userId, BusyType.Comment.name(), commentIds);
            } else {
                likeVoMap = new HashMap<>();
            }
            Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
            commentVoList.forEach(commentVo -> {
                LoginUser loginUser = userMap.get(commentVo.getCommentUser());
                if (Objects.nonNull(loginUser)) {
                    commentVo.setCommentUserName(loginUser.getNickname());
                    commentVo.setCommentUserAvatar(loginUser.getAvatar());
                }
                commentVo.setPraiseObj(likeVoMap.get(commentVo.getId()) != null);
            });
        }
        return commentVoList;
    }

    private LambdaQueryWrapper<SohuComment> buildQueryWrapper(SohuCommentBo bo) {
        LambdaQueryWrapper<SohuComment> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getBusyType()), SohuComment::getBusyType, bo.getBusyType());
        lqw.eq(bo.getSiteId() != null, SohuComment::getSiteId, bo.getSiteId());
        lqw.eq(bo.getBusyCode() != null, SohuComment::getBusyCode, bo.getBusyCode());
        lqw.eq(bo.getBusyUser() != null, SohuComment::getBusyUser, bo.getBusyUser());
        lqw.eq(bo.getCommentUser() != null, SohuComment::getCommentUser, bo.getCommentUser());
        lqw.eq((bo.getPid() != null && bo.getPid() > 0L), SohuComment::getPid, bo.getPid());
        lqw.like(StringUtils.isNotBlank(bo.getContent()), SohuComment::getContent, bo.getContent());
        lqw.eq(StringUtils.isNotBlank(bo.getIp()), SohuComment::getIp, bo.getIp());
        String state = bo.getState();
        if (StrUtil.isBlank(state) && (bo.getPid() == null || bo.getPid() == 0L)) {
            lqw.eq(true, SohuComment::getPid, 0);
        }
        if (StrUtil.isNotBlank(state)) {
            lqw.eq(true, SohuComment::getState, state);
        } else {
            Long userId = LoginHelper.getUserId();
            String roleKey = bo.getRoleKey();
            if (StrUtil.isNotBlank(roleKey) && StrUtil.equalsAnyIgnoreCase(roleKey, RoleCodeEnum.Project.getCode())) {
                lqw.eq(SohuComment::getBusyType, RoleCodeEnum.Project.getCode());
                lqw.eq(SohuComment::getBusyUser, userId);
                lqw.eq(SohuComment::getState, CommonState.OnShelf.name());
            }
            if (StrUtil.isNotBlank(roleKey) && StrUtil.equalsAnyIgnoreCase(roleKey, RoleCodeEnum.Article.getCode())) {
                lqw.in(StrUtil.isBlank(bo.getBusyType()), SohuComment::getBusyType, "Article", "Video", "Answer");
                lqw.eq(SohuComment::getBusyUser, userId);
                lqw.eq(SohuComment::getState, CommonState.OnShelf.name());
            }
            if (StrUtil.isBlank(roleKey)) {
                lqw.and(wrapper -> wrapper
                        .and(i -> i.eq(SohuComment::getCommentUser, userId)
                                .eq(SohuComment::getState, CommonState.WaitApprove.name()))
                        .or(i -> i.eq(SohuComment::getState, CommonState.OnShelf.name()))
                );
            }

        }

        if (bo.getOrderType() == null) {
            lqw.orderByDesc(SohuComment::getTop, SohuComment::getId);
        } else {
            switch (bo.getOrderType()) {
                case TimeAsc:
                    lqw.orderByAsc(SohuComment::getCommentTime);
                    break;
                case TimeDesc:
                    lqw.orderByDesc(SohuComment::getCommentTime);
                    break;
                case LikeAsc:
                    lqw.orderByAsc(SohuComment::getPraiseCount);
                    break;
                case LikeDesc:
                    lqw.orderByDesc(SohuComment::getPraiseCount).orderByDesc(SohuComment::getCommentTime);
                    break;
                default:
                    break;
            }
        }
        lqw.last(bo.getSelectTwo(), " limit 2");
        return lqw;
    }

    /**
     * 新增评论记录
     */
    @Override
    public Boolean insertByBo(SohuCommentBo bo) {
        return Objects.nonNull(insertBo(bo));
    }

    /**
     * 修改评论记录
     */
    @Override
    public Boolean updateByBo(SohuCommentBo bo) {
        validEntityBeforeSave(bo);
        SohuComment update = BeanUtil.toBean(bo, SohuComment.class);
        update.setTop(0L);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuCommentBo entity) {
        MiddleProcessor processor = new MiddleProcessor();

        BusyType busyType = BusyType.valueOf(entity.getBusyType());
        // 内容对象的作者id
        Long authorId = processor.getStrategy(busyType).getAuthorId(entity.getBusyCode());
        if (authorId == null || authorId <= 0L) {
            if (entity.getId() == null || entity.getId() <= 0L) {
                throw new RuntimeException(String.format("评论的%s不存在", busyType.getDescription()));
            } else {
                this.baseMapper.deleteById(entity.getId());
                log.info("删除脏数据成功！");
                return;
            }
        }
        entity.setBusyUser(authorId);
        Long pid = entity.getPid();
        if (pid != null && pid > 0L) {
            SohuCommentVo sohuCommentVo = this.queryById(pid);
            validateArticleExists(sohuCommentVo);
            entity.setPid(pid);
        }
        // 上级评论id
        Long replyId = entity.getReplyId();
        if (replyId == null) {
            entity.setReplyId(0L);
        }
        if (entity.getReplyId() > 0L) {
            SohuCommentVo sohuCommentVo = this.queryById(entity.getReplyId());
            validateArticleExists(sohuCommentVo);
            // 设置上级评论人用户id
            entity.setReplyUser(sohuCommentVo.getCommentUser());
            entity.setTopReplyUser(authorId);
        } else {
            entity.setReplyUser(authorId);
        }
        Long loginId = LoginHelper.getUserId();
        entity.setCommentUser(loginId);
        entity.setTop(0L);

    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuComment entity) {
        Long pid = entity.getPid();
        if (pid != null && pid > 0L) {
            SohuCommentVo sohuCommentVo = this.queryById(pid);
            validateArticleExists(sohuCommentVo);
            entity.setPid(pid);
        }
        // 上级评论id
        Long replyId = entity.getReplyId();
        if (replyId == null) {
            entity.setReplyId(0L);
        }
        if (entity.getReplyId() > 0L) {
            SohuCommentVo sohuCommentVo = this.queryById(pid);
            validateArticleExists(sohuCommentVo);
            // 设置上级评论人用户id
            entity.setReplyUser(sohuCommentVo.getCommentUser());
        }
    }

    private void validateArticleExists(SohuCommentVo sohuCommentVo) {
        if (Objects.isNull(sohuCommentVo)) {
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
    }

    private void validateArticleExists(SohuComment sohuComment) {
        if (Objects.isNull(sohuComment)) {
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
    }

    /**
     * 批量删除评论记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        List<SohuComment> sohuComments = this.baseMapper.selectBatchIds(ids);
        if (CollUtil.isEmpty(sohuComments)) {
            return Boolean.FALSE;
        }
        for (SohuComment sohuComment : sohuComments) {
            LambdaUpdateWrapper<SohuComment> luw = new LambdaUpdateWrapper<>();
            luw.eq(SohuComment::getLastCommentId, sohuComment.getId()).set(SohuComment::getLastCommentId, 0L);
            baseMapper.update(new SohuComment(), luw);
            if (!StrUtil.equalsAnyIgnoreCase(CommonState.OnShelf.getCode(), sohuComment.getState())) {
                continue;
            }
            if (StrUtil.equalsAnyIgnoreCase(sohuComment.getBusyType(), BusyType.Answer.name())) {
                // 问题的回答的评论
                SohuQoraInfo qoraInfo = sohuQoraInfoMapper.selectOne(SohuQoraInfo::getQoraType, BusyType.Answer.name(), SohuQoraInfo::getQoraId, sohuComment.getBusyCode());
                if (Objects.nonNull(qoraInfo)) {
                    qoraInfo.setCommentCount(qoraInfo.getCommentCount() - 1);
                    sohuQoraInfoMapper.updateById(qoraInfo);
                }
            } else if (StrUtil.equalsAnyIgnoreCase(sohuComment.getBusyType(), BusyType.Question.name())) {
                // 问题的评论
                SohuQoraInfo qoraInfo = sohuQoraInfoMapper.selectOne(SohuQoraInfo::getQoraType, BusyType.Question.name(), SohuQoraInfo::getQoraId, sohuComment.getBusyCode());
                if (Objects.nonNull(qoraInfo)) {
                    qoraInfo.setCommentCount(qoraInfo.getCommentCount() - 1);
                }
            } else if (StrUtil.equalsAnyIgnoreCase(sohuComment.getBusyType(), BusyType.Article.name())) {
                // 文章的评论
                SohuArticleInfo articleInfo = sohuArticleInfoMapper.selectOne(SohuArticleInfo::getArticleId, sohuComment.getBusyCode());
                if (Objects.nonNull(articleInfo)) {
                    articleInfo.setCommentCount(articleInfo.getCommentCount() - 1);
                    sohuArticleInfoMapper.updateById(articleInfo);
                }
            } else if (StrUtil.equalsAnyIgnoreCase(sohuComment.getBusyType(), BusyType.Video.name())) {
                // 视频的评论
                SohuVideoInfo videoInfo = sohuVideoInfoMapper.selectOne(SohuVideoInfo::getVideoId, sohuComment.getBusyCode());
                if (Objects.nonNull(videoInfo)) {
                    videoInfo.setCommentCount(videoInfo.getCommentCount() - 1);
                    sohuVideoInfoMapper.updateById(videoInfo);
                }
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean operate(SohuCommentBo bo) {
        List<Long> ids = bo.getIds();
        if (CollUtil.isEmpty(ids)) {
            return Boolean.FALSE;
        }
        String state = (StrUtil.isNotBlank(bo.getState()) && StrUtil.equalsIgnoreCase(bo.getState(), CommonState.OnShelf.getCode())) ?
                CommonState.OnShelf.getCode() : CommonState.Refuse.getCode();
        LambdaUpdateWrapper<SohuComment> update = new LambdaUpdateWrapper<>();
        if (StrUtil.isNotBlank(bo.getRejectReason())) {
            update.set(SohuComment::getRejectReason, bo.getRejectReason());
        }
        if (CommonState.OnShelf.getCode().equals(state)) {
            List<SohuCommentVo> sohuCommentVos = baseMapper.selectVoBatchIds(ids);
            sohuCommentVos.forEach(item -> {
                //一级评论加评论数
                if (item.getPid() == null || item.getPid() == 0L) {
                    // 审核通过，添加评论数
                    SohuCommentBo busyBO = new SohuCommentBo();
                    busyBO.setBusyType(item.getBusyType());
                    busyBO.setBusyCode(item.getBusyCode());
                    MiddleProcessor processor = new MiddleProcessor();
                    processor.getStrategy(BusyType.valueOf(item.getBusyType())).comment(busyBO, Boolean.TRUE);

                } /*else {
                    //二级评论添加一级评论的下级评论id
                    SohuComment parentComment = this.baseMapper.selectById(item.getPid());
                    if (Objects.nonNull(parentComment)) {
                        parentComment.setLastCommentId(item.getId());
                        this.baseMapper.updateById(parentComment);
                    }
                }*/
            });
        }
        update.set(SohuComment::getState, state).in(SohuComment::getId, ids);
        baseMapper.update(new SohuComment(), update);
        return Boolean.TRUE;
    }

    @Override
    public void asTop(SohuCommentBo bo) {
        SohuComment sohuComment = this.baseMapper.selectById(bo.getId());
        validateArticleExists(sohuComment);
        Long top = bo.getTop();
        sohuComment.setTop((top != null && top == 1L) ? 1L : 0L);
        this.baseMapper.updateById(sohuComment);
    }
}
