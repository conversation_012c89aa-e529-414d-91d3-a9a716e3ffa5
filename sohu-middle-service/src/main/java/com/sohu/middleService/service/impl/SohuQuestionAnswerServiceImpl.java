package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.NoticeInteractEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.bo.notice.SohuInteractNoticeBo;
import com.sohu.middle.api.vo.*;
import com.sohu.middleService.domain.*;
import com.sohu.middleService.mapper.*;
import com.sohu.middleService.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 回答主体Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@RequiredArgsConstructor
@Service
public class SohuQuestionAnswerServiceImpl extends SohuBaseServiceImpl<SohuQuestionAnswerMapper, SohuQuestionAnswer, SohuQuestionAnswerVo> implements ISohuQuestionAnswerService {

    private final SohuQuestionMapper sohuQuestionMapper;
    private final SohuVideoMapper sohuVideoMapper;
    private final SohuContentMainMapper sohuContentMainMapper;
    private final SohuQoraInfoMapper sohuQoraInfoMapper;
    private final SohuCommentMapper sohuCommentMapper;
    private final ISohuContentMainService sohuContentMainService;
    private final ISohuSyncContentService sohuSyncContentService;
    private final ISohuQoraInfoService sohuQoraInfoService;
    private final ISohuQoraRelateService sohuQoraRelateService;
    private final ISohuAuditService sohuAuditService;
    private final ISohuUserCollectService sohuUserCollectService;
    private final ISohuUserLikeService sohuUserLikeService;
    private final ISohuUserFollowService sohuUserFollowService;
    private final ISohuCommentService sohuCommentService;
    private final ISohuInteractNoticeService sohuInteractNoticeService;
    private final ISohuUserService sohuUserService;
    private final ISohuContentMainService iSohuContentMainService;

    @Override
    public Long getAuthorId(Long id) {
        if (CalUtils.isNullOrZero(id)) {
            return 0L;
        }
        SohuQuestionAnswerVo answerVo = get(id);
        return Objects.isNull(answerVo) ? 0L : answerVo.getUserId();
    }

    @Override
    public SohuQuestionAnswerVo get(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询回答主体
     */
    @Override
    public SohuQuestionAnswerVo queryById(Long id) {
        SohuQuestionAnswerVo questionAnswerVo = baseMapper.selectVoById(id);
        if (Objects.isNull(questionAnswerVo)) {
            return null;
        }
        SohuQuestionVo sohuQuestionVo = sohuQuestionMapper.selectVoById(questionAnswerVo.getQuestionId());
        if (Objects.nonNull(sohuQuestionVo)) {
            questionAnswerVo.setTitle(sohuQuestionVo.getTitle());
            questionAnswerVo.setSiteId(sohuQuestionVo.getSiteId());
            questionAnswerVo.setCategoryId(sohuQuestionVo.getCategoryId());
        }
        // 构建关联
        buildRelate(questionAnswerVo);
        return questionAnswerVo;
    }

    /**
     * 查询回答主体列表
     */
    @Override
    public TableDataInfo<SohuQuestionAnswerVo> queryPageList(SohuQuestionAnswerBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuQuestionAnswer> lqw = buildQueryWrapper(bo);
        Page<SohuQuestionAnswerVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询回答主体列表
     */
    @Override
    public List<SohuQuestionAnswerVo> queryList(SohuQuestionAnswerBo bo) {
        LambdaQueryWrapper<SohuQuestionAnswer> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuQuestionAnswer> buildQueryWrapper(SohuQuestionAnswerBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuQuestionAnswer> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getQuestionId() != null, SohuQuestionAnswer::getQuestionId, bo.getQuestionId());
        lqw.eq(bo.getPid() != null, SohuQuestionAnswer::getPid, bo.getPid());
        lqw.like(StringUtils.isNotBlank(bo.getContent()), SohuQuestionAnswer::getContent, bo.getContent());
        lqw.eq(bo.getUserId() != null, SohuQuestionAnswer::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuQuestionAnswer::getState, bo.getState());
        return lqw;
    }

    /**
     * 新增回答主体
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SohuQuestionAnswerBo bo) {
        SohuQuestionAnswer add = BeanUtil.toBean(bo, SohuQuestionAnswer.class);
        validEntityBeforeSave(add);
        String state = StrUtil.isNotBlank(bo.getState()) && StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.Edit.name()) ?
                CommonState.Edit.name() : CommonState.WaitApprove.name();
        add.setState(state);
        add.setCreateTime(new Date());
        add.setUpdateTime(new Date());
        add.setUserId(LoginHelper.getUserId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        // 保存至万能表
        sohuSyncContentService.sync(add);
        SohuQoraInfoBo qoraInfoBo = new SohuQoraInfoBo();
        qoraInfoBo.setQoraId(add.getId());
        qoraInfoBo.setQoraType(BusyType.Answer.name());
        // 保存拓展表
        sohuQoraInfoService.insertByBo(qoraInfoBo);
        // 保存关联项
        addQoraAnswerRelate(bo);
        /*if (bo.getBusyCode() != null && StrUtil.isNotBlank(bo.getBusyType())) {
            SohuQoraRelateBo relateBo = new SohuQoraRelateBo();
            relateBo.setQoraId(add.getId());
            relateBo.setQoraType(BusyType.Answer.name());
            relateBo.setBusyCode(bo.getBusyCode());
            relateBo.setBusyType(bo.getBusyType());
            sohuQoraRelateService.insertByBo(relateBo);
        }*/
        // 保存审核记录
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
            this.initCreateAudited(add);
        }
        return flag;
    }

    /**
     * 保存关联项
     *
     * @param bo
     */
    public void addQoraAnswerRelate(SohuQuestionAnswerBo bo) {
        //删除关联项
        sohuQoraRelateService.deleteByQuestionIds(Arrays.asList(bo.getId()));
        if (StrUtil.isNotBlank(bo.getBusyType()) && ((StrUtil.equalsAnyIgnoreCase(bo.getBusyType(), BusyType.Window.name()) || bo.getBusyCode() != null) || StrUtil.isNotBlank(bo.getBusyInfo()))) {
            SohuQoraRelateBo relateBo = new SohuQoraRelateBo();
            relateBo.setQoraId(bo.getId());
            relateBo.setQoraType(BusyType.Answer.name());
            relateBo.setBusyType(bo.getBusyType());
            relateBo.setBusyInfo(bo.getBusyInfo());
            Long busyCode = bo.getBusyCode();
            String busyTitle = bo.getBusyTitle();
            if (StrUtil.equalsAnyIgnoreCase(bo.getBusyType(), BusyType.Window.name())) {
                LoginUser loginUser = LoginHelper.getLoginUser();
                if (loginUser != null) {
                    busyTitle = loginUser.getNickname();
                    busyCode = loginUser.getUserId();
                }
            } else if (StrUtil.equalsAnyIgnoreCase(bo.getBusyType(), BusyType.GoodsWindow.name())) {
                LoginUser loginUser = LoginHelper.getLoginUser();
                if (loginUser != null) {
                    busyCode = loginUser.getUserId();
                }
            }
            relateBo.setBusyCode(busyCode);
            relateBo.setBusyTitle(busyTitle);
            sohuQoraRelateService.insertByBo(relateBo);
        }
    }

    /**
     * 修改回答主体
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(SohuQuestionAnswerBo bo) {
        SohuQuestionAnswer update = BeanUtil.toBean(bo, SohuQuestionAnswer.class);
        validEntityBeforeSave(update);
//        String state = StrUtil.isNotBlank(bo.getState()) && StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.Edit.name()) ?
//                CommonState.Edit.name() : CommonState.WaitApprove.name();
        String state = bo.getState();
        update.setState(state);
        update.setUserId(LoginHelper.getUserId());
        update.setUpdateTime(new Date());
        // 保存至万能表
        sohuSyncContentService.sync(update);
        //保存关联项
        addQoraAnswerRelate(bo);
        /*Long busyCode = bo.getBusyCode();
        String relateType = bo.getBusyType();
        if (StrUtil.isNotBlank(relateType) && (busyCode != null && busyCode > 0L)) {
            SohuQoraRelate relate = sohuQoraRelateService.getByQora(bo.getId(), BusyType.Answer.name());
            if (Objects.nonNull(relate)) {
                SohuQoraRelateBo relateBo = new SohuQoraRelateBo();
                relateBo.setId(relate.getId());
                // 更新
                relateBo.setBusyCode(busyCode);
                relateBo.setBusyType(relateType);
                sohuQoraRelateService.updateByBo(relateBo);
            } else {
                //新增
                SohuQoraRelateBo relateBo = new SohuQoraRelateBo();
                relateBo.setQoraId(bo.getId());
                relateBo.setQoraType(BusyType.Answer.name());
                relateBo.setBusyCode(busyCode);
                relateBo.setBusyType(relateType);
                sohuQoraRelateService.insertByBo(relateBo);
            }
        }*/
        // 保存审核记录
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
            this.initCreateAudited(update);
        }
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuQuestionAnswer entity) {
        Long questionId = entity.getQuestionId();
        SohuQuestion question = sohuQuestionMapper.selectById(questionId);
        if (Objects.isNull(question)) {
            throw new ServiceException(MessageUtils.message(""));
        }
    }

//    /**
//     * 批量删除回答主体
//     */
//    @Override
//    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        /*if (isValid) {
//            // TODO 做一些业务上的校验,判断是否需要校验
//        }*/
//        return baseMapper.deleteBatchIds(ids) > 0;
//    }

    /**
     * 回答同步至审核
     *
     * @param answer 回答对象
     */
    private void initCreateAudited(SohuQuestionAnswer answer) {
        SohuQuestion question = sohuQuestionMapper.selectById(answer.getQuestionId());
        SohuAuditBo auditBo = new SohuAuditBo();
        auditBo.setBusyType(BusyType.Answer.name());
        auditBo.setBusyCode(answer.getId());
        auditBo.setBusyTitle(question.getTitle());
        auditBo.setBusyBelonger(answer.getUserId());
        auditBo.setCitySiteId(question.getSiteId());
        auditBo.setPublishTime(new Date());
        sohuAuditService.initCreateAudited(auditBo);
    }

    /**
     * @param bo
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<SohuContentMainVo> answerList(SohuQuestionAnswerBo bo, PageQuery pageQuery) {
        Long questionId = bo.getQuestionId();
        SohuQuestion sohuQuestion = sohuQuestionMapper.selectById(questionId);
        if (Objects.isNull(sohuQuestion)) {
            throw new RuntimeException(MessageUtils.message("question.not.exist"));
        }
        LambdaQueryWrapper<SohuQuestionAnswer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SohuQuestionAnswer::getQuestionId, questionId);
        Long userId = LoginHelper.getUserId();
        if (userId != null && userId > 0L) {
            queryWrapper.in(SohuQuestionAnswer::getState, Arrays.asList(CommonState.Edit.name(), CommonState.WaitApprove.name(), CommonState.OnShelf.name()));
        } else {
            queryWrapper.eq(SohuQuestionAnswer::getState, CommonState.OnShelf.name());
        }
        queryWrapper.orderByDesc(SohuQuestionAnswer::getCreateTime);
        Page<SohuQuestionAnswerVo> answerVoPage = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), queryWrapper);
        List<SohuQuestionAnswerVo> answerVoList = answerVoPage.getRecords();
        TableDataInfo<SohuContentMainVo> tableDataInfo = TableDataInfoUtils.build();
        if (CollUtil.isEmpty(answerVoList)) {
            return tableDataInfo;
        }
        tableDataInfo.setTotal(answerVoPage.getTotal());
        List<Long> answerIds = new ArrayList<>();
        Set<Long> userIds = new HashSet<>();
        for (SohuQuestionAnswerVo answerVo : answerVoList) {
            answerIds.add(answerVo.getId());
            userIds.add(answerVo.getUserId());
        }
        Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);

        Map<Long, SohuQoraInfo> qoraInfoMap = sohuQoraInfoService.mapByQora(answerIds, BusyType.Answer.name());
        Map<Long, SohuUserCollectVo> userCollectVoMap = new HashMap<>();
        Map<Long, SohuUserLikeVo> userLikeVoMap = new HashMap<>();
        if (userId != null && userId > 0L) {
            userCollectVoMap = sohuUserCollectService.queryMap(userId, BusyType.Answer.name(), answerIds);
            userLikeVoMap = sohuUserLikeService.queryMap(userId, BusyType.Answer.name(), answerIds);
        }
        List<SohuContentMainVo> sohuContentMains = sohuContentMainService.listByObj(answerIds, BusyType.Answer.name());
        Map<Long, SohuContentMainVo> contentMainMap = sohuContentMains.stream().collect(Collectors.toMap(SohuContentMainVo::getObjId, u -> u));

        List<SohuContentMainVo> contentMainVoList = new LinkedList<>();
        for (SohuQuestionAnswerVo answerVo : answerVoList) {
            SohuContentMainVo mainVo = new SohuContentMainVo();
            SohuContentMainVo sohuContentMain = contentMainMap.get(answerVo.getId());
            if (Objects.nonNull(sohuContentMain)) {
                mainVo.setId(sohuContentMain.getId());
            }
            SohuQoraInfo qoraInfo = qoraInfoMap.get(answerVo.getId());
            mainVo.setUserId(answerVo.getUserId());
            mainVo.setObjTitle(sohuQuestion.getTitle());
            mainVo.setObjContent(answerVo.getContent());
            mainVo.setCollectObj(Objects.nonNull(userCollectVoMap.get(answerVo.getId())));
            mainVo.setPraiseObj(Objects.nonNull(userLikeVoMap.get(answerVo.getId())));
            if (Objects.nonNull(qoraInfo)) {
                mainVo.setViewCount(qoraInfo.getViewCount());
                mainVo.setCollectCount(qoraInfo.getCollectCount());
                mainVo.setCommentCount(qoraInfo.getCommentCount());
                mainVo.setPraiseCount(qoraInfo.getPraiseCount());
            }
            LoginUser loginUser = userMap.get(answerVo.getUserId());
            if (Objects.isNull(loginUser)) {
                continue;
            }
            mainVo.setUserName(loginUser.getUsername());
            mainVo.setUserName(loginUser.getNickname());
            mainVo.setUserAvatar(loginUser.getAvatar());
            contentMainVoList.add(mainVo);
        }
        tableDataInfo.setData(contentMainVoList);
        return tableDataInfo;
    }

    @Override
    public TableDataInfo<SohuQuestionAnswerVo> answerTop(SohuQuestionAnswerBo bo, PageQuery pageQuery) {
        Long userId = LoginHelper.getUserId();
        if (userId != null && userId > 0L) {
            bo.setUserId(userId);
        }
        Page<SohuQuestionAnswerVo> page = sohuQuestionMapper.selectAnswerTop(bo, PageQueryUtils.build(pageQuery));
        if (CollUtil.isEmpty(page.getRecords())) {
            return TableDataInfoUtils.build();
        }
        List<Long> answerIds = new ArrayList<>();
        List<Long> userIds = new ArrayList<>();
        for (SohuQuestionAnswerVo answerVo : page.getRecords()) {
            answerIds.add(answerVo.getId());
            userIds.add(answerVo.getUserId());
        }
        Map<Long, SohuUserFollowVo> userFollowMap;
        Map<Long, SohuUserLikeVo> likeVoMap;
        Map<Long, SohuUserCollectVo> collectVoMap;
        if (userId != null && userId > 0L) {
            userFollowMap = sohuUserFollowService.mapUserFollows(userId, userIds);
            likeVoMap = sohuUserLikeService.queryMap(userId, BusyType.Answer.name(), answerIds);
            collectVoMap = sohuUserCollectService.queryMap(userId, BusyType.Answer.name(), answerIds);
        } else {
            userFollowMap = new HashMap<>();
            likeVoMap = new HashMap<>();
            collectVoMap = new HashMap<>();
        }
        Boolean isFirst = true;
        for (SohuQuestionAnswerVo item : page.getRecords()) {
            // 对第一个评论进行其他操作
            if (isFirst) {
                // 第一条回答添加两条评论
                SohuCommentBo commentBo = new SohuCommentBo();
                commentBo.setBusyCode(item.getId());
                commentBo.setBusyType(BusyType.Answer.name());
                commentBo.setSelectTwo(true);
                List<SohuCommentVo> commentVoList = sohuCommentService.queryList(commentBo);
                item.setCommentList(commentVoList);
                isFirst = false;
            }
            // 我关注状态，true = 关注
            item.setFollowObj(Objects.nonNull(userFollowMap.get(item.getUserId())));
            // 我点赞状态，true = 点赞
            item.setPraiseObj(Objects.nonNull(likeVoMap.get(item.getId())));
            // 我收藏状态，true = 收藏
            item.setCollectObj(Objects.nonNull(collectVoMap.get(item.getId())));
            // 回答关联项目
            buildRelate(item);
        }

        return TableDataInfoUtils.build(page);
    }

    @Override
    public Boolean forward(SohuBusyBO bo) {
        SohuQoraInfoVo infoVo = sohuQoraInfoService.queryObj(bo.getBusyCode(), bo.getBusyType().name());
        if (Objects.isNull(infoVo) || infoVo.getId() == null) {
            return Boolean.FALSE;
        }
        int count = infoVo.getForwardCount() + 1;
        SohuQoraInfoBo infoBo = new SohuQoraInfoBo();
        infoBo.setId(infoVo.getId());
        infoBo.setForwardCount(count);
        sohuQoraInfoService.updateByBo(infoBo);
        SohuContentMainBo mainBo = new SohuContentMainBo();
        mainBo.setObjId(bo.getBusyCode());
        mainBo.setObjType(BusyType.Answer.name());
        mainBo.setForwardCount(count);
        sohuContentMainService.updateByBo(mainBo);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateUserQuestionAnswerState(Long userId, String state) {
        if (userId == null || userId <= 0L) {
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<SohuQuestionAnswer> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuQuestionAnswer::getUserId, userId).set(SohuQuestionAnswer::getState, state);
        // 修改万能表的状态
        sohuContentMainService.updateUserObjState(userId, BusyType.Answer.name(), state);
        return baseMapper.update(new SohuQuestionAnswer(), luw) > 0;
    }

    @Override
    public Boolean updateState(SohuBusyUpdateStateBo bo) {
        Long busyCode = bo.getBusyCode();
        SohuQuestionAnswer sohuQuestionAnswer = baseMapper.selectById(busyCode);
        if (Objects.isNull(sohuQuestionAnswer)) {
            return Boolean.FALSE;
        }
        sohuQuestionAnswer.setState(StrUtil.isBlankIfStr(bo.getState()) ? CommonState.CompelOff.getCode() : bo.getState());
        sohuQuestionAnswer.setUpdateTime(new Date());
        // 更新问题
        baseMapper.updateById(sohuQuestionAnswer);
        // 更新万能表
        sohuContentMainMapper.updateState(sohuQuestionAnswer.getState(), busyCode, BusyType.Answer.name());

        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean like(SohuBusyBO bo) {
        SohuQoraInfo info = sohuQoraInfoService.queryEntityObj(bo.getBusyCode(), bo.getBusyType().name());
        if (Objects.isNull(info) || info.getId() == null) {
            return false;
        }
        Integer oldCount = info.getPraiseCount();
        int count = bo.getIsAdd() != null && bo.getIsAdd() ? oldCount + 1 : Math.max((oldCount - 1), 0);
        info.setPraiseCount(count);
        // 更新万能表的点赞数量
        sohuContentMainMapper.setPraiseCount(bo.getBusyCode(), BusyType.Answer.name(), count);

        // 增加点赞
        if (bo.getIsAdd() != null && bo.getIsAdd()) {
            SohuQuestionAnswerVo questionAnswerVo = baseMapper.selectVoById(bo.getBusyCode());
            SohuQuestion sohuQuestion = sohuQuestionMapper.selectById(questionAnswerVo.getQuestionId());
            bo.setTopCode(sohuQuestion.getId());
            bo.setTopType(BusyType.Question.name());
            bo.setSourceUser(questionAnswerVo.getUserId());
            bo.setOperateUser(bo.getOperateUser());
            bo.setBusyCoverImage(sohuQuestion.getCoverImage());
            bo.setBusyUser(questionAnswerVo.getUserId());
            SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildBusy(bo, questionAnswerVo.getUserId(), NoticeInteractEnum.like.name());
            interactNoticeReceive.setContent(String.format(NoticeInteractEnum.likeContent, "回答"));
            sohuInteractNoticeService.insertByBo(interactNoticeReceive);
        }
        // 删除问题的拓展数据缓存
        sohuQoraInfoService.evictObj(bo.getBusyCode(), bo.getBusyType().name());
        return sohuQoraInfoMapper.updateById(info) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean collect(SohuBusyBO bo) {
        SohuQoraInfo info = sohuQoraInfoService.queryEntityObj(bo.getBusyCode(), bo.getBusyType().name());
        if (Objects.isNull(info) || info.getId() == null) {
            return false;
        }
        Integer oldCount = info.getCollectCount();
        int count = bo.getIsAdd() ? oldCount + 1 : Math.max((oldCount - 1), 0);
        info.setCollectCount(count);
        boolean result = sohuQoraInfoMapper.updateById(info) > 0;
        // 更新万能表的收藏数量
        sohuContentMainMapper.setCollectCount(bo.getBusyCode(), BusyType.Answer.name(), count);

        if (bo.getIsAdd() != null && bo.getIsAdd()) {
            SohuQuestionAnswerVo questionAnswerVo = baseMapper.selectVoById(bo.getBusyCode());
            SohuQuestion sohuQuestion = sohuQuestionMapper.selectById(questionAnswerVo.getQuestionId());
            bo.setTopCode(sohuQuestion.getId());
            bo.setTopType(BusyType.Question.name());
            bo.setSourceUser(questionAnswerVo.getUserId());
            bo.setOperateUser(bo.getOperateUser());
            bo.setBusyCoverImage(sohuQuestion.getCoverImage());
            bo.setBusyUser(questionAnswerVo.getUserId());
            SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildBusy(bo, questionAnswerVo.getUserId(), NoticeInteractEnum.collect.name());
            interactNoticeReceive.setContent(String.format(NoticeInteractEnum.collectContent, "回答"));
            sohuInteractNoticeService.insertByBo(interactNoticeReceive);
        }
        // 删除问题的拓展数据缓存
        sohuQoraInfoService.evictObj(bo.getBusyCode(), bo.getBusyType().name());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean comment(SohuCommentBo bo, Boolean commentCountAdd) {
        SohuQoraInfo info = sohuQoraInfoService.queryEntityObj(bo.getBusyCode(), bo.getBusyType());
        if (Objects.isNull(info) || info.getId() == null) {
            return false;
        }
        if (BooleanUtil.isTrue(commentCountAdd)) {
            info.setCommentCount(info.getCommentCount() + 1);
        }
        boolean result = sohuQoraInfoMapper.updateById(info) > 0;
        // 更新万能表的评论数量
        SohuContentMain sohuContentMain = iSohuContentMainService.getEntityByObj(bo.getBusyCode(), BusyType.Answer.name());
        if (Objects.nonNull(sohuContentMain)) {
            sohuContentMain.setCommentCount(info.getCommentCount());
            iSohuContentMainService.updateById(sohuContentMain);
        }

        SohuQuestionAnswerVo questionAnswerVo = baseMapper.selectVoById(bo.getBusyCode());
        SohuQuestion sohuQuestion = sohuQuestionMapper.selectById(questionAnswerVo.getQuestionId());

        bo.setTopCode(sohuQuestion.getId());
        bo.setBusyUser(questionAnswerVo.getUserId());
        bo.setBusyType(BusyType.Answer.name());

        if (bo.getTopReplyUser() != null && bo.getTopReplyUser() > 0L) {
            bo.setTopType(BusyType.Question.name());
            bo.setBusyCoverImage(sohuQuestion.getCoverImage());
            SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildComment(bo, bo.getReplyUser(), NoticeInteractEnum.commentReceive.name());
            sohuInteractNoticeService.insertByBo(interactNoticeReceive);
        } else {
            if (!Objects.equals(bo.getReplyUser(), bo.getOperateUser())) {
                // 判断是不是自己回复自己的评论

                bo.setTopType(BusyType.Question.name());
                bo.setBusyCoverImage(sohuQuestion.getCoverImage());
                SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildComment(bo, bo.getReplyUser(), NoticeInteractEnum.commentReceive.name());
                sohuInteractNoticeService.insertByBo(interactNoticeReceive);
            }

            // 操作者收到
            bo.setBusyCoverImage(sohuQuestion.getCoverImage());
            SohuInteractNoticeBo interactNoticeSend = SohuInteractNoticeBo.buildComment(bo, bo.getOperateUser(), NoticeInteractEnum.commentSend.name());
            sohuInteractNoticeService.insertByBo(interactNoticeSend);
        }

        // 删除问题的拓展数据缓存
        sohuQoraInfoService.evictObj(bo.getBusyCode(), bo.getBusyType());
        return result;
    }

    private void buildRelate(SohuQuestionAnswerVo answerVo) {
        //回答关联项目
        SohuQoraRelate relate = sohuQoraRelateService.getByQora(answerVo.getId(), BusyType.Answer.name());
        if (Objects.isNull(relate)) {
            return;
        }
        if (StrUtil.equalsAnyIgnoreCase(relate.getBusyType(), BusyType.GoodsWindow.name())) {
            //橱窗商品
            RelationRespVo relationRespVO = new RelationRespVo();
            relationRespVO.setBusyCode(relate.getBusyCode());
            relationRespVO.setBusyType(BusyType.GoodsWindow.name());
            relationRespVO.setBusyTitle(relate.getBusyTitle());
            relationRespVO.setBusyInfo(relate.getBusyInfo());
            answerVo.setRelation(relationRespVO);
        } else if (StrUtil.equalsAnyIgnoreCase(relate.getBusyType(), BusyType.Window.name())) {
            //橱窗
            RelationRespVo relationRespVO = new RelationRespVo();
            relationRespVO.setBusyCode(relate.getBusyCode());
            relationRespVO.setBusyType(BusyType.Window.name());
            relationRespVO.setBusyTitle(relate.getBusyTitle());
            answerVo.setRelation(relationRespVO);
        } else if (StrUtil.equalsAnyIgnoreCase(relate.getBusyType(), BusyType.BusyTask.name())) {
            //关联任务
            RelationRespVo relationRespVO = new RelationRespVo();
            relationRespVO.setBusyCode(relate.getBusyCode());
            relationRespVO.setBusyType(BusyType.BusyTask.name());
            relationRespVO.setBusyTitle(relate.getBusyTitle());
            relationRespVO.setBusyInfo(relate.getBusyInfo());
            relationRespVO.setChildTaskNumber(relate.getBusyInfo());
            answerVo.setRelation(relationRespVO);
        } else if (StrUtil.equalsAnyIgnoreCase(relate.getBusyType(), BusyType.Video.name())) {
            SohuVideoVo videoVo = sohuVideoMapper.selectVoById(relate.getBusyCode());
            if (Objects.isNull(videoVo)) {
                return;
            }
            RelationRespVo relation = new RelationRespVo();
            relation.setBusyCode(relate.getBusyCode());
            relation.setBusyType(BusyType.Video.name());
            relation.setBusyTitle(videoVo.getTitle());
            relation.setBusyBelonger(videoVo.getUserId());
            relation.setBusyCoverImg(videoVo.getCoverImage());
            relation.setBusyPublishTime(DateUtil.format(videoVo.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
            buildBelonged(relation, videoVo.getUserId());
            answerVo.setRelation(relation);
        }
    }

    /**
     * 设置用户头像，名称
     */
    public void buildBelonged(RelationRespVo relation, Long userId) {
        LoginUser loginUser = sohuUserService.selectById(userId);
        if (Objects.nonNull(loginUser)) {
            relation.setBelongerAvatar(loginUser.getAvatar());
            relation.setBelongerName(loginUser.getUsername());
        }
    }

}
