package com.sohu.middleService.service.ai.impl;

import cn.hutool.core.bean.BeanUtil;
import com.sohu.common.core.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.middle.api.bo.ai.SohuPracticeBo;
import com.sohu.middle.api.vo.ai.SohuPracticeVo;
import com.sohu.middleService.domain.ai.SohuPractice;
import com.sohu.middleService.mapper.ai.SohuPracticeMapper;
import com.sohu.middleService.service.ai.ISohuPracticeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Collection;

/**
 * 数据训练Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@RequiredArgsConstructor
@Service
public class SohuPracticeServiceImpl implements ISohuPracticeService {

    private final SohuPracticeMapper baseMapper;

    /**
     * 查询数据训练
     */
    @Override
    public SohuPracticeVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询数据训练列表
     */
    @Override
    public List<SohuPracticeVo> queryList(SohuPracticeBo bo) {
        LambdaQueryWrapper<SohuPractice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuPractice> buildQueryWrapper(SohuPracticeBo bo) {
        LambdaQueryWrapper<SohuPractice> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuPractice::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestion()), SohuPractice::getQuestion, bo.getQuestion());
        lqw.eq(StringUtils.isNotBlank(bo.getAnswer()), SohuPractice::getAnswer, bo.getAnswer());
        lqw.eq(bo.getPracticeType() != null, SohuPractice::getPracticeType, bo.getPracticeType());
        lqw.eq(bo.getIsPractice() != null, SohuPractice::getIsPractice, bo.getIsPractice());
        lqw.eq(StringUtils.isNotBlank(bo.getPracticeExt()), SohuPractice::getPracticeExt, bo.getPracticeExt());
        return lqw;
    }

    /**
     * 新增数据训练
     */
    @Override
    public Boolean insertByBo(SohuPracticeBo bo) {
        SohuPractice add = BeanUtil.toBean(bo, SohuPractice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改数据训练
     */
    @Override
    public Boolean updateByBo(SohuPracticeBo bo) {
        SohuPractice update = BeanUtil.toBean(bo, SohuPractice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuPractice entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除数据训练
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<SohuPracticeVo> listByUserIdAndIsPractice(Long userId, Boolean isPractice) {
        SohuPracticeBo sohuPracticeBo = new SohuPracticeBo();
        sohuPracticeBo.setUserId(userId);
        sohuPracticeBo.setIsPractice(isPractice);
        return this.queryList(sohuPracticeBo);
    }
}
