package com.sohu.middleService.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuBillRecordBo;
import com.sohu.middle.api.bo.SohuBillRecordQueryBo;
import com.sohu.middle.api.bo.SohuMerBillRecordBo;
import com.sohu.middle.api.vo.*;
import com.sohu.middleService.domain.SohuBillRecord;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 用户账单记录Service接口
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
public interface ISohuBillRecordService extends ISohuBaseService<SohuBillRecord, SohuBillRecordVo> {

    /**
     * 查询用户账单记录
     */
    SohuBillRecordVo queryById(Long id);

    /**
     * 查询用户账单记录列表
     */
    TableDataInfo<SohuBillRecordVo> queryPageList(SohuBillRecordBo bo, PageQuery pageQuery);

    /**
     * 查询用户账单记录列表
     */
    List<SohuBillRecordVo> queryList(SohuBillRecordBo bo);

    /**
     * 修改用户账单记录
     */
    Boolean insertByBo(SohuBillRecordBo bo);

    /**
     * 修改用户账单记录
     */
    Boolean updateByBo(SohuBillRecordBo bo);

    /**
     * 校验并批量删除用户账单记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询用户今日收益
     *
     * @param loginUserId
     * @return
     */
    List<SohuBillRecordVo> selectTodayIncome(Long loginUserId);

    /**
     * 查询用户昨日收益
     *
     * @param loginUserId
     * @return
     */
    List<SohuBillRecordVo> selectYesterdayIncome(Long loginUserId);

    /**
     * 查询累计预付款
     *
     * @param loginUserId
     * @return
     */
    List<SohuBillRecordVo> prepaymentsList(Long loginUserId);

    /**
     * 更新商单预付款成功付款
     *
     * @param orderId
     * @return
     */
    Boolean updateBusyOrderSuccess(Long orderId);

    /**
     * 更新发单方支付酬金成功付款
     *
     * @param receiveId
     */
    Boolean updateBusyOrderReceiveSuccess(Long receiveId);

    /**
     * 查询已经用商单预付款支付的金额总和
     *
     * @param orderId 商单ID
     * @return
     */
    BigDecimal sumAlreadyPrepay(Long orderId);

    List<SohuBillRecordVo> selectYesterdayWithdrawal(Long loginUserId);

    /**
     * 查询7天 15天 或者是30天的收益和提现
     *
     * @param day
     */
    List<BigDecimal> selectAmountList(Integer day, String transactionType, Long loginUserId);

    IPage<SohuUserBillInfoVo> billInfoList(String startDate, String endDate, Long loginUserId, PageQuery pageQuery);

    /**
     * 站长今日/累计收益
     */
    SohuInComeVo income();

    /**
     * 站点内容数
     */
    Long contentNum(Long siteId);

    /**
     * 站长收益
     */
    List<SohuInComeListVo> incomeList(Long days);

    /**
     * 站长收益-按时间
     */
    List<SohuInComeListVo> incomeListByDate(String date);

    /**
     * 站长销售额排名
     */
    List<SohuSalesRankVo> salesRank();

    /**
     * 通过Id集合查询体现记录
     * @param billIds
     * @return
     */
    List<SohuBillRecordVo> queryWithdrawalByIds(List<Long> billIds);

    /**
     * 同步更新翼码流水号
     * @param billIds
     * @return
     */
    Boolean updatePayNumberByIds(List<Long> billIds,String posSeq);

    /**
     * 更新状态
     * @param posSeq
     * @param status
     * @return
     */
    Boolean updateStatusByPayNumber(String posSeq,String status);


    /**
     * 根据tradeNo更新提现记录状态
     * @param posSeq
     * @param newTradeNo
     * @param status
     * @return
     */
    Boolean updateStatusByTradeNo(String posSeq,String newTradeNo,String status);

    /**
     * 发起翼码提现
     * @param posSeq
     * @param isRetry 是否重新发起
     * @return
     */
    Boolean sendYmWithdrawal(String posSeq,Boolean isRetry);

    /**
     * 财务提现记录
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuBillRecordVo> withdrawalList(SohuBillRecordQueryBo bo, PageQuery pageQuery);

    /**
     * 提现状态定时任务
     * @return
     */
    Boolean billRecordJobHandler();

    /**
     * 提现详情
     * @param tradeNo
     * @return
     */
    SohuBillRecordVo withdrawDetail(String tradeNo);

    /**
     * 根据taskNumber查询提现记录
     * @param taskNumber
     * @return
     */
    SohuBillRecordVo queryByTaskNumber(String taskNumber,Long userId);

    /**
     * 通过用户类型获取用户提现总额
     * @param userId
     * @param siteType
     * @param siteId
     * @param withdrawalUserType
     * @return
     */
    BigDecimal sumWithdrawalByUserType(Long userId, Integer siteType, Long siteId, String withdrawalUserType, Date startTime, Date endTime);
}
