package com.sohu.middleService.service.diy.impl;

import cn.hutool.core.bean.BeanUtil;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.diy.SohuMicroPageBo;
import com.sohu.middle.api.vo.diy.SohuMicroPageVo;
import com.sohu.middleService.domain.diy.SohuMicroPage;
import com.sohu.middleService.mapper.diy.SohuMicroPageMapper;
import com.sohu.middleService.service.diy.ISohuMicroPageService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 装修微页面Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@RequiredArgsConstructor
@Service
public class SohuMicroPageServiceImpl implements ISohuMicroPageService {

    private final SohuMicroPageMapper baseMapper;

    /**
     * 查询装修微页面
     */
    @Override
    public SohuMicroPageVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询装修微页面列表
     */
    @Override
    public TableDataInfo<SohuMicroPageVo> queryPageList(SohuMicroPageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuMicroPage> lqw = buildQueryWrapper(bo);
        lqw.eq(SohuMicroPage::getUserId, LoginHelper.getUserId());
        Page<SohuMicroPageVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询装修微页面列表
     */
    @Override
    public List<SohuMicroPageVo> queryList(SohuMicroPageBo bo) {
        LambdaQueryWrapper<SohuMicroPage> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuMicroPage> buildQueryWrapper(SohuMicroPageBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuMicroPage> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), SohuMicroPage::getName, bo.getName());
        lqw.eq(bo.getSceneType() != null, SohuMicroPage::getSceneType, bo.getSceneType());
        lqw.eq(StringUtils.isNotBlank(bo.getConfig()), SohuMicroPage::getConfig, bo.getConfig());
        lqw.eq(bo.getIsShare() != null, SohuMicroPage::getIsShare, bo.getIsShare());
        lqw.eq(bo.getStatus() != null, SohuMicroPage::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增装修微页面
     */
    @Override
    public Boolean insertByBo(SohuMicroPageBo bo) {
        SohuMicroPage add = BeanUtil.toBean(bo, SohuMicroPage.class);
        validEntityBeforeSave(add);
        add.setUserId(LoginHelper.getUserId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改装修微页面
     */
    @Override
    public Boolean updateByBo(SohuMicroPageBo bo) {
        SohuMicroPage update = BeanUtil.toBean(bo, SohuMicroPage.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuMicroPage entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除装修微页面
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean copy(Long id) {
        SohuMicroPage sohuMicroPage = baseMapper.selectById(id);
        if (Objects.isNull(sohuMicroPage)) {
            throw new ServiceException("复制的微页面不存在，请刷新后再试");
        }
        sohuMicroPage.setId(null);
        sohuMicroPage.setLastPublishTime(new Date());
        sohuMicroPage.setIsShare(0L);
        sohuMicroPage.setStatus(1L);
        return baseMapper.insert(sohuMicroPage) > 0;
    }
}
