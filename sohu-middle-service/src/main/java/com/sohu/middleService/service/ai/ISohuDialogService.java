package com.sohu.middleService.service.ai;


import com.sohu.middle.api.bo.ai.SohuDialogBo;
import com.sohu.middle.api.vo.ai.SohuDialogInfoVo;
import com.sohu.middle.api.vo.ai.SohuDialogVo;

import java.util.Collection;
import java.util.List;

/**
 * 对话框Service接口
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
public interface ISohuDialogService {

    /**
     * 查询对话框
     */
    SohuDialogVo queryById(Long id);

    /**
     * 修改对话框
     */
    Boolean insertByBo(SohuDialogBo bo);

    /**
     * 修改对话框
     */
    Boolean updateByBo(SohuDialogBo bo);

    /**
     * 校验并批量删除对话框信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取个人会话明细
     *
     * @return
     */
    SohuDialogInfoVo getDialogInfo(String busyType);

    /**
     * 查询用户最近的一条会话
     *
     * @param userId
     * @return
     */
    SohuDialogVo getNearDialog(Long userId);

    /**
     * 基于dialogName 查询相关会话
     *
     * @param dialogName
     * @param busyType
     * @return
     */
    List<SohuDialogVo> listByDialogName(String dialogName, String busyType);
}
