package com.sohu.middleService.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuBillRecordBo;
import com.sohu.middle.api.bo.SohuMerBillRecordBo;
import com.sohu.middle.api.vo.SohuMerBillRecordVo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/11/8 14:55
 */
public interface ISohuMerBillRecordService {

    /**
     * 插入流水记录
     * @param bo
     * @return
     */
    Boolean insertMerRecord(SohuMerBillRecordBo bo);

    /**
     * 更新流水记录
     * @param bo
     * @return
     */
    Boolean updateMerByBo(SohuMerBillRecordBo bo);

    /**
     * 查询商户提现的总额
     * @param userId
     * @param merId
     * @return
     */
    BigDecimal totalWithdrawalAmount(Long userId, Long merId);

    /**
     * 查询商户提现的流水记录
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuMerBillRecordVo> queryPageMerBillList(SohuMerBillRecordBo bo, PageQuery pageQuery);
}
