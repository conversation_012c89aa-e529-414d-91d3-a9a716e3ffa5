package com.sohu.middleService.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuVideoInfoBo;
import com.sohu.middle.api.vo.SohuVideoInfoVo;
import com.sohu.middleService.domain.SohuVideoInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 视频拓展信息Service接口
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
public interface ISohuVideoInfoService extends ISohuBaseService<SohuVideoInfo,SohuVideoInfoVo> {

//    /**
//     * 查询视频拓展信息
//     */
//    SohuVideoInfoVo queryById(Long id);
//
    /**
     * 查询视频拓展信息列表
     */
    TableDataInfo<SohuVideoInfoVo> queryPageList(SohuVideoInfoBo bo, PageQuery pageQuery);

    /**
     * 查询视频拓展信息列表
     */
    List<SohuVideoInfoVo> queryList(SohuVideoInfoBo bo);

    /**
     * 修改视频拓展信息
     */
    Boolean insertByBo(SohuVideoInfoBo bo);

    /**
     * 修改视频拓展信息
     */
    Boolean updateByBo(SohuVideoInfoBo bo);
//
//    /**
//     * 校验并批量删除视频拓展信息信息
//     */
//    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询视频拓展信息
     */
    SohuVideoInfoVo queryByVideoId(Long videoId);

    /**
     * 查询视频拓展信息
     */
    SohuVideoInfo queryEntityByVideoId(Long videoId);


    /**
     * 批量查询视频拓展信息，返回map
     */
    Map<Long, SohuVideoInfoVo> selectMap(Collection<Long> videoIds);

    /**
     * 删除视频拓展数据
     *
     * @param ids
     */
    void deleteVideoIds(Collection<Long> ids);

    /**
     * 清除视频拓展缓存数据
     * @param videoId 视频ID
     */
    void evictByVideoId(Long videoId);
}
