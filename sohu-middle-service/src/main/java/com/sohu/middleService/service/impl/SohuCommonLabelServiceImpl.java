package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuCommonLabelBo;
import com.sohu.middle.api.bo.SohuCommonLabelListBo;
import com.sohu.middle.api.vo.SohuCommonLabelListVo;
import com.sohu.middle.api.vo.SohuCommonLabelVo;
import com.sohu.middleService.domain.SohuCommonLabel;
import com.sohu.middleService.mapper.SohuCommonLabelMapper;
import com.sohu.middleService.service.ISohuCommonLabelService;
import com.sohu.middleService.service.ISohuUserLabelRelationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 通用标签Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@RequiredArgsConstructor
@Service
public class SohuCommonLabelServiceImpl implements ISohuCommonLabelService {

    private final SohuCommonLabelMapper baseMapper;
    private final ISohuUserLabelRelationService iSohuUserLabelRelationService;

    @Override
    public List<SohuCommonLabelListVo> queryPageList() {
        return baseMapper.selectVoList(Wrappers.emptyWrapper(), SohuCommonLabelListVo.class);
    }

    /**
     * 查询通用标签列表
     */
    @Override
    public TableDataInfo<SohuCommonLabelVo> queryPageList(SohuCommonLabelListBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuCommonLabel> lqw = buildQueryWrapper(bo);
        Page<SohuCommonLabelVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        if (result.getRecords().isEmpty()) {
            return TableDataInfoUtils.build();
        }
        List<Long> labelIds = new ArrayList<>();
        for (SohuCommonLabelVo vo : result.getRecords()) {
            labelIds.add(vo.getId());
        }
        // 获取关联用户数
        Map<Long, Long> relationUserCountMap = iSohuUserLabelRelationService.queryRelationUserCount(labelIds);
        for (SohuCommonLabelVo vo : result.getRecords()) {
            Long relationUserCount = relationUserCountMap.get(vo.getId());
            if (relationUserCount!= null) {
                vo.setRelationUserCount(relationUserCount);
            }
        }
        return TableDataInfoUtils.build(result);
    }

    private LambdaQueryWrapper<SohuCommonLabel> buildQueryWrapper(SohuCommonLabelListBo bo) {
        LambdaQueryWrapper<SohuCommonLabel> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getLabelName()), SohuCommonLabel::getLabelName, bo.getLabelName());
        lqw.between(StringUtils.isNotBlank(bo.getStartTime()) && StringUtils.isNotBlank(bo.getEndTime()), SohuCommonLabel::getUpdateTime, bo.getStartTime(), bo.getEndTime());
        return lqw;
    }

    /**
     * 新增通用标签
     */
    @Override
    public Boolean insertByBo(SohuCommonLabelBo bo) {
        SohuCommonLabel add = BeanUtil.toBean(bo, SohuCommonLabel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改通用标签
     */
    @Override
    public Boolean updateByBo(SohuCommonLabelBo bo) {
        SohuCommonLabel update = BeanUtil.toBean(bo, SohuCommonLabel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuCommonLabel entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除通用标签
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Map<Long, String> queryLabelNamesByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<SohuCommonLabelListVo> sohuCommonLabelListVos = baseMapper.queryLabelNamesByIds(ids);
        if (CollUtil.isEmpty(sohuCommonLabelListVos)) {
            return Collections.emptyMap();
        }
        Map<Long, String> labelNameMap = new HashMap<>();
        for (SohuCommonLabelListVo vo : sohuCommonLabelListVos) {
            labelNameMap.put(vo.getId(), vo.getLabelName());
        }
        return labelNameMap;
    }

    @Override
    public List<SohuCommonLabelListVo> queryLabelNameListByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return baseMapper.selectVoList(Wrappers.<SohuCommonLabel>lambdaQuery().in(SohuCommonLabel::getId, ids), SohuCommonLabelListVo.class);
    }
}
