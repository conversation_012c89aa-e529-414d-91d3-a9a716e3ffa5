package com.sohu.middleService.utils;

import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import org.apache.http.entity.ContentType;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 鬼手AI剪辑
 * 使用限制：6分钟以内，且文件大小不超过400MB
 * 文档地址：https://jollytoday.feishu.cn/docx/ItgYd4MDvooxFNx37DdcYxQ9n8c
 */
public class JollyAiUtils {

    /**
     * 视频翻译，中文语音转英文语音，英文字幕
     *
     * @param videoUrl   视频地址
     * @param sourceLang 视频中的原语种,中文-zh,英文-en
     * @param lang       要翻译成的目标语种
     * @return
     */
    public static String translateVideo(String videoUrl, String sourceLang, String lang) {
        Map<String, Object> params = new HashMap<>();
        params.put("callback", "");
        params.put("sourceLang", sourceLang);
        params.put("lang", lang);
        params.put("music", 0);
        params.put("musicRegion", "");//视频音乐  音乐区域
        params.put("needChineseOcclude", "");//自动识别视频中的文字并擦除,0 关闭 1 自动去除静止的文字 2 根据配置去文字 3 自动去除运动的文字
        params.put("needMask", 0);
        params.put("needMirror", 0);
        params.put("needRescale", 0);
        params.put("needTrim", 0);
        params.put("removeBgAudio", 1);//原视频声音处理 0 保留原视频背景音，仅删除人声部分音轨；1 原视频静音，删除原视频全部音轨
        params.put("resolution", "720p");//生成作品的分辨率, 默认为720p，支持以下分辨率,480p，720p ，1080p
        params.put("urls", Arrays.asList(videoUrl));
        params.put("voice", 1);
        params.put("wyNeedText", 1);//翻译后的字幕是否要合成到视频中 0否，1是
        params.put("wyVoiceParam", "{\"id\":62}");//配音角色和字幕配置
        String a = MD5.create().digestHex(MD5.create().digestHex(JSONUtil.toJsonStr(params)) + "8002dc7917924fc39243f7bb9e91dcbf");
        String body = HttpRequest.post("https://api.zhaoli.com/v-w-c/gateway/ve/work/translation")
                //.setProxy(proxy)
                .header("Content-type", "application/json")
                .header("AppKey", "f88e45e129164716ae79e3e050e79e8c")
                .header("AppSign", a)
                .body(JSONUtil.toJsonStr(params), ContentType.APPLICATION_JSON.getMimeType()).execute().body();

        return body;
    }

    public static void main(String[] args) {
        System.out.println(translateVideo("https://api.zhaoli.com/v-w-c/gateway/ve/work/translation" +
                "play/%40%E4%B8%88%E5%A4%AB%E7%9A%84%E5%8F%8D%E5%87%BB/02.mp4", "zh", "en"));

    }
}
