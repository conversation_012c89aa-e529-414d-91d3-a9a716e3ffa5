package com.sohu.middleService.dubbo;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuDislikeBo;
import com.sohu.middle.api.service.RemoteMiddleDislikeService;
import com.sohu.middle.api.vo.SohuDislikeVo;
import com.sohu.middleService.service.ISohuDislikeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 不感兴趣
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleDislikeServiceImpl implements RemoteMiddleDislikeService {

    private final ISohuDislikeService sohuDislikeService;

    @Override
    public SohuDislikeVo queryById(Long id) {
        return sohuDislikeService.queryById(id);
    }

    @Override
    public TableDataInfo<SohuDislikeVo> queryPageList(SohuDislikeBo bo, PageQuery pageQuery) {
        return sohuDislikeService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<SohuDislikeVo> queryList(SohuDislikeBo bo) {
        return sohuDislikeService.queryList(bo);
    }

    @Override
    public Boolean insertByBo(SohuDislikeBo bo) {
        return sohuDislikeService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(SohuDislikeBo bo) {
        return sohuDislikeService.updateByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return sohuDislikeService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public Boolean insertByDislike(SohuDislikeBo bo) {
        return sohuDislikeService.insertByDislike(bo);
    }

    @Override
    public List<SohuDislikeVo> selectByUserId(Long userId) {
        return sohuDislikeService.selectByUserId(userId);
    }
}
