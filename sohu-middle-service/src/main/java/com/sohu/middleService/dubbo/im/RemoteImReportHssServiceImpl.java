package com.sohu.middleService.dubbo.im;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.im.*;
import com.sohu.middle.api.service.im.RemoteImReportHssService;
import com.sohu.middle.api.service.im.RemoteImReportService;
import com.sohu.middle.api.vo.im.SohuImReportHssVo;
import com.sohu.middle.api.vo.im.SohuImReportVo;
import com.sohu.middleService.service.im.ISohuImReportHssService;
import com.sohu.middleService.service.im.ISohuImReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * IM举报信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-23
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteImReportHssServiceImpl implements RemoteImReportHssService {

    private final ISohuImReportHssService sohuImReportHssService;


    @Override
    public SohuImReportHssVo queryById(Long id) {
        return sohuImReportHssService.queryById(id);
    }

    @Override
    public TableDataInfo<SohuImReportHssVo> queryPageList(SohuImReportHssBo bo, PageQuery pageQuery) {
        return sohuImReportHssService.queryPageList(bo,pageQuery);
    }

    @Override
    public TableDataInfo<SohuImReportHssVo> queryPageListOfAudit(SohuImReportHssQueryBo bo, PageQuery pageQuery) {
        return sohuImReportHssService.queryPageListOfAudit(bo,pageQuery);
    }

    @Override
    public List<SohuImReportHssVo> queryList(SohuImReportHssBo bo) {
        return sohuImReportHssService.queryList(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return sohuImReportHssService.deleteWithValidByIds(ids,isValid);
    }

    @Override
    public Boolean addReport(SohuImReportAppBo bo) {
        return sohuImReportHssService.addReport(bo);
    }

    @Override
    public Boolean audit(SohuImReportAuditBo bo) {
        return sohuImReportHssService.audit(bo);
    }
}
