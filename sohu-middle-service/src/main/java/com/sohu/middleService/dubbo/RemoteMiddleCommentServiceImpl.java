package com.sohu.middleService.dubbo;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuCommentBo;
import com.sohu.middle.api.service.RemoteMiddleCommentService;
import com.sohu.middle.api.vo.SohuCommentVo;
import com.sohu.middleService.service.ISohuCommentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleCommentServiceImpl implements RemoteMiddleCommentService {

    private final ISohuCommentService sohuCommentService;

    /**
     * 审核评论列表
     *
     * @param bo
     * @param pageQuery 分页参数
     * @return {@link TableDataInfo}
     */
    @Override
    public TableDataInfo<SohuCommentVo> queryListBySiteId(SohuCommentBo bo, PageQuery pageQuery) {
        return sohuCommentService.queryListBySiteId(bo, pageQuery);
    }

    /**
     * 审核或拒绝
     *
     * @param bo
     * @return {@link Boolean}
     */
    @Override
    public Boolean operate(SohuCommentBo bo) {
        return sohuCommentService.operate(bo);
    }

    /**
     * 置顶
     *
     * @param bo
     */
    @Override
    public void asTop(SohuCommentBo bo) {
        sohuCommentService.asTop(bo);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @param isValid
     * @return {@link Boolean}
     */
    @Override
    public Boolean deleteWithValidByIds(List<Long> ids, Boolean isValid) {
        return sohuCommentService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public SohuCommentVo insertBo(SohuCommentBo bo) {
        return sohuCommentService.insertBo(bo);
    }

}
