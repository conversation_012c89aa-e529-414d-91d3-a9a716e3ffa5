package com.sohu.middleService.dubbo.notice;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuMcnNoticeBo;
import com.sohu.middle.api.bo.notice.SohuTaskNoticeBo;
import com.sohu.middle.api.service.notice.RemoteMiddleMcnNoticeService;
import com.sohu.middle.api.vo.notice.SohuMcnNoticeVo;
import com.sohu.middleService.service.ISohuMcnNoticeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleMcnNoticeServiceImpl implements RemoteMiddleMcnNoticeService {

    private final ISohuMcnNoticeService sohuMcnNoticeService;

    @Override
    public TableDataInfo<SohuMcnNoticeVo> queryPageList(SohuMcnNoticeBo bo, PageQuery pageQuery) {
        return sohuMcnNoticeService.queryPageList(bo, pageQuery);
    }

    @Override
    public void sendMCNNotice(SohuMcnNoticeBo bo) {
        sohuMcnNoticeService.sendMCNNotice(bo);
    }
}
