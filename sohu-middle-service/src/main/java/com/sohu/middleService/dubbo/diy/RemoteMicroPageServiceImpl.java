package com.sohu.middleService.dubbo.diy;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.diy.SohuMicroPageBo;
import com.sohu.middle.api.service.diy.RemoteMicroPageService;
import com.sohu.middle.api.vo.diy.SohuMicroPageVo;
import com.sohu.middleService.service.diy.ISohuMicroPageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 装修微页面
 *
 * @Author: leibo
 * @Date: 2025/4/15 15:37
 **/
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMicroPageServiceImpl implements RemoteMicroPageService {

    private final ISohuMicroPageService microPageService;

    @Override
    public TableDataInfo<SohuMicroPageVo> queryPageList(SohuMicroPageBo bo, PageQuery pageQuery) {
        return microPageService.queryPageList(bo, pageQuery);
    }

    @Override
    public Long insertByBo(SohuMicroPageBo bo) {
        microPageService.insertByBo(bo);
        return bo.getId();
    }

    @Override
    public Long updateByBo(SohuMicroPageBo bo) {
        microPageService.updateByBo(bo);
        return bo.getId();
    }

    @Override
    public Boolean remove(List<Long> ids) {
        return microPageService.deleteWithValidByIds(ids, true);
    }

    @Override
    public Boolean copy(Long id) {
        return microPageService.copy(id);
    }
}
