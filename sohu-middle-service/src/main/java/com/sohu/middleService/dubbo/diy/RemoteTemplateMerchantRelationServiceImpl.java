package com.sohu.middleService.dubbo.diy;

import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.JsonUtils;
import com.sohu.middle.api.bo.diy.SohuTemplateMerchantRelationBo;
import com.sohu.middle.api.bo.diy.SohuTemplateRelationAddBo;
import com.sohu.middle.api.service.diy.RemoteTemplateMerchantRelationService;
import com.sohu.middle.api.vo.diy.SohuDiyTemplateVo;
import com.sohu.middle.api.vo.diy.SohuTemplateMerchantRelationVo;
import com.sohu.middle.api.vo.diy.SohuTemplateRelationVo;
import com.sohu.middleService.service.diy.ISohuDiyTemplateService;
import com.sohu.middleService.service.diy.ISohuTemplateMerchantRelationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 模版关联信息
 *
 * @Author: leibo
 * @Date: 2025/4/16 16:31
 **/
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteTemplateMerchantRelationServiceImpl implements RemoteTemplateMerchantRelationService {

    private final ISohuTemplateMerchantRelationService templateMerchantRelationService;
    private final ISohuDiyTemplateService diyTemplateService;

    @DubboReference
    private RemoteMerchantService remoteMerchantService;

    @Override
    public SohuTemplateRelationVo getRelationByTemplateId(Long id) {
        List<SohuTemplateMerchantRelationVo> relationList = templateMerchantRelationService.queryListByTemplateId(id);
        if (CollectionUtils.isEmpty(relationList)) {
            return null;
        }
        SohuTemplateRelationVo relationVo = new SohuTemplateRelationVo();
        relationVo.setTemplateId(id);
        // 1. 获取去重的商户ID数组
        List<Long> merchantIds = relationList.stream()
                .map(SohuTemplateMerchantRelationVo::getMerchantId)
                // 过滤可能的null值
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(merchantIds)) {
            Map<Long, SohuMerchantModel> merchantMap = remoteMerchantService.selectMapByIds(merchantIds);
            List<SohuTemplateRelationVo.MerchantVo> merchantList = new ArrayList<>();
            for (Long merchantId : merchantIds) {
                if (Objects.nonNull(merchantMap) && merchantMap.containsKey(merchantId)) {
                    SohuMerchantModel sohuMerchantModel = merchantMap.get(merchantId);
                    SohuTemplateRelationVo.MerchantVo merchantVo = new SohuTemplateRelationVo.MerchantVo();
                    merchantVo.setMerchantId(merchantId);
                    merchantVo.setMerchantName(sohuMerchantModel.getName());
                    merchantList.add(merchantVo);
                } else if (merchantId == 0L) {
                    SohuTemplateRelationVo.MerchantVo merchantVo = new SohuTemplateRelationVo.MerchantVo();
                    merchantVo.setMerchantId(merchantId);
                    merchantVo.setMerchantName("狐少少平台");
                    merchantList.add(merchantVo);
                }
            }
            relationVo.setMerchantList(merchantList);
        }
        // 2.获取去重的端口+版本号组合列表（PortVo对象列表）
        List<SohuTemplateRelationVo.PortVo> portList = relationList.stream()
                .map(vo -> {
                    // 处理版本号默认值
                    String version = (vo.getVersion() != null) ? vo.getVersion() : "1.0";
                    // 处理端口空值（根据业务需求选择是否过滤）
                    if (vo.getPort() == null) {
                        return null; // 或者保留空字符串，根据实际需求决定
                    }
                    SohuTemplateRelationVo.PortVo portVo = new SohuTemplateRelationVo.PortVo();
                    portVo.setPort(vo.getPort());
                    portVo.setVersion(version);
                    return portVo;
                })
                // 过滤空值（如果允许空端口则去掉这行）
                .filter(Objects::nonNull)
                .distinct() // 根据PortVo的equals/hashCode去重
                .collect(Collectors.toList());
        relationVo.setPortList(portList);
        return relationVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addRelation(SohuTemplateRelationAddBo addBo) {
        // 基于模板id查询模版信息
        SohuDiyTemplateVo templateVo = diyTemplateService.queryById(addBo.getTemplateId());
        if (Objects.isNull(templateVo)) {
            throw new ServiceException("设置的模版不存在，请确认后再试");
        }
        log.info("SohuTemplateRelationAddBo:{}", JsonUtils.toJsonString(addBo));
        // 商户ids
        List<Long> merchantIds = addBo.getMerchantIds();
        // 端口及版本集合
        List<SohuTemplateRelationAddBo.PortVo> portList = addBo.getPortList();
        // 删除关联关系
        templateMerchantRelationService.deleteByTemplateId(addBo.getTemplateId());
        log.info("merchantIds:{}", JsonUtils.toJsonString(merchantIds));
        log.info("portList:{}", JsonUtils.toJsonString(portList));
        if (CollectionUtils.isNotEmpty(portList) && CollectionUtils.isNotEmpty(merchantIds)) {
            // 组装关联关系数据
            List<SohuTemplateMerchantRelationBo> relationBoList = this.buildRelation(addBo.getTemplateId(), merchantIds, portList);
            log.info("relationBoList:{}", JsonUtils.toJsonString(relationBoList));
            if (CollectionUtils.isEmpty(relationBoList)) {
                throw new ServiceException("数据异常，请确认后再试");
            }
            List<SohuTemplateMerchantRelationVo> relationVoList = this.duplicateCheck(relationBoList, addBo.getTemplateId());
            log.info("relationVoList:{}", JsonUtils.toJsonString(relationVoList));
            if (CollectionUtils.isNotEmpty(relationVoList)) {
                templateMerchantRelationService.deleteByIds(relationVoList.stream().map(SohuTemplateMerchantRelationVo :: getId).collect(Collectors.toList()));
            }
            // 存储关联关系
            templateMerchantRelationService.batchSave(relationBoList);
        }
        return Boolean.TRUE;
    }

    @Override
    public List<SohuTemplateMerchantRelationVo> check(SohuTemplateRelationAddBo addBo) {
        SohuDiyTemplateVo templateVo = diyTemplateService.queryById(addBo.getTemplateId());
        if (Objects.isNull(templateVo)) {
            throw new ServiceException("校验的模版不存在，请确认后再试");
        }
        // 商户ids
        List<Long> merchantIds = addBo.getMerchantIds();
        // 端口及版本集合
        List<SohuTemplateRelationAddBo.PortVo> portList = addBo.getPortList();
        if (CollectionUtils.isNotEmpty(portList) && CollectionUtils.isNotEmpty(merchantIds)) {
            List<SohuTemplateMerchantRelationBo> relationBoList = this.buildRelation(null, merchantIds, portList);
            if (CollectionUtils.isEmpty(relationBoList)) {
                throw new ServiceException("数据异常，请确认后再试");
            }
            return this.duplicateCheck(relationBoList, addBo.getTemplateId());
        }
        return new ArrayList<>();
    }

    /**
     * 组装构建对象
     *
     * @param merchantIds
     * @param portList
     * @return
     */
    private List<SohuTemplateMerchantRelationBo> buildRelation(Long templateId,
                                                               List<Long> merchantIds,
                                                               List<SohuTemplateRelationAddBo.PortVo> portList) {
        List<SohuTemplateMerchantRelationBo> relationList = new ArrayList<>();
        for (Long merchantId : merchantIds) {
            for (SohuTemplateRelationAddBo.PortVo portVo : portList) {
                if (portVo.getVersionList() == null) {
                    continue; // 跳过无版本的端口
                }
                String port = portVo.getPort();
                for (String version : portVo.getVersionList()) {
                    relationList.add(new SohuTemplateMerchantRelationBo(templateId, merchantId, port, version));
                }
            }
        }
        return relationList;
    }

    /**
     * 查重,返回重复值
     *
     * @param relationBoList
     * @param templateId
     * @return
     */
    private List<SohuTemplateMerchantRelationVo> duplicateCheck(List<SohuTemplateMerchantRelationBo> relationBoList, Long templateId) {
        // 循环校验,确认唯一值
        List<SohuTemplateMerchantRelationVo> relationList = new ArrayList<>();
        for (SohuTemplateMerchantRelationBo relationBo : relationBoList) {
            SohuTemplateMerchantRelationVo relationVo = templateMerchantRelationService.getByParam(relationBo.getMerchantId(), relationBo.getPort(), relationBo.getVersion());
            if (Objects.nonNull(relationVo) && !relationVo.getTemplateId().equals(templateId)) {
                relationList.add(relationVo);
            }
        }
        return relationList;
    }

}
