package com.sohu.middleService.dubbo;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuUserFollowBo;
import com.sohu.middle.api.bo.SohuUserFollowQueryBo;
import com.sohu.middle.api.bo.playlet.PlayletUserFollowQueryBo;
import com.sohu.middle.api.service.RemoteMiddleUserFollowService;
import com.sohu.middle.api.vo.SohuUserFollowVo;
import com.sohu.middle.api.vo.playlet.PlayletUserFollowVo;
import com.sohu.middleService.service.ISohuUserFollowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 行业分类
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleUserFollowServiceImpl implements RemoteMiddleUserFollowService {

    private final ISohuUserFollowService sohuUserFollowService;


    @Override
    public SohuUserFollowVo queryById(Long id) {
        return sohuUserFollowService.queryById(id);
    }

    @Override
    public TableDataInfo<SohuUserFollowVo> queryPageList(SohuUserFollowBo bo, PageQuery pageQuery) {
        return sohuUserFollowService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<SohuUserFollowVo> queryList(SohuUserFollowBo bo) {
        return sohuUserFollowService.queryList(bo);
    }

    @Override
    public Boolean insertByBo(SohuUserFollowBo bo) {
        return sohuUserFollowService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(SohuUserFollowBo bo) {
        return sohuUserFollowService.updateByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return sohuUserFollowService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public Long fansCount(Long loginUserId) {
        return sohuUserFollowService.fansCount(loginUserId);
    }

    @Override
    public Long followCount(Long loginUserId) {
        return sohuUserFollowService.followCount(loginUserId);
    }

    @Override
    public Boolean follow(SohuUserFollowBo bo) {
        return sohuUserFollowService.follow(bo);
    }

    @Override
    public List<SohuUserFollowVo> userFollows(Long userId, List<Long> focusUserIds) {
        return sohuUserFollowService.userFollows(userId, focusUserIds);
    }

    @Override
    public Map<Long, SohuUserFollowVo> mapUserFollows(Long userId, List<Long> focusUserIds) {
        return sohuUserFollowService.mapUserFollows(userId, focusUserIds);
    }

    @Override
    public List<SohuUserFollowVo> focusFans(List<Long> userIds, Long userId) {
        return sohuUserFollowService.focusFans(userIds, userId);
    }

    @Override
    public Map<Long, SohuUserFollowVo> mapFocusFans(List<Long> userIds, Long userId) {
        return sohuUserFollowService.mapFocusFans(userIds, userId);
    }

    @Override
    public Long addFansCount(Long userId) {
        return sohuUserFollowService.addFansCount(userId);
    }

    @Override
    public List<SohuUserFollowVo> friends(Long userId, String name, PageQuery pageQuery) {
        return sohuUserFollowService.friends(userId, name, pageQuery);
    }

    @Override
    public List<SohuUserFollowVo> friendIdList(Long userId) {
        return sohuUserFollowService.friendIdList(userId);
    }

    @Override
    public Boolean delete(Long userId, Long focusUserId) {
        return sohuUserFollowService.delete(userId, focusUserId);
    }

    @Override
    public TableDataInfo<SohuUserFollowVo> follows(SohuUserFollowQueryBo bo, PageQuery pageQuery) {
        return sohuUserFollowService.follows(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuUserFollowVo> fans(SohuUserFollowQueryBo bo, PageQuery pageQuery) {
        return sohuUserFollowService.fans(bo, pageQuery);
    }

    @Override
    public List<SohuUserFollowVo> setUserFansCount(List<SohuUserFollowVo> list, Long userId, boolean myFollow) {
        return sohuUserFollowService.setUserFansCount(list, userId, myFollow);
    }

    @Override
    public List<SohuUserFollowVo> eachFocusList(Long userId, Long focusUserId) {
        return sohuUserFollowService.eachFocusList(userId, focusUserId);
    }

    @Override
    public boolean eachFocus(Long userId, Long focusUserId) {
        return sohuUserFollowService.eachFocus(userId, focusUserId);
    }

    @Override
    public void updateFocusUser(Long focusUserId, String userName, String avatar) {
        sohuUserFollowService.updateFocusUser(focusUserId, userName, avatar);
    }

    @Override
    public List<SohuUserFollowVo> newFans() {
        return sohuUserFollowService.newFans();
    }

    @Override
    public TableDataInfo<PlayletUserFollowVo> getFollowListByPlayletUserId(PlayletUserFollowQueryBo bo, PageQuery pageQuery) {
        return sohuUserFollowService.getFollowListByPlayletUserId(bo, pageQuery);
    }

    @Override
    public Boolean followAuthor(Long playletUserId) {
        return sohuUserFollowService.followAuthor(playletUserId);
    }
}
