package com.sohu.middleService.dubbo.finance;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.finance.SohuShareFeedbackBindingBo;
import com.sohu.middle.api.bo.finance.SohuShareFeedbackBo;
import com.sohu.middle.api.bo.finance.SohuShareFeedbackQueryBo;
import com.sohu.middle.api.service.finance.RemoteMiddleShareFeedBackService;
import com.sohu.middle.api.vo.finance.SohuShareFeedbackStatVo;
import com.sohu.middle.api.vo.finance.SohuShareFeedbackVo;
import com.sohu.middleService.service.finance.ISohuShareFeedbackService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleShareFeedBackServiceImpl implements RemoteMiddleShareFeedBackService {
    
    private final ISohuShareFeedbackService iSohuShareFeedbackService;

    @Override
    public SohuShareFeedbackVo queryById(Long id) {
        return iSohuShareFeedbackService.queryById(id);
    }

    @Override
    public TableDataInfo<SohuShareFeedbackVo> queryPageListOfBinding(SohuShareFeedbackQueryBo bo, PageQuery pageQuery) {
        return iSohuShareFeedbackService.queryPageListOfBinding(bo, pageQuery);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return iSohuShareFeedbackService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public List<SohuShareFeedbackVo> batchMatchApply(Collection<Long> ids) {
        return iSohuShareFeedbackService.batchMatchApply(ids);
    }

    @Override
    public void bindingApply(List<SohuShareFeedbackBindingBo> boList) {
        iSohuShareFeedbackService.bindingApply(boList);
    }



    @Override
    public SohuShareFeedbackStatVo getShareStat() {
        return iSohuShareFeedbackService.getShareStat();
    }

    @Override
    public Boolean insertByBo(SohuShareFeedbackBo bo) {
        return iSohuShareFeedbackService.insertByBo(bo);
    }

    @Override
    public List<SohuShareFeedbackVo> queryList(SohuShareFeedbackBo bo) {
        return iSohuShareFeedbackService.queryList(bo);
    }
}
