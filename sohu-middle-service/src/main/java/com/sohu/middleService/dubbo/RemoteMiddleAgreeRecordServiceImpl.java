package com.sohu.middleService.dubbo;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuAgreeRecordBo;
import com.sohu.middle.api.service.RemoteMiddleAgreeRecordService;
import com.sohu.middle.api.vo.SohuAgreeRecordVo;
import com.sohu.middleService.service.ISohuAgreeRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleAgreeRecordServiceImpl implements RemoteMiddleAgreeRecordService {

    private final ISohuAgreeRecordService sohuAgreeRecordService;

    @Override
    public TableDataInfo<SohuAgreeRecordVo> queryPageList(SohuAgreeRecordBo bo, PageQuery pageQuery) {
        return sohuAgreeRecordService.queryPageList(bo, pageQuery);
    }

    @Override
    public Boolean insertByBo(SohuAgreeRecordBo bo) {
        return sohuAgreeRecordService.insertByBo(bo);
    }
}
