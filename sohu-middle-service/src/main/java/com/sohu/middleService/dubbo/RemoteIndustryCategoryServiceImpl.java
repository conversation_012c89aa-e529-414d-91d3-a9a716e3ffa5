package com.sohu.middleService.dubbo;

import cn.hutool.core.lang.tree.Tree;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuIndustryCategoryBo;
import com.sohu.middle.api.service.RemoteIndustryCategoryService;
import com.sohu.middle.api.vo.SohuIndustryCategoryVo;
import com.sohu.middleService.service.ISohuIndustryCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 行业分类
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteIndustryCategoryServiceImpl implements RemoteIndustryCategoryService {

    private final ISohuIndustryCategoryService sohuIndustryCategoryService;

    @Override
    public SohuIndustryCategoryVo queryById(Long id) {
        return sohuIndustryCategoryService.queryById(id);
    }

    @Override
    public TableDataInfo<SohuIndustryCategoryVo> queryPageList(SohuIndustryCategoryBo bo, PageQuery pageQuery) {
        return sohuIndustryCategoryService.queryPageList(bo,pageQuery);
    }

    @Override
    public List<SohuIndustryCategoryVo> queryList(SohuIndustryCategoryBo bo) {
        return sohuIndustryCategoryService.queryList(bo);
    }

    @Override
    public Boolean insertByBo(SohuIndustryCategoryBo bo) {
        return sohuIndustryCategoryService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(SohuIndustryCategoryBo bo) {
        return sohuIndustryCategoryService.updateByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return sohuIndustryCategoryService.deleteWithValidByIds(ids,isValid);
    }

    @Override
    public List<SohuIndustryCategoryVo> tree() {
        return sohuIndustryCategoryService.tree();
    }

    @Override
    public List<SohuIndustryCategoryVo> getEntryTreeOfInclude(Collection<Long> industryIds) {
        return sohuIndustryCategoryService.getEntryTreeOfInclude(industryIds);
    }

    @Override
    public List<SohuIndustryCategoryVo> getEntryTreeOfExclude(List<Long> industryIds) {
        return sohuIndustryCategoryService.getEntryTreeOfExclude(industryIds);
    }

    @Override
    public List<Tree<Long>> getAllTrees(Long platformIndustryId) {
        return sohuIndustryCategoryService.getAllTrees(platformIndustryId);
    }

    @Override
    public Map<Long, SohuIndustryCategoryVo> queryMap(Collection<Long> ids) {
        return sohuIndustryCategoryService.queryMap(ids);
    }

    @Override
    public List<String> getUserEntryAuth(List<Long> industryIds) {
        return sohuIndustryCategoryService.getUserEntryAuth(industryIds);
    }

    @Override
    public SohuIndustryCategoryVo queryRootById(Long childId) {
        return sohuIndustryCategoryService.queryRootById(childId);
    }

    @Override
    public SohuIndustryCategoryVo queryByName(String industryName) {
        return sohuIndustryCategoryService.queryByName(industryName);
    }
}
