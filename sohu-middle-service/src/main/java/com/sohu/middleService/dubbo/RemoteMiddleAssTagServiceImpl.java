package com.sohu.middleService.dubbo;

import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.PayTypeShow;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuAssTagBo;
import com.sohu.middle.api.bo.SohuAssTagRelateBo;
import com.sohu.middle.api.service.RemoteMiddleAssTagService;
import com.sohu.middle.api.vo.SohuAssTagVo;
import com.sohu.middleService.service.ISohuAssTagService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 行业分类
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleAssTagServiceImpl implements RemoteMiddleAssTagService {

    private final ISohuAssTagService sohuAssTagService;

    @Override
    public SohuAssTagVo queryById(Long id) {
        return sohuAssTagService.queryById(id);
    }

    @Override
    public TableDataInfo<SohuAssTagVo> queryPageList(SohuAssTagBo bo, PageQuery pageQuery) {
        return sohuAssTagService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<SohuAssTagVo> queryList(SohuAssTagBo bo) {
        return sohuAssTagService.queryList(bo);
    }

    @Override
    public Boolean insertByBo(SohuAssTagBo bo) {
        return sohuAssTagService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(SohuAssTagBo bo) {
        return sohuAssTagService.updateByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return sohuAssTagService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public void saveTagRelate(BusyType type, Long busyCode, List<Long> tags) {
        sohuAssTagService.saveTagRelate(type, busyCode, tags);
    }

    @Override
    public List<SohuAssTagVo> queryRelateList(SohuAssTagRelateBo bo) {
        return sohuAssTagService.queryRelateList(bo);
    }

    @Override
    public List<SohuAssTagVo> roleControlQuery(SohuAssTagBo bo) {
        return sohuAssTagService.roleControlQuery(bo);
    }

    @Override
    public List<SohuAssTagVo> selectBusyOrder(String type, List<String> idents) {
        return sohuAssTagService.selectBusyOrder(type, idents);
    }

    @Override
    public Map<Long, SohuAssTagVo> queryMap(Collection<Long> ids) {
        return sohuAssTagService.queryMap(ids);
    }

    @Override
    public List<PayTypeShow.Channel> payType(String channel) {
        return sohuAssTagService.payType(channel);
    }
}
