package com.sohu.middleService.dubbo;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuArticleRelateBo;
import com.sohu.middle.api.service.RemoteMiddleArticleRelateService;
import com.sohu.middle.api.vo.RelationRespVo;
import com.sohu.middle.api.vo.SohuArticleRelateVo;
import com.sohu.middleService.service.ISohuArticleRelateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 图文与关联项的关联
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleArticleRelateServiceImpl implements RemoteMiddleArticleRelateService {

    private final ISohuArticleRelateService sohuArticleRelateService;

    @Override
    public SohuArticleRelateVo queryById(Long id) {
        return sohuArticleRelateService.queryById(id);
    }

    @Override
    public TableDataInfo<SohuArticleRelateVo> queryPageList(SohuArticleRelateBo bo, PageQuery pageQuery) {
        return sohuArticleRelateService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<SohuArticleRelateVo> queryList(SohuArticleRelateBo bo) {
        return sohuArticleRelateService.queryList(bo);
    }

    @Override
    public Boolean insertByBo(SohuArticleRelateBo bo) {
        return sohuArticleRelateService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(SohuArticleRelateBo bo) {
        return sohuArticleRelateService.updateByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return sohuArticleRelateService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public SohuArticleRelateVo getByArticleId(Long articleId) {
        return sohuArticleRelateService.getByArticleId(articleId);
    }

    @Override
    public void insert(String relateType, Long busyCode) {
        sohuArticleRelateService.insert(relateType, busyCode);
    }

    @Override
    public void deleteByArticleIds(Collection<Long> ids) {
        sohuArticleRelateService.deleteByArticleIds(ids);
    }

    @Override
    public RelationRespVo buildRelationInfo(SohuArticleRelateVo relate) {
        return sohuArticleRelateService.buildRelationInfo(relate);
    }
}
