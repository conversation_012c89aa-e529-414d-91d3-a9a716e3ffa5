package com.sohu.middleService.dubbo.finance;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.finance.SohuShareOrderBo;
import com.sohu.middle.api.bo.finance.SohuShareOrderReqBo;
import com.sohu.middle.api.service.finance.RemoteMiddleShareOrderService;
import com.sohu.middle.api.vo.finance.SohuShareFeedbackVo;
import com.sohu.middle.api.vo.finance.SohuShareOrderVo;
import com.sohu.middleService.service.finance.ISohuShareOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 金融分享-订单
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleShareOrderServiceImpl implements RemoteMiddleShareOrderService {

    private final ISohuShareOrderService sohuShareOrderService;

    @Override
    public List<SohuShareOrderVo> getListByMasterNo(String masterOrderNo) {
        return sohuShareOrderService.getListByMasterNo(masterOrderNo);
    }

    @Override
    public Boolean updateBatchById(List<SohuShareOrderBo> list) {
        return sohuShareOrderService.updateBatchById(list);
    }

    @Override
    public void updateStateOfTimeout() {
        this.sohuShareOrderService.updateStateOfTimeout();
    }

    @Override
    public TableDataInfo<SohuShareOrderVo> queryPageList(SohuShareOrderBo bo, PageQuery pageQuery) {
        return sohuShareOrderService.queryPageList(bo, pageQuery);
    }

    @Override
    public Boolean insertByBo(SohuShareOrderBo bo) {
        return sohuShareOrderService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(SohuShareOrderBo bo) {
        return sohuShareOrderService.updateByBo(bo);
    }

    @Override
    public List<SohuShareFeedbackVo> preOrder(List<Long> feedbackIds) {
        return sohuShareOrderService.preOrder(feedbackIds);
    }

    @Override
    public Map<String, Object> createOrder(SohuShareOrderReqBo bo) {
        return sohuShareOrderService.createOrder(bo);
    }

    @Override
    public List<SohuShareFeedbackVo> queryPageListByMasterOrderId(Long masterOrderId) {
        return sohuShareOrderService.queryPageListByMasterOrderId(masterOrderId);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return sohuShareOrderService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public SohuShareOrderVo queryById(Long id) {
        return sohuShareOrderService.queryById(id);
    }
}
