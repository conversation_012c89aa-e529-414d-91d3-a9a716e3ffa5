package com.sohu.middleService.dubbo;

import com.sohu.common.core.domain.MsgContent;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.bo.playlet.PlayletOpenAdsQueryBo;
import com.sohu.middle.api.bo.playlet.PlayletPatchesAdsQueryBo;
import com.sohu.middle.api.enums.report.ActionTypeEnum;
import com.sohu.middle.api.enums.report.AdReportEnum;
import com.sohu.middle.api.service.RemoteMiddleAdInfoService;
import com.sohu.middle.api.vo.*;
import com.sohu.middle.api.vo.playlet.PlayletOpenAdsListVo;
import com.sohu.middle.api.vo.playlet.PlayletOpenAdsVo;
import com.sohu.middle.api.vo.playlet.PlayletPatchesAdsListVo;
import com.sohu.middle.api.vo.playlet.PlayletPatchesAdsVo;
import com.sohu.middleService.service.ISohuAdInfoService;
import com.sohu.middleService.service.ISohuEventReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;


/**
 * 广告远程服务
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleAdInfoServiceImpl implements RemoteMiddleAdInfoService {

    private final ISohuAdInfoService iSohuAdInfoService;
    private final ISohuEventReportService sohuEventReportService;

    @Override
    public SohuAdInfoVo queryById(Long id) {
        SohuAdInfoVo sohuAdInfoVo = iSohuAdInfoService.queryById(id);
        // 广告埋点
        SohuEventReportBo reportBo = new SohuEventReportBo();
        reportBo.setBusyType(ActionTypeEnum.Ad.name());
        reportBo.setEventType(AdReportEnum.GGDJ.getType());
        reportBo.setUserId(LoginHelper.getUserId());
        sohuEventReportService.getEventId(reportBo);
        return sohuAdInfoVo;
    }

    @Override
    public TableDataInfo<SohuAdInfoVo> queryPageList(SohuAdInfoBo bo, PageQuery pageQuery) {
        return iSohuAdInfoService.queryPageList(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuAdInfoVo> queryPageListV2(SohuAdInfoQueryBo bo, PageQuery pageQuery) {
        return iSohuAdInfoService.queryPageListV2(bo, pageQuery);
    }

    @Override
    public List<SohuAdInfoVo> queryList(SohuAdInfoBo bo) {
        return iSohuAdInfoService.queryList(bo);
    }

    @Override
    public Boolean insertByBo(SohuAdInfoBo bo) {
        return iSohuAdInfoService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(SohuAdInfoBo bo) {
        return iSohuAdInfoService.updateByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return iSohuAdInfoService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, String type) {
        return iSohuAdInfoService.deleteWithValidByIds(ids, type);
    }

    @Override
    public Boolean operate(SohuAdInfoBo bo) {
        return iSohuAdInfoService.operate(bo);
    }

    @Override
    public TableDataInfo<PlayletOpenAdsListVo> queryOpenAdsList(PlayletOpenAdsQueryBo bo, PageQuery pageQuery) {
        return iSohuAdInfoService.queryOpenAdsList(bo, pageQuery);
    }

    @Override
    public Boolean updateAdInfoState() {
        return iSohuAdInfoService.updateAdInfoState();
    }

    @Override
    public TableDataInfo<PlayletPatchesAdsListVo> queryPatchesAdsList(PlayletPatchesAdsQueryBo bo, PageQuery pageQuery) {
        return iSohuAdInfoService.queryPatchesAdsList(bo, pageQuery);
    }

    @Override
    public PlayletPatchesAdsVo queryPatchesById(Long id) {
        return iSohuAdInfoService.queryPatchesById(id);
    }

    @Override
    public PlayletOpenAdsVo queryOpenDetail() {
        return iSohuAdInfoService.queryOpenDetail();
    }

    @Override
    public PlayletPatchesAdsVo queryPatchesAdsByVideoId(Long videoId) {
        return iSohuAdInfoService.queryPatchesAdsByVideoId(videoId);
    }

    @Override
    public Boolean advertisementJobHandler() {
        return iSohuAdInfoService.advertisementJobHandler();
    }

    @Override
    public Boolean refreshAdvertisementCache(MsgContent msgContent) {
        return iSohuAdInfoService.refreshAdvertisementCache(msgContent);
    }

    @Override
    public TableDataInfo<SohuGuessYouLikeVo> queryPageListOfAirec(SohuGuessYouLikeQueryBo bo, PageQuery pageQuery) {
        return iSohuAdInfoService.queryPageListOfAirec(bo, pageQuery);
    }

    @Override
    public Boolean refreshAdInfoByAdPlace(String adPlace) {
        return iSohuAdInfoService.refreshAdInfoByAdPlace(adPlace);
    }

    @Override
    public Boolean refreshAdInfoTest() {
        return iSohuAdInfoService.refreshAdInfoTest();
    }

    @Override
    public TableDataInfo<SohuVideoAdInfoVo> queryPageListOfVideo(SohuVideoAdInfoQueryBo bo, PageQuery pageQuery) {
        return iSohuAdInfoService.queryPageListOfVideo(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuArticleAdInfoVo> queryPageListOfArticle(SohuArticleAdInfoQueryBo bo, PageQuery pageQuery) {
        return iSohuAdInfoService.queryPageListOfArticle(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuTaskSiteAdInfoVo> queryPageListOfTaskSite(SohuTaskSiteAdInfoQueryBo bo, PageQuery pageQuery) {
        return iSohuAdInfoService.queryPageListOfTaskSite(bo, pageQuery);
    }

    @Override
    public SohuAdInfoVo getRandomAd(List<Long> excludeIds, String adPlaceCode) {
        return iSohuAdInfoService.getRandomAd(excludeIds, adPlaceCode);
    }

    @Override
    public List<SohuAdInfoVo> getAdListByAdPlaceCode(String adPlaceCode) {
        return iSohuAdInfoService.getAdListByAdPlaceCode(adPlaceCode);
    }

    @Override
    public void offShelfAdInfoByGroupId(Long groupId) {
        iSohuAdInfoService.offShelfAdInfoByGroupId(groupId);
    }

    @Override
    public SohuArticleAdInfoVo queryArticleAdInfoDetail(SohuArticleAdInfoQueryBo bo) {
        return iSohuAdInfoService.queryArticleAdInfoDetail(bo);
    }
}
