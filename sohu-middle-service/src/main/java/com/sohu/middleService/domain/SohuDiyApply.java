package com.sohu.middleService.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * app申请私人定制审核对象 sohu_diy_apply
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@Data
@TableName("sohu_diy_apply")
public class SohuDiyApply extends SohuEntity implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     *
     */
    private Long userId;
    /**
     * 定制类型
     */
    private Long diyType;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 联系方式
     */
    private Long phoneNumber;
    /**
     * 申请说明
     */
    private String applyInstruction;
    /**
     * 状态;Delete:删除，WaitApprove：待审核,Pass：通过，Refuse：拒绝
     */
    private String state;
    /**
     * 拒绝理由
     */
    private String rejectReason;

    /**
     * 申请人姓名
     */
    private String personName;

    /**
     * 定制类型 APP应用 AI(AI)
     */
    private String diyCategory;

    /**
     * 行业
     */
    private String industry;

    /**
     * 意向度 HIGH高意向 MIDDLE中意向 LOW低意向
     */
    private String intentionality;

}
