package com.sohu.middleService.domain.im;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * IM租户服务配置对象 sohu_im_tenant_config
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_im_tenant_config")
public class SohuImTenantConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    private Long userId;
    /**
     * 租户ID
     */
    private String tenantId;
    /**
     * 邮箱配置json
     */
    private String mailConfig;
    /**
     * 短信配置json
     */
    private String smsConfig;
    /**
     * 极光推送配置json
     */
    private String jiguangConfig;
    /**
     * 音视频服务地址
     */
    private String avUrl;
    /**
     * 文件服务json
     */
    private String ossConfig;

    /**
     * im服务地址
     */
    private String imServerUrl;

    /**
     * im服务socket地址
     */
    private String imSocketUrl;

}
