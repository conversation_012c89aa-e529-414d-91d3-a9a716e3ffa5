package com.sohu.middleService.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * 图文与关联项的关联对象 sohu_article_relate
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@Data
@TableName("sohu_article_relate")
@BaseEntity.Cache(region = "SohuArticleRelate")
public class SohuArticleRelate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 图文id
     */
    private Long articleId;
    /**
     * 关联类型:[User:用户,Project:项目,Goods:商品,BusyOrder:商单,Shop:店铺,Address:地址]
     */
    private String busyType;
    /**
     * 业务id
     */
    private Long busyCode;
    /**
     *  推广标题
     */
    private String busyTitle;
    /**
     *  推广信息(如商品ids|逗号隔开)
     */
    private String busyInfo;
}
