package com.sohu.middleService.domain.im;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * im租户对象 sohu_im_tenant
 * <AUTHOR>
 * @date 2024/12/12 14:56
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_im_tenant")
public class SohuImTenant extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 服务器编码
     */
    private String serverCode;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 是否激活(0 否 1 是)
     */
    private Integer status;
    /**
     * 是否启用(0 否 1 是)
     */
    private Integer isEnable;
    /**
     * 注册数量
     */
    private Integer registerNum;

    /**
     * 有效期开始时间
     */
    private Date validStartTime;

    /**
     * 有效期结束时间
     */
    private Date validEndTime;

    /**
     * 修改服务器ID提交次数
     */
    private Integer submitNum;
}
