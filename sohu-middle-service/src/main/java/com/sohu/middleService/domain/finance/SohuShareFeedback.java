package com.sohu.middleService.domain.finance;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 金融分享-申请反馈对象 sohu_share_feedback
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_share_feedback")
public class SohuShareFeedback extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 客户姓名
     */
    private String customerName;
    /**
     * 客户证件号码	
     */
    private String customerCard;
    /**
     * 客户手机号
     */
    private String customerPhone;
    /**
     * 模板类型
     * {@link com.sohu.dao.enums.FinanceShare.ShareTemplateTypeEnum}
     */
    private String templateType;
    /**
     * 状态：DRAFT-未匹配，BINDING-有效，INVALID-失效
     * {@link com.sohu.dao.enums.FinanceShare.ShareFeedbackStatusEnum}
     */
    private String status;
    /**
     * 申请id
     */
    private Long applyId;
    /**
     * 进件时间
     */
    private Date entryTime;
    /**
     * 成功时间
     */
    private Date succeedTime;
    /**
     * 贷款金额
     */
    private BigDecimal amount;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 进件编号
     */
    private String entryCode;

    /**
     * 来源类型：SYS-系统；USER-用户
     * {@link com.sohu.dao.enums.FinanceShare.ShareSourceTypeEnum}
     */
    private String sourceType;

    /**
     * 产品类型：CREDIT-信用卡，LOANS-贷款
     * {@link com.sohu.dao.enums.FinanceShare.ShareProductTypeEnum}
     */
    private String productType;

    /**
     * 支付状态（UNPAID：待支付COMPLETED：已完成）
     * {@link com.sohu.dao.enums.FinanceShare.ShareFeedbackPayStatusEnum}
     */
    private String payStatus;

}
