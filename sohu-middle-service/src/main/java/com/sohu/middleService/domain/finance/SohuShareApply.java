package com.sohu.middleService.domain.finance;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 金融分享-申请对象 sohu_share_apply
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_share_apply")
public class SohuShareApply extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 分享id
     */
    private Long shareId;
    /**
     * 推广者id，如果是平台推广数填0
     */
    private Long shareUserId;
    /**
     * 商品价格（冗余）
     */
    private BigDecimal price;
    /**
     * 币种，默认CNY人名币—人民币（CNY）、美元（USD）、欧元（EUR）、日元（JPY）、英镑（GBP）、加拿大元（CAD）、澳大利亚元（AUD）、瑞士法郎（CHF）、瑞典克朗（SEK）、新西兰元（NZD）（冗余）
     */
    private String currency;
    /**
     * 申请人姓名
     */
    private String applicant;
    /**
     * 证件号码
     */
    private String identityNo;
    /**
     * 联系人电话
     */
    private String contactPhone;
    /**
     * 产品类型（冗余）
     * {@link com.sohu.dao.enums.FinanceShare.ShareProductTypeEnum}
     */
    private String productType;
    /**
     * 推广产品名称（冗余）
     */
    private String productName;
    /**
     * 状态：APPLY-申请中，BINDING-绑定
     * {@link com.sohu.dao.enums.FinanceShare.ShareStatusEnum}
     */
    private String status;

    /**
     * 来源类型：SYS-系统；USER-用户
     * {@link com.sohu.dao.enums.FinanceShare.ShareSourceTypeEnum}
     */
    private String sourceType;

    /**
     * 模板类型
     * {@link com.sohu.dao.enums.FinanceShare.ShareTemplateTypeEnum}
     */
    private String templateType;

    /**
     * 申请人姓名（密文）
     */
    private String applicantCipher;
    /**
     * 证件号码（密文）
     */
    private String identityNoCipher;
    /**
     * 联系人电话（密文）
     */
    private String contactPhoneCipher;

}
