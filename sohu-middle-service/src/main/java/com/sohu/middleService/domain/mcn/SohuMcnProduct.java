package com.sohu.middleService.domain.mcn;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import com.sohu.middle.api.enums.McnMemberType;
import lombok.Data;

import java.math.BigDecimal;

/**
 * MCN商品表
 */
@Data
@TableName(value = "sohu_mcn_product")
public class SohuMcnProduct extends SohuEntity {
    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品简介
     */
    private String productInfo;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 币种，默认CNY人名币—人民币（CNY）、美元（USD）、欧元（EUR）、日元（JPY）、英镑（GBP）、加拿大元（CAD）、澳大利亚元（AUD）、瑞士法郎（CHF）、瑞典克朗（SEK）、新西兰元（NZD）
     */
    private String currency;

    /**
     * 会员类型:NORMAL-普通版，GOLD-黄金版，DIAMOND-钻石版，FLAGSHIP-旗舰版
     * {@link McnMemberType}
     */
    private String memberType;

    /**
     * 有效期类型:MONTH-月，QUARTER-季，YEAR-年
     * {@link McnDateType}
     */
    private String useType;

    /**
     * 有效天数(30,90,365)
     */
    private Integer useDays;

    /**
     * 获得积分(预留字段)
     */
    private Long giveIntegral;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 是否回收站
     */
    private Boolean isRecycle;

    /**
     * 是否强制下架，0-否，1-是
     */
    private Boolean isForced;

    /**
     * 审核状态：WAITAPPROVE-待审核，PASS-审核成功，FALSE-审核拒绝
     */
    private String auditStatus;

    /**
     * 拒绝原因
     */
    private String reason;

    /**
     * 状态（0：未上架，1：上架）
     */
    private Boolean isShow;

    /**
     * 是否删除
     */
    private Boolean isDel;

    /**
     * 可绑定媒体账号数量
     */
    private Integer accountNum;

//    /**
//     * 创建时间
//     */
//    private Date createTime;
//
//    /**
//     * 更新时间
//     */
//    private Date updateTime;

    private static final long serialVersionUID = 1L;

}
