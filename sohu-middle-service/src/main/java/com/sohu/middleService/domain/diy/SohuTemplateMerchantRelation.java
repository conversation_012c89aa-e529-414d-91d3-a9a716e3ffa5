package com.sohu.middleService.domain.diy;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 装修模版与生效商户关联对象 sohu_template_merchant_relation
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_template_merchant_relation")
public class SohuTemplateMerchantRelation extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 模版id
     */
    private Long templateId;
    /**
     * 生效的商户id
     */
    private Long merchantId;
    /**
     * 端口  IOS、Android、applet
     */
    private String port;
    /**
     * 版本号 默认 1.0
     */
    private String version;

}
