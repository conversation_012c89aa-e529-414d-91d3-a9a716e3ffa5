package com.sohu.middleService.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位对象 sohu_ad_place
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@Data
@TableName("sohu_ad_place")
public class SohuAdPlace implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 广告位主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 广告位Code
     */
    private String placeCode;
    /**
     * 广告位所属页面
     */
    private String placePage;
    /**
     * 广告类型
     */
    private String type;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 端口.
     * {@link com.sohu.middle.api.enums.AdPlacePortEnum}
     */
    private String port;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
