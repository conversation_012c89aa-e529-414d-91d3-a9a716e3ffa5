package com.sohu.middleService.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 素材推广订单流水对象 sohu_material_promotion_order
 *
 * <AUTHOR>
 * @date 2024/9/24 10:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_material_promotion_order")
public class SohuMaterialPromotionOrder extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 分销人id
     */
    private Long shareUserId;
    /**
     * 被分销人id
     */
    private Long buyUserId;
    /**
     * 被分销人名称
     */
    private String buyUserName;
    /**
     * 站点id
     */
    private Long siteId;
    /**
     * 素材id
     */
    private Long materialId;
    /**
     * 素材类型(短剧(Playlet) 商品(Goods) 商单(BuyOrder))
     */
    private String materialType;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 第三方交易单号-唯一
     */
    private String tradeNo;
    /**
     * 第三方支付流水号
     */
    private String transactionId;
    /**
     * 实付金额-0.00
     */
    private BigDecimal payPrice;
    /**
     * 分账金额-0.00
     */
    private BigDecimal independentPrice;
    /**
     * 分账状态：0 未分账  1 已分账  2 分账处理中  3 分账异常  4 已退款
     */
    private Integer independentStatus;
    /**
     * 交易类型（按集付费(Episodes)，按剧付费(Drama)，充值付费(Recharge)）
     */
    private String tradeType;

}
