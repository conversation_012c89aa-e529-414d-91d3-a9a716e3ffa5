package com.sohu.middleService.domain.playlet;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 短剧广告关联对象
 *
 * <AUTHOR>
 * @date 2024-09-06
 */
@Data
@TableName("sohu_playlet_relate")
public class SohuPlayletRelate {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 关联id
     */
    private Long busyCode;
    /**
     * 关联类型（Video-剧集,GOODS-商品，BUSINESS-商单）
     */
    private String busyType;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 视频展示样式 0-横版 1-竖版
     */
    private Integer videoStyle;

    /**
     * 短剧id
     */
    private Long playletId;
    /**
     * 视频id
     */
    private Long videoId;
    /**
     * WaitShelf-待上架,OnShelf-上架,OffShelf-下架，Exceed-已过期
     */
    private String state;
    /**
     * 广告投放开始时间
     */
    private Date startTime;
    /**
     * 广告投放结束时间
     */
    private Date overTime;
    /**
     * 播放时间
     */
    private Long playTime;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    ;
}
