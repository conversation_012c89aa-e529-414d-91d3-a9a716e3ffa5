package com.sohu.middleService.domain.mcn;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * MCN订单表
 */
@Data
@TableName(value = "sohu_mcn_order")
public class SohuMcnOrder extends SohuEntity {
    /**
     * 订单ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商户订单号
     */
    private String orderNo;

    /**
     * 主订单号
     */
    private String masterOrderNo;

    /**
     * 支付单号-对应商户订单号-子单(预留字段)
     */
    private String payOrderNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 商品总价
     */
    private BigDecimal productTotalPrice;

    /**
     * 订单总价
     */
    private BigDecimal totalPrice;

    /**
     * 实际支付金额
     */
    private BigDecimal payPrice;

    /**
     * 优惠券id(预留字段)
     */
    private Long couponId;

    /**
     * 优惠券金额(预留字段)
     */
    private BigDecimal couponPrice;

    /**
     * 第三方服务费-0.00
     */
    private BigDecimal chargePrice;

    /**
     * 支付状态
     */
    private Boolean paid;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 支付方式:paypal
     */
    private String payType;

    /**
     * 支付积分(预留字段)
     */
    private Long payIntegral;

    /**
     * 订单状态（UNPAID：待支付COMPLETED：已完成，CANCEL：已取消）
     */
    private String status;

    /**
     * 退款状态：FALSE:-未退款 WAITAPPROVE:待审核-申请中  REFUNDREFUSE:审核未通过REFUNDING：退款中 REFUNDSUCCESS:已退款
     */
    private String refundStatus;

    /**
     * 购买方是否删除
     */
    private Boolean isBuyerDel;

    /**
     * 使用方用户是否删除
     */
    private Boolean isUseDel;

    /**
     * 是否用户取消
     */
    private Boolean isUserCancel;

//    /**
//     * 创建时间
//     */
//    private Date createTime;
//
//    /**
//     * 更新时间
//     */
//    private Date updateTime;

    private static final long serialVersionUID = 1L;

}
