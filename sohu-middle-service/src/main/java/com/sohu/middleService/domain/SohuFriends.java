package com.sohu.middleService.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 好友对象 sohu_friends
 *
 * <AUTHOR>
 * @date 2023-11-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_friends")
@BaseEntity.Cache(region = "SohuFriends")
public class SohuFriends extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 超时时间
     */
    public transient static final int TIME_OUT = 72;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 好友ID
     */
    private Long friendId;
    /**
     * 申请消息
     */
    private String applyMsg;
    /**
     * 申请好友状态（pass,audit,reject）
     * {@link com.sohu.common.core.enums.ApplyStateEnum}
     */
    private String applyState;
    /**
     * 好友备注
     */
    private String alias;

    /**
     * 备注电话
     */
    private String notePhone;

    /**
     * 备注文字
     */
    private String noteTxt;

    /**
     * 备注图片
     */
    private String noteImage;

    /**
     * 主动方与被动方（Y：主动）
     * {@link com.sohu.common.core.constant.Constants#Y}
     */
    private String active;

    /**
     * 唯一值
     */
    private String unq;

    /**
     * 仅聊天
     */
    private Boolean onlyChat;

    /**
     * 不看TA
     */
    private Boolean seeHim;

    /**
     * 不让TA看
     */
    private Boolean letSeeHim;

    /**
     * 标签
     */
    private String tag;

    /**
     * 加好友来源
     */
    private String source;

    /**
     * 他是否是我的黑名单，true=是，false=否
     */
    private Boolean blacklist;

    /**
     * 是否已读(true=1,false=0)
     */
    private Boolean readState;
}
