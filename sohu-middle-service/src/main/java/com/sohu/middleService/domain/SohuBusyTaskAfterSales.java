package com.sohu.middleService.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 售后记录对象 sohu_busy_task_after_sales
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_busy_task_after_sales")
@Deprecated
public class SohuBusyTaskAfterSales extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 主任务编号
     */
    private String masterTaskNumber;
    /**
     * 子任务编号
     */
    private String taskNumber;
    /**
     * 国家站点id
     */
    private String countrySiteId;
    /**
     * 站点id
     */
    private Long siteId;
    /**
     * 任务标题
     */
    private String title;
    /**
     * 价值金额
     */
    private BigDecimal fullAmount;
    /**
     * 价值币种
     */
    private Long fullCurrency;
    /**
     * 报酬金额
     */
    private BigDecimal remuneration;
    /**
     * 支付状态;支付状态：WaitPay-待支付，Paid-已支付，Fail-支付失败，TimeOut-超时
     */
    private String payState;
    /**
     * 支付时间
     */
    private Date payTime;
    /**
     * 支付单号
     */
    private String transactionNo;
    /**
     * 任务类型
     */
    private String type;
    /**
     * 售后类型 Abort=终止任务，Replace=更换接单人
     */
    private String afterSalesType;
    /**
     * 售后状态 ToConfirmed=待接单方确认， ReceiveRefuse=接单方拒绝 , Intervention=平台介入处理中， Terminate=已终止, Ended = 已完结 ,Closed = 售后已关闭,Canceled = 售后已取消,ToSettlement = 待结算
     */
    private String afterSalesState;
    /**
     * 售后申请次数
     */
    private Long afterSalesNum;
    /**
     * 售后原因
     */
    private String afterSalesReason;
    /**
     * 平台处理原因
     */
    private String platformInterventionReason;
    /**
     * 平台处理类型  1=同意 ，0=拒绝
     */
    private Integer platformInterventionType;
    /**
     * 平台介入状态  1=已介入 ，0=未介入
     */
    private Integer platformInterventionState;
    /**
     * 派单人id
     */
    private Long userId;
    /**
     * 指派接单人用户ids
     */
    private String receiveOnlyUserId;
    /**
     * 审核人
     */
    private Long auditUser;
    /**
     * 审核时间
     */
    private Date auditTime;
    /**
     * 接单是是否同意 1=同意 ，0=拒绝
     */
    private Integer isReceiveConfirm;
    /**
     * 接单方原因描述
     */
    private String receiveReason;
    /**
     * 是否允许复制,1=是,0=否
     */
    private Integer isCopy;

}
