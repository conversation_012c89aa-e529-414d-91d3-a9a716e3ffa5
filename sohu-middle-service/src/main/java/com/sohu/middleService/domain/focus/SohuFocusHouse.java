package com.sohu.middleService.domain.focus;

import com.baomidou.mybatisplus.annotation.*;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 房源主体对象 sohu_house
 *
 * <AUTHOR>
 * @date 2023-06-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_house")
public class SohuFocusHouse extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 房源名称
     */
    private String houseName;
    /**
     * 房子总价
     */
    private String totalPrice;
    /**
     * 价格单位
     */
    private Long unitName;
    /**
     * 物业费
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String propertyFee;
    /**
     * 地理位置
     */
    private String address;
    /**
     * 房屋类型id
     */
    private Long houseType;
    /**
     * 开发商名称
     */
    private String propertyDeveloper;
    /**
     * 房源照片
     */
    private String housePhoto;
    /**
     * 开盘时间
     */
    private String openTime;
    /**
     * 交房时间
     */
    private String completeTime;
    /**
     * 产权时间
     */
    private String propertyRightTime;
    /**
     * 建筑面积
     */
    private String acreage;
    /**
     * 主力户型
     */
    private String mainHouseType;
    /**
     * 状态
     */
    private String state;
    /**
     * 驳回理由
     */
    private String rejectReason;
    /**
     * 排序值
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long sortIndex;
    /**
     * 站点id
     */
    private Long siteId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 浏览量
     */
    private Long viewCount;
    /**
     * 房源别名
     */
    private String houseOtherName;
    /**
     * 城市站点id
     */
    private Integer citySiteId;
    /**
     * 每平方单价
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer squarePrice;
    /**
     * 车库车位数量
     */
    private Integer parkingNumber;
    /**
     * 总楼层数
     */
    private Integer totalFloor;
    /**
     * 所在楼层数
     */
    private Integer currentFloor;
    /**
     * 房源描述
     */
    private String houseInfo;
    /**
     * 联系方式
     */
    private String contactInformation;
    /**
     * 是否开启首页推荐
     */
    private Boolean homeRecommend;
    /**
     * 详细位置经纬度
     */
    private String location;
    /**
     * 最小面积
     */
    private String minAcreage;
    /**
     * 最大面积
     */
    private String maxAcreage;
    /**
     * 汇率转换后的总价
     */
    private String totalRatePrice;
    /**
     * 汇率转换后的单价
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String squareRatePrice;
    /**
     * 汇率转换后的物业费
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String propertyRatePrice;

}
