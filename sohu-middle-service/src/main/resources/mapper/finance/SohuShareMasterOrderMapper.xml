<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.finance.SohuShareMasterOrderMapper">
  <resultMap id="BaseResultMap" type="com.sohu.middleService.domain.finance.SohuShareMasterOrder">
    <!--@mbg.generated-->
    <!--@Table sohu_share_master_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="product_total_price" jdbcType="DECIMAL" property="productTotalPrice" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="pay_price" jdbcType="DECIMAL" property="payPrice" />
    <result column="charge_price" jdbcType="DECIMAL" property="chargePrice" />
    <result column="paid" jdbcType="TINYINT" property="paid" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="pay_type" jdbcType="VARCHAR" property="payType" />
    <result column="pay_channel" jdbcType="VARCHAR" property="payChannel" />
    <result column="mark" jdbcType="VARCHAR" property="mark" />
    <result column="is_cancel" jdbcType="TINYINT" property="isCancel" />
    <result column="pay_master_order_no" jdbcType="VARCHAR" property="payMasterOrderNo" />
    <result column="out_trade_no" jdbcType="VARCHAR" property="outTradeNo" />
    <result column="transaction_id" jdbcType="VARCHAR" property="transactionId" />
    <result column="redirect" jdbcType="VARCHAR" property="redirect" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="total_num" jdbcType="BIGINT" property="totalNum" />
    <result column="admin_price" jdbcType="DECIMAL" property="adminPrice" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
  </resultMap>

</mapper>