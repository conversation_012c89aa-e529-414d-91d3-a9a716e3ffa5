<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuBillRecordMapper">

    <resultMap type="com.sohu.middleService.domain.SohuBillRecord" id="SohuBillRecordResult">
        <result property="id" column="id"/>
        <result property="payerId" column="payer_id"/>
        <result property="userId" column="user_id"/>
        <result property="payeeId" column="payee_id"/>
        <result property="title" column="title"/>
        <result property="tradeNo" column="trade_no"/>
        <result property="yiMaNo" column="yi_ma_no"/>
        <result property="shopOrderNo" column="shop_order_no"/>
        <result property="amount" column="amount"/>
        <result property="transactionType" column="transaction_type"/>
        <result property="state" column="state"/>
        <result property="rejectionReason" column="rejection_reason"/>
        <result property="busyCode" column="busy_code"/>
        <result property="busyType" column="busy_type"/>
        <result property="payType" column="pay_type"/>
        <result property="paySource" column="pay_source"/>
        <result property="billType" column="bill_type"/>
        <result property="taskNumber" column="task_number"/>
        <result property="walletBalance" column="wallet_balance"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="selectAmountList" resultType="BigDecimal">
        -- 生成一个包含一个月内所有日期的日期序列
        WITH DateSequence AS (SELECT DATE_SUB(CURDATE(), INTERVAL (n.n - 1) DAY) AS date
        FROM (
            SELECT @row := @row + 1 AS n
            FROM (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) AS a
            CROSS JOIN (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) AS b
            CROSS JOIN (SELECT @row := 0) AS dummy
            ) AS n
        WHERE DATE_SUB(CURDATE()
            , INTERVAL #{day} DAY) &lt;= DATE_SUB(CURDATE()
            , INTERVAL (n.n - 1) DAY)
          AND DATE_SUB(CURDATE()
            , INTERVAL 1 DAY) >= DATE_SUB(CURDATE()
            , INTERVAL (n.n - 1) DAY)
            )
            ,

        -- 查询结果并与日期序列左连接
            QueryResult AS (
        SELECT DATE_FORMAT(sr.create_time, '%Y-%m-%d') AS createTime, COALESCE (SUM(sr.amount), 0) AS amount
        FROM (
            SELECT *
            FROM sohu_bill_record r
            WHERE DATE_SUB(CURDATE(), INTERVAL #{day} DAY) &lt;= DATE (r.create_time)
            AND DATE_SUB(CURDATE(), INTERVAL 1 DAY) >= DATE (r.create_time)
            AND r.transaction_type = #{transactionType}
            AND r.state = 'Paid'
            AND r.user_id=#{loginUserId}
            ) AS sr
        GROUP BY createTime
            )

        -- 将日期序列与查询结果左连接，并设置没有记录的日期对应的值为 0
        SELECT COALESCE(qr.amount, 0) AS amount
        FROM DateSequence ds
                 LEFT JOIN QueryResult qr ON ds.date = qr.createTime
        ORDER BY ds.date;
    </select>
    <select id="billInfoList" resultType="com.sohu.middle.api.vo.SohuUserBillInfoVo">
        SELECT
        r.create_time createTime,
        r.amount,
        s.title,
        s.type,
        c.name typeName
        FROM
        sohu_bill_record r
        INNER JOIN sohu_busy_task_site s ON r.task_number=s.task_number
        INNER JOIN sohu_category c ON c.id=s.type
        WHERE r.transaction_type= 'income' AND r.state='Paid' AND s.state='Over'
        <if test="startDate !=null">
            AND r.create_time >= #{startDate}
        </if>
        <if test="endDate !=null">
            AND r.create_time &lt;= #{endDate}
        </if>
        and r.user_id=#{loginUserId}
    </select>

    <select id="withdrawalList" parameterType="com.sohu.middle.api.bo.SohuBillRecordQueryBo" resultType="com.sohu.middle.api.vo.SohuBillRecordVo">
        SELECT
            sbr.id,
            su.user_name,
            su.phone_number,
            sbr.amount,
            sbr.wallet_balance,
            sbr.state,
            sbr.create_time,
            sbr.rejection_reason,
            sbr.trade_no
        FROM
            `sohu_bill_record` sbr
                JOIN sys_user su ON sbr.user_id = su.user_id
        WHERE
            sbr.transaction_type = 'withdrawal'
        <if test="bo.phone != null and bo.phone != ''">
            AND su.phone_number LIKE concat('%',#{bo.phone},'%')
        </if>
        <if test="bo.state != null and bo.state != ''">
            AND sbr.state = #{bo.state}
        </if>
        <if test="bo.startTime != null and bo.startTime != ''">
            AND sbr.create_time &gt;= #{bo.startTime}
        </if>
        <if test="bo.endTime != null and bo.endTime != ''">
            AND sbr.create_time &lt;= #{bo.endTime}
        </if>
        order by sbr.create_time desc
    </select>

    <select id="selectProcessingWithdrawal" resultType="com.sohu.middleService.domain.SohuBillRecord">
        SELECT
            *
        FROM
            sohu_bill_record
        WHERE
            transaction_type = 'withdrawal'
        AND state = 'Processing' and DATE_ADD(withdraw_time,INTERVAL 1 DAY) &lt;NOW()
    </select>


</mapper>
