<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuEventReportMapper">

    <resultMap type="com.sohu.middleService.domain.SohuEventReport" id="SohuEventReportResult">
    <result property="eventId" column="event_id"/>
    <result property="busyCode" column="busy_code"/>
    <result property="busyName" column="busy_name"/>
    <result property="busyType" column="busy_type"/>
    <result property="busyExt" column="busy_ext"/>
    <result property="userId" column="user_id"/>
    <result property="userName" column="user_name"/>
    <result property="eventType" column="event_type"/>
    <result property="createTime" column="create_time"/>
    <result property="stayDuration" column="stay_duration"/>
    </resultMap>

    <select id="countByTypeAndTime" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT user_id)
        FROM sohu_event_report
        WHERE
        user_id IS NOT NULL
        <if test="busyType != null and busyType != ''">
            AND busy_type = #{busyType}
            AND stay_duration > 29
        </if>
        <if test="startTime != null and endTime != null" >
            AND create_time BETWEEN #{startTime} AND #{endTime}
        </if>
    </select>

    <select id="selectAdReportList" resultType="com.sohu.middle.api.vo.AdReportVo">
        SELECT
            busy_code,
            COUNT(CASE WHEN event_type ='2' THEN 1 END) AS ggbgNum,
            COUNT(CASE WHEN event_type ='3' THEN 1 END) AS ggdjNum
        FROM `sohu_event_report`
        WHERE busy_type='Ad'
        <if test="busyCodeList != null and busyCodeList.size > 0">
            and busy_code in
            <foreach item="item" index="index" collection="busyCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY busy_code
    </select>
</mapper>
