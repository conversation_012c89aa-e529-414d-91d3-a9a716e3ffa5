<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuUserLabelRelationMapper">

    <resultMap type="com.sohu.middleService.domain.SohuUserLabelRelation" id="SohuUserLabelRelationResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="labelId" column="label_id"/>
        <result property="labelType" column="label_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="queryRelationUserCount" resultType="java.util.Map">
        SELECT label_id AS labelId, COUNT(DISTINCT user_id) AS userCount
        FROM sohu_user_label_relation
        WHERE label_id IN
        <foreach collection="labelIds" item="labelId" open="(" separator="," close=")">
            #{labelId}
        </foreach>
        GROUP BY label_id
    </select>

</mapper>
