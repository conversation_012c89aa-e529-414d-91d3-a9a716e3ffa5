<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.ai.SohuDialogRecordMapper">

    <resultMap type="com.sohu.middleService.domain.ai.SohuDialogRecord" id="SohuDialogRecordResult">
        <result property="id" column="id"/>
        <result property="dialogId" column="dialog_id"/>
        <result property="userId" column="user_id"/>
        <result property="model" column="model"/>
        <result property="question" column="question"/>
        <result property="answer" column="answer"/>
        <result property="thinkValue" column="think_value"/>
        <result property="answerExt" column="answer_ext"/>
        <result property="doneReason" column="done_reason"/>
        <result property="totalDuration" column="total_duration"/>
        <result property="loadDuration" column="load_duration"/>
        <result property="evalCount" column="eval_count"/>
        <result property="evalDuration" column="eval_duration"/>
        <result property="totalToken" column="total_token"/>
        <result property="promptToken" column="prompt_token"/>
        <result property="completionToken" column="completion_token"/>
        <result property="isLike" column="is_like"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

</mapper>
