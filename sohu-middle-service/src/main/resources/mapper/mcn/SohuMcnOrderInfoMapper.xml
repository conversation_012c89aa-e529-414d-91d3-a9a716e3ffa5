<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.mcn.SohuMcnOrderInfoMapper">
    <select id="queryPageRecordList" resultType="com.sohu.middle.api.vo.mcn.SohuMcnOrderInfoVo">
        SELECT mo.order_no
             , mo.master_order_no
             , mo.pay_time
             , mo.paid
             , mo.`status` AS orderStatus
             , mo.product_total_price
             , mo.total_price
             , mo.pay_price
             , moi.use_user_id
             , moi.product_name
             , moi.price
             , moi.use_start_time
             , moi.use_end_time
             , moi.use_days
        FROM sohu_mcn_order mo
                 INNER JOIN sohu_mcn_order_info moi ON moi.order_no = mo.order_no
        WHERE mo.user_id = #{bo.userId}
        AND mo.status != 'UNPAID'
        ORDER BY mo.create_time DESC
    </select>
</mapper>
