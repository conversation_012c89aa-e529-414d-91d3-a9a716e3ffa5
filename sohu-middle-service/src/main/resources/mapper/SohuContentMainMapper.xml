<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuContentMainMapper">

    <resultMap type="com.sohu.middleService.domain.SohuContentMain" id="SohuContentMainResult">
        <result property="id" column="id"/>
        <result property="siteId" column="site_id"/>
        <result property="userId" column="user_id"/>
        <result property="objId" column="obj_id"/>
        <result property="objType" column="obj_type"/>
        <result property="objTitle" column="obj_title"/>
        <result property="category" column="category"/>
        <result property="viewCount" column="view_count"/>
        <result property="commentCount" column="comment_count"/>
        <result property="praiseCount" column="praise_count"/>
        <result property="collectCount" column="collect_count"/>
        <result property="forwardCount" column="forward_count"/>
        <result property="state" column="state"/>
        <result property="coverImage" column="cover_image"/>
        <result property="videoUrl" column="video_url"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <delete id="deleteByObj">
        DELETE
        FROM sohu_content_main
        WHERE obj_id = #{objId}
          AND obj_type = #{objType}
    </delete>

    <select id="pageOrderCenter" resultType="com.sohu.middle.api.vo.SohuContentMainVo">
        SELECT
        u.nick_name AS username,u.avatar AS userAvatar,m.*
        FROM sohu_content_main m
        LEFT JOIN sohu_person_share s ON m.id = s.busy_code
        LEFT JOIN sys_user u ON m.user_id = u.user_id
        WHERE
        m.state IN
        <foreach collection="dto.stateList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND m.obj_type in ('Video','Question','Article','Answer', 'Poetry', 'Prose')
        AND(
        m.user_id = #{dto.userId}
        OR
        ( s.apply_person = #{dto.userId}
        AND s.busy_type = 'Content'
        AND s.audit_state = 'Pass'
        <if test="dto.viewType">
            AND s.view_type =#{dto.viewType}
        </if>
        )
        )
        GROUP BY m.id
        ORDER BY
        CASE
        WHEN s.pass_time IS NOT NULL THEN
        s.pass_time ELSE m.create_time
        END DESC
    </select>

    <select id="myDraftList" resultType="com.sohu.middle.api.vo.SohuContentMainVo">
        SELECT m.*, a.content AS objContent
        FROM sohu_content_main m
        <if test="bo.objType == 'Article'">
            LEFT JOIN sohu_article_info a on m.obj_id = a.article_id
        </if>
        <if test="bo.objType == 'Question'">
            LEFT JOIN sohu_question a on m.obj_id=a.id
        </if>
        <if test="bo.objType == 'Video'">
            LEFT JOIN sohu_video a on m.obj_id=a.id
        </if>
        <if test="bo.objType == 'Poetry' or bo.objType == 'Prose'">
            LEFT JOIN sohu_literature a on m.obj_id=a.id
        </if>
        WHERE m.obj_type = #{bo.objType}
        AND m.user_id = #{bo.userId}
        AND m.state = #{bo.state}
        ORDER BY m.update_time DESC
    </select>

    <select id="countPrice" resultType="java.lang.Long">
        SELECT sum(praise_count)
        FROM sohu_content_main
        where user_id = #{userId}
    </select>

    <select id="recycleList" resultType="com.sohu.middle.api.vo.SohuRecycleVo">
        <choose>
            <when test="bo.contentType == 'Video'">
                SELECT id,
                'Video' AS type,
                title,
                state,
                cover_image,
                category_id AS categoryId,
                site_id AS siteId,
                create_time,
                del_time AS delTime
                FROM sohu_video
                WHERE state IN ('Delete', 'ForceDelete')
                AND del_flag = 0
                <if test="bo.userId != null and bo.userId != ''">
                    AND user_id = #{bo.userId}
                </if>
                <if test="bo.title != null and bo.title != ''">
                    AND title LIKE CONCAT('%', #{bo.title}, '%')
                </if>
                <if test="bo.siteIds != null">
                    AND site_id IN
                    <foreach item="siteId" collection="bo.siteIds" open="(" separator="," close=")">
                        #{siteId}
                    </foreach>
                </if>
                <if test="bo.categoryIds != null and bo.categoryIds.size > 0">
                    AND category_id IN
                    <foreach item="categoryId" collection="bo.categoryIds" open="(" separator="," close=")">
                        #{categoryId}
                    </foreach>
                </if>
                ORDER BY delTime DESC
            </when>

            <!-- 当 contentType 为 article 时 -->
            <when test="bo.contentType == 'Article'">
                SELECT id,
                'Article' AS type,
                title,
                state,
                cover_image,
                category_id AS categoryId,
                site_id AS siteId,
                create_time,
                del_time AS delTime
                FROM sohu_article
                WHERE state IN ('Delete', 'ForceDelete')
                AND del_flag = 0
                <if test="bo.userId != null and bo.userId != ''">
                    AND user_id = #{bo.userId}
                </if>
                <if test="bo.title != null and bo.title != ''">
                    AND title LIKE CONCAT('%', #{bo.title}, '%')
                </if>
                <if test="bo.siteIds != null">
                    AND site_id IN
                    <foreach item="siteId" collection="bo.siteIds" open="(" separator="," close=")">
                        #{siteId}
                    </foreach>
                </if>
                <if test="bo.categoryIds != null and bo.categoryIds.size > 0">
                    AND category_id IN
                    <foreach item="categoryId" collection="bo.categoryIds" open="(" separator="," close=")">
                        #{categoryId}
                    </foreach>
                </if>
                ORDER BY delTime DESC
            </when>

            <!-- 当 contentType 为 question 时 -->
            <when test="bo.contentType == 'Question'">
                SELECT id,
                'Question' AS type,
                title,
                state,
                cover_image,
                category_id AS categoryId,
                site_id AS siteId,
                create_time,
                del_time AS delTime
                FROM sohu_question
                WHERE state IN ('Delete', 'ForceDelete')
                AND del_flag = 0
                <if test="bo.userId != null and bo.userId != ''">
                    AND user_id = #{bo.userId}
                </if>
                <if test="bo.title != null and bo.title != ''">
                    AND title LIKE CONCAT('%', #{bo.title}, '%')
                </if>
                <if test="bo.siteIds != null">
                    AND site_id IN
                    <foreach item="siteId" collection="bo.siteIds" open="(" separator="," close=")">
                        #{siteId}
                    </foreach>
                </if>
                <if test="bo.categoryIds != null and bo.categoryIds.size > 0">
                    AND category_id IN
                    <foreach item="categoryId" collection="bo.categoryIds" open="(" separator="," close=")">
                        #{categoryId}
                    </foreach>
                </if>
                ORDER BY delTime DESC
            </when>
            <when test="bo.contentType == 'Poetry' or bo.contentType == 'Prose'">
                SELECT id,
                type AS type,
                title,
                state,
                category_id,
                cover_image,
                site_id,
                create_time,
                del_time AS delTime
                FROM sohu_literature
                WHERE state IN ('Delete', 'ForceDelete')
                AND del_flag = 0
                AND type = #{bo.contentType}
                <if test="bo.userId != null and bo.userId != ''">
                    AND user_id = #{bo.userId}
                </if>
                <if test="bo.title != null and bo.title != ''">
                    AND title LIKE CONCAT('%', #{bo.title}, '%')
                </if>
                <if test="bo.siteIds != null">
                    AND site_id IN
                    <foreach collection="bo.siteIds" item="siteId" open="(" separator="," close=")">
                        #{siteId}
                    </foreach>
                </if>
                <if test="bo.categoryIds != null and bo.categoryIds.size > 0">
                    AND category_id IN
                    <foreach item="categoryId" collection="bo.categoryIds" open="(" separator="," close=")">
                        #{categoryId}
                    </foreach>
                </if>
                ORDER BY delTime DESC
            </when>

            <!-- 当 contentType 为 null 或其他值时，返回空数据 -->
            <otherwise>
                SELECT id,
                'Video' AS type,
                title,
                state,
                category_id,
                cover_image,
                site_id,
                create_time,
                del_time AS delTime
                FROM sohu_video
                WHERE state IN ('Delete', 'ForceDelete')
                AND del_flag = 0
                <if test="bo.userId != null and bo.userId != ''">
                    AND user_id = #{bo.userId}
                </if>
                <if test="bo.title != null and bo.title != ''">
                    AND title LIKE CONCAT('%', #{bo.title}, '%')
                </if>
                <if test="bo.siteIds != null">
                    AND site_id IN
                    <foreach collection="bo.siteIds" item="siteId" open="(" separator="," close=")">
                        #{siteId}
                    </foreach>
                </if>
                <if test="bo.categoryIds != null and bo.categoryIds.size > 0">
                    AND category_id IN
                    <foreach collection="bo.categoryIds" item="categoryId" open="(" separator="," close=")">
                        #{categoryId}
                    </foreach>
                </if>
                UNION ALL
                SELECT id,
                'Article' AS type,
                title,
                state,
                category_id,
                cover_image,
                site_id,
                create_time,
                del_time AS delTime
                FROM sohu_article
                WHERE state IN ('Delete', 'ForceDelete')
                AND del_flag = 0
                <if test="bo.userId != null and bo.userId != ''">
                    AND user_id = #{bo.userId}
                </if>
                <if test="bo.title != null and bo.title != ''">
                    AND title LIKE CONCAT('%', #{bo.title}, '%')
                </if>
                <if test="bo.siteIds != null">
                    AND site_id IN
                    <foreach collection="bo.siteIds" item="siteId" open="(" separator="," close=")">
                        #{siteId}
                    </foreach>
                </if>
                <if test="bo.categoryIds != null and bo.categoryIds.size > 0">
                    AND category_id IN
                    <foreach collection="bo.categoryIds" item="categoryId" open="(" separator="," close=")">
                        #{categoryId}
                    </foreach>
                </if>
                UNION ALL
                SELECT id,
                'Question' AS type,
                title,
                state,
                category_id,
                cover_image,
                site_id,
                create_time,
                del_time AS delTime
                FROM sohu_question
                WHERE state IN ('Delete', 'ForceDelete')
                AND del_flag = 0
                <if test="bo.userId != null and bo.userId != ''">
                    AND user_id = #{bo.userId}
                </if>
                <if test="bo.title != null and bo.title != ''">
                    AND title LIKE CONCAT('%', #{bo.title}, '%')
                </if>
                <if test="bo.siteIds != null">
                    AND site_id IN
                    <foreach collection="bo.siteIds" item="siteId" open="(" separator="," close=")">
                        #{siteId}
                    </foreach>
                </if>
                <if test="bo.categoryIds != null and bo.categoryIds.size > 0">
                    AND category_id IN
                    <foreach collection="bo.categoryIds" item="categoryId" open="(" separator="," close=")">
                        #{categoryId}
                    </foreach>
                </if>
                UNION ALL
                SELECT id,
                type AS type,
                title,
                state,
                category_id,
                cover_image,
                site_id,
                create_time,
                del_time AS delTime
                FROM sohu_literature
                WHERE state IN ('Delete', 'ForceDelete')
                AND del_flag = 0
                <if test="bo.userId != null and bo.userId != ''">
                    AND user_id = #{bo.userId}
                </if>
                <if test="bo.title != null and bo.title != ''">
                    AND title LIKE CONCAT('%', #{bo.title}, '%')
                </if>
                <if test="bo.siteIds != null">
                    AND site_id IN
                    <foreach collection="bo.siteIds" item="siteId" open="(" separator="," close=")">
                        #{siteId}
                    </foreach>
                </if>
                <if test="bo.categoryIds != null and bo.categoryIds.size > 0">
                    AND category_id IN
                    <foreach item="categoryId" collection="bo.categoryIds" open="(" separator="," close=")">
                        #{categoryId}
                    </foreach>
                </if>
                ORDER BY delTime DESC
            </otherwise>
        </choose>

    </select>

    <update id="updateState">
        UPDATE sohu_content_main
        SET state = #{state}
        WHERE obj_id = #{busyCode}
          AND obj_type = #{busyType}
    </update>

    <update id="updateViewCount">
        UPDATE sohu_content_main
        SET view_count=view_count + 1
        WHERE obj_id = #{busyCode}
          AND obj_type = #{busyType}
    </update>

    <update id="setPraiseCount">
        UPDATE sohu_content_main
        SET praise_count=#{praiseCount}
        WHERE obj_id = #{busyCode}
          AND obj_type = #{busyType}
    </update>

    <update id="setCollectCount">
        UPDATE sohu_content_main
        SET collect_count=#{collectCount}
        WHERE obj_id = #{busyCode}
          AND obj_type = #{busyType}
    </update>

    <update id="flushRecycleVideo">
        <!-- 清空视频回收站 -->
        UPDATE sohu_video
        SET del_flag = 2,
        user_id = #{userId},
        update_time = NOW()
        WHERE state IN ('Edit', 'Delete', 'ForceDelete')
        <if test="ids != null and ids.size > 0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

    </update>

    <update id="flushRecycleArticle">
        <!-- 清空文章回收站 -->
        UPDATE sohu_article
        SET del_flag = 2,
        user_id = #{userId},
        update_time = NOW()
        WHERE state IN ('Edit','Delete', 'ForceDelete')
        <if test="ids != null and ids.size > 0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <update id="flushRecycleQuestion">
        <!-- 清空问答回收站 -->
        UPDATE sohu_question
        SET del_flag = 2,
        user_id = #{userId},
        update_time = NOW()
        WHERE state IN ('Edit', 'Delete', 'ForceDelete')
        <if test="ids != null and ids.size > 0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <update id="flushDraftsVideo">
        <!-- 清空草稿箱视频 -->
        UPDATE sohu_video
        SET del_flag = 2,
        user_id = #{userId},
        update_time = NOW()
        WHERE state IN ('Edit')
        <if test="ids != null and ids.size > 0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <update id="flushDraftsArticle">
        <!-- 清空草稿箱文章 -->
        UPDATE sohu_article
        SET del_flag = 2,
        user_id = #{userId},
        update_time = NOW()
        WHERE state IN ('Edit')
        <if test="ids != null and ids.size > 0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>
    <update id="flushDraftsQuestion">
        <!-- 清空草稿箱问答 -->
        UPDATE sohu_question
        SET del_flag = 2,
        user_id = #{userId},
        update_time = NOW()
        WHERE state IN ('Edit')
        <if test="ids != null and ids.size > 0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <update id="flushRecycleLiterature">
        <!-- 清空回收站诗文 -->
        UPDATE sohu_literature
        SET del_flag = 2,
        user_id = #{userId},
        update_time = NOW()
        WHERE state IN ('Edit','Delete', 'ForceDelete')
        <if test="ids != null and ids.size > 0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <update id="flushDraftsLiterature">
        <!-- 清空草稿箱诗文 -->
        UPDATE sohu_literature
        SET del_flag = 2,
        user_id = #{userId},
        update_time = NOW()
        WHERE state IN ('Edit')
        <if test="ids != null and ids.size > 0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

</mapper>
