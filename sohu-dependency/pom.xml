<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sohu</groupId>
    <artifactId>sohu-dependency</artifactId>
    <version>1.0.0</version>

    <name>sohu-dependency</name>
    <url>https://gitee.com/dromara/sohu-Cloud-Plus</url>
    <description>SoHu-Cloud-微服务系统</description>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <sohu.version>1.0.0</sohu.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>11</java.version>
        <spring-boot.version>2.7.13</spring-boot.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>
        <spring-boot-admin.version>2.7.10</spring-boot-admin.version>
        <spring-boot.mybatis>2.2.2</spring-boot.mybatis>
        <mybatis.version>3.5.10</mybatis.version>
        <mybatis-plus.version>*******</mybatis-plus.version>
        <p6spy.version>3.9.1</p6spy.version>
        <dynamic-ds.version>3.5.2</dynamic-ds.version>
        <velocity.version>2.3</velocity.version>
        <swagger.core.version>2.2.8</swagger.core.version>
        <springdoc.version>1.6.15</springdoc.version>
        <poi.version>5.2.3</poi.version>
        <easyexcel.version>3.3.1</easyexcel.version>
        <hutool.version>5.8.18</hutool.version>
        <redisson.version>3.20.1</redisson.version>
        <lock4j.version>2.2.3</lock4j.version>
        <xxl-job.version>2.4.0</xxl-job.version>
        <satoken.version>1.35.0.RC</satoken.version>
        <lombok.version>1.18.26</lombok.version>
        <logstash.version>7.2</logstash.version>
        <easy-es.version>1.1.1</easy-es.version>
        <elasticsearch.version>7.14.0</elasticsearch.version>
        <skywalking-toolkit.version>8.14.0</skywalking-toolkit.version>
        <bouncycastle.version>1.72</bouncycastle.version>
        <!-- 离线IP地址定位库 -->
        <ip2region.version>2.7.0</ip2region.version>

        <!-- 临时修复 snakeyaml 漏洞 -->
        <snakeyaml.version>1.33</snakeyaml.version>
        <!-- 临时修复 fastjson 漏洞 -->
        <fastjson.version>1.2.83</fastjson.version>

        <!-- OSS 配置 -->
        <aws-java-sdk-s3.version>1.12.400</aws-java-sdk-s3.version>
        <okhttp.version>4.10.0</okhttp.version>

        <!-- SMS 配置 -->
        <sms4j.version>3.2.1</sms4j.version>

        <!-- 火山视频 -->
        <volc.version>1.0.102</volc.version>

        <!--gson-->
        <gson.version>2.10.1</gson.version>

        <!--jsoup爬虫-->
        <jsoup.version>1.15.3</jsoup.version>

    </properties>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应-->
                <profiles.active>dev</profiles.active>
                <nacos.ip>***************</nacos.ip>
                <nacos.server>***************:8848</nacos.server>
                <nacos.discovery.group>DEFAULT_GROUP</nacos.discovery.group>
                <nacos.config.group>DEFAULT_GROUP</nacos.config.group>
                <logstash.address>***************:4560</logstash.address>
                <nacos.username>nacos</nacos.username>
                <nacos.password>sohuglobal123</nacos.password>
                <name.server>***************:9876</name.server>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <nacos.server>*************:8848</nacos.server>
                <nacos.discovery.group>DEFAULT_GROUP</nacos.discovery.group>
                <nacos.config.group>DEFAULT_GROUP</nacos.config.group>
                <logstash.address>*************:4560</logstash.address>
                <nacos.username>nacos</nacos.username>
                <nacos.password>sohuglobal321</nacos.password>
                <name.server>*************:9876</name.server>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
    </profiles>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.sohu</groupId>
                <artifactId>sohu-common-alibaba-bom</artifactId>
                <version>${sohu.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
                <exclusions>
                    <exclusion>
                        <groupId>redis.clients</groupId>
                        <artifactId>jedis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- hutool 的依赖配置-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- common 的依赖配置-->
            <dependency>
                <groupId>com.sohu</groupId>
                <artifactId>sohu-common-bom</artifactId>
                <version>${sohu.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- api 的依赖配置-->
            <dependency>
                <groupId>com.sohu</groupId>
                <artifactId>sohu-api-bom</artifactId>
                <version>${sohu.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-core</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <!-- Mybatis 依赖配置 -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${spring-boot.mybatis}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!-- sql性能分析插件 -->
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-webmvc-core</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-javadoc</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml-schemas</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!--redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.redisson</groupId>
                        <artifactId>redisson-spring-data-30</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-data-27</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
            </dependency>

            <!-- xxl-job-core -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <!-- 加密包引入 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15to18</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>

            <!-- logstash -->
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.easy-es</groupId>
                <artifactId>easy-es-boot-starter</artifactId>
                <version>${easy-es.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <!-- skywalking 整合 logback -->
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking-toolkit.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${skywalking-toolkit.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>

            <!--短信sms4j-->
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-spring-boot-starter</artifactId>
                <version>${sms4j.version}</version>
            </dependency>

            <!-- 离线IP地址定位库 ip2region -->
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <!-- 临时修复 snakeyaml 漏洞 -->
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!--火山视频-->
            <dependency>
                <groupId>com.volcengine</groupId>
                <artifactId>volc-sdk-java</artifactId>
                <version>${volc.version}</version>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>2.9.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.google.code.gson/gson -->
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.15.3</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <packaging>pom</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.9.0</version>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>com.github.therapi</groupId>
                            <artifactId>therapi-runtime-javadoc-scribe</artifactId>
                            <version>0.15.0</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                            <version>${spring-boot.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <!-- 单元测试使用 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <argLine>-Dfile.encoding=UTF-8</argLine>
                    <!-- 根据打包环境执行对应的@Tag测试方法 -->
                    <groups>${profiles.active}</groups>
                    <!-- 排除标签 -->
                    <excludedGroups>exclude</excludedGroups>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 关闭过滤 -->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/webapp/</directory>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 引入所有 匹配文件进行过滤 -->
                <includes>
                    <include>application*</include>
                    <include>bootstrap*</include>
                    <include>logback*</include>
                </includes>
                <!-- 启用过滤 即该资源中的变量将会被过滤器中的值替换 -->
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>


    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <!--<url>http://192.168.130.47:8081/repository/maven-public/</url>-->
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <!--<url>http://192.168.130.47:8081/repository/maven-public/</url>-->
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <!--<distributionManagement>
        <repository>
            <id>nexus</id>
            <url>http://192.168.130.47:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus</id>
            <url>http://192.168.130.47:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>-->

</project>