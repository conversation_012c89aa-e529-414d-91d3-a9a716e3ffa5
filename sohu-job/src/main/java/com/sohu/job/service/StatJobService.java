package com.sohu.job.service;

import com.sohu.busyorder.api.RemoteBusyTaskAfterSalesService;
import com.sohu.busyorder.api.RemoteBusyTaskService;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.SohuBusyTaskState;
import com.sohu.common.core.enums.SohuBusyTaskaAfterSalesState;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.middle.api.bo.SohuAuditTaskReportBo;
import com.sohu.middle.api.bo.SohuTaskReportBo;
import com.sohu.middle.api.service.*;
import com.sohu.middle.api.vo.SohuAuditTaskReportVo;
import com.sohu.middle.api.vo.SohuTaskReportVo;
import com.sohu.shopgoods.api.RemoteProductService;
import com.sohu.shoporder.api.RemoteShopRefundOrderService;
import com.sohu.shoporder.api.constants.RefundConstants;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 统计相关定时任务
 *
 * @Author: leibo
 * @Date: 2024/12/02 10:14
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class StatJobService {

    @DubboReference
    private RemoteBusyTaskService remoteBusyTaskService;
    @DubboReference
    private RemoteMiddleTaskReportService remoteMiddleTaskReportService;
    @DubboReference
    private RemoteMiddleAuditTaskReportService remoteMiddleAuditTaskReportService;
    @DubboReference
    private RemoteMiddleAuditService remoteMiddleAuditService;
    @DubboReference
    private RemoteMiddlePlayletService remoteMiddlePlayletService;
    @DubboReference
    private RemoteProductService remoteProductService;
    @DubboReference
    private RemoteShopRefundOrderService remoteShopRefundOrderService;
    @DubboReference
    private RemoteBusyTaskAfterSalesService remoteBusyTaskAfterSalesService;

    /**
     * 5分钟执行一次 统计审核中数据报表
     *
     * @throws Exception
     */
    @XxlJob(value = "sohuAuditTaskJobHandler", init = "init", destroy = "destroy")
    public void sohuAuditTaskJobHandler() throws Exception {
        String dateTime = DateUtils.getDateMap(1).get(DateUtils.START_DAY);
        // 统计审核代办的数量
        SohuAuditTaskReportBo reportBo = new SohuAuditTaskReportBo();
        // 待售后
        reportBo.setPendingAfterSales(remoteBusyTaskAfterSalesService.countAdminList(SohuBusyTaskaAfterSalesState.Intervention.name()));
        // 待审核图文
        Long articleCount = remoteMiddleAuditService.countByBusyTypeAndState(BusyType.Article.getType(), CommonState.WaitApprove.getCode());
        reportBo.setPendingArticle(articleCount);
        // 待审核短剧
        Long playletCount = remoteMiddlePlayletService.countByState(CommonState.WaitApprove.getCode());
        reportBo.setPendingPlaylet(playletCount);
        // 待审核商品
        Long productCount = remoteProductService.countByAuditStatus(CommonState.WaitApprove.getCode());
        reportBo.setPendingProduct(productCount);
        // 待审核视频
        Long videoCount = remoteMiddleAuditService.countByBusyTypeAndState(BusyType.Video.getType(), CommonState.WaitApprove.getCode());
        reportBo.setPendingVideo(videoCount);
        // 待处理退货
        List<String> refundStatusList = new ArrayList<>();
        refundStatusList.add(RefundConstants.ONLY_REFUND);
        refundStatusList.add(RefundConstants.RETURN_REFUND);
        Long refundOrderCount = remoteShopRefundOrderService.countByRefundStatusList(refundStatusList);
        reportBo.setPendingReturnProcessing(refundOrderCount);
        // 待审核诗文
        Long poetryCount = remoteMiddleAuditService.countByBusyTypeAndState(BusyType.Poetry.getType(), CommonState.WaitApprove.getCode());
        Long proseCount = remoteMiddleAuditService.countByBusyTypeAndState(BusyType.Prose.getType(), CommonState.WaitApprove.getCode());
        reportBo.setPendingLiterature(poetryCount + proseCount);
        // 待审核小说
        reportBo.setPendingNovel(0L);
        reportBo.setDate(dateTime);
        // 基于时间查询统计表中数据是否存在
        SohuAuditTaskReportVo report = remoteMiddleAuditTaskReportService.queryReportByTime(dateTime);
        // 判断是做新增还是更新操作
        if (Objects.isNull(report)) {
            remoteMiddleAuditTaskReportService.insertByBo(reportBo);
        } else {
            reportBo.setId(report.getId());
            remoteMiddleAuditTaskReportService.updateByBo(reportBo);
        }
    }

    /**
     * 5分钟执行一次 统计任务报表
     *
     * @throws Exception
     */
    @XxlJob(value = "sohuTaskJobHandler", init = "init", destroy = "destroy")
    public void sohuTaskJobHandler() throws Exception {
        String dateTime = DateUtils.getDateMap(1).get(DateUtils.START_DAY);
        // 统计任务数量
        SohuTaskReportBo reportBo = new SohuTaskReportBo();
        reportBo.setDate(dateTime);
        // 总任务数
        reportBo.setTotalTasks(remoteBusyTaskService.countByState(null));
        // 待审核任务数
        reportBo.setWaitApproveTasks(remoteBusyTaskService.countByState(SohuBusyTaskState.WaitApprove.name()));
        // 待拆单任务数
        reportBo.setWaitSplitTasks(remoteBusyTaskService.countByState(SohuBusyTaskState.WaitSplit.name()));
        // 已通过任务数
        reportBo.setPassTasks(remoteBusyTaskService.countByState(SohuBusyTaskState.Pass.name()));
        // 已取消任务数
        reportBo.setCancelTasks(remoteBusyTaskService.countByState(SohuBusyTaskState.Cancel.name()));
        // 审核拒绝任务数
        reportBo.setRefuseTasks(remoteBusyTaskService.countByState(SohuBusyTaskState.Refuse.name()));
        // 执行中任务数
        reportBo.setExecuteTasks(remoteBusyTaskService.countByState(SohuBusyTaskState.Execute.name()));
        // 待接单任务数
        reportBo.setWaitReceiveTasks(remoteBusyTaskService.countByState(SohuBusyTaskState.WaitReceive.name()));
        // 已完成任务数
        reportBo.setOverSettleTasks(remoteBusyTaskService.countByState(SohuBusyTaskState.OverSettle.name()));
        // 异常任务数
        reportBo.setErrorTasks(remoteBusyTaskService.countByState(SohuBusyTaskState.Error.name()));
        // 下架任务数
        reportBo.setOffShelfTasks(remoteBusyTaskService.countByState(SohuBusyTaskState.OffShelf.name()));
        // 已终止任务数
        reportBo.setOverTasks(remoteBusyTaskService.countByState(SohuBusyTaskState.Over.name()));
        // 强制下架任务数
        reportBo.setCompelOffTasks(remoteBusyTaskService.countByState(SohuBusyTaskState.CompelOff.name()));
        // 售后任务数
        reportBo.setAfterSalesTasks(remoteBusyTaskAfterSalesService.countAdminList(null).intValue());
        // 基于时间查询统计表中数据是否存在
        SohuTaskReportVo report = remoteMiddleTaskReportService.queryReportByTime(dateTime);
        // 判断是做新增还是更新操作
        if (Objects.isNull(report)) {
            remoteMiddleTaskReportService.insertByBo(reportBo);
        } else {
            reportBo.setId(report.getId());
            remoteMiddleTaskReportService.updateByBo(reportBo);
        }
    }

    public void init() {
        log.info("NovelJobService定时任务启动");
        XxlJobHelper.log("NovelJobService定时任务启动");
    }

    public void destroy() {
        log.info("NovelJobService定时任务结束");
        XxlJobHelper.log("NovelJobService定时任务结束");
    }

}
