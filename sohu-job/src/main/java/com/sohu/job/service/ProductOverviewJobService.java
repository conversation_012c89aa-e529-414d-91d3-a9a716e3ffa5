package com.sohu.job.service;

import cn.hutool.core.date.DateUtil;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.shopgoods.api.RemoteProductService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 商品总览数据统计->定时服务
 * <AUTHOR>
 * @date 2024/12/3 9:27
 */
@Slf4j
@Service
public class ProductOverviewJobService {

    @DubboReference
    private RemoteProductService remoteProductService;

    /**
     * 1、简单任务示例（Bean模式）
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @XxlJob(value = "productOverviewJobHandler", init = "init", destroy = "destroy")
    public void productOverviewJobHandler() throws Exception {
        String yesterday = DateUtils.getDateMap(Constants.TWO).get(DateUtils.START_DAY);
        remoteProductService.productOverviewExecute(yesterday);
        // default success
    }

    public void init() {
        log.info("productOverviewJobHandler定时任务启动");
        XxlJobHelper.log("productOverviewJobHandler定时任务启动");
    }

    public void destroy() {
        log.info("productOverviewJobHandler定时任务结束");
        XxlJobHelper.log("productOverviewJobHandler定时任务结束");
    }
}
