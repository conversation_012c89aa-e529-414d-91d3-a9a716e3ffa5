package com.sohu.job.service;

import com.sohu.resource.api.RemoteFileService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 分片上传清除超时任务
 * <AUTHOR>
 * @since 2024/4/23
 */
@Slf4j
@Service
public class MultiPartUploadClearTimeOutTaskService {

    @DubboReference
    private RemoteFileService remoteFileService;

    /**
     * 分片上传清除超时任务
     */
    @XxlJob(value = "sohuMultiPartUploadClearTimeOutTaskHandler", init = "init", destroy = "destroy")
    public void sohuMultiPartUploadClearTimeOutTaskHandler()throws Exception {
        remoteFileService.clearUploadTimeoutTask();
    }

    public void init() {
        log.info("MultiPartUploadClearTimeOutTaskService定时任务启动");
        XxlJobHelper.log("MultiPartUploadClearTimeOutTaskService定时任务启动");
    }

    public void destroy() {
        log.info("MultiPartUploadClearTimeOutTaskService定时任务结束");
        XxlJobHelper.log("MultiPartUploadClearTimeOutTaskService定时任务结束");
    }
}
