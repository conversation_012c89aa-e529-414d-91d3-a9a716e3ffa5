package com.sohu.busyOrder.controller.app;

import com.sohu.busyorder.api.bo.SohuBusyTaskTemplateBo;
import com.sohu.busyorder.api.vo.SohuBusyTaskTemplateVo;
import com.sohu.busyOrder.service.ISohuBusyTaskTemplateService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * APP商单新版-拆单模板
 * 前端访问路由地址为:/app/busy/task/template
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/busy/task/template")
public class AppBusyTaskTemplateController extends BaseController {

    private final ISohuBusyTaskTemplateService iSohuBusyTaskTemplateService;

    /**
     * 模板管理列表
     */
    @GetMapping("/manage/list")
    public TableDataInfo<SohuBusyTaskTemplateVo> managePage(SohuBusyTaskTemplateBo bo, PageQuery pageQuery) {
        return iSohuBusyTaskTemplateService.queryPageList(bo, pageQuery);
    }

    @GetMapping("/query/manage/list")
    public TableDataInfo<SohuBusyTaskTemplateVo> queryManageList(SohuBusyTaskTemplateBo bo) {
        return iSohuBusyTaskTemplateService.queryManageList(bo);
    }

    /**
     * 模板数据列表
     */
    @GetMapping("/data/list")
    public TableDataInfo<SohuBusyTaskTemplateVo> dataPage(SohuBusyTaskTemplateBo bo, PageQuery pageQuery) {
        return iSohuBusyTaskTemplateService.dataPage(bo, pageQuery);
    }

    /**
     * 获取拆单模板详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<SohuBusyTaskTemplateVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuBusyTaskTemplateService.queryById(id));
    }

    /**
     * 新增拆单模板
     */
    @Log(title = "拆单模板", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuBusyTaskTemplateBo bo) {
        return toAjax(iSohuBusyTaskTemplateService.insertByBo(bo));
    }

    /**
     * 修改拆单模板
     */
    @Log(title = "修改拆单模板", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuBusyTaskTemplateBo bo) {
        return toAjax(iSohuBusyTaskTemplateService.updateByBo(bo));
    }

    /**
     * 删除拆单模板
     *
     * @param ids 主键串
     */
    @Log(title = "拆单模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuBusyTaskTemplateService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

}
