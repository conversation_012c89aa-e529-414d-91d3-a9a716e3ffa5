package com.sohu.busyOrder.controller;

import com.sohu.busyOrder.service.ISohuBusyOrderExecuteService;
import com.sohu.busyorder.api.bo.SohuBusyOrderExecuteBo;
import com.sohu.busyorder.api.vo.SohuBusyOrderExecuteVo;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 商单执行记录
 * 前端访问路由地址为:/busy-order/execute
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/execute")
public class SohuBusyOrderExecuteController extends BaseController {

    private final ISohuBusyOrderExecuteService iSohuBusyOrderExecuteService;

    /**
     * 查询商单执行记录列表
     */
    @GetMapping("/list")
    @Log(title = "查询商单执行记录列表")
    public TableDataInfo<SohuBusyOrderExecuteVo> list(SohuBusyOrderExecuteBo bo, PageQuery pageQuery) {
        return iSohuBusyOrderExecuteService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出商单执行记录列表
     */
    @Log(title = "导出商单执行记录列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuBusyOrderExecuteBo bo, HttpServletResponse response) {
        List<SohuBusyOrderExecuteVo> list = iSohuBusyOrderExecuteService.queryList(bo);
        ExcelUtil.exportExcel(list, "商单执行记录", SohuBusyOrderExecuteVo.class, response);
    }

    /**
     * 获取商单执行记录详细信息
     */
    @GetMapping("/{id}")
    @Log(title = "获取商单执行记录详细信息")
    public R<SohuBusyOrderExecuteVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuBusyOrderExecuteService.queryById(id));
    }

    /**
     * 新增商单执行记录
     */
    @Log(title = "新增商单执行记录", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuBusyOrderExecuteBo bo) {
        return toAjax(iSohuBusyOrderExecuteService.insertByBo(bo));
    }

    @PostMapping("/handle")
    @Operation(summary = "处理执行")
    public R<Long> handleExecute(@RequestBody SohuBusyOrderExecuteBo bo) {
        return R.ok(iSohuBusyOrderExecuteService.handleExecute(bo));
    }

    @PostMapping("/complete")
    @Operation(summary = "发单方完成接单")
    public R<Long> completeExecute(@RequestBody SohuBusyOrderExecuteBo bo) {
        return R.ok(iSohuBusyOrderExecuteService.completeExecute(bo));
    }

    @PostMapping("/pass")
    @Operation(summary = "发单方同意或拒绝接单")
    public R<Long> passExecute(@RequestBody SohuBusyOrderExecuteBo bo) {
        return R.ok(iSohuBusyOrderExecuteService.passExecute(bo));
    }

    @PostMapping("/rece/complete")
    @Operation(summary = "接单方点击完成接单")
    public R<Long> receCompleteExecute(@RequestBody SohuBusyOrderExecuteBo bo) {
        return R.ok(iSohuBusyOrderExecuteService.receCompleteExecute(bo));
    }

    /**
     * 删除商单执行记录
     */
    @Log(title = "删除商单执行记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuBusyOrderExecuteService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
