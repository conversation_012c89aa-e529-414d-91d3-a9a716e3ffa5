package com.sohu.busyOrder.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.busyOrder.service.ISohuBusyOrderAnnexService;
import com.sohu.busyorder.api.bo.SohuBusyOrderAnnexBo;
import com.sohu.busyorder.api.vo.SohuBusyOrderAnnexVo;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.utils.BigDecimalUtils;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Arrays;

/**
 * 商单附件
 * 前端访问路由地址为:/busy-order/annex
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/annex")
public class SohuBusyOrderAnnexController extends BaseController {

    private final ISohuBusyOrderAnnexService iSohuBusyOrderAnnexService;

    /**
     * 查询商单附件列表
     */
    @GetMapping("/list")
    @Log(title = "商单附件")
    public TableDataInfo<SohuBusyOrderAnnexVo> list(SohuBusyOrderAnnexBo bo, PageQuery pageQuery) {
        return iSohuBusyOrderAnnexService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取商单附件详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:busyOrderAnnex:query")
    @GetMapping("/{id}")
    @Log(title = "商单附件")
    public R<SohuBusyOrderAnnexVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuBusyOrderAnnexService.queryById(id));
    }

    /**
     * 新增商单附件
     */
    @SaCheckPermission("system:busyOrderAnnex:add")
    @Log(title = "商单附件", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuBusyOrderAnnexBo bo) {
        return toAjax(iSohuBusyOrderAnnexService.insertByBo(bo));
    }

    /**
     * 修改商单附件
     */
    @SaCheckPermission("system:busyOrderAnnex:edit")
    @Log(title = "商单附件", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuBusyOrderAnnexBo bo) {
        return toAjax(iSohuBusyOrderAnnexService.updateByBo(bo));
    }

    /**
     * 删除商单附件
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:busyOrderAnnex:remove")
    @Log(title = "商单附件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuBusyOrderAnnexService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
