package com.sohu.busyOrder.appevent.event;

import com.sohu.busyOrder.domain.SohuBusyTaskReceive;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2024/5/23
 */
@Getter
public class BusyTaskReceiveDeliveryPassEvent extends ApplicationEvent {

    private final SohuBusyTaskReceive receive;

    public BusyTaskReceiveDeliveryPassEvent(Object source, SohuBusyTaskReceive receive) {
        super(source);
        this.receive = receive;
    }
}
