package com.sohu.busyOrder.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.busyOrder.domain.SohuBusyTaskSplitApply;
import com.sohu.busyorder.api.bo.SohuBusyTaskSplitApplyBo;
import com.sohu.busyorder.api.vo.SohuBusyTaskSplitApplyVo;
import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

/**
 * 拆单方任务申请Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-11
 */
public interface SohuBusyTaskSplitApplyMapper extends BaseMapperPlus<SohuBusyTaskSplitApplyMapper, SohuBusyTaskSplitApply, SohuBusyTaskSplitApplyVo> {

    /**
     * 查看个人拆单分页列表
     *
     * @param build
     * @param bo
     */
    Page<SohuBusyTaskSplitApplyVo> selectVoListPage(@Param("build") Page<Object> build, @Param("bo") SohuBusyTaskSplitApplyBo bo);

}
