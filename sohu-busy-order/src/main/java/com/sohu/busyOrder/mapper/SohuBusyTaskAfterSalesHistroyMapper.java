package com.sohu.busyOrder.mapper;

import com.sohu.busyOrder.domain.SohuBusyTaskAfterSalesHistroy;
import com.sohu.busyorder.api.vo.SohuBusyTaskAfterSalesHistroyVo;
import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 售后操作历史Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface SohuBusyTaskAfterSalesHistroyMapper extends BaseMapperPlus<SohuBusyTaskAfterSalesHistroyMapper, SohuBusyTaskAfterSalesHistroy, SohuBusyTaskAfterSalesHistroyVo> {

    List<SohuBusyTaskAfterSalesHistroyVo> selectAllList(@Param("afterSalesId")Long afterSalesId);
}
