package com.sohu.busyOrder.mapper;

import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.busyOrder.domain.SohuBusyTaskExecute;
import com.sohu.busyorder.api.vo.SohuBusyTaskExecuteVo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 商单接单执行记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Mapper
public interface SohuBusyTaskExecuteMapper extends BaseMapperPlus<SohuBusyTaskExecuteMapper, SohuBusyTaskExecute, SohuBusyTaskExecuteVo> {

}
