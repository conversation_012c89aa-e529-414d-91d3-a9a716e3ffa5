package com.sohu.busyOrder.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.busyOrder.domain.SohuBusyOrderRece;
import com.sohu.busyorder.api.bo.SohuBusyOrderReceBo;
import com.sohu.busyorder.api.vo.SohuBusyOrderDistributionVo;
import com.sohu.busyorder.api.vo.SohuBusyOrderReceVo;
import com.sohu.busyorder.api.vo.SohuBusyOrderVo;
import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 商单接单Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
public interface SohuBusyOrderReceMapper extends BaseMapperPlus<SohuBusyOrderReceMapper, <PERSON><PERSON><PERSON>usyOrderRece, SohuBusyOrderReceVo> {


    /**
     * 用户已接商单列表
     *
     * @param userId         用户ID
     * @param orderTitle     商单标题，模糊搜索
     * @param receStatusList 接单状态
     * @param pageQuery      分页参数
     * @return
     */
    Page<SohuBusyOrderVo> userRece(@Param("userId") Long userId,
                                   @Param("orderTitle") String orderTitle,
                                   @Param("receStatusList") List<String> receStatusList,
                                   @Param("page") IPage pageQuery);

    void updateByOrderId(@Param("orderId") Long orderId, @Param("receStatusList") List<String> receStatusList, @Param("receiveStatus") String receiveStatus);

    /**
     * 查询接单人列表，每个商单只查询最新的一条接单
     *
     * @param orderIds
     */
    List<SohuBusyOrderRece> selectReceLimitOneByOrderIds(@Param("orderIds") List<Long> orderIds);

    Page<SohuBusyOrderDistributionVo> queryPageDistribution(@Param("bo") SohuBusyOrderReceBo bo, @Param("page") Page page);

    @Select("SELECT COUNT(*) FROM sohu_busy_order_receive a WHERE a.order_id = #{orderId} AND a.receive_status IN ('Cooperating','Finished')")
    Integer countCooperatingStat(@Param("orderId") Long orderId);


    void updateReceStatus(@Param("orderId") Long orderId, @Param("receStatus") String receStatus, @Param("oldReceStatus") String oldReceStatus);

    /**
     * 终止正在合作中的接单或待审核的商单
     *
     * @param orderId
     */
    void offCooperatingRece(@Param("orderId") Long orderId);

}
