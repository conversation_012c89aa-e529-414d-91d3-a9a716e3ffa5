package com.sohu.busyOrder.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.busyOrder.domain.SohuBusyTaskRefund;
import com.sohu.busyorder.api.bo.SohuPayBusyBo;
import com.sohu.busyorder.api.vo.SohuBusyTaskRefundVo;
import com.sohu.busyorder.api.vo.SohuPayBusyVo;
import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商单退款Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface SohuBusyTaskRefundMapper extends BaseMapperPlus<SohuBusyTaskRefundMapper, SohuBusyTaskRefund, SohuBusyTaskRefundVo> {
    IPage<SohuPayBusyVo> pageList(@Param("page") Page page, @Param("bo") SohuPayBusyBo sohuPayBusyBo);

    List<SohuPayBusyVo> queryByTaskNumbers(@Param("taskNumbers") List<String> taskNumbers);
}
