package com.sohu.busyOrder.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;

/**
 * 商单与素材关联对象 sohu_busy_task_source_material
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@TableName("sohu_busy_task_source_material")
public class SohuBusyTaskSourceMaterial {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 商单ID
     */
    private Long busyTaskId;
    /**
     * 素材地址
     */
    private String sourceMaterialUrl;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
