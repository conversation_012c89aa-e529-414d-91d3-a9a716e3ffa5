package com.sohu.busyOrder.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商单接单对象 sohu_busy_task_receive
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_busy_task_receive")
public class SohuBusyTaskReceive extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 子任务编号
     */
    private String taskNumber;
    /**
     * 接单人ID
     */
    private Long userId;
    /**
     * 申请理由
     */
    private String applyMsg;
    /**
     * 申请附件
     */
    private String applyAnnex;
    /**
     * 保证金;接单方希望任务方交的保证金
     */
    private BigDecimal amount;
    /**
     * 拆单思路ID
     */
    private Long templateId;
    /**
     * 状态;WaitApprove-审核中，Pass-通过，Refuse-审核拒绝，Execute-执行中且发单方已支付保证金，WaitSettle-待结算，OverSettle-已结算即完成，Error-商单异常
     */
    private String state;
    /**
     * 拒绝理由
     */
    private String refuseMsg;

    /**
     * 审核通过时间
     */
    private Date passTime;

    /**
     * 商单接单完成时间
     */
    private Date finishTime;

    /**
     * 选择状态：0=淘汰 1=同意
     */
    private Boolean backup;

    /**
     * 分销人ID
     */
    private Long sharePerson;

    /**
     * 是否支付佣金
     */
    private Boolean isIndependent;

    /**
     * 分销人金额
     */
    private BigDecimal distributionAmount;

    /**
     * MCN机构ID(用户ID)
     */
    private Long mcnId;

    /**
     * 过期时间
     */
    private Date expireTime;
    
    /**
     * 群id
     */
    private Long relationId;

    /**
     * 删除标志（0代表存在 2代表删除）（功能组）
     */
    @TableLogic
    private String delFlag;

    /**
     * 站点类型 1 城市站 2 行业站
     */
    private Integer siteType;

    /**
     * 站点ID
     */
    private Long siteId;
}
