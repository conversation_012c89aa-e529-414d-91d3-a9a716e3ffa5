package com.sohu.busyOrder.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.math.BigDecimal;
import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 商单退款对象 sohu_busy_task_refund
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_busy_task_refund")
public class SohuBusyTaskRefund extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 商单任务编号
     */
    private String taskNumber;
    /**
     * 用户userId
     */
    private Long userId;
    /**
     * 三方支付流水号
     */
    private String transactionId;
    /**
     * 退单编码
     */
    private String refundOrderNo;
    /**
     * 退款流水号
     */
    private String refundNumber;
    /**
     * 退款状态 success,fail
     */
    private String refundStatus;
    /**
     * 退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 是否删除
     */
    private Integer deleted;

    /**
     * 退款原因
     */
    private String refundReason;

}
