package com.sohu.busyOrder.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商单被邀请人列对象 sohu_busy_task_invite
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_busy_task_invite")
public class SohuBusyTaskInvite extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 主任务编号
     */
    private String masterTaskNumber;
    /**
     * 子任务编号
     */
    private String taskNumber;
    /**
     * 被邀请人ID
     */
    private Long userId;

}
