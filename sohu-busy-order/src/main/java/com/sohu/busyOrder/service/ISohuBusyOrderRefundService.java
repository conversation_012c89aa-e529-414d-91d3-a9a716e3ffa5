package com.sohu.busyOrder.service;

import com.sohu.busyorder.api.bo.SohuBusyOrderRefundBo;
import com.sohu.busyorder.api.vo.SohuBusyOrderRefundVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 商单退款Service接口
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
public interface ISohuBusyOrderRefundService {

    /**
     * 查询商单退款
     */
    SohuBusyOrderRefundVo queryById(Long id);

    /**
     * 查询商单退款列表
     */
    TableDataInfo<SohuBusyOrderRefundVo> queryPageList(SohuBusyOrderRefundBo bo, PageQuery pageQuery);

    /**
     * 查询商单退款列表
     */
    List<SohuBusyOrderRefundVo> queryList(SohuBusyOrderRefundBo bo);

    /**
     * 修改商单退款
     */
    Boolean insertByBo(SohuBusyOrderRefundBo bo);

    /**
     * 修改商单退款
     */
    Boolean updateByBo(SohuBusyOrderRefundBo bo);

    /**
     * 校验并批量删除商单退款信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
