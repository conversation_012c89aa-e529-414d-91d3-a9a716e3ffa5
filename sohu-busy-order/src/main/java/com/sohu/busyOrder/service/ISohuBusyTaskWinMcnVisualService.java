package com.sohu.busyOrder.service;

import com.sohu.busyorder.api.bo.SohuBusyTaskWinMcnVisualBo;
import com.sohu.busyorder.api.vo.SohuBusyTaskWinMcnVisualVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * MCN任务库可见人（辅助查询）Service接口
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface ISohuBusyTaskWinMcnVisualService {

    /**
     * 查询MCN任务库可见人（辅助查询）
     */
    SohuBusyTaskWinMcnVisualVo queryById(Long id);

    /**
     * 查询MCN任务库可见人（辅助查询）列表
     */
    TableDataInfo<SohuBusyTaskWinMcnVisualVo> queryPageList(SohuBusyTaskWinMcnVisualBo bo, PageQuery pageQuery);

    /**
     * 查询MCN任务库可见人（辅助查询）列表
     */
    List<SohuBusyTaskWinMcnVisualVo> queryList(SohuBusyTaskWinMcnVisualBo bo);

    /**
     * 修改MCN任务库可见人（辅助查询）
     */
    Boolean insertByBo(SohuBusyTaskWinMcnVisualBo bo);

    /**
     * 修改MCN任务库可见人（辅助查询）
     */
    Boolean updateByBo(SohuBusyTaskWinMcnVisualBo bo);

    /**
     * 校验并批量删除MCN任务库可见人（辅助查询）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
