package com.sohu.busyOrder.service;

import com.sohu.busyorder.api.bo.SohuBusyOrderExecuteBo;
import com.sohu.busyorder.api.vo.SohuBusyOrderExecuteVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 商单执行记录Service接口
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
public interface ISohuBusyOrderExecuteService {

    /**
     * 查询商单执行记录
     */
    SohuBusyOrderExecuteVo queryById(Long id);

    /**
     * 查询商单执行记录列表
     */
    TableDataInfo<SohuBusyOrderExecuteVo> queryPageList(SohuBusyOrderExecuteBo bo, PageQuery pageQuery);

    /**
     * 查询商单执行记录列表
     */
    List<SohuBusyOrderExecuteVo> queryList(SohuBusyOrderExecuteBo bo);

    /**
     * 修改商单执行记录
     */
    Boolean insertByBo(SohuBusyOrderExecuteBo bo);

    /**
     * 修改商单执行记录
     */
    Boolean updateByBo(SohuBusyOrderExecuteBo bo);

    /**
     * 校验并批量删除商单执行记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 处理执行
     *
     * @param bo
     * @return
     */
    Long handleExecute(SohuBusyOrderExecuteBo bo);

    /**
     * 发单方完成接单
     * @param bo
     * @return
     */
    Long completeExecute(SohuBusyOrderExecuteBo bo);

    Long receCompleteExecute(SohuBusyOrderExecuteBo bo);

    /**
     * 发单方同意或拒绝接单
     * @param bo
     * @return
     */
    Long passExecute(SohuBusyOrderExecuteBo bo);
    /**
     * 合作状态数量统计
     */
    Integer CooperatingStat(Long id);
}
