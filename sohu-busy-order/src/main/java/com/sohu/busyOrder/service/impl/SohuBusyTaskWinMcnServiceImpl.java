package com.sohu.busyOrder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.busyOrder.domain.SohuBusyTaskSite;
import com.sohu.busyOrder.domain.SohuBusyTaskWinMcn;
import com.sohu.busyOrder.domain.SohuBusyTaskWinMcnVisual;
import com.sohu.busyOrder.mapper.SohuBusyTaskSiteMapper;
import com.sohu.busyOrder.mapper.SohuBusyTaskWinMcnMapper;
import com.sohu.busyOrder.mapper.SohuBusyTaskWinMcnVisualMapper;
import com.sohu.busyOrder.service.ISohuBusyTaskWinMcnService;
import com.sohu.busyorder.api.bo.SohuBusyTaskWinMcnBo;
import com.sohu.busyorder.api.enums.BusyTaskVisualTypeEnums;
import com.sohu.busyorder.api.vo.SohuBusyTaskWinMcnVo;
import com.sohu.common.core.enums.SohuBusyTaskState;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.service.mcn.RemoteMiddleMcnUserService;
import com.sohu.middle.api.vo.mcn.SohuMcnUserVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * MCN任务库Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
@RequiredArgsConstructor
@Service
public class SohuBusyTaskWinMcnServiceImpl implements ISohuBusyTaskWinMcnService {

    private final SohuBusyTaskWinMcnMapper baseMapper;
    private final SohuBusyTaskWinMcnVisualMapper sohuBusyTaskWinMcnVisualMapper;
    private final SohuBusyTaskSiteMapper sohuBusyTaskSiteMapper;
    @DubboReference
    private final RemoteMiddleMcnUserService mcnUserService;

    /**
     * 查询MCN任务库
     */
    @Override
    public SohuBusyTaskWinMcnVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询MCN任务库列表
     */
    @Override
    public TableDataInfo<SohuBusyTaskWinMcnVo> queryPageList(SohuBusyTaskWinMcnBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuBusyTaskWinMcn> lqw = buildQueryWrapper(bo);
        Page<SohuBusyTaskWinMcnVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询MCN任务库列表
     */
    @Override
    public List<SohuBusyTaskWinMcnVo> queryList(SohuBusyTaskWinMcnBo bo) {
        LambdaQueryWrapper<SohuBusyTaskWinMcn> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuBusyTaskWinMcn> buildQueryWrapper(SohuBusyTaskWinMcnBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuBusyTaskWinMcn> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getMcnId() != null, SohuBusyTaskWinMcn::getMcnId, bo.getMcnId());
        lqw.eq(StringUtils.isNotBlank(bo.getTaskNumber()), SohuBusyTaskWinMcn::getTaskNumber, bo.getTaskNumber());
        lqw.eq(bo.getReceiveState() != null, SohuBusyTaskWinMcn::getReceiveState, bo.getReceiveState());
        lqw.eq(StringUtils.isNotBlank(bo.getVisualType()), SohuBusyTaskWinMcn::getVisualType, bo.getVisualType());
        lqw.eq(StringUtils.isNotBlank(bo.getVisualVal()), SohuBusyTaskWinMcn::getVisualVal, bo.getVisualVal());
        return lqw;
    }

    /**
     * 新增MCN任务库
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(SohuBusyTaskWinMcnBo bo) {
        SohuBusyTaskWinMcn add = BeanUtil.toBean(bo, SohuBusyTaskWinMcn.class);
        add.setId(null);
        add.setMcnId(LoginHelper.getUserId());
        add.setReceiveState(false);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        //保存MCN任务库可见范围达人
        saveWindowVisual(bo);
        return flag;
    }

    /**
     * 修改MCN任务库
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(SohuBusyTaskWinMcnBo bo) {
        SohuBusyTaskWinMcn update = BeanUtil.toBean(bo, SohuBusyTaskWinMcn.class);
        update.setMcnId(null);
        update.setReceiveState(false);
        validEntityBeforeSave(update);
        SohuBusyTaskWinMcn entity = baseMapper.selectById(bo.getId());
        if (Objects.isNull(entity)) {
            throw new RuntimeException("数据不存在");
        }
        if (entity.getReceiveState()) {
            throw new RuntimeException("已接单，不能修改");
        }
        LambdaUpdateWrapper<SohuBusyTaskWinMcn> luw = new LambdaUpdateWrapper<>();
        luw.set(SohuBusyTaskWinMcn::getVisualVal, bo.getVisualVal())
                .eq(SohuBusyTaskWinMcn::getId, bo.getId());
        baseMapper.update(update, luw);
        deleteVisualById(bo.getId());
        //保存MCN任务库可见范围达人
        saveWindowVisual(bo);
        return Boolean.TRUE;
    }

    /**
     * 保存MCN任务库可见范围达人
     *
     * @param bo
     */
    private void saveWindowVisual(SohuBusyTaskWinMcnBo bo) {
        if (Objects.equals(BusyTaskVisualTypeEnums.GROUP.getCode(), bo.getVisualType())) {
            if (StrUtil.isBlank(bo.getVisualVal())) {
                throw new RuntimeException("请选择指定分组");
            }
            String[] visualVal = bo.getVisualVal().split(",");
            if (visualVal == null || visualVal.length == 0) {
                return;
            }
            List<SohuMcnUserVo> mcnUserList = mcnUserService.selectBatchIds(Arrays.asList(visualVal));
            if (CollUtil.isEmpty(mcnUserList)) {
                return;
            }
            List<SohuBusyTaskWinMcnVisual> visualList = new ArrayList<>();
            for (SohuMcnUserVo user : mcnUserList) {
                SohuBusyTaskWinMcnVisual visual = new SohuBusyTaskWinMcnVisual();
                visual.setWindowId(bo.getId());
                visual.setUserId(user.getUserId());
                visualList.add(visual);
            }
            sohuBusyTaskWinMcnVisualMapper.insertBatch(visualList);
        } else if (Objects.equals(BusyTaskVisualTypeEnums.INDIVIDUAL.getCode(), bo.getVisualType())) {
            if (StrUtil.isBlank(bo.getVisualVal())) {
                throw new RuntimeException("请选择指定达人");
            }
            String[] visualVal = bo.getVisualVal().split(",");
            if (visualVal == null || visualVal.length == 0) {
                return;
            }
            List<SohuBusyTaskWinMcnVisual> visualList = new ArrayList<>();
            for (String val : visualVal) {
                SohuBusyTaskWinMcnVisual visual = new SohuBusyTaskWinMcnVisual();
                visual.setWindowId(bo.getId());
                visual.setUserId(Long.valueOf(val));
                visualList.add(visual);
            }
            sohuBusyTaskWinMcnVisualMapper.insertBatch(visualList);
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuBusyTaskWinMcn entity) {
        //TODO 做一些数据校验,如唯一约束
        SohuBusyTaskSite busyTaskSite = sohuBusyTaskSiteMapper.selectOne(SohuBusyTaskSite::getTaskNumber, entity.getTaskNumber());
        if (Objects.isNull(busyTaskSite)) {
            throw new RuntimeException("任务不存在");
        }
        //需要是：上架、待接单、全部可以接单
        if (!(Objects.equals(SohuBusyTaskState.WaitReceive.name(), busyTaskSite.getState())
                && Objects.equals(SohuBusyTaskState.OnShelf.name(), busyTaskSite.getShelfState())
                && (!busyTaskSite.getReceiveLimit()))) {
            throw new RuntimeException("任务状态不满足加入条件");
        }
    }

    /**
     * 批量删除MCN任务库
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (CollUtil.isNotEmpty(ids)) {
            for (Long id : ids) {
                SohuBusyTaskWinMcn entity = baseMapper.selectById(id);
                if (Objects.isNull(entity)) {
                    throw new RuntimeException("数据不存在");
                }
                if (entity.getReceiveState()) {
                    throw new RuntimeException("已接单，不能删除");
                }
                deleteVisualById(id);
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 删除关联的可见范围
     *
     * @param id 活动ID
     * @return
     */
    private Boolean deleteVisualById(Long id) {
        return sohuBusyTaskWinMcnVisualMapper.delete(SohuBusyTaskWinMcnVisual::getWindowId, id);
    }
}
