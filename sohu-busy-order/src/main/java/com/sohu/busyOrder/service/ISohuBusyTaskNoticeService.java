package com.sohu.busyOrder.service;

import com.sohu.busyOrder.domain.SohuBusyTaskSite;
import com.sohu.busyorder.api.enums.TaskNoticeEnum;

/**
 * 商单消息通知通用逻辑
 *
 * @Author: leibo
 * @Date: 2025/2/13 12:00
 **/
public interface ISohuBusyTaskNoticeService {

    /**
     * 发送主单消息通知
     *
     * @param id
     * @param noticeEnum
     * @param userId
     * @param ext
     * @param jiguangPush
     * @return
     */
    Boolean sendTaskNotice(Long id, TaskNoticeEnum noticeEnum, Long userId, String ext, String nickName , Boolean jiguangPush);

    /**
     * 发送子单消息通知
     *
     * @param taskNumber
     * @param noticeEnum
     * @param userId
     * @param ext
     * @param jiguangPush
     * @param afterSalesId
     * @param afterSaleState
     * @return
     */
    Boolean sendTaskSiteNotice(String taskNumber, TaskNoticeEnum noticeEnum, Long userId, String ext, String nickName,
                               Boolean jiguangPush, Long afterSalesId, String afterSaleState);

    /**
     * 发送极光推送消息
     *
     * @param userId
     * @param noticeEnum
     * @param sohuBusyTaskSite
     * @return
     */
    Boolean pushJiguangNotice(Long userId, TaskNoticeEnum noticeEnum, SohuBusyTaskSite sohuBusyTaskSite);
}
