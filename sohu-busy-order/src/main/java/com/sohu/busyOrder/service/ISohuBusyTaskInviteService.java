package com.sohu.busyOrder.service;

import com.sohu.busyorder.api.vo.SohuBusyTaskInviteVo;
import com.sohu.busyorder.api.bo.SohuBusyTaskInviteBo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 商单被邀请人列Service接口
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
public interface ISohuBusyTaskInviteService {

    /**
     * 查询商单被邀请人列
     */
    SohuBusyTaskInviteVo queryById(Long id);

    /**
     * 查询商单被邀请人列列表
     */
    TableDataInfo<SohuBusyTaskInviteVo> queryPageList(SohuBusyTaskInviteBo bo, PageQuery pageQuery);

    /**
     * 查询商单被邀请人列列表
     */
    List<SohuBusyTaskInviteVo> queryList(SohuBusyTaskInviteBo bo);

    /**
     * 修改商单被邀请人列
     */
    Boolean insertByBo(SohuBusyTaskInviteBo bo);

    /**
     * 修改商单被邀请人列
     */
    Boolean updateByBo(SohuBusyTaskInviteBo bo);

    /**
     * 校验并批量删除商单被邀请人列信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
