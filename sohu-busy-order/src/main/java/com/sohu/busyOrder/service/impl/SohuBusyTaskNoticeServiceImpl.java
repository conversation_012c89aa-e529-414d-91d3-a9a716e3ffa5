package com.sohu.busyOrder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.sohu.busyOrder.domain.SohuBusyTask;
import com.sohu.busyOrder.domain.SohuBusyTaskReceive;
import com.sohu.busyOrder.domain.SohuBusyTaskSite;
import com.sohu.busyOrder.mapper.SohuBusyTaskMapper;
import com.sohu.busyOrder.mapper.SohuBusyTaskReceiveMapper;
import com.sohu.busyOrder.mapper.SohuBusyTaskSiteMapper;
import com.sohu.busyOrder.service.ISohuBusyTaskNoticeService;
import com.sohu.busyorder.api.enums.TaskNoticeEnum;
import com.sohu.busyorder.api.vo.SohuBusyTaskReceiveVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.UserConstants;
import com.sohu.common.core.enums.AppPathTypeEnum;
import com.sohu.common.core.enums.PushJiGuangBizTypeEnum;
import com.sohu.common.core.enums.SohuBusyTaskState;
import com.sohu.common.core.utils.JsonUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.middle.api.bo.notice.SohuTaskNoticeBo;
import com.sohu.middle.api.service.RemoteMiddleCategoryService;
import com.sohu.middle.api.service.notice.RemoteMiddleTaskNoticeService;
import com.sohu.middle.api.vo.SohuCategoryVo;
import com.sohu.resource.api.RemoteJiguangService;
import com.sohu.resource.api.domain.bo.SohuJiguangPush2UserReqBo;
import com.sohu.system.api.RemoteUserService;
import io.seata.common.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 商单消息通知通用逻辑实现
 *
 * @Author: leibo
 * @Date: 2025/2/13 12:01
 **/
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuBusyTaskNoticeServiceImpl implements ISohuBusyTaskNoticeService {

    private final AsyncConfig asyncConfig;

    @DubboReference
    protected RemoteMiddleCategoryService remoteMiddleCategoryService;
    @DubboReference
    protected RemoteMiddleTaskNoticeService remoteMiddleTaskNoticeService;
    @DubboReference
    private RemoteJiguangService remoteJiguangService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @Autowired
    private SohuBusyTaskMapper busyTaskMapper;
    @Autowired
    private SohuBusyTaskSiteMapper sohuBusyTaskSiteMapper;
    @Autowired
    private SohuBusyTaskReceiveMapper sohuBusyTaskReceiveMapper;

    @Override
    public Boolean sendTaskNotice(Long id, TaskNoticeEnum noticeEnum, Long userId, String ext, String nickName, Boolean jiguangPush) {
        log.info("发送主单消息入参 id:{}, noticeEnum:{}, userId:{}, ext:{}, nickName:{}, jiguangPush:{}", id, noticeEnum.name(), userId, ext, nickName, jiguangPush);
        SohuBusyTask sohuBusyTask = busyTaskMapper.selectById(id);
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(sohuBusyTask.getType());
        if (Objects.isNull(sohuBusyTask)) {
            log.error("商单不存在, 无法发送消息。id:{}", id);
            return Boolean.FALSE;
        }
        List<SohuBusyTaskSiteVo> siteList = sohuBusyTaskSiteMapper.selectVoList(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, sohuBusyTask.getTaskNumber()));
        List<String> taskNumberList = new ArrayList<>();
        SohuBusyTaskSite site = new SohuBusyTaskSite();
        if (CollectionUtils.isNotEmpty(siteList)) {
            taskNumberList = siteList.stream().map(SohuBusyTaskSiteVo::getTaskNumber).collect(Collectors.toList());
            BeanUtil.copyProperties(siteList.get(0), site);
            ;
        }
        SohuBusyTaskReceiveVo receiveVo = sohuBusyTaskReceiveMapper.selectVoOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                .in(SohuBusyTaskReceive::getTaskNumber, taskNumberList)
                .eq(SohuBusyTaskReceive::getUserId, userId)
                .ne(SohuBusyTaskReceive::getState, SohuBusyTaskState.Cancel.name())
                .last("limit 1"));
        Map<String, Object> map = new HashMap<>();
        map.put("busyTaskId", sohuBusyTask.getId());
        map.put("masterTaskNumber", sohuBusyTask.getTaskNumber());
        map.put("taskNumber", Objects.isNull(receiveVo) ? taskNumberList.get(0) : receiveVo.getTaskNumber());
        map.put("receiveNum", sohuBusyTask.getReceiveNum());
        map.put("noticeCode", noticeEnum.getNoticeCode());
        map.put("constMark", sohuCategoryVo.getConstMark());
        map.put("kickbackType", sohuBusyTask.getKickbackType());
        // 发送系统通知
        this.sendNotice(userId, String.valueOf(id), noticeEnum.getTitle(), String.format(noticeEnum.getMsg(), ext),
                noticeEnum.getNoticeCode(), sohuBusyTask.getTitle(), nickName, JsonUtils.toJsonString(map));
        if (jiguangPush) {
            // 发送极光推送通知
            CompletableFuture.runAsync(() -> this.pushJiguangNotice(userId, noticeEnum,
                    sohuBusyTask, site, null, null, nickName, sohuCategoryVo.getConstMark()), asyncConfig.getAsyncExecutor());
        }
        return null;
    }

    @Override
    public Boolean sendTaskSiteNotice(String taskNumber, TaskNoticeEnum noticeEnum, Long userId,
                                      String ext, String nickName, Boolean jiguangPush,
                                      Long afterSalesId, String afterSaleState) {
        log.info("发送子单消息入参 taskNumber:{}, noticeEnum:{}, userId:{}, ext:{}, nickName:{}, jiguangPush:{}", taskNumber, noticeEnum.name(), userId, ext, nickName, jiguangPush);
        SohuBusyTaskSite sohuBusyTaskSite = sohuBusyTaskSiteMapper.selectOne(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getTaskNumber, taskNumber));
        if (Objects.isNull(sohuBusyTaskSite)) {
            log.error("商单不存在, 无法发送消息。taskNumber:{}", taskNumber);
            return Boolean.FALSE;
        }
        SohuBusyTask sohuBusyTask = busyTaskMapper.selectOne(Wrappers.<SohuBusyTask>lambdaQuery()
                .eq(SohuBusyTask::getTaskNumber, sohuBusyTaskSite.getMasterTaskNumber()));
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(sohuBusyTask.getType());
        Map<String, Object> map = new HashMap<>();
        map.put("busyTaskId", sohuBusyTask.getId());
        map.put("masterTaskNumber", sohuBusyTask.getTaskNumber());
        map.put("taskNumber", sohuBusyTaskSite.getTaskNumber());
        map.put("receiveNum", sohuBusyTask.getReceiveNum());
        map.put("noticeCode", noticeEnum.getNoticeCode());
        map.put("constMark", sohuCategoryVo.getConstMark());
        map.put("kickbackType", sohuBusyTask.getKickbackType());
        // 发送系统通知
        this.sendNotice(userId, taskNumber, noticeEnum.getTitle(), String.format(noticeEnum.getMsg(), ext),
                noticeEnum.getNoticeCode(), sohuBusyTaskSite.getTitle(), nickName, JsonUtils.toJsonString(map));
        if (jiguangPush) {
            // 发送极光推送通知
            CompletableFuture.runAsync(() -> this.pushJiguangNotice(userId, noticeEnum,
                    sohuBusyTask, sohuBusyTaskSite, afterSalesId, afterSaleState, nickName, sohuCategoryVo.getConstMark()), asyncConfig.getAsyncExecutor());
        }
        return null;
    }

    @Override
    public Boolean pushJiguangNotice(Long userId, TaskNoticeEnum noticeEnum, SohuBusyTaskSite sohuBusyTaskSite) {
        // 发送极光推送通知
        SohuBusyTask sohuBusyTask = busyTaskMapper.selectOne(Wrappers.<SohuBusyTask>lambdaQuery()
                .eq(SohuBusyTask::getTaskNumber, sohuBusyTaskSite.getMasterTaskNumber()));
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(sohuBusyTask.getType());
        return this.pushJiguangNotice(userId, noticeEnum, sohuBusyTask, sohuBusyTaskSite, null, null, null, sohuCategoryVo.getConstMark());
    }

    /**
     * 发送极光通知的方法
     *
     * @param userId
     * @param noticeEnum
     * @param sohuBusyTaskSite
     * @return
     */
    private Boolean pushJiguangNotice(Long userId, TaskNoticeEnum noticeEnum,
                                      SohuBusyTask sohuBusyTask, SohuBusyTaskSite sohuBusyTaskSite,
                                      Long afterSalesId, String afterSaleState, String nickName, String constMark) {
        AppPathTypeEnum jumpPathType = null;
        String jiguangAlert = "";
        Map<String, Object> param = new HashMap<>();
        switch (noticeEnum) {
            case TASK_PUBLISH_SUCCESS:
                // 商单发布成功
                jumpPathType = AppPathTypeEnum.MAKE_MONEY_TASK_PUBLISH_SUCCESS;
                jiguangAlert = String.format(noticeEnum.getJiguangAlert(), sohuBusyTask.getTitle());
                param.put("taskId", sohuBusyTask.getId());
                param.put("taskNumber", sohuBusyTask.getTaskNumber());
                param.put("constMark", constMark);
                break;
            case TASK_APPLY:
                // 接单申请
                jumpPathType = AppPathTypeEnum.MAKE_MONEY_TASK_APPLY;
                param.put("taskId", sohuBusyTask.getId());
                param.put("taskNumber", sohuBusyTask.getTaskNumber());
                param.put("constMark", constMark);
                break;
            case TASK_UPDATE_STEP:
                // 商单有新进展,上传执行进度
            case TASK_UPDATE:
                // 商单有新进展,申请结算
                jumpPathType = AppPathTypeEnum.MAKE_MONEY_TASK_SETTLE_APPLY;
                LoginUser loginUser = remoteUserService.queryById(userId);
                jiguangAlert = String.format(noticeEnum.getJiguangAlert(), sohuBusyTask.getTitle());
                if (Objects.nonNull(loginUser)) {
                    param.put("userName", loginUser.getUsername());
                }
                param.put("taskId", sohuBusyTask.getId());
                param.put("taskNumber", sohuBusyTask.getTaskNumber());
                param.put("type", "SHOrderDetailExecutionController");
                param.put("constMark", constMark);
                break;
            case AGREE_CHANGE_RECEIVER:
                // 接单方同意更换接单人
            case REFUSE_CHANGE_RECEIVER:
                // 接单方拒绝更换接单人
            case AGREE_STOP_TASK_APPLY:
                // 接单方同意终止任务
            case REFUSE_STOP_TASK_APPLY:
                // 接单方拒绝终止任务
                jumpPathType = AppPathTypeEnum.MAKE_MONEY_TASK_APPLY;
                param.put("taskId", sohuBusyTaskSite.getId());
                param.put("afterSalesId", afterSalesId);
                param.put("afterSaleState", afterSaleState);
                param.put("type", "SHTaskOrderSalesDetailController");
                param.put("constMark", constMark);
                break;
            case TASK_APPLY_REFUSE:
                // 接单申请被驳回
                jumpPathType = AppPathTypeEnum.MAKE_MONEY_TASK_RECEIVE_REFUND;
                jiguangAlert = String.format(noticeEnum.getJiguangAlert(), sohuBusyTaskSite.getTitle());
                param.put("taskNumber", sohuBusyTaskSite.getTaskNumber());
                param.put("bottomType", "Refuse");
                param.put("constMark", constMark);
                break;
            case APPLY_SUCCESS:
                // 接单成功
                jumpPathType = AppPathTypeEnum.MAKE_MONEY_TASK_RECEIVE;
                jiguangAlert = String.format(noticeEnum.getJiguangAlert(), sohuBusyTaskSite.getTitle());
                param.put("taskNumber", sohuBusyTaskSite.getTaskNumber());
                param.put("constMark", constMark);
                break;
            case TASK_RECEIVE_CANCEL:
                // 接单已取消
                jumpPathType = AppPathTypeEnum.MAKE_MONEY_TASK_DETAIL;
                jiguangAlert = String.format(noticeEnum.getJiguangAlert(), sohuBusyTask.getTitle());
                param.put("taskId", sohuBusyTask.getId());
                param.put("constMark", constMark);
                break;
            case EXECUTE_PLAN_REFUSE:
                // 执行进度未通过
                jumpPathType = AppPathTypeEnum.MAKE_MONEY_TASK_APPLY_OUT;
                jiguangAlert = String.format(noticeEnum.getJiguangAlert(), sohuBusyTaskSite.getTitle());
                param.put("taskNumber", sohuBusyTaskSite.getTaskNumber());
                param.put("bottomType", "Execute");
                param.put("constMark", constMark);
                break;
            case PUBLISHER_APPLY_CHANGE_RECEIVER:
                // 任务方申请更换接单人
            case PUBLISHER_APPLY_STOP_TASK:
                // 任务方申请终止任务
                jumpPathType = AppPathTypeEnum.MAKE_MONEY_TASK_APPLY_CHANGE;
                jiguangAlert = String.format(noticeEnum.getJiguangAlert(), sohuBusyTaskSite.getTitle());
                param.put("afterSalesId", afterSalesId);
                param.put("taskNumber", sohuBusyTaskSite.getTaskNumber());
                param.put("constMark", constMark);
                break;
            case TASK_END:
                // 商单已完结
                jumpPathType = AppPathTypeEnum.MAKE_MONEY_TASK_OVER;
                jiguangAlert = String.format(noticeEnum.getJiguangAlert(), sohuBusyTaskSite.getTitle());
                param.put("constMark", constMark);
                break;
            case RECEIVER_PAY_DEPOSIT_SUCCESS:
                // 接单方保证金缴纳成功
            case RECEIVER_PAY_DEPOSIT_OVERTIME:
                // 接单方保证金超时未缴纳
                jumpPathType = AppPathTypeEnum.RECEIVER_PAY_DEPOSIT;
                jiguangAlert = String.format(noticeEnum.getJiguangAlert(), sohuBusyTask.getTitle());
                param.put("taskId", sohuBusyTask.getId());
                param.put("constMark", constMark);
                break;
            case PAY_DEPOSIT:
                // 保证金缴纳通知
            case PAY_DEPOSIT_OVERTIME:
                // 保证金缴纳超时
                jumpPathType = AppPathTypeEnum.PAY_DEPOSIT;
                jiguangAlert = String.format(noticeEnum.getJiguangAlert(), sohuBusyTaskSite.getTitle());
                param.put("taskNumber", sohuBusyTaskSite.getTaskNumber());
                param.put("constMark", constMark);
                break;
            case TASK_SETTLE_APPLY_FAIL:
                // 结算申请未通过
                jumpPathType = AppPathTypeEnum.TASK_SETTLE_APPLY_FAIL;
                jiguangAlert = String.format(noticeEnum.getJiguangAlert(), sohuBusyTaskSite.getTitle());
                param.put("taskNumber", sohuBusyTaskSite.getTaskNumber());
                param.put("constMark", constMark);
                break;
            case TASK_APPLY_SUCCESS_COMMON:
                // 接单成功
                jumpPathType = AppPathTypeEnum.MAKE_MONEY_TASK_RECEIVE;
                jiguangAlert = String.format(noticeEnum.getJiguangAlert(), sohuBusyTask.getTitle());
                param.put("taskNumber",
                        Objects.nonNull(sohuBusyTaskSite) ? sohuBusyTaskSite.getTaskNumber() : sohuBusyTask.getTaskNumber());
                param.put("constMark", constMark);
                break;
            default:
                break;
        }
        if (StringUtils.isNotBlank(noticeEnum.getJiguangTitle())) {
            this.sendJiguangNotice(userId, jumpPathType, noticeEnum.getJiguangTitle(),
                    StringUtils.isBlank(jiguangAlert) ? noticeEnum.getJiguangAlert() : jiguangAlert, param);
        }
        // 发送极光推送通知
        return Boolean.TRUE;
    }

    /**
     * 发送极光推送消息
     *
     * @param userId
     * @param jumpPathType
     * @param jiguangTitle
     * @param jiguangAlert
     * @param param
     */
    public void sendJiguangNotice(Long userId, AppPathTypeEnum jumpPathType, String jiguangTitle, String jiguangAlert, Map<String, Object> param) {
        log.error("userId:{},jumpPathType:{},jiguangTitle:{},jiguangAlert:{},param:{}", userId, JsonUtils.toJsonString(jumpPathType), jiguangTitle, jiguangAlert, JsonUtils.toJsonString(param));
        SohuJiguangPush2UserReqBo reqBo = new SohuJiguangPush2UserReqBo();
        reqBo.setUserIds(Lists.newArrayList(userId));
        reqBo.setJumpPathType(jumpPathType);
        reqBo.setBizType(PushJiGuangBizTypeEnum.ACCOUNT);
        reqBo.setTitle(jiguangTitle);
        reqBo.setAlert(jiguangAlert);
        reqBo.setParameters(param);
        try {
            remoteJiguangService.push2User(reqBo);
        } catch (Exception e) {
            log.warn("商单消息:{},极光推送异常，原因: {}", jiguangTitle, e.getMessage());
        }
    }

    /**
     * 发送接单通知
     *
     * @param receiverId
     * @param taskNumber
     * @param title
     * @param content
     * @param noticeType
     * @param relateTitle
     */
    public void sendNotice(Long receiverId, String taskNumber, String title, String content,
                           String noticeType, String relateTitle, String nickName, String extValue) {
        SohuTaskNoticeBo sohuTaskNoticeBo = new SohuTaskNoticeBo();
        sohuTaskNoticeBo.setSenderId(UserConstants.ADMIN_ID);
        sohuTaskNoticeBo.setReceiverId(receiverId);
        sohuTaskNoticeBo.setRelateId(taskNumber);
        sohuTaskNoticeBo.setRelateTitle(relateTitle);
        sohuTaskNoticeBo.setTitle(title);
        sohuTaskNoticeBo.setContent(content);
        sohuTaskNoticeBo.setNoticeType(noticeType);
        sohuTaskNoticeBo.setReceiverTaskNickName(nickName);
        sohuTaskNoticeBo.setExtValue(extValue);
        remoteMiddleTaskNoticeService.sendTaskNotice(sohuTaskNoticeBo);
    }

}
