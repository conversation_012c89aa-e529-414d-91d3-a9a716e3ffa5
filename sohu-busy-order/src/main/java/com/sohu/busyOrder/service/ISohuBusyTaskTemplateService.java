package com.sohu.busyOrder.service;

import com.sohu.busyorder.api.bo.SohuBusyTaskTemplateBo;
import com.sohu.busyorder.api.vo.SohuBusyTaskTemplateStatVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskTemplateVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 拆单模板Service接口
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
public interface ISohuBusyTaskTemplateService {

    /**
     * 查询拆单模板
     */
    SohuBusyTaskTemplateVo queryById(Long id);

    /**
     * 模板管理列表
     */
    TableDataInfo<SohuBusyTaskTemplateVo> queryPageList(SohuBusyTaskTemplateBo bo, PageQuery pageQuery);

    /**
     * 模板数据列表
     */
    TableDataInfo<SohuBusyTaskTemplateVo> dataPage(SohuBusyTaskTemplateBo bo, PageQuery pageQuery);

    /**
     * 模板使用次数
     */
    Map<Long, Long> countMap(Collection<Long> ids);

    /**
     * 查询拆单模板列表
     */
    List<SohuBusyTaskTemplateVo> queryList(SohuBusyTaskTemplateBo bo);

    /**
     * 新增拆单模板
     */
    Boolean insertByBo(SohuBusyTaskTemplateBo bo);

    /**
     * 修改拆单模板
     */
    Boolean updateByBo(SohuBusyTaskTemplateBo bo);

    /**
     * 校验并批量删除拆单模板信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询拆单模板列表--不分页
     */
    TableDataInfo<SohuBusyTaskTemplateVo> queryManageList(SohuBusyTaskTemplateBo bo);

    /**
     * 拆单map
     *
     * @param templateIds 模板id列表
     * @return
     */
    Map<Long, SohuBusyTaskTemplateVo> queryMap(List<Long> templateIds);

    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    Boolean batchInsert(List<SohuBusyTaskTemplateBo> list);

    /**
     * 查询拆单模板列表
     */
    List<SohuBusyTaskTemplateVo> queryList(String taskNumber);

    /**
     * 根据主任务编号查询拆单模版
     *
     * @param taskNumber
     */
    SohuBusyTaskTemplateVo queryByMasterTaskNo(String taskNumber);

    /**
     * 模板使用总次数统计
     */
    SohuBusyTaskTemplateStatVo getTemplateStat();

}
