package com.sohu.busyOrder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.busyOrder.domain.SohuBusyOrderNotice;
import com.sohu.busyOrder.mapper.SohuBusyOrderNoticeMapper;
import com.sohu.busyOrder.service.ISohuBusyOrderNoticeService;
import com.sohu.busyorder.api.bo.SohuBusyOrderNoticeBo;
import com.sohu.busyorder.api.enums.NoticeStatus;
import com.sohu.busyorder.api.vo.SohuBusyOrderNoticeVo;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商单通知Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@RequiredArgsConstructor
@Service
public class SohuBusyOrderNoticeServiceImpl implements ISohuBusyOrderNoticeService {

    private final SohuBusyOrderNoticeMapper baseMapper;

    /**
     * 查询商单通知
     */
    @Override
    public SohuBusyOrderNoticeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商单通知列表
     */
    @Override
    public TableDataInfo<SohuBusyOrderNoticeVo> queryPageList(SohuBusyOrderNoticeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuBusyOrderNotice> lqw = buildQueryWrapper(bo);
        Page<SohuBusyOrderNoticeVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询商单通知列表
     */
    @Override
    public List<SohuBusyOrderNoticeVo> queryList(SohuBusyOrderNoticeBo bo) {
        LambdaQueryWrapper<SohuBusyOrderNotice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuBusyOrderNotice> buildQueryWrapper(SohuBusyOrderNoticeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuBusyOrderNotice> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderId() != null, SohuBusyOrderNotice::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getNotifyPerson()), SohuBusyOrderNotice::getNotifyPerson, bo.getNotifyPerson());
        lqw.eq(StringUtils.isNotBlank(bo.getNoticeStatus()), SohuBusyOrderNotice::getNoticeStatus, bo.getNoticeStatus());
        return lqw;
    }

    /**
     * 新增商单通知
     */
    @Override
    public Boolean insertByBo(SohuBusyOrderNoticeBo bo) {
        SohuBusyOrderNotice add = BeanUtil.toBean(bo, SohuBusyOrderNotice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商单通知
     */
    @Override
    public Boolean updateByBo(SohuBusyOrderNoticeBo bo) {
        SohuBusyOrderNotice update = BeanUtil.toBean(bo, SohuBusyOrderNotice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuBusyOrderNotice entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商单通知
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean addNotice(Long orderId, List<Long> noticeList) {
        if (orderId == null || orderId <= 0 || CollectionUtils.isEmpty(noticeList)) {
            throw new ServiceException("参数错误");
        }
        List<SohuBusyOrderNotice> addList = new ArrayList<>();
        noticeList.forEach(notice -> {
            SohuBusyOrderNotice receDao = new SohuBusyOrderNotice();
                receDao.setOrderId(orderId);
                receDao.setNotifyPerson(notice);
                receDao.setNoticeStatus(NoticeStatus.Wait.name());
                addList.add(receDao);
            }
        );
        return baseMapper.insertBatch(addList);
    }

}
