package com.sohu.busyOrder.dubbo;

import com.sohu.busyorder.api.bo.SohuBusyTaskExecuteBo;
import com.sohu.busyorder.api.vo.SohuBusyTaskExecuteVo;
import com.sohu.busyOrder.service.ISohuBusyTaskExecuteService;
import com.sohu.busyorder.api.RemoteBusyTaskExecuteService;
import com.sohu.busyorder.api.domain.SohuBusyTaskExecuteReqBo;
import com.sohu.busyorder.api.model.SohuBusyTaskExecuteModel;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteBusyTaskExecuteServiceImpl implements RemoteBusyTaskExecuteService {

    private final ISohuBusyTaskExecuteService iSohuBusyTaskExecuteService;

    @Override
    public SohuBusyTaskExecuteModel queryById(Long id) {
        SohuBusyTaskExecuteVo sohuBusyTaskExecuteVo = iSohuBusyTaskExecuteService.queryById(id);
        return BeanCopyUtils.copy(sohuBusyTaskExecuteVo, SohuBusyTaskExecuteModel.class);
    }

    @Override
    public TableDataInfo<SohuBusyTaskExecuteModel> queryPageList(SohuBusyTaskExecuteReqBo bo, PageQuery pageQuery) {
        SohuBusyTaskExecuteBo busyTaskExecuteBo = BeanCopyUtils.copy(bo, SohuBusyTaskExecuteBo.class);
        TableDataInfo<SohuBusyTaskExecuteVo> sohuBusyTaskExecuteVoTableDataInfo = iSohuBusyTaskExecuteService.queryPageList(busyTaskExecuteBo, pageQuery);
        List<SohuBusyTaskExecuteModel> sohuBusyTaskExecuteModelList = BeanCopyUtils.copyList(sohuBusyTaskExecuteVoTableDataInfo.getData(), SohuBusyTaskExecuteModel.class);
        TableDataInfo<SohuBusyTaskExecuteModel> modelTableDataInfo = TableDataInfoUtils.build(sohuBusyTaskExecuteModelList);
        modelTableDataInfo.setTotal(sohuBusyTaskExecuteVoTableDataInfo.getTotal());
        return modelTableDataInfo;
    }

    @Override
    public List<SohuBusyTaskExecuteModel> queryList(SohuBusyTaskExecuteReqBo bo) {
        SohuBusyTaskExecuteBo busyTaskExecuteBo = BeanCopyUtils.copy(bo, SohuBusyTaskExecuteBo.class);
        List<SohuBusyTaskExecuteVo> sohuBusyTaskExecuteVoList = iSohuBusyTaskExecuteService.queryList(busyTaskExecuteBo);
        return BeanCopyUtils.copyList(sohuBusyTaskExecuteVoList, SohuBusyTaskExecuteModel.class);
    }

    @Override
    public Boolean insertByBo(SohuBusyTaskExecuteReqBo bo) {
        SohuBusyTaskExecuteBo busyTaskExecuteBo = BeanCopyUtils.copy(bo, SohuBusyTaskExecuteBo.class);
        return iSohuBusyTaskExecuteService.insertByBo(busyTaskExecuteBo);
    }

    @Override
    public Boolean updateByBo(SohuBusyTaskExecuteReqBo bo) {
        SohuBusyTaskExecuteBo busyTaskExecuteBo = BeanCopyUtils.copy(bo, SohuBusyTaskExecuteBo.class);
        return iSohuBusyTaskExecuteService.updateByBo(busyTaskExecuteBo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return iSohuBusyTaskExecuteService.deleteWithValidByIds(ids, isValid);
    }
}
