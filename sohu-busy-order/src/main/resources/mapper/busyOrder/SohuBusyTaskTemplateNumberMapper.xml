<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.busyOrder.mapper.SohuBusyTaskTemplateNumberMapper">

    <resultMap type="com.sohu.busyOrder.domain.SohuBusyTaskTemplateNumber" id="SohuBusyTaskTemplateNumberResult">
    <result property="id" column="id"/>
    <result property="userId" column="user_id"/>
    <result property="masterTaskNumber" column="master_task_number"/>
    <result property="templateJson" column="template_json"/>
    <result property="createTime" column="create_time"/>
    <result property="updateTime" column="update_time"/>
    <result property="templateId" column="template_id"/>
    <result property="content" column="content"/>
    <result property="isSelect" column="is_select"/>
    </resultMap>
    <select id="selectTemplateList" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskTemplateNumberVo">
        SELECT
        n.*,
        t.title,
        c.industry_name AS industry_name,
        y.name AS type_name
        FROM
        sohu_busy_task_template_number n
        INNER JOIN sohu_busy_task_template t ON t.id = n.template_id
        INNER JOIN sohu_industry_category c ON t.industry_type = c.id
        INNER JOIN sohu_category y ON t.template_type = y.id
        WHERE
        1=1
        <if test="bo.masterTaskNumber !=null and bo.masterTaskNumber != '' ">
            AND n.master_task_number = #{bo.masterTaskNumber}
        </if>
        <if test="bo.userId != null and bo.userId != '' ">
            AND t.user_id = #{bo.userId}
        </if>
        <if test="bo.isSelect !=null ">
            AND n.is_select = #{bo.isSelect}
        </if>
        <if test="bo.startTime != null and bo.startTime != ''">
            AND t.create_time between #{bo.createTime} and #{bo.updateTime}
        </if>
        ORDER BY n.create_time DESC
    </select>

    <select id="queryByMasterTaskNo" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskTemplateNumberVo">
        SELECT
        sbttn.*,sbtt.title,
        c.industry_name AS industry_name,
        y.name AS type_name
        FROM
        sohu_busy_task_template_number sbttn
        INNER JOIN sohu_busy_task_template sbtt ON sbttn.template_id = sbtt.id
        INNER JOIN sohu_industry_category c ON sbtt.industry_type = c.id
        INNER JOIN sohu_category y ON sbtt.template_type = y.id
        WHERE
            sbttn.is_select = '1'
        AND
            sbttn.master_task_number = #{taskNumber}
    </select>

</mapper>
