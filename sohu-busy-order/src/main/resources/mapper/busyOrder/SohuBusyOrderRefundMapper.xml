<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.busyOrder.mapper.SohuBusyOrderRefundMapper">

    <resultMap type="com.sohu.busyOrder.domain.SohuBusyOrderRefund" id="SohuBusyOrderRefundResult">
    <result property="id" column="id"/>
    <result property="busyOrder" column="busy_order"/>
    <result property="transactionId" column="transaction_id"/>
    <result property="orderNo" column="order_no"/>
    <result property="refundOrderNo" column="refund_order_no"/>
    <result property="refundNumber" column="refund_number"/>
    <result property="refundStatus" column="refund_status"/>
    <result property="refundAbleAmount" column="refund_able_amount"/>
    <result property="refundAmount" column="refund_amount"/>
    <result property="createBy" column="create_by"/>
    <result property="createTime" column="create_time"/>
    <result property="updateBy" column="update_by"/>
    <result property="updateTime" column="update_time"/>
    <result property="deleted" column="deleted"/>
    </resultMap>


</mapper>
