<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.busyOrder.mapper.SohuBusyTaskReceiveMapper">

    <resultMap type="com.sohu.busyOrder.domain.SohuBusyTaskReceive" id="SohuBusyTaskReceiveResult">
        <result property="id" column="id"/>
        <result property="taskNumber" column="task_number"/>
        <result property="userId" column="user_id"/>
        <result property="applyMsg" column="apply_msg"/>
        <result property="applyAnnex" column="apply_annex"/>
        <result property="amount" column="amount"/>
        <result property="isIndependent" column="is_independent"/>
        <result property="templateId" column="template_id"/>
        <result property="distributionAmount" column="distribution_amount"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="state" column="state"/>
        <result property="refuseMsg" column="refuse_msg"/>
    </resultMap>
    <select id="selectTaskReceivePage" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskReceiveVo">
        SELECT
        r.*,
        s.title,s.full_amount AS full_amount,
        u.nick_name AS nick_name,
        y.name AS type_name,
        y.id AS type
        FROM
        sohu_busy_task_receive r
        INNER JOIN sohu_busy_task_site s ON s.task_number = r.task_number AND s.user_id = #{bo.taskUserId}
        <if test="bo.type != null ">
            AND s.type = #{bo.type}
        </if>
        <if test="bo.title !=null and bo.title != '' ">
            AND s.title like concat('%', #{bo.title}, '%')
        </if>
        <if test="bo.shelfState != null and bo.shelfState != '' ">
            AND s.shelf_state = #{shelfState}
        </if>
        INNER JOIN sohu_category y ON s.type = y.id
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        WHERE
        r.task_number = #{bo.taskNumber}
        <if test="bo.siteId !=null ">
            AND r.site_id = #{bo.siteId}
        </if>
        <if test="bo.countrySiteId !=null ">
            AND r.country_site_id = #{bo.countrySiteId}
        </if>
        <if test="bo.taskNumber !=null and bo.taskNumber != '' ">
            AND r.task_number = #{bo.taskNumber}
        </if>
        <if test="bo.state != null and bo.state != '' ">
            AND r.state = #{bo.state}
        </if>
        <if test="bo.startTime != null and bo.startTime != ''">
            AND t.create_time between #{bo.createTime} and #{bo.updateTime}
        </if>
        ORDER BY r.update_time DESC
    </select>

    <select id="selectTaskReceiveUserPage" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskReceiveVo">
        SELECT
        r.*,
        s.title,s.full_amount AS full_amount,s.industry_type AS industry_type,
        s.master_task_number AS masterTaskNumber,
        s.settle_type AS settle_type,s.delivery_step AS delivery_step,s.content,
        s.delivery_day AS delivery_day,s.user_id AS task_user_id,
        c.industry_name AS industry_name ,
        e.`name` AS site_name,
        u.nick_name AS nick_name,
        u.avatar AS userAvatar,
        y.name AS type_name,
        y.const_mark AS constMark,
        y.id AS type
        FROM
        sohu_busy_task_receive r
        INNER JOIN (
        SELECT task_number, MAX(create_time) as latest_update
        FROM sohu_busy_task_receive
        WHERE user_id = #{bo.userId}
        GROUP BY task_number
        ) latest ON r.task_number = latest.task_number AND r.create_time = latest.latest_update
        INNER JOIN sohu_busy_task_site s ON s.task_number = r.task_number
        <if test="bo.type != null ">
            AND s.type = #{bo.type}
        </if>
        <if test="bo.title !=null and bo.title != '' ">
            AND s.title like concat('%', #{bo.title}, '%')
        </if>
        <if test="bo.shelfState != null and bo.shelfState != '' ">
            AND s.shelf_state = #{bo.shelfState}
        </if>
        INNER JOIN sohu_category y ON s.type = y.id
        AND y.const_mark IN ('NOVEL', 'ARTICLE', 'VIDEO', 'FLOW_TASK','COMMON_TASK')
        INNER JOIN sohu_site e ON e.id = s.site_id
        INNER JOIN sohu_industry_category c ON s.industry_type = c.id
        LEFT JOIN sys_user u ON s.user_id = u.user_id
        WHERE
        s.is_after_sales = 0
        AND r.del_flag = 0
        <if test="bo.siteId !=null ">
            AND r.site_id = #{bo.siteId}
        </if>
        <if test="bo.countrySiteId !=null ">
            AND r.country_site_id = #{bo.countrySiteId}
        </if>
        <if test="bo.taskNumber !=null and bo.taskNumber != '' ">
            AND r.task_number = #{bo.taskNumber}
        </if>
        <if test="bo.state != null and bo.state != '' ">
            <choose>
                <when test="bo.state == 'WaitPay'">
                    AND r.state IN ('WaitPay', 'WaitFullAmountPay', 'WaitIndependentPay', 'WaitPromisePay')
                </when>
                <when test="bo.state == 'WaitFullAmountPay'">
                    AND r.state IN ('WaitFullAmountPay', 'WaitApprove')
                </when>
                <otherwise>
                    AND r.state = #{bo.state}
                </otherwise>
            </choose>
        </if>
        <if test="bo.startTime != null and bo.startTime != ''">
            AND r.create_time between #{bo.createTime} and #{bo.updateTime}
        </if>
        GROUP BY
        r.task_number
        ORDER BY
        <if test="bo.state != null and bo.state == 'Execute' ">
            r.expire_time ASC,
        </if>
        r.update_time DESC
    </select>
    <select id="selectReceiveAndInfo" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskReceiveVo">
        SELECT t.*,
               u.nick_name AS nick_name,
               u.avatar    AS user_avatar
        FROM sohu_busy_task_receive t
                 LEFT JOIN sys_user u ON t.user_id = u.user_id
        WHERE t.task_number = #{taskNumber}
          AND t.backup = #{aTrue};
    </select>

    <select id="getReceiveUserStat" resultType="java.lang.Long">
        SELECT COUNT(*) AS total
        FROM sohu_busy_task_site bts
                 INNER JOIN
             sohu_busy_task_receive btr
             ON bts.task_number = btr.task_number
        WHERE bts.user_id = #{userId}
          AND bts.create_time BETWEEN #{startDateTime} AND #{endDateTime}
    </select>
    <select id="getExecuteBusyTaskByUserId" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskReceiveVo">
        SELECT btr.*,
               bts.*,
               s.name           AS site_name,
               c.name           AS type_name,
               ic.industry_name AS industry_name
        FROM sohu_busy_task_receive btr
                 INNER JOIN sohu_busy_task_site bts ON bts.task_number = btr.task_number
                 INNER JOIN sohu_site s ON s.id = bts.site_id
                 INNER JOIN sohu_category c ON c.id = bts.type
                 INNER JOIN sohu_industry_category ic ON ic.id = bts.industry_type
        WHERE btr.user_id = #{userId}
          AND btr.state = 'Execute'
        ORDER BY btr.update_time DESC
    </select>
    <select id="selectByTaskNumberList" resultType="com.sohu.busyOrder.domain.SohuBusyTaskReceive">
        SELECT *
        FROM sohu_busy_task_receive
        WHERE task_number IN
        <foreach collection="taskNumberList" item="taskNumber" open="(" separator="," close=")">
            #{taskNumber}
        </foreach>
    </select>

    <select id="selectTaskReceiveInfoPage" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskReceiveInfoVo">
        SELECT
        re.id,
        re.task_number,
        re.state,
        re.user_id,
        re.create_time,
        re.relation_id AS groupId,
        su.nick_name AS userName
        FROM
        sohu_busy_task_receive re
        JOIN sys_user su ON su.user_id = re.user_id
        <if test="bo.userName != null and bo.userName != ''">
            AND
            su.nick_name like concat('%', #{bo.userName}, '%')
        </if>
        WHERE
        re.task_number IN
        <foreach collection="taskNumberList" item="taskNumber" open="(" separator="," close=")">
            #{taskNumber}
        </foreach>
        <if test="bo.taskNumber != null and bo.taskNumber != ''">
            AND re.task_number = #{bo.taskNumber}
        </if>
        <if test="bo.state != null and bo.state != ''">
            AND re.state = #{bo.state}
        </if>
        <if test="bo.startTime != null and bo.startTime != ''">
            AND re.create_time between #{bo.createTime} and #{bo.updateTime}
        </if>
        <if test="bo.userId != null">
            AND re.user_id = #{userId}
        </if>
    </select>
    <select id="queryReceiveListByTaskNumber" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskReceiveVo">
        SELECT t.*,
        u.nick_name AS nickName
        FROM sohu_busy_task_receive t
        LEFT JOIN sys_user u ON t.user_id = u.user_id
        WHERE t.task_number IN
        <foreach collection="taskNumbers" item="taskNumber" open="(" separator="," close=")">
            #{taskNumber}
        </foreach>
    </select>
    <select id="selectCountByTaskNumber" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM sohu_busy_task_receive sbtr
                 INNER JOIN sohu_busy_task_site sbts
                            ON sbtr.task_number = sbts.task_number
        WHERE sbts.master_task_number = #{taskNumber}
          AND sbtr.state NOT IN ('Cancel', 'WaitReceive')
    </select>

    <select id="selectOnlineNum" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM sohu_busy_task_receive sbtr
                 INNER JOIN sohu_busy_task_site sbts ON sbtr.task_number = sbts.task_number
                 INNER JOIN sohu_category c
                            ON c.id = sbts.type AND c.const_mark IN ('GAME', 'NOVEL', 'ARTICLE', 'VIDEO')
        WHERE sbtr.user_id = #{userId}
          AND sbtr.state = #{state}
    </select>

    <select id="listReceiveByTaskNumberListAndStateList" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskReceiveVo">
        SELECT t.*,
        u.user_name AS userName
        FROM sohu_busy_task_receive t
        LEFT JOIN sys_user u ON t.user_id = u.user_id
        WHERE t.task_number IN
        <foreach collection="taskNumberList" item="taskNumber" open="(" separator="," close=")">
            #{taskNumber}
        </foreach>
        AND
        t.state IN
        <foreach collection="stateList" item="state" open="(" separator="," close=")">
            #{state}
        </foreach>
    </select>

    <select id="countTasksByUser" resultType="com.sohu.busyorder.api.vo.TaskGroupVo">
        SELECT user_id AS userId, COUNT(*) AS taskCount
        FROM sohu_busy_task_receive
        where 1=1
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;#{endTime}
        </if>
        GROUP BY user_id
    </select>

</mapper>
