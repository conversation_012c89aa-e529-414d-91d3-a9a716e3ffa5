package com.sohu.open.controller.client.mall;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.service.shop.RemoteMiddleOpenClientMerchantService;
import com.sohu.open.domain.vo.mall.SohuMerchantBaseInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 商城-商户
 * 前端访问路由地址为:/client/mall/merchant
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/client/mall/merchant")
public class SohuMallMerchantController extends BaseController {

    @DubboReference
    private RemoteMiddleOpenClientMerchantService remoteMiddleOpenClientMerchantService;

    @Operation(summary = "获取开放平台客户端绑定的商城商户", description = "author:phc。获取开放平台客户端绑定的商城商户")
    @GetMapping("/getListByClientId")
    public R<List<SohuMerchantBaseInfoVo>> getListByClientId() {
        return R.ok(BeanCopyUtils.copyList(remoteMiddleOpenClientMerchantService.getListByClientId(LoginHelper.getOpenClientId()),SohuMerchantBaseInfoVo.class));
    }

}
