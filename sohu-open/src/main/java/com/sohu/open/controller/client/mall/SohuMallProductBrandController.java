package com.sohu.open.controller.client.mall;

import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.RemoteProductBrandStoreService;
import com.sohu.shopgoods.api.bo.SohuProducrBrandPageQueryBo;
import com.sohu.shopgoods.api.domain.SohuCategoryRemoteBo;
import com.sohu.shopgoods.api.model.SohuProductBrandModel;
import com.sohu.shopgoods.api.vo.SohuProductBrandVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商城-商品品牌
 * 前端访问路由地址为:/client/mall/productBrand
 *
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/client/mall/productBrand")
public class SohuMallProductBrandController extends BaseController {

    @DubboReference
    private RemoteProductBrandStoreService remoteProductBrandStoreService;

//    /**
//     * 查询商品品牌列表-分页-作废
//     */
//    @Operation(summary = "查询商品品牌列表-分页",description = "author:phc。查询商品品牌列表-分页")
//    @GetMapping("/list")
//    public TableDataInfo<SohuProductBrandModel> storeList(SohuCategoryRemoteBo bo, PageQuery pageQuery) {
//        return remoteProductBrandStoreService.queryPageListByCate(bo, pageQuery);
//    }

    /**
     * 查询商品品牌列表-分页-作废
     */
    @Operation(summary = "查询商品品牌列表-分页",description = "author:phc。查询商品品牌列表-分页")
    @GetMapping("/list")
    public TableDataInfo<SohuProductBrandVo> storeList(SohuProducrBrandPageQueryBo bo, PageQuery pageQuery) {
        return remoteProductBrandStoreService.queryPageList(bo, pageQuery);
    }

}
