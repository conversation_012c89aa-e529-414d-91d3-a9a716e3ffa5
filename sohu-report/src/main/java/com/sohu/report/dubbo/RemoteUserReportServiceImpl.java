package com.sohu.report.dubbo;

import com.sohu.report.api.RemoteUserReportService;
import com.sohu.report.api.bo.SohuUserTaskReportBo;
import com.sohu.report.api.bo.SohuUserViewReportBo;
import com.sohu.report.service.ISohuUserTaskReportService;
import com.sohu.report.service.ISohuUserViewReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25 10:53
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteUserReportServiceImpl implements RemoteUserReportService {

    private final ISohuUserViewReportService sohuUserViewReportService;

    private final ISohuUserTaskReportService sohuUserTaskReportService;
    @Override
    public Boolean userBehaviorBatchSave(List<SohuUserViewReportBo> bos) {
        return sohuUserViewReportService.batchInsert(bos);
    }

    @Override
    public Boolean userTaskBatchSave(List<SohuUserTaskReportBo> bos) {
        return sohuUserTaskReportService.batchInsert(bos);
    }
}
