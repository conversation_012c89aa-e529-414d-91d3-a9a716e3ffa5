package com.sohu.report.dubbo;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sohu.admin.api.RemoteAdminService;
import com.sohu.admin.api.vo.SohuAgentUserVo;
import com.sohu.admin.api.vo.SohuInviteChannelVo;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.enums.InviteEnum;
import com.sohu.pay.api.RemoteIndependentOrderService;
import com.sohu.pay.api.model.SohuIndependentOrderModel;
import com.sohu.report.api.RemoteIncomeStatisticsService;
import com.sohu.report.api.bo.*;
import com.sohu.report.api.vo.*;
import com.sohu.report.service.*;
import com.sohu.report.util.DateUtils;
import com.sohu.report.util.SohuReportConvertUtil;
import com.sohu.report.util.SohuReportRoleUtil;
import com.sohu.report.vo.GroupedIncomeVO;
import com.sohu.report.vo.UserIncomeVO;
import com.sohu.system.api.RemoteUserService;
import com.sohu.system.api.vo.SohuUserOrderInfoVo;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: leibo
 * @Date: 2025/5/30 09:03
 **/
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteIncomeStatisticsServiceImpl implements RemoteIncomeStatisticsService {

    private final ISohuUserIncomeStatisticsService userIncomeStatisticsService;
    private final ISohuUserIncomeStatisticsDayService userIncomeStatisticsDayService;
    private final ISohuUserIncomeStatisticsWeekService userIncomeStatisticsWeekService;
    private final ISohuUserIncomeStatisticsMonthService userIncomeStatisticsMonthService;
    private final SohuReportConvertUtil sohuReportConvertUtil;
    private final ISohuUserIncomeStatisticsInfoService userIncomeStatisticsInfoService;
    private final SohuReportRoleUtil sohuReportRoleUtil;
    private final ISohuUserRetentionStatService sohuUserRetentionStatService;
    @DubboReference
    private RemoteUserService userService;
    @DubboReference
    private RemoteAdminService remoteAdminService;
    @DubboReference
    private RemoteIndependentOrderService independentOrderService;

    @Override
    public void handleStatistics() {
        // 查询总数据
        List<UserIncomeVO> userIncomeList = userIncomeStatisticsService.queryUserIncomeByTime(null, null);
        if (CollectionUtils.isEmpty(userIncomeList)) {
            return;
        }
        List<GroupedIncomeVO> groupIncomeList = sohuReportConvertUtil.groupIncomeData(userIncomeList, Boolean.FALSE);
        // 处理汇总数据进总表
        for (GroupedIncomeVO groupedIncomeVO : groupIncomeList) {
            SohuUserIncomeStatisticsBo incomeStatisticsBo = new SohuUserIncomeStatisticsBo();
            incomeStatisticsBo.setUserId(groupedIncomeVO.getUserId());
            incomeStatisticsBo.setRoleType(groupedIncomeVO.getIndependentObject());
            incomeStatisticsBo.setStationId(groupedIncomeVO.getSiteId());
            incomeStatisticsBo.setReceiveIncome(groupedIncomeVO.getEntryAmount());
            incomeStatisticsBo.setWaitIncome(groupedIncomeVO.getWaitEntryAmount());
            // 查询已提现总额
            BigDecimal userWithdrawal = userService.getUserWithdrawal(groupedIncomeVO.getUserId(), groupedIncomeVO.getSiteType(), groupedIncomeVO.getSiteId(), groupedIncomeVO.getIndependentObject(), null, null);
            incomeStatisticsBo.setAlreadyWithdrawal(userWithdrawal);
            // 计算待提现总额
            incomeStatisticsBo.setWaitWithdrawal(CalUtils.sub(groupedIncomeVO.getEntryAmount(), userWithdrawal));
            SohuUserIncomeStatisticsVo incomeStatisticsVo = userIncomeStatisticsService.queryByUserIdAndRoleTypeAndStationId(groupedIncomeVO.getUserId(), groupedIncomeVO.getIndependentObject(), groupedIncomeVO.getSiteId());
            if (Objects.isNull(incomeStatisticsVo)) {
                // 新增
                userIncomeStatisticsService.insertByBo(incomeStatisticsBo);
            } else {
                // 编辑
                incomeStatisticsBo.setId(incomeStatisticsVo.getId());
                userIncomeStatisticsService.updateByBo(incomeStatisticsBo);
            }
        }
        // 按天查询收益收据
        Date now = new Date();
        Date startOfDay = DateUtils.getStartOfDay(now);
        Date yesterdayStartOfDay = DateUtils.getYesterdayStartOfDay(now);
        this.handleForDay(yesterdayStartOfDay, startOfDay);
        // 按周查询收益数据
        Date startOfWeek = DateUtils.getStartOfWeekOrPrevious(now);
        Date endOfWeek = DateUtils.getEndOfWeekOrPrevious(now);
        this.handleForWeek(startOfWeek, endOfWeek);
        // 按月查询收益数据
        Date startOfMonth = DateUtils.getStartOfMonthOrPrevious(now);
        Date endOfMonth = DateUtils.getEndOfMonthOrPrevious(now);
        this.handleForMonth(startOfMonth, endOfMonth);
    }

    @Override
    public void handleDayStatistics() {
        // 按天查询收益收据
        Date now = new Date();
        Date startOfDay = DateUtils.getStartOfDay(now);
        this.handleForDay(startOfDay, now);
    }

    @Override
    public TableDataInfo<SohuUserIncomeStatisticsInfoVo> queryIncomePageList(SohuUserIncomeStatisticsInfoBo bo, PageQuery pageQuery) {
        return userIncomeStatisticsInfoService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<SohuUserIncomeStatisticsInfoVo> queryIncomeList(SohuUserIncomeStatisticsInfoBo bo) {
        return userIncomeStatisticsInfoService.queryList(bo);
    }

    @Override
    public SohuUserIncomeStatisticsInfoVo queryIncomeById(Long id) {
        return userIncomeStatisticsInfoService.queryIncomeById(id);
    }

    @Override
    public List<SohuUserIncomeStatisticsInfoVo> incomeNewest() {
        return userIncomeStatisticsInfoService.incomeNewest();
    }

    @Override
    public void handleIncomeStatistics(String orderNo) {
        // 查询分账记录
        List<SohuIndependentOrderModel> orderModelList = independentOrderService.queryByOrderNo(orderNo, null, null);
        if (CollectionUtils.isEmpty(orderModelList)) {
            return;
        }
        // 基于分账id查询对应的收益记录
        for (SohuIndependentOrderModel independentOrderModel : orderModelList) {
            SohuUserIncomeStatisticsInfoVo infoVo = userIncomeStatisticsInfoService.queryByIndependentOrderId(independentOrderModel.getId());
            if (Objects.nonNull(infoVo)) {
                // 不为空做编辑操作
                SohuUserIncomeStatisticsInfoBo infoBo = new SohuUserIncomeStatisticsInfoBo();
                infoBo.setId(infoVo.getId());
                if (independentOrderModel.getIndependentStatus() == 1) {
                    infoBo.setIncomeTime(independentOrderModel.getUpdateTime());
                }
                infoBo.setDelFlag(independentOrderModel.getDelFlag());
                infoBo.setIndependentStatus(independentOrderModel.getIndependentStatus());
                userIncomeStatisticsInfoService.updateByBo(infoBo);
            } else {
                // 为空,则补充新增
                SohuUserIncomeStatisticsInfoBo infoBo = new SohuUserIncomeStatisticsInfoBo();
                infoBo.setIndependentOrderId(independentOrderModel.getId());
                infoBo.setUserId(independentOrderModel.getUserId());
                infoBo.setRoleType(sohuReportRoleUtil.getIndependentObjectRoleType(independentOrderModel.getIndependentObject()));
                infoBo.setStationId(independentOrderModel.getSiteId());
                infoBo.setStationType(independentOrderModel.getSiteType());
                infoBo.setBusyType(independentOrderModel.getTradeType());
                infoBo.setBusyInfoName(independentOrderModel.getTaskTitle());
                infoBo.setIncomeType(sohuReportRoleUtil.getIncomeType(independentOrderModel.getIndependentObject()));
                infoBo.setIncome(independentOrderModel.getIndependentPrice());
                infoBo.setConsumerUserId(independentOrderModel.getConsumerUserId());
                // 查询消费者渠道
                SohuInviteChannelVo inviteChannel = remoteAdminService.queryInviteChannel(independentOrderModel.getConsumerUserId());
                if (Objects.nonNull(inviteChannel)) {
                    infoBo.setConsumerChannelId(inviteChannel.getId());
                    infoBo.setConsumerChannelName(inviteChannel.getName());
                }
                infoBo.setOrderAmount(independentOrderModel.getTaskFullAmount());
                infoBo.setOrderNo(independentOrderModel.getOrderNo());
                infoBo.setTradeNo(independentOrderModel.getTradeNo());
                infoBo.setOrderTime(independentOrderModel.getCreateTime());
                infoBo.setIndependentStatus(independentOrderModel.getIndependentStatus());
                if (independentOrderModel.getIndependentStatus() == 1) {
                    infoBo.setIncomeTime(independentOrderModel.getUpdateTime());
                }
                infoBo.setDelFlag(independentOrderModel.getDelFlag());
                userIncomeStatisticsInfoService.insertByBo(infoBo);
            }
        }
    }

    /**
     * 按天处理
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    private void handleForDay(Date startTime, Date endTime) {
        String day = DateUtils.formatToDate(startTime);
        handleTimeBasedStatistics(startTime, endTime, day, SohuDateEnum.DAY);
    }

    /**
     * 处理仅邀请数据的日统计记录
     */
    private void processInviteOnlyDayRecord(Long userId, String dateStr, InviteDataResult inviteDataResult) {
        SohuUserIncomeStatisticsDayBo bo = createInviteOnlyDayBo(userId, dateStr, inviteDataResult);
        SohuUserIncomeStatisticsDayVo vo = userIncomeStatisticsDayService.queryByParam(
                userId, bo.getRoleType(), bo.getStationId(), bo.getBusyType(), dateStr);
        saveOrUpdateDayRecord(bo, vo);
    }

    /**
     * 处理仅邀请数据的周统计记录
     */
    private void processInviteOnlyWeekRecord(Long userId, String dateStr, InviteDataResult inviteDataResult) {
        SohuUserIncomeStatisticsWeekBo bo = createInviteOnlyWeekBo(userId, dateStr, inviteDataResult);
        SohuUserIncomeStatisticsWeekVo vo = userIncomeStatisticsWeekService.queryByParam(
                userId, bo.getRoleType(), bo.getStationId(), bo.getBusyType(), dateStr);
        saveOrUpdateWeekRecord(bo, vo);
    }

    /**
     * 处理仅邀请数据的月统计记录
     */
    private void processInviteOnlyMonthRecord(Long userId, String dateStr, InviteDataResult inviteDataResult) {
        SohuUserIncomeStatisticsMonthBo bo = createInviteOnlyMonthBo(userId, dateStr, inviteDataResult);
        SohuUserIncomeStatisticsMonthVo vo = userIncomeStatisticsMonthService.queryByParam(
                userId, bo.getRoleType(), bo.getStationId(), bo.getBusyType(), dateStr);
        saveOrUpdateMonthRecord(bo, vo);
    }

    /**
     * 创建仅邀请数据的日统计BO
     */
    private SohuUserIncomeStatisticsDayBo createInviteOnlyDayBo(Long userId, String dateStr, InviteDataResult inviteDataResult) {
        SohuUserIncomeStatisticsDayBo bo = new SohuUserIncomeStatisticsDayBo();
        bo.setUserId(userId);
        bo.setDayDate(dateStr);
        bo.setInviteNum((long) inviteDataResult.getInviteNum(userId));
        bo.setInviteBindNum((long) inviteDataResult.getInviteBindNum(userId));
        setDefaultInviteValues(bo);
        return bo;
    }

    /**
     * 创建仅邀请数据的周统计BO
     */
    private SohuUserIncomeStatisticsWeekBo createInviteOnlyWeekBo(Long userId, String dateStr, InviteDataResult inviteDataResult) {
        SohuUserIncomeStatisticsWeekBo bo = new SohuUserIncomeStatisticsWeekBo();
        bo.setUserId(userId);
        bo.setWeekDate(dateStr);
        bo.setInviteNum((long) inviteDataResult.getInviteNum(userId));
        bo.setInviteBindNum((long) inviteDataResult.getInviteBindNum(userId));
        setDefaultInviteValues(bo);
        return bo;
    }

    /**
     * 创建仅邀请数据的月统计BO
     */
    private SohuUserIncomeStatisticsMonthBo createInviteOnlyMonthBo(Long userId, String dateStr, InviteDataResult inviteDataResult) {
        SohuUserIncomeStatisticsMonthBo bo = new SohuUserIncomeStatisticsMonthBo();
        bo.setUserId(userId);
        bo.setMonthDate(dateStr);
        bo.setInviteNum((long) inviteDataResult.getInviteNum(userId));
        bo.setInviteBindNum((long) inviteDataResult.getInviteBindNum(userId));
        setDefaultInviteValues(bo);
        return bo;
    }

    /**
     * 设置默认的邀请相关值
     */
    private void setDefaultInviteValues(Object bo) {
        if (bo instanceof SohuUserIncomeStatisticsDayBo) {
            SohuUserIncomeStatisticsDayBo dayBo = (SohuUserIncomeStatisticsDayBo) bo;
            dayBo.setStationId(0L);
            dayBo.setRoleType(UserRoleEnum.AGENCY.getType());
            dayBo.setBusyType(BusyType.Invite.getType());
            dayBo.setTotalIncome(BigDecimal.ZERO);
            dayBo.setInviteIncome(BigDecimal.ZERO);
            dayBo.setTradeAmount(BigDecimal.ZERO);
            dayBo.setOrderNum(0L);
        } else if (bo instanceof SohuUserIncomeStatisticsWeekBo) {
            SohuUserIncomeStatisticsWeekBo weekBo = (SohuUserIncomeStatisticsWeekBo) bo;
            weekBo.setStationId(0L);
            weekBo.setRoleType(UserRoleEnum.AGENCY.getType());
            weekBo.setBusyType(BusyType.Invite.getType());
            weekBo.setTotalIncome(BigDecimal.ZERO);
            weekBo.setInviteIncome(BigDecimal.ZERO);
            weekBo.setTradeAmount(BigDecimal.ZERO);
            weekBo.setOrderNum(0L);
        } else if (bo instanceof SohuUserIncomeStatisticsMonthBo) {
            SohuUserIncomeStatisticsMonthBo monthBo = (SohuUserIncomeStatisticsMonthBo) bo;
            monthBo.setStationId(0L);
            monthBo.setRoleType(UserRoleEnum.AGENCY.getType());
            monthBo.setBusyType(BusyType.Invite.getType());
            monthBo.setTotalIncome(BigDecimal.ZERO);
            monthBo.setInviteIncome(BigDecimal.ZERO);
            monthBo.setTradeAmount(BigDecimal.ZERO);
            monthBo.setOrderNum(0L);
        }
    }

    /**
     * 保存或更新日统计记录
     */
    private void saveOrUpdateDayRecord(SohuUserIncomeStatisticsDayBo bo, SohuUserIncomeStatisticsDayVo vo) {
        if (Objects.isNull(vo)) {
            userIncomeStatisticsDayService.insertByBo(bo);
        } else {
            bo.setId(vo.getId());
            userIncomeStatisticsDayService.updateByBo(bo);
        }
    }

    /**
     * 保存或更新周统计记录
     */
    private void saveOrUpdateWeekRecord(SohuUserIncomeStatisticsWeekBo bo, SohuUserIncomeStatisticsWeekVo vo) {
        if (Objects.isNull(vo)) {
            userIncomeStatisticsWeekService.insertByBo(bo);
        } else {
            bo.setId(vo.getId());
            userIncomeStatisticsWeekService.updateByBo(bo);
        }
    }

    /**
     * 保存或更新月统计记录
     */
    private void saveOrUpdateMonthRecord(SohuUserIncomeStatisticsMonthBo bo, SohuUserIncomeStatisticsMonthVo vo) {
        if (Objects.isNull(vo)) {
            userIncomeStatisticsMonthService.insertByBo(bo);
        } else {
            bo.setId(vo.getId());
            userIncomeStatisticsMonthService.updateByBo(bo);
        }
    }

    /**
     * 时间维度统计处理的通用方法
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param dateStr    格式化的日期字符串
     * @param sohuDateEnum 时间维度类型
     */
    private void handleTimeBasedStatistics(Date startTime, Date endTime, String dateStr, SohuDateEnum sohuDateEnum) {
        List<UserIncomeVO> userIncomeList = userIncomeStatisticsService.queryUserIncomeByTime(startTime, endTime);
        if (CollectionUtils.isEmpty(userIncomeList)) {
            return;
        }

        List<GroupedIncomeVO> groupIncomeList = sohuReportConvertUtil.groupIncomeData(userIncomeList, Boolean.TRUE);

        // 处理邀请数据
        InviteDataResult inviteDataResult = processInviteData(startTime, endTime);

        // 合并处理所有涉及到的用户ID
        Set<Long> allUserIdsToProcess = new HashSet<>();
        groupIncomeList.forEach(vo -> allUserIdsToProcess.add(vo.getUserId()));
        allUserIdsToProcess.addAll(inviteDataResult.getAgentUserMap().keySet());

        for (Long userId : allUserIdsToProcess) {
            // 获取该用户的收益统计数据 (如果存在)
            List<GroupedIncomeVO> userSpecificIncomeList = groupIncomeList.stream()
                    .filter(vo -> vo.getUserId().equals(userId))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(userSpecificIncomeList)) {
                // 如果只有邀请数据，没有收益数据，也要插入一条记录
                processInviteOnlyRecord(userId, dateStr, sohuDateEnum, inviteDataResult);
            } else {
                // 处理有收益数据的记录
                for (GroupedIncomeVO groupedIncomeVO : userSpecificIncomeList) {
                    processIncomeRecord(groupedIncomeVO, dateStr, sohuDateEnum, inviteDataResult, startTime, endTime);
                }
            }
        }
    }

    /**
     * 处理邀请数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 邀请数据处理结果
     */
    private InviteDataResult processInviteData(Date startTime, Date endTime) {
        // 获取该时间段内的所有邀请数据
        List<SohuAgentUserVo> rawAgentUserVoList = remoteAdminService.queryInviteList();

        // 过滤出当前日期范围内的邀请数据
        List<SohuAgentUserVo> filteredAgentUserVoList = rawAgentUserVoList.stream()
                .filter(vo -> {
                    Date inviteCreateTime = vo.getCreateTime();
                    return inviteCreateTime != null &&
                            inviteCreateTime.compareTo(startTime) >= 0 &&
                            inviteCreateTime.compareTo(endTime) < 0;
                })
                .collect(Collectors.toList());

        // 聚合邀请数据，得到每个agentId的邀请总数和绑定数
        Map<Long, List<SohuAgentUserVo>> agentUserMap = filteredAgentUserVoList.stream()
                .collect(Collectors.groupingBy(SohuAgentUserVo::getAgentId));

        Map<Long, List<SohuAgentUserVo>> agentUserSuccessMap = filteredAgentUserVoList.stream()
                .filter(sohuAgentUserVo -> InviteEnum.SUCCESS.getCode().equals(sohuAgentUserVo.getState()))
                .collect(Collectors.groupingBy(SohuAgentUserVo::getAgentId));

        return new InviteDataResult(agentUserMap, agentUserSuccessMap);
    }

    /**
     * 处理仅有邀请数据的记录
     *
     * @param userId           用户ID
     * @param dateStr          日期字符串
     * @param sohuDateEnum       时间维度
     * @param inviteDataResult 邀请数据结果
     */
    private void processInviteOnlyRecord(Long userId, String dateStr, SohuDateEnum sohuDateEnum, InviteDataResult inviteDataResult) {
        switch (sohuDateEnum) {
            case DAY:
                processInviteOnlyDayRecord(userId, dateStr, inviteDataResult);
                break;
            case WEEK:
                processInviteOnlyWeekRecord(userId, dateStr, inviteDataResult);
                break;
            case MONTH:
                processInviteOnlyMonthRecord(userId, dateStr, inviteDataResult);
                break;
        }
    }

    /**
     * 处理收益记录
     *
     * @param groupedIncomeVO  分组收益数据
     * @param dateStr          日期字符串
     * @param sohuDateEnum       时间维度
     * @param inviteDataResult 邀请数据结果
     * @param startTime        开始时间
     * @param endTime          结束时间
     */
    private void processIncomeRecord(GroupedIncomeVO groupedIncomeVO, String dateStr, SohuDateEnum sohuDateEnum,
                                     InviteDataResult inviteDataResult, Date startTime, Date endTime) {
        switch (sohuDateEnum) {
            case DAY:
                processIncomeDayRecord(groupedIncomeVO, dateStr, inviteDataResult, startTime, endTime);
                break;
            case WEEK:
                processIncomeWeekRecord(groupedIncomeVO, dateStr, inviteDataResult, startTime, endTime);
                break;
            case MONTH:
                processIncomeMonthRecord(groupedIncomeVO, dateStr, inviteDataResult, startTime, endTime);
                break;
        }
    }

    /**
     * 处理收益日统计记录
     */
    private void processIncomeDayRecord(GroupedIncomeVO groupedIncomeVO, String dateStr,
                                        InviteDataResult inviteDataResult, Date startTime, Date endTime) {
        SohuUserIncomeStatisticsDayBo bo = createIncomeDayBo(groupedIncomeVO, dateStr, inviteDataResult, startTime, endTime);
        SohuUserIncomeStatisticsDayVo vo = userIncomeStatisticsDayService.queryByParam(
                groupedIncomeVO.getUserId(), groupedIncomeVO.getIndependentObject(),
                groupedIncomeVO.getSiteId(), groupedIncomeVO.getTradeType(), dateStr);
        saveOrUpdateDayRecord(bo, vo);
    }

    /**
     * 处理收益周统计记录
     */
    private void processIncomeWeekRecord(GroupedIncomeVO groupedIncomeVO, String dateStr,
                                         InviteDataResult inviteDataResult, Date startTime, Date endTime) {
        SohuUserIncomeStatisticsWeekBo bo = createIncomeWeekBo(groupedIncomeVO, dateStr, inviteDataResult, startTime, endTime);
        SohuUserIncomeStatisticsWeekVo vo = userIncomeStatisticsWeekService.queryByParam(
                groupedIncomeVO.getUserId(), groupedIncomeVO.getIndependentObject(),
                groupedIncomeVO.getSiteId(), groupedIncomeVO.getTradeType(), dateStr);
        saveOrUpdateWeekRecord(bo, vo);
    }

    /**
     * 处理收益月统计记录
     */
    private void processIncomeMonthRecord(GroupedIncomeVO groupedIncomeVO, String dateStr,
                                          InviteDataResult inviteDataResult, Date startTime, Date endTime) {
        SohuUserIncomeStatisticsMonthBo bo = createIncomeMonthBo(groupedIncomeVO, dateStr, inviteDataResult, startTime, endTime);
        SohuUserIncomeStatisticsMonthVo vo = userIncomeStatisticsMonthService.queryByParam(
                groupedIncomeVO.getUserId(), groupedIncomeVO.getIndependentObject(),
                groupedIncomeVO.getSiteId(), groupedIncomeVO.getTradeType(), dateStr);
        saveOrUpdateMonthRecord(bo, vo);
    }

    /**
     * 创建收益日统计BO
     */
    private SohuUserIncomeStatisticsDayBo createIncomeDayBo(GroupedIncomeVO groupedIncomeVO, String dateStr,
                                                            InviteDataResult inviteDataResult, Date startTime, Date endTime) {
        SohuUserIncomeStatisticsDayBo bo = new SohuUserIncomeStatisticsDayBo();
        setCommonIncomeValues(bo, groupedIncomeVO, dateStr, inviteDataResult, startTime, endTime);
        bo.setDayDate(dateStr);
        return bo;
    }

    /**
     * 创建收益周统计BO
     */
    private SohuUserIncomeStatisticsWeekBo createIncomeWeekBo(GroupedIncomeVO groupedIncomeVO, String dateStr,
                                                              InviteDataResult inviteDataResult, Date startTime, Date endTime) {
        SohuUserIncomeStatisticsWeekBo bo = new SohuUserIncomeStatisticsWeekBo();
        setCommonIncomeValues(bo, groupedIncomeVO, dateStr, inviteDataResult, startTime, endTime);
        bo.setWeekDate(dateStr);
        return bo;
    }

    /**
     * 创建收益月统计BO
     */
    private SohuUserIncomeStatisticsMonthBo createIncomeMonthBo(GroupedIncomeVO groupedIncomeVO, String dateStr,
                                                                InviteDataResult inviteDataResult, Date startTime, Date endTime) {
        SohuUserIncomeStatisticsMonthBo bo = new SohuUserIncomeStatisticsMonthBo();
        setCommonIncomeValues(bo, groupedIncomeVO, dateStr, inviteDataResult, startTime, endTime);
        bo.setMonthDate(dateStr);
        return bo;
    }

    /**
     * 设置通用的收益相关值
     */
    private void setCommonIncomeValues(Object bo, GroupedIncomeVO groupedIncomeVO, String dateStr,
                                       InviteDataResult inviteDataResult, Date startTime, Date endTime) {
        Long userId = groupedIncomeVO.getUserId();
        int inviteNum = inviteDataResult.getInviteNum(userId);
        int inviteBindNum = inviteDataResult.getInviteBindNum(userId);

        // 拉新收益
        BigDecimal inviteIncome = userService.getUserInviteIncome(
                groupedIncomeVO.getUserId(), groupedIncomeVO.getSiteType(),
                groupedIncomeVO.getSiteId(), groupedIncomeVO.getIndependentObject(),
                startTime, endTime);

        // 获取订单信息
        SohuUserOrderInfoVo userOrderInfo = userService.getUserOrderInfo(
                groupedIncomeVO.getUserId(), groupedIncomeVO.getSiteType(),
                groupedIncomeVO.getSiteId(),
                sohuReportRoleUtil.getPersonRolesByRoleType(groupedIncomeVO.getIndependentObject()),
                groupedIncomeVO.getTradeType(), startTime, endTime);

        BigDecimal tradeAmount = BigDecimal.ZERO;
        Long orderNum = 0L;
        if (Objects.nonNull(userOrderInfo)) {
            tradeAmount = userOrderInfo.getOrderAmount();
            orderNum = userOrderInfo.getOrderCount();
        }

        // 设置通用字段
        if (bo instanceof SohuUserIncomeStatisticsDayBo) {
            SohuUserIncomeStatisticsDayBo dayBo = (SohuUserIncomeStatisticsDayBo) bo;
            setIncomeBoValues(dayBo, groupedIncomeVO, inviteNum, inviteBindNum, inviteIncome, tradeAmount, orderNum);
        } else if (bo instanceof SohuUserIncomeStatisticsWeekBo) {
            SohuUserIncomeStatisticsWeekBo weekBo = (SohuUserIncomeStatisticsWeekBo) bo;
            setIncomeBoValues(weekBo, groupedIncomeVO, inviteNum, inviteBindNum, inviteIncome, tradeAmount, orderNum);
        } else if (bo instanceof SohuUserIncomeStatisticsMonthBo) {
            SohuUserIncomeStatisticsMonthBo monthBo = (SohuUserIncomeStatisticsMonthBo) bo;
            setIncomeBoValues(monthBo, groupedIncomeVO, inviteNum, inviteBindNum, inviteIncome, tradeAmount, orderNum);
        }
    }

    /**
     * 设置收益BO的通用值 - 日统计
     */
    private void setIncomeBoValues(SohuUserIncomeStatisticsDayBo bo, GroupedIncomeVO groupedIncomeVO,
                                   int inviteNum, int inviteBindNum, BigDecimal inviteIncome,
                                   BigDecimal tradeAmount, Long orderNum) {
        bo.setUserId(groupedIncomeVO.getUserId());
        bo.setRoleType(groupedIncomeVO.getIndependentObject());
        bo.setStationId(groupedIncomeVO.getSiteId());
        bo.setBusyType(groupedIncomeVO.getTradeType());
        bo.setTotalIncome(groupedIncomeVO.getEntryAmount());
        bo.setInviteNum((long) inviteNum);
        bo.setInviteBindNum((long) inviteBindNum);
        bo.setInviteIncome(inviteIncome);
        bo.setTradeAmount(tradeAmount);
        bo.setOrderNum(orderNum);
    }

    /**
     * 设置收益BO的通用值 - 周统计
     */
    private void setIncomeBoValues(SohuUserIncomeStatisticsWeekBo bo, GroupedIncomeVO groupedIncomeVO,
                                   int inviteNum, int inviteBindNum, BigDecimal inviteIncome,
                                   BigDecimal tradeAmount, Long orderNum) {
        bo.setUserId(groupedIncomeVO.getUserId());
        bo.setRoleType(groupedIncomeVO.getIndependentObject());
        bo.setStationId(groupedIncomeVO.getSiteId());
        bo.setBusyType(groupedIncomeVO.getTradeType());
        bo.setTotalIncome(groupedIncomeVO.getEntryAmount());
        bo.setInviteNum((long) inviteNum);
        bo.setInviteBindNum((long) inviteBindNum);
        bo.setInviteIncome(inviteIncome);
        bo.setTradeAmount(tradeAmount);
        bo.setOrderNum(orderNum);
    }

    /**
     * 设置收益BO的通用值 - 月统计
     */
    private void setIncomeBoValues(SohuUserIncomeStatisticsMonthBo bo, GroupedIncomeVO groupedIncomeVO,
                                   int inviteNum, int inviteBindNum, BigDecimal inviteIncome,
                                   BigDecimal tradeAmount, Long orderNum) {
        bo.setUserId(groupedIncomeVO.getUserId());
        bo.setRoleType(groupedIncomeVO.getIndependentObject());
        bo.setStationId(groupedIncomeVO.getSiteId());
        bo.setBusyType(groupedIncomeVO.getTradeType());
        bo.setTotalIncome(groupedIncomeVO.getEntryAmount());
        bo.setInviteNum((long) inviteNum);
        bo.setInviteBindNum((long) inviteBindNum);
        bo.setInviteIncome(inviteIncome);
        bo.setTradeAmount(tradeAmount);
        bo.setOrderNum(orderNum);
    }

    /**
     * 按周处理
     *
     * @param startOfWeek 开始周
     * @param endOfWeek 结束周
     */
    private void handleForWeek(Date startOfWeek, Date endOfWeek) {
        String week = DateUtils.formatToYearWeek(startOfWeek);
        handleTimeBasedStatistics(startOfWeek, endOfWeek, week, SohuDateEnum.WEEK);
    }

    /**
     * 按月处理
     *
     * @param startOfMonth 开始月份
     * @param endOfMonth 结束月份
     */
    private void handleForMonth(Date startOfMonth, Date endOfMonth) {
        String month = DateUtils.formatToYearMonth(startOfMonth);
        handleTimeBasedStatistics(startOfMonth, endOfMonth, month, SohuDateEnum.MONTH);
    }

    @Override
    public List<SohuUserIncomeStatisticsInfoVo> queryUserIncomeByTime(Long userId, Date startTime, Date endTime) {
        if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
            return List.of();
        }
        Date safeStartDate = new Date(startTime.getTime());
        Date safeEndDate = new Date(endTime.getTime());
        return userIncomeStatisticsInfoService.queryUserIncomeByTime(userId, safeStartDate, safeEndDate);
    }

    /**
     * 邀请数据处理结果
     */
    @Getter
    static class InviteDataResult {
        private final Map<Long, List<SohuAgentUserVo>> agentUserMap;
        private final Map<Long, List<SohuAgentUserVo>> agentUserSuccessMap;

        public InviteDataResult(Map<Long, List<SohuAgentUserVo>> agentUserMap,
                                Map<Long, List<SohuAgentUserVo>> agentUserSuccessMap) {
            this.agentUserMap = agentUserMap;
            this.agentUserSuccessMap = agentUserSuccessMap;
        }

        public int getInviteNum(Long userId) {
            return agentUserMap.getOrDefault(userId, Collections.emptyList()).size();
        }

        public int getInviteBindNum(Long userId) {
            return agentUserSuccessMap.getOrDefault(userId, Collections.emptyList()).size();
        }
    }

    @Override
    public void saveOrUpdateBatchRetentionStats(List<SohuUserRetentionStatVo> retentionStats) {
        sohuUserRetentionStatService.saveOrUpdateBatchRetentionStats(retentionStats);
    }
}