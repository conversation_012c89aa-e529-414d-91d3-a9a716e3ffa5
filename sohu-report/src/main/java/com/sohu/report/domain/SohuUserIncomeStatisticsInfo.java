package com.sohu.report.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.math.BigDecimal;
import java.util.Date;
import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 用户收益明细对象 sohu_user_income_statistics_info
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_user_income_statistics_info")
public class SohuUserIncomeStatisticsInfo extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 站长ID
     */
    private Long userId;
    /**
     * 角色类型
     */
    private String roleType;
    /**
     * 行业ID或城市站点ID
     */
    private Long stationId;
    /**
     * 类型
     */
    private Integer stationType;
    /**
     * 业务类型 BusyTask 愿望 Goods 商品 Novel 小说 ShortPlay 短剧
     */
    private String busyType;
    /**
     * 业务明细名称
     */
    private String busyInfoName;
    /**
     * 收益类型  1.拉新  2.分红
     */
    private Integer incomeType;
    /**
     * 到账收益
     */
    private BigDecimal income;
    /**
     * 消费者id
     */
    private Long consumerUserId;
    /**
     * 消费者对应的渠道id
     */
    private Long consumerChannelId;
    /**
     * 消费者对应的渠道名称
     */
    private String consumerChannelName;
    /**
     * 订单消费金额
     */
    private BigDecimal orderAmount;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 第三方交易单号
     */
    private String tradeNo;
    /**
     * 下单时间
     */
    private Date orderTime;
    /**
     * 收益到账时间
     */
    private Date incomeTime;
    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;
    /**
     * 分账单id
     */
    private Long independentOrderId;

    /**
     * 分账状态
     */
    private Integer independentStatus;


}
