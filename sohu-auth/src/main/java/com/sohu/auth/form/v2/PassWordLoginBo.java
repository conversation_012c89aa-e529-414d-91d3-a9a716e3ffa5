package com.sohu.auth.form.v2;

import com.sohu.auth.enums.LoginTypeEnum;
import com.sohu.common.core.constant.UserConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 账号密码登录
 *
 * <AUTHOR>
 * @since 2024/6/21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PassWordLoginBo extends AbsLoginReqBo {

    /**
     * 用户名
     */
    @NotBlank(message = "{user.username.not.blank}")
    //@Length(min = UserConstants.USERNAME_MIN_LENGTH, max = UserConstants.USERNAME_MAX_LENGTH, message = "{user.username.length.valid}")
    private String userName;

    /**
     * 用户密码
     */
    @NotBlank(message = "{user.password.not.blank}")
    @Length(min = UserConstants.PASSWORD_MIN_LENGTH, max = UserConstants.PASSWORD_MAX_LENGTH, message = "{user.password.length.valid}")
    private String password;

    public PassWordLoginBo() {
        super(LoginTypeEnum.PASSWORD);
    }
}
