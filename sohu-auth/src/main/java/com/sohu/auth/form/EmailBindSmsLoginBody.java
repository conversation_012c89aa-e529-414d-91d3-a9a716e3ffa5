package com.sohu.auth.form;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 邮箱绑定手机号登录对象
 *
 * <AUTHOR> Li
 */

@Data
public class EmailBindSmsLoginBody {

    /**
     * 用户id
     */
    @NotNull(message = "{user.not.blank}")
    private Long userId;

    /**
     * 手机号
     */
    @NotBlank(message = "{user.phonenumber.not.blank}")
    private String phoneNumber;

    /**
     * 短信code
     */
    @NotBlank(message = "{sms.code.not.blank}")
    private String smsCode;

}
