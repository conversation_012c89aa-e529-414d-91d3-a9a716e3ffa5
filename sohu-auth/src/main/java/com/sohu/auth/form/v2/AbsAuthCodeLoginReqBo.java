package com.sohu.auth.form.v2;

import com.sohu.auth.enums.LoginTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @since 2024/6/21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AbsAuthCodeLoginReqBo extends AbsLoginReqBo{

    @Schema(description = "授权uuid", required = true, example = "o6l1k6qlKf0UWY1M4EcG4pvZc1")
    private String uuid;

    @Schema(description = "手机号", required = false, example = "15601691300")
    private String mobile;

    @Schema(description = "手机验证码", required = false, example = "1024")
    @Length(min = 4, max = 6, message = "手机验证码长度为 4-6 位")
    @Pattern(regexp = "^[0-9]+$", message = "手机验证码必须都是数字")
    private String code;

    @Schema(description = "登录 code", required = true, example = "o6l1k6qlKf0UWY1M4EcG4pvZc1")
    private String tokenCode;

    protected AbsAuthCodeLoginReqBo(LoginTypeEnum loginTypeEnum){
        super(loginTypeEnum);
    }
}
