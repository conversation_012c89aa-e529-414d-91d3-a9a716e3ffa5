package com.sohu.auth.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@Schema(description = "用户 APP - IOS授权登录 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppleAppLoginBody {

    @Schema(description = "apple授权登录码", required = true, example = "o6l1k6qlKf0UWY1M4EcG4pvZc1")
    @NotEmpty(message = "apple授权登录码 不能为空")
    private String identityToken;

}
