package com.sohu.auth.enums;

import com.sohu.auth.form.v2.*;
import com.sohu.common.core.enums.DeviceType;
import com.sohu.common.core.enums.SocialSourceEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import static com.sohu.common.core.enums.DeviceType.*;

/**
 * <AUTHOR>
 * @since 2024/6/21
 */
@Getter
@AllArgsConstructor
public enum LoginTypeEnum {
    /**
     * 密码登录
     */
    PASSWORD("PASSWORD", "密码", PassWordLoginBo.class, PC, false) {
        /**
         * 是否首次登录即注册
         * @return {@link Boolean}
         */
        @Override
        public boolean loginWithRegister() {
            return Boolean.FALSE;
        }
    },
    /**
     * 短信验证码登录
     */
    SMS_CODE("SMS_CODE", "手机验证码", SmsCodeLoginBo.class, APP, false),
    /**
     * 邮箱验证码登录
     */
    EMAIL_CODE("EMAIL_CODE", "邮箱验证码", EmailCodeLoginBo.class, APP, false),
    /**
     * 邮箱密码登录
     */
    EMAIL_PASSWORD("EMAIL_PASSWORD", "邮箱密码", EmailPassWordLoginBo.class, APP, false),
    /**
     * 微信小程序登录
     */
    WX_MINI_APP("WX_MINI_APP", "微信小程序", WxMiniAppLoginBo.class, Applet, true),
    /**
     * 微信扫码登录
     */
    WX_QR("WX_QR", "微信扫码", WxQrLoginBo.class, PC, true),
    /**
     * 微信APP登录
     */
    WX_APP("WX_APP", "微信APP", WxAppLoginBo.class, APP, true),
    /**
     * 苹果APP登录
     */
    APPLE_APP("APPLE_APP", "苹果APP", AppleLoginBo.class, APP, true),
    /**
     * 谷歌登录
     */
    google("google", "谷歌", AppleLoginBo.class, PC, true);


    private final String type;

    private final String desc;

    private final Class<? extends AbsLoginReqBo> requestClz;

    private final DeviceType deviceType;

    /**
     * 是否三方授权登录
     */
    private final boolean thirdAuth;


    /**
     * 是否首次登录即注册，默认TRUE
     */
    public boolean loginWithRegister() {
        return Boolean.TRUE;
    }

    public SocialSourceEnum convert2SocialSourceEnum() {
        switch (this) {
            case WX_MINI_APP:
                return SocialSourceEnum.wechat_miniapp;
            case WX_APP:
                return SocialSourceEnum.wechat_app;
            case WX_QR:
                return SocialSourceEnum.wechat_qr;
            case APPLE_APP:
                return SocialSourceEnum.ios_app;
            case google:
                return SocialSourceEnum.google;
            default:
                throw new IllegalArgumentException();
        }
    }
}
