package com.sohu.auth.service.strategy;

import cn.hutool.core.util.StrUtil;
import com.sohu.auth.enums.LoginTypeEnum;
import com.sohu.auth.form.v2.EmailCodeLoginBo;
import com.sohu.common.core.enums.LoginType;
import com.sohu.common.core.utils.SecretUtil;
import com.sohu.common.satoken.config.LoginUser;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/6/21
 */
@Component
public class EmailCodeStrategy extends AbsLoginStrategy<EmailCodeLoginBo>{
    @Override
    protected LoginTypeEnum getType() {
        return LoginTypeEnum.EMAIL_CODE;
    }

    @Override
    protected LoginUser prepareLogin(EmailCodeLoginBo req) {
        validateSmsCodeBeforeSave(req.getEmail(), req.getEmailCode());

        // 通过邮箱查找用户
        //LoginUser userInfo = this.createUserByEmailIfAbsent(req.getEmail(), req.getRoleKey());
        LoginUser userInfo = null;
        if (StrUtil.isEmpty(req.getPassword())) {
            userInfo = this.createUserByEmailIfAbsent(req);
        } else {
            String reqPassword = SecretUtil.desEncrypt(req.getPassword(), SecretUtil.KEY);
            req.setPassword(reqPassword);
            userInfo = this.createUserByEmailIfAbsent(req);
        }

        checkLogin(LoginType.EMAIL, userInfo.getUsername(), () -> !validateEmailCode(req.getEmail(), req.getEmailCode()));

        return userInfo;
    }

    @Override
    protected LoginUser prepareRegister(EmailCodeLoginBo req) {
        if(StrUtil.isEmpty(req.getPassword())){
            throw new RuntimeException("密码不能为空");
        }
        validateSmsCodeBeforeSave(req.getEmail(), req.getEmailCode());
        String reqPassword = SecretUtil.desEncrypt(req.getPassword(), SecretUtil.KEY);
        req.setPassword(reqPassword);
        LoginUser userInfo = this.registerUserByEmailIfAbsent(req);
        checkLogin(LoginType.EMAIL, userInfo.getUsername(), () -> !validateEmailCode(req.getEmail(), req.getEmailCode()));
        return userInfo;
    }
}
