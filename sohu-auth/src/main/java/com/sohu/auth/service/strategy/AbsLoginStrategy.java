package com.sohu.auth.service.strategy;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.sohu.admin.api.RemoteAdminService;
import com.sohu.auth.enums.LoginTypeEnum;
import com.sohu.auth.form.CodeVerification;
import com.sohu.auth.form.RegisterBody;
import com.sohu.auth.form.ThirdAppLoginBody;
import com.sohu.auth.form.v2.AbsLoginReqBo;
import com.sohu.auth.form.v2.EmailCodeLoginBo;
import com.sohu.auth.form.v2.SmsCodeLoginBo;
import com.sohu.auth.properties.UserPasswordProperties;
import com.sohu.auth.vo.LoginAuthVO;
import com.sohu.auth.vo.WxAccessToken;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.base.BaseException;
import com.sohu.common.core.exception.user.CaptchaExpireException;
import com.sohu.common.core.exception.user.UserException;
import com.sohu.common.core.utils.*;
import com.sohu.common.core.utils.ip.AddressUtils;
import com.sohu.common.log.event.LogininforEvent;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.config.RoleDTO;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuEventReportBo;
import com.sohu.middle.api.bo.SohuUserBehaviorRecordPointBo;
import com.sohu.middle.api.enums.behavior.BehaviorBusinessTypeEnum;
import com.sohu.middle.api.enums.behavior.OperaTypeEnum;
import com.sohu.middle.api.enums.report.ActionTypeEnum;
import com.sohu.middle.api.enums.report.RegisterReportEnum;
import com.sohu.middle.api.service.RemoteMiddleEventReportService;
import com.sohu.middle.api.service.RemoteMiddleInviteService;
import com.sohu.middle.api.service.RemoteMiddleUserBehaviorRecordService;
import com.sohu.middle.api.service.im.RemoteMiddleImTenantService;
import com.sohu.middle.api.vo.SohuCommonEventReportVo;
import com.sohu.middle.api.vo.SohuInviteVo;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.resource.api.RemoteResourceSendRecordService;
import com.sohu.system.api.*;
import com.sohu.system.api.bo.SohuDownloadRegisterReportBo;
import com.sohu.system.api.domain.SocialUserDomain;
import com.sohu.system.api.domain.SysRole;
import com.sohu.system.api.domain.SysUser;
import com.sohu.system.api.vo.SysPlatformRoleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.sohu.common.core.constant.Constants.PROFILE_PROD;
import static com.sohu.common.core.enums.SocialSourceEnum.*;


/**
 * <AUTHOR>
 * @since 2024/6/21
 */
@Slf4j
public abstract class AbsLoginStrategy<T extends AbsLoginReqBo> {

    /**
     * 初始密码
     */
    protected static final String DEFAULT_PWD = "123456";

    @Value("${spring.profiles.active}")
    private String activeProfile;


    @DubboReference
    protected RemoteUserService remoteUserService;
    @DubboReference
    private RemoteAdminService remoteAdminService;
    @DubboReference
    protected RemoteSocialUserService remoteSocialUserService;
    @DubboReference
    protected RemoteSysRoleService remoteSysRoleService;
    @DubboReference
    protected RemoteAccountService remoteAccountService;
    @DubboReference
    private RemotePlatformRoleService remotePlatformRoleService;
    @DubboReference
    private RemoteSohuDownloadRegisterReportService remoteSohuDownloadRegisterReportService;
    @DubboReference
    private RemoteMiddleImTenantService remoteMiddleImTenantService;
    @DubboReference
    private RemoteMiddleEventReportService remoteMiddleEventReportService;
    @DubboReference
    private RemoteResourceSendRecordService remoteResourceSendRecordService;
    @DubboReference
    protected RemoteMiddleUserBehaviorRecordService remoteMiddleUserBehaviorRecordService;

    @DubboReference
    protected RemoteMiddleInviteService remoteMiddleInviteService;

    @Resource
    protected UserPasswordProperties userPasswordProperties;
    @Resource
    protected WxMaService wxMaService;

    @Autowired
    private AsyncConfig asyncConfig;

//    @Resource
//    private SysLoginService sysLoginService;


    /**
     * 登录方式
     */
    protected abstract LoginTypeEnum getType();

    /**
     * 校验逻辑，如账号密码、手机号(验证码)
     */
    protected void match(T req) {

    }

    /**
     * 登录前置逻辑
     */
    protected abstract LoginUser prepareLogin(T req);

    /**
     * 注册前置逻辑
     *
     * @param req
     * @return
     */
    protected abstract LoginUser prepareRegister(T req);


    /**
     * 登录逻辑
     */
    public final LoginAuthVO login(T req) {
        LoginTypeEnum loginType = getType();

        LoginUser userInfo = prepareLogin(req);
        userInfo = this.beforeLogin(req, userInfo);
        userInfo.setExpireTime(userPasswordProperties.getExpiresTime());
        LoginHelper.loginByDevice(userInfo, Objects.isNull(req.getDeviceType()) ? loginType.getDeviceType() : req.getDeviceType());
        recordLogininfor(userInfo.getUsername(), userInfo.getUserId(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));

        successHandleOfLogin(req, userInfo);

//        // 邀请绑定异步
//        if (StringUtils.isNotEmpty(req.getBizType())) {
////            sysLoginService.asyncBind(req);
//            this.inviteBinding(req.getBizType(), req.getBizCode(), req.getRoleKey());
//        }
        List<String> roles = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userInfo.getRoles())) {
            roles = userInfo.getRoles().stream().map(RoleDTO::getRoleKey).collect(Collectors.toList());
        }
        return build(userInfo.getUserId(), StpUtil.getTokenValue(), roles, userInfo);
    }

    /**
     * 登录之前要做的操作
     *
     * @param req
     * @param userInfo
     */
    public LoginUser beforeLogin(T req, LoginUser userInfo) {
        if (Objects.equals(req.getLoginPlatform(), PlatformRoleCodeEnum.IM_TENANT.getCode())) {
            return this.giveImTenantRole(userInfo);
        }
        return userInfo;
    }

//    /**
//     * IM租户登录逻辑
//     */
//    public final LoginAuthVO imTenantLogin(T req) {
//        LoginTypeEnum loginType = getType();
//
//        LoginUser userInfo = prepareLogin(req);
//        userInfo = this.giveImTenantRole(userInfo);
//        userInfo.setExpireTime(userPasswordProperties.getExpiresTime());
//        LoginHelper.loginByDevice(userInfo, loginType.getDeviceType());
//        recordLogininfor(userInfo.getUsername(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
//        //初始化IM租户
//        remoteMiddleImTenantService.initImTenant();
//        return build(userInfo.getUserId(), StpUtil.getTokenValue());
//    }

    /**
     * 授予租户权限
     */
    private LoginUser giveImTenantRole(LoginUser userInfo) {
        String loginPlatform = PlatformRoleCodeEnum.IM_TENANT.getCode();
        List<String> platformKeys = remotePlatformRoleService.selectKeysOfEnableByUserId(userInfo.getUserId());
        if (CollUtil.isNotEmpty(platformKeys) && platformKeys.contains(loginPlatform)) {
            return userInfo;
        }
        // 赋予用户角色列表
        List<String> giveRoleList = new ArrayList<>();
        // 赋予用户平台角色列表
        List<String> givePlatformRoleList = new ArrayList<>();
        // 默认赋予 IM租户功能
        giveRoleList.add(RoleCodeEnum.IM_TENANT.getCode());
        // 默认赋予 创作者服务平台角色
        givePlatformRoleList.add(PlatformRoleCodeEnum.IM_TENANT.getCode());
        log.info("给租户授予功能角色:{}", JSONObject.toJSON(giveRoleList));
        savePlatformRoles(userInfo.getUserId(), givePlatformRoleList);
        saveUserRoles(userInfo.getUserId(), giveRoleList);
        this.remoteUserService.flushLoginCacheByUserId(userInfo.getUserId());
        return this.remoteUserService.selectById(userInfo.getUserId());
    }

    /**
     * 登录逻辑
     */
    public final Boolean register(T req) {
        LoginUser userInfo = prepareRegister(req);
        successHandleOfRegister(req, userInfo);
        return true;
    }

    /**
     * 成功处理逻辑，TODO 异步化
     */
    protected void successHandleOfLogin(T req, LoginUser loginUser) {
        // 邀请绑定异步
        if (StringUtils.isNotEmpty(req.getBizType())) {
            this.inviteBinding(req.getBizType(), req.getBizCode(), req.getRoleKey(), loginUser.getUserId(), req.getSiteId(), req.getChannelId());
        }
        if (Objects.equals(req.getLoginPlatform(), PlatformRoleCodeEnum.IM_TENANT.getCode())) {
            //初始化IM租户
            remoteMiddleImTenantService.initImTenant();
        }
        // 登录事件埋点
        buildLoginEventReport(loginUser);
        // 更新登录日期，ip
        this.updateUser(loginUser);
    }

    /**
     * 登录事件埋点
     *
     * @param loginUser LoginUser
     */
    private void buildLoginEventReport(LoginUser loginUser) {
        LoginTypeEnum type = getType();
        SohuEventReportBo bo = new SohuEventReportBo(
                RegisterReportEnum.getCode(RegisterReportEnum.YZDL.getType()),
                RegisterReportEnum.YZDL.getDesc(),
                ActionTypeEnum.Register.name(),
                JSONUtil.toJsonStr(new SohuCommonEventReportVo().setNewUser(false).setLoginType(type.getType()).setLoginTypeName(type.getDesc())),
                RegisterReportEnum.YZDL.getType(),
                loginUser.getUserId());
        remoteMiddleEventReportService.getEventId(bo);
    }

    /**
     * 成功处理逻辑，TODO 异步化
     */
    protected void successHandleOfRegister(T req, LoginUser loginUser) {
        // 邀请绑定异步
        if (StringUtils.isNotEmpty(req.getBizType())) {
//            sysLoginService.asyncBind(req);
            this.inviteBinding(req.getBizType(), req.getBizCode(), req.getRoleKey(), loginUser.getUserId(), req.getSiteId(), req.getChannelId());
        }

        // 注册事件埋点
        buildRegisterEventReport(loginUser);
        this.updateUser(loginUser);
    }

    private void buildRegisterEventReport(LoginUser loginUser) {
        LoginTypeEnum type = getType();
        SohuEventReportBo bo = new SohuEventReportBo(
                RegisterReportEnum.getCode(RegisterReportEnum.ZCDL.getType()),
                RegisterReportEnum.ZCDL.getDesc(),
                ActionTypeEnum.Register.name(),
                JSONUtil.toJsonStr(new SohuCommonEventReportVo().setNewUser(true).setLoginType(type.getType()).setLoginTypeName(type.getDesc())),
                RegisterReportEnum.ZCDL.getType(),
                loginUser.getUserId());
        remoteMiddleEventReportService.getEventId(bo);
    }


    /**
     * 记录登录信息
     *
     * @param username 用户名
     * @param status   状态
     * @param message  消息内容
     * @return
     */
    public void recordLogininfor(String username, String status, String message) {
        HttpServletRequest request = ServletUtils.getRequest();
        final UserAgent userAgent = UserAgentUtil.parse(request.getHeader("User-Agent"));
        final String ip = ServletUtils.getClientIP(request);

        String address = AddressUtils.getRealAddressByIP(ip);
        // 获取客户端操作系统
        String os = userAgent.getOs().getName();
        // 获取客户端浏览器
        String browser = userAgent.getBrowser().getName();
        // 封装对象
        LogininforEvent logininfor = new LogininforEvent();
        logininfor.setUserName(username);
        logininfor.setIpaddr(ip);
        logininfor.setLoginLocation(address);
        logininfor.setBrowser(browser);
        logininfor.setOs(os);
        logininfor.setMsg(message);
        logininfor.setUserId(LoginHelper.getUserId());
        // 日志状态
        if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER)) {
            logininfor.setStatus(Constants.LOGIN_SUCCESS_STATUS);
        } else if (Constants.LOGIN_FAIL.equals(status)) {
            logininfor.setStatus(Constants.LOGIN_FAIL_STATUS);
        }
        SpringUtils.context().publishEvent(logininfor);
    }

    /**
     * 记录登录信息
     *
     * @param username 用户名
     * @param userId
     * @param status   状态
     * @param message  消息内容
     * @return
     */
    public void recordLogininfor(String username, Long userId, String status, String message) {
        HttpServletRequest request = ServletUtils.getRequest();
        final UserAgent userAgent = UserAgentUtil.parse(request.getHeader("User-Agent"));
        final String ip = ServletUtils.getClientIP(request);

        String address = AddressUtils.getRealAddressByIP(ip);
        // 获取客户端操作系统
        String os = userAgent.getOs().getName();
        // 获取客户端浏览器
        String browser = userAgent.getBrowser().getName();
        // 封装对象
        LogininforEvent logininfor = new LogininforEvent();
        logininfor.setUserName(username);
        logininfor.setIpaddr(ip);
        logininfor.setLoginLocation(address);
        logininfor.setBrowser(browser);
        logininfor.setOs(os);
        logininfor.setMsg(message);
        logininfor.setUserId(userId);
        // 日志状态
        if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER)) {
            logininfor.setStatus(Constants.LOGIN_SUCCESS_STATUS);
        } else if (Constants.LOGIN_FAIL.equals(status)) {
            logininfor.setStatus(Constants.LOGIN_FAIL_STATUS);
        }
        SpringUtils.context().publishEvent(logininfor);
    }

    /**
     * 构建登录返回VO
     *
     * @param userId     用户ID
     * @param tokenValue token值
     * @return {@link LoginAuthVO}
     */
    public LoginAuthVO build(Long userId, String tokenValue, List<String> roles, LoginUser userInfo) {
        LoginAuthVO vo = new LoginAuthVO();
        vo.setUserId(userId);
        vo.setAccessToken(tokenValue);
        vo.setFlag(Boolean.TRUE);
        vo.setExpiresTime(userPasswordProperties.getExpiresTime());
        vo.setRoles(roles);
        vo.setHavePhone(StrUtil.isNotBlank(userInfo.getPhoneNumber()));
        return vo;
    }


    /**
     * 邀请绑定
     */
    private void inviteBinding(String bizType, String bizCode, PlatformRoleCodeEnum roleKey, Long userId, Long siteId, Long channelId) {
        //记载邀请人信息，如果已存在绑定关系，则直接返回
        SohuInviteVo sohuInviteVo = remoteMiddleInviteService.queryByRegUser(userId);
        if (Objects.nonNull(sohuInviteVo)) {
            return;
        }
        List<String> agentRoles = List.of(PlatformRoleCodeEnum.Agent.getCode(), PlatformRoleCodeEnum.CityStationAgent.getCode());
        // 邀请绑定
        Long inviteUserId = null;
        if (StrUtil.equalsAnyIgnoreCase(PlatformRoleCodeEnum.MCN.getCode(), bizType)) {
            inviteUserId = Long.valueOf(bizCode);
            remoteUserService.bindMcnUser(Long.valueOf(bizCode), userId);
        } else if (agentRoles.contains(bizType)) {
            inviteUserId = Long.valueOf(bizCode);
            remoteAdminService.bindAgentUser(Long.valueOf(bizCode), roleKey.getCode(), userId, siteId, bizType, channelId);
        } else if (StrUtil.equalsAnyIgnoreCase(PlatformRoleCodeEnum.Article.getCode(), bizType)) {
//            remoteUserService.saveInviteInfo(bizType, bizCode, userId);
        }
        remoteUserService.updateInviteUserId(userId, inviteUserId);
    }

    /**
     * 登录校验
     */
    protected final void checkLogin(LoginType loginType, String username, Supplier<Boolean> supplier) {
        String errorKey = CacheConstants.PWD_ERR_CNT_KEY + username;
        String loginFail = Constants.LOGIN_FAIL;
        Integer maxRetryCount = userPasswordProperties.getMaxRetryCount();
        Integer lockTime = userPasswordProperties.getLockTime();

        // 获取用户登录错误次数(可自定义限制策略 例如: key + username + ip)
        Integer errorNumber = RedisUtils.getCacheObject(errorKey);
        // 锁定时间内登录 则踢出
        if (ObjectUtil.isNotNull(errorNumber) && errorNumber.equals(maxRetryCount)) {
            recordLogininfor(username, loginFail, MessageUtils.message(loginType.getRetryLimitExceed(), maxRetryCount, lockTime));
            throw new UserException(loginType.getRetryLimitExceed(), maxRetryCount, lockTime);
        }

        if (supplier.get()) {
            // 是否第一次
            errorNumber = ObjectUtil.isNull(errorNumber) ? 1 : errorNumber + 1;
            // 达到规定错误次数 则锁定登录
            if (errorNumber.equals(maxRetryCount)) {
                RedisUtils.setCacheObject(errorKey, errorNumber, Duration.ofMinutes(lockTime));
                recordLogininfor(username, loginFail, MessageUtils.message(loginType.getRetryLimitExceed(), maxRetryCount, lockTime));
                throw new UserException(loginType.getRetryLimitExceed(), maxRetryCount, lockTime);
            } else {
                // 未达到规定错误次数 则递增
                RedisUtils.setCacheObject(errorKey, errorNumber);
                recordLogininfor(username, loginFail, MessageUtils.message(loginType.getRetryLimitCount(), errorNumber));
                String failMsg = "账号或密码错误，还可以再尝试" + (5 - errorNumber) + "次，请重试。";
                throw new UserException(failMsg);
            }
        }
        // 登录成功 清空错误次数
        RedisUtils.deleteObject(errorKey);
    }

    /**
     * 注册之前的验证码校验
     *
     * @param phonenumber 手机号
     * @param smsCode     验证码
     */
    protected final void validateSmsCodeBeforeSave(String phonenumber, String smsCode) {
        log.info("validateSmsCodeBeforeSave , activeProfile:{}", activeProfile);
        if (StrUtil.isNotBlank(activeProfile) && !StrUtil.equalsAnyIgnoreCase(PROFILE_PROD, activeProfile)) {
            return;
        }
        String code = RedisUtils.getCacheObject(CacheConstants.CAPTCHA_CODE_KEY + phonenumber);
        if (StrUtil.isNotBlank(code) && StrUtil.equalsAnyIgnoreCase(code, smsCode)) {
            return;
        }
        log.info("缓存的验证码:{},用户输入的验证码：{}", code, smsCode);
        String serviceSmsCode = remoteResourceSendRecordService.getSmsCode(phonenumber, "login");
        if (StrUtil.isBlankIfStr(serviceSmsCode) || !StrUtil.equalsAnyIgnoreCase(serviceSmsCode, smsCode)) {
            recordLogininfor(phonenumber, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error"));
            throw new RuntimeException("验证码输入有误");
        }
//        if (StringUtils.isBlank(code)) {
//            recordLogininfor(phonenumber, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire"));
//            throw new CaptchaExpireException();
//        }
//        if (!code.equals(smsCode)) {
//            recordLogininfor(phonenumber, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error"));
//            throw new RuntimeException("验证码输入有误");
//        }
    }

    /**
     * 手机号注册
     *
     * @param phoneNumber 手机号
     * @return 结果
     */
    protected LoginUser createUserByPhoneIfAbsent(String phoneNumber, PlatformRoleCodeEnum roleKey, String email) {
        //return this.createUserByPhoneIfAbsent(phoneNumber, roleKey, email, DEFAULT_PWD);
        SmsCodeLoginBo bo = new SmsCodeLoginBo();
        bo.setPhoneNumber(phoneNumber);
        bo.setEmail(email);
        bo.setRoleKey(roleKey);
        return this.createUserByPhoneIfAbsent(bo);
    }

    /**
     * 手机号注册
     *
     * @param phoneNumber 手机号
     * @return 结果
     */
    protected LoginUser createUserByPhoneIfAbsent(String phoneNumber, PlatformRoleCodeEnum roleKey, String email, String password) {
        // 通过手机号查找用户
        LoginUser userInfo = remoteUserService.getUserInfoByPhone(phoneNumber);
        // 如果用户不存在就走注册流程
        if (userInfo != null) {
            if (StrUtil.isNotBlank(email)) {
                // 更新邮箱
                remoteUserService.updateEmail(phoneNumber, email);
            }
            // 给用户赋予角色
            this.giveRole(roleKey, userInfo.getUserId());
            return userInfo;
        }

//        // 走注册流程
//        RegisterBody registerBody = new RegisterBody();
//        String name = RandomUtils.genUserName();
//        registerBody.setUserName(name);
//        registerBody.setPhoneNumber(phoneNumber);
//        //registerBody.setPassword(DEFAULT_PWD);
//        if(StrUtil.isEmpty(password)){
//            registerBody.setPassword(DEFAULT_PWD);
//        }else {
//            registerBody.setPassword(password);
//        }
//        registerBody.setUserType(UserType.APP_USER.getUserType());
//        registerBody.setEmail(email);
//        Long userId = this.register(registerBody);
//        userInfo = new LoginUser();
//        userInfo.setUserType(registerBody.getUserType());
//        userInfo.setUserId(userId);
//        // 给予新注册用户 创作者服务中心 角色
//        if (roleKey == null) {
//            roleKey = PlatformRoleCodeEnum.Article;
//        }
//        // 给予新注册用户角色
//        this.giveRole(roleKey, userId);
//        return userInfo;
        return this.registerUserByPhoneIfAbsent(phoneNumber, roleKey, email, password);
    }

    /**
     * 手机号注册
     */
    protected LoginUser createUserByPhoneIfAbsent(SmsCodeLoginBo req) {
        // 通过手机号查找用户
        LoginUser userInfo = remoteUserService.getUserInfoByPhone(req.getPhoneNumber());
        // 如果用户不存在就走注册流程
        if (userInfo != null) {
            if (StrUtil.isNotBlank(req.getEmail())) {
                // 更新邮箱
                remoteUserService.updateEmail(req.getPhoneNumber(), req.getEmail());
            }
            // 给用户赋予角色
            this.giveRole(req.getRoleKey(), userInfo.getUserId());
            return userInfo;
        }

        return this.registerUserByPhoneIfAbsent(req);
    }

    /**
     * 手机号注册
     */
    protected LoginUser registerUserByPhoneIfAbsent(SmsCodeLoginBo req) {
//        if (!remoteSohuDownloadRegisterReportService.canRegister(req.getDeviceCode())) {
//            throw new ServiceException("当前设备号注册已达上限，请更换设备重新注册");
//        }
        // 走注册流程
        RegisterBody registerBody = new RegisterBody();
        String name = RandomUtils.genUserName();
        registerBody.setUserName(name);
        registerBody.setPhoneNumber(req.getPhoneNumber());
        if (StrUtil.isNotBlank(req.getPassword())) {
            String reqPassword = SecretUtil.desEncrypt(req.getPassword(), SecretUtil.KEY);
            registerBody.setPassword(reqPassword);
        }
        registerBody.setUserType(UserType.APP_USER.getUserType());
        registerBody.setEmail(req.getEmail());
        Long userId = this.register(registerBody);
        // 给予新注册用户 创作者服务中心 角色
        PlatformRoleCodeEnum roleKey = req.getRoleKey();
        if (roleKey == null) {
            roleKey = PlatformRoleCodeEnum.Article;
        }
        // 给予新注册用户角色
        this.giveRole(roleKey, userId);
        // 记录注册用户信息
        recordRegisterUserInfo(req, userId);
        return this.remoteUserService.selectById(userId);
    }

    /**
     * 记录用户注册信息
     *
     * @param req 注册信息
     */
    protected void recordRegisterUserInfo(SmsCodeLoginBo req, Long userId) {
        HttpServletRequest request = ServletUtils.getRequest();
        String ip = ServletUtils.getClientIP(request);
        String address = AddressUtils.getRealAddressByIP(ip);

        SohuDownloadRegisterReportBo bo = new SohuDownloadRegisterReportBo();
        bo.setDeviceCode(Optional.ofNullable(req.getDeviceCode()).orElse("unknown"));
        bo.setDeviceBrand(Optional.ofNullable(req.getDeviceBrand()).orElse("other"));
        bo.setDevicePlatform(Optional.ofNullable(req.getDevicePlatform()).orElse("Android"));
        bo.setAppMarket(Optional.ofNullable(req.getAppMarket()).orElse(""));
        bo.setDownloadChannel(Optional.ofNullable(req.getDownloadChannel()).orElse(""));
        bo.setRegisterChannel(Optional.ofNullable(req.getRegisterChannel()).orElse(""));
        bo.setRegisterTime(new Date());
        bo.setRegisterAccount(Objects.nonNull(req.getPhoneNumber()) ? req.getPhoneNumber() : req.getEmail());
        bo.setRegisterAddress(address);
        bo.setRegisterIp(ip);
        bo.setUserId(userId);

        remoteSohuDownloadRegisterReportService.saveRegisterUserInfo(bo);
    }

    /**
     * 手机号注册
     *
     * @param phoneNumber 手机号
     * @return 结果
     */
    protected LoginUser registerUserByPhoneIfAbsent(String phoneNumber, PlatformRoleCodeEnum roleKey, String email, String password) {
//        // 通过手机号查找用户
//        LoginUser userInfo = remoteUserService.getUserInfoByPhone(phoneNumber);
//        // 如果用户不存在就走注册流程
//        if (userInfo != null) {
//            if (StrUtil.isNotBlank(email)) {
//                // 更新邮箱
//                remoteUserService.updateEmail(phoneNumber, email);
//            }
//            // 给用户赋予角色
//            this.giveRole(roleKey, userInfo.getUserId());
//            return userInfo;
//        }

        // 走注册流程
        RegisterBody registerBody = new RegisterBody();
        String name = RandomUtils.genUserName();
        registerBody.setUserName(name);
        registerBody.setPhoneNumber(phoneNumber);
        //registerBody.setPassword(DEFAULT_PWD);
        if (StrUtil.isEmpty(password)) {
            //registerBody.setPassword(DEFAULT_PWD);
        } else {
            registerBody.setPassword(password);
        }
        registerBody.setUserType(UserType.APP_USER.getUserType());
        registerBody.setEmail(email);
        Long userId = this.register(registerBody);
//        LoginUser userInfo = new LoginUser();
//        userInfo.setUserType(registerBody.getUserType());
//        userInfo.setUserId(userId);
        // 给予新注册用户 创作者服务中心 角色
        if (roleKey == null) {
            roleKey = PlatformRoleCodeEnum.Article;
        }
        // 给予新注册用户角色
        this.giveRole(roleKey, userId);
//        return userInfo;
        return this.remoteUserService.selectById(userId);
    }


    /**
     * 注册
     */
    public Long register(RegisterBody registerBody) {
        String username = RandomUtils.genUserName();
        String password = registerBody.getPassword();
        // 校验用户类型是否存在
        String userType = UserType.getUserType(registerBody.getUserType()).getUserType();

        // 注册用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setNickName(username);
        if (StringUtils.isNotBlank(password)) {
            sysUser.setPassword(BCrypt.hashpw(password));
        }
        sysUser.setUserType(userType);
        String regStr = null;
        if (StrUtil.isNotBlank(registerBody.getPhoneNumber()) && ValidatorUtil.isMobile(registerBody.getPhoneNumber())) {
            sysUser.setPhoneNumber(registerBody.getPhoneNumber());
            regStr = registerBody.getPhoneNumber();
            // 通过手机号查找用户
            LoginUser exist = remoteUserService.getUserInfoByPhone(regStr);
            if (Objects.nonNull(exist)) {
                throw new UserException("user.exist");
            }
        }
        if (StrUtil.isNotBlank(registerBody.getEmail()) && ValidatorUtil.isEmail(registerBody.getEmail())) {
            sysUser.setEmail(registerBody.getEmail());
            regStr = registerBody.getEmail();
            // 通过手机号查找用户
            LoginUser exist = remoteUserService.getUserInfoByMail(regStr);
            if (Objects.nonNull(exist)) {
                throw new UserException("user.exist");
            }
        }
        String existCode = registerBody.getCode();
        if (!StrUtil.equalsAnyIgnoreCase(existCode, Constants.DEFAULT_CODE)) {
            // 校验验证码对不对
            String code = RedisUtils.getCacheObject(CacheConstants.CAPTCHA_CODE_KEY + regStr);
            if (StringUtils.isBlank(code)) {
                throw new CaptchaExpireException();
            }
            if (!StrUtil.equals(code, registerBody.getCode())) {
                throw new UserException("code.error");
            }
//            // 验证码只能使用一次
//            if (StrUtil.equals(code, registerBody.getCode())){
//                RedisUtils.deleteObject(CacheConstants.CAPTCHA_CODE_KEY + regStr);
//            }
        }

        sysUser.setAvatar(Constants.DEFAULT_AVATAR);
        Long userId = remoteUserService.registerUserInfo(sysUser);
        if (userId == null || userId <= 0L) {
            throw new UserException("user.register.error");
        }
        recordLogininfor(username, userId, Constants.REGISTER, MessageUtils.message("user.register.success"));
        // 注册事件埋点
        sysUser.setUserId(userId);
        buildRegisterEventReport(sysUser);
        return userId;
    }

    /**
     * 构建注册事件埋点
     *
     * @param user
     */
    private void buildRegisterEventReport(SysUser user) {
        LoginTypeEnum type = getType();
        SohuEventReportBo bo = new SohuEventReportBo(
                RegisterReportEnum.getCode(RegisterReportEnum.ZCDL.getType()),
                RegisterReportEnum.ZCDL.getDesc(),
                ActionTypeEnum.Register.name(),
                JSONUtil.toJsonStr(new SohuCommonEventReportVo().setNewUser(true).setLoginType(type.getType()).setLoginTypeName(type.getDesc())),
                RegisterReportEnum.ZCDL.getType(),
                user.getUserId());
        remoteMiddleEventReportService.getEventId(bo);
        //异步新增埋点数据
        CompletableFuture.runAsync(() -> {
            SohuUserBehaviorRecordPointBo pointBo = new SohuUserBehaviorRecordPointBo();
            pointBo.setUserId(user.getUserId());
            pointBo.setUserName(user.getNickName());
            pointBo.setEventSign("user_login");
            pointBo.setEventName("登录app");
            pointBo.setBusinessType(BehaviorBusinessTypeEnum.Other.getCode());
            pointBo.setOperaType(OperaTypeEnum.LOGIN.getOperaType());
            pointBo.setOperaSource(Constants.ZERO);
            pointBo.setSourceType("HSS");
            pointBo.setRequestId(UUID.randomUUID().toString());
            remoteMiddleUserBehaviorRecordService.addList(Arrays.asList(pointBo));
        }, asyncConfig.getAsyncExecutor());
    }

    /**
     * 邮箱注册
     *
     * @param email 邮箱
     * @return
     */
    protected LoginUser createUserByEmailIfAbsent(String email, PlatformRoleCodeEnum roleKey) {
        //return this.createUserByEmailIfAbsent(email, roleKey, DEFAULT_PWD);
//        SmsCodeLoginBo bo = new SmsCodeLoginBo();
//        bo.setEmail(email);
//        bo.setRoleKey(roleKey);
//        return this.createUserByEmailIfAbsent(email, roleKey, null);
        return null;
    }

    /**
     * 邮箱注册
     *
     * @param req EmailCodeLoginBo
     * @return
     */
    protected LoginUser createUserByEmailIfAbsent(EmailCodeLoginBo req) {
        // 通过邮箱查找用户
        LoginUser userInfo = remoteUserService.getUserInfoByMail(req.getEmail());
        if (userInfo != null) {
            // 给用户赋予角色
            if (req.getRoleKey() != null) {
                this.giveRole(req.getRoleKey(), userInfo.getUserId());
            }
            return userInfo;
        }
//        // 走注册流程
//        RegisterBody registerBody = new RegisterBody();
//        String name = RandomUtils.genUserName();
//        registerBody.setUserName(name);
//        registerBody.setEmail(email);
//        //registerBody.setPassword(DEFAULT_PWD);
//        if(StrUtil.isEmpty(password)){
//            registerBody.setPassword(DEFAULT_PWD);
//        }else {
//            registerBody.setPassword(password);
//        }
//        registerBody.setUserType(UserType.APP_USER.getUserType());
//        Long userId = this.register(registerBody);
//        userInfo = new LoginUser();
//        userInfo.setUserType(registerBody.getUserType());
//        userInfo.setUserId(userId);
//        // 给予新注册用户角色
//        this.giveRole(roleKey, userId);
//        return userInfo;
        return this.registerUserByEmailIfAbsent(req);
    }

    /**
     * 邮箱注册
     *
     * @param req EmailCodeLoginBo
     * @return
     */
    protected LoginUser registerUserByEmailIfAbsent(EmailCodeLoginBo req) {
//        // 通过邮箱查找用户
//        LoginUser userInfo = remoteUserService.getUserInfoByMail(email);
//        if (userInfo != null) {
//            // 给用户赋予角色
//            if (roleKey != null) {
//                this.giveRole(roleKey, userInfo.getUserId());
//            }
//            return userInfo;
//        }
        // 走注册流程
        RegisterBody registerBody = new RegisterBody();
        String name = RandomUtils.genUserName();
        registerBody.setUserName(name);
        registerBody.setEmail(req.getEmail());
        //registerBody.setPassword(DEFAULT_PWD);
        if (StrUtil.isEmpty(req.getPassword())) {
            //registerBody.setPassword(DEFAULT_PWD);
        } else {
            registerBody.setPassword(req.getPassword());
        }
        registerBody.setUserType(UserType.APP_USER.getUserType());
        Long userId = this.register(registerBody);
//        LoginUser userInfo = new LoginUser();
//        userInfo.setUserType(registerBody.getUserType());
//        userInfo.setUserId(userId);
        // 给予新注册用户角色
        this.giveRole(req.getRoleKey(), userId);
        //return userInfo;
        // 记录注册用户信息
        recordRegisterUserInfo(BeanUtil.copyProperties(req, SmsCodeLoginBo.class), userId);
        return this.remoteUserService.selectById(userId);
    }

    /**
     * 给用户授予角色
     *
     * @param roleKey 角色标识
     * @param userId  用户ID
     */
    private void giveRole(PlatformRoleCodeEnum roleKey, Long userId) {
        // 赋予用户角色列表
        List<String> giveRoleList = new ArrayList<>();
        // 赋予用户平台角色列表
        List<String> givePlatformRoleList = new ArrayList<>();
        // 默认赋予 创作者功能 普通功能
        giveRoleList.add(RoleCodeEnum.Article.getCode());
        giveRoleList.add(RoleCodeEnum.COMMON.getCode());
        // 默认赋予 创作者服务平台角色
        givePlatformRoleList.add(PlatformRoleCodeEnum.Article.getCode());

//        if (roleKey == null || StrUtil.equalsAnyIgnoreCase(roleKey.getCode(), RoleCodeEnum.Article.getCode())) {
//            // 如果角色为空或者是默认角色，则直接返回
//            log.info("给用户授予角色:{}", JSONObject.toJSON(giveRoleList));
//            savePlatformRoles(userId, giveRoleList);
//            saveUserRoles(userId, giveRoleList);
//            return;
//        }

        // 获取当前用户已入驻的角色(不能同时拥有的角色)
        //RoleCodeEnum currentRole = remoteSysRoleService.getCurrentExclusiveRole(userId);
        if (Objects.nonNull(roleKey)) {
            PlatformRoleCodeEnum currentRole = remotePlatformRoleService.getCurrentExclusiveRole(userId);
            if (currentRole != null && !StrUtil.equalsAnyIgnoreCase(roleKey.getCode(), currentRole.getCode())) {
                throw new RuntimeException("您已认证其他角色");
            }
        }

        // 获取当前用户的实名认证信息
        //SohuAccountModel sohuAccountModel = remoteUserService.selectAccountByUserId(userId);
//        SohuAccountVo sohuAccountModel = remoteAccountService.queryByUserIdOfPass(userId);
//        if (sohuAccountModel == null && !isExcludedRole(roleKey)) {
//            giveRoleList.add(roleKey.getCode());
//        } else {
//            assert sohuAccountModel != null;
//            String accountType = sohuAccountModel.getAccountType();
//            if (UserAuthEnum.business.getType().equalsIgnoreCase(accountType)) {
//                handleBusinessAccount(roleKey, giveRoleList);
//            } else if (UserAuthEnum.personal.getType().equalsIgnoreCase(accountType)) {
//                handlePersonalAccount(roleKey, giveRoleList);
//            }
//        }
        log.info("给用户授予功能角色:{}", JSONObject.toJSON(giveRoleList));
        savePlatformRoles(userId, givePlatformRoleList);
        saveUserRoles(userId, giveRoleList);
    }

    /**
     * 是否是排除角色
     *
     * @param roleKey 角色标识
     * @return boolean
     */
    private boolean isExcludedRole(RoleCodeEnum roleKey) {
        return roleKey == RoleCodeEnum.Conversion || roleKey == RoleCodeEnum.Server;
    }

    /**
     * 处理个人账户
     *
     * @param roleKey      角色标识
     * @param giveRoleList 赋予角色列表
     */
    private void handlePersonalAccount(RoleCodeEnum roleKey, List<String> giveRoleList) {
        if (roleKey == RoleCodeEnum.Professor || roleKey == RoleCodeEnum.Project) {
            giveRoleList.add(roleKey.getCode());
        } else {
            throw new RuntimeException("您当前账号已成功进行个人业认证，如需注册需要更换账号进行认证。");
        }
    }

    /**
     * 处理企业账户
     *
     * @param roleKey      角色标识
     * @param giveRoleList 赋予角色列表
     */
    private void handleBusinessAccount(RoleCodeEnum roleKey, List<String> giveRoleList) {
        if (roleKey == RoleCodeEnum.MCN || roleKey == RoleCodeEnum.Agent || roleKey == RoleCodeEnum.Project) {
            giveRoleList.add(roleKey.getCode());
        } else {
            throw new RuntimeException("您当前账号已成功进行企业认证，如需注册需要更换账号进行认证。");
        }
    }

    /**
     * 保存平台角色信息
     *
     * @param userId
     * @param giveRoleList
     */
    private void savePlatformRoles(Long userId, List<String> giveRoleList) {
        List<SysPlatformRoleVo> roleVos = remotePlatformRoleService.listByRoleCodes(giveRoleList);
        if (CollUtil.isNotEmpty(roleVos)) {
            // 保存用户角色关联信息
            remotePlatformRoleService.insertList(roleVos, userId);
        }
    }

    /**
     * 保存用户角色信息
     *
     * @param userId       用户ID
     * @param giveRoleList 赋予角色列表
     */
    private void saveUserRoles(Long userId, List<String> giveRoleList) {
        // 查询用户拥有的角色列表
        List<SysRole> roleDOS = remoteSysRoleService.listByRoleCodes(giveRoleList);
        if (CollUtil.isNotEmpty(roleDOS)) {
            // 保存用户角色关联信息
            remoteSysRoleService.insertList(roleDOS, userId);
        }
    }

    public Boolean codeVerification(CodeVerification loginBody) {
        switch (loginBody.getType()) {
            case "phone":
                return validateSmsCode(loginBody.getKey(), loginBody.getKeyCode());
            case "email":
                return validateEmailCode(loginBody.getKey(), loginBody.getKeyCode());
            default:
                throw new RuntimeException("");
        }
    }

    /**
     * 校验短信验证码
     */
    protected boolean validateSmsCode(String phonenumber, String smsCode) {
        log.info("validateSmsCode , activeProfile:{}", activeProfile);
        if (StrUtil.isNotBlank(activeProfile) && !StrUtil.equalsAnyIgnoreCase(PROFILE_PROD, activeProfile)) {
            return true;
        }
        String code = RedisUtils.getCacheObject(CacheConstants.CAPTCHA_CODE_KEY + phonenumber);
        if (StringUtils.isBlank(code)) {
            recordLogininfor(phonenumber, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire"));
            throw new CaptchaExpireException();
        }
        boolean result = code.equals(smsCode);
        if (result) {
            // 验证码只能使用一次
            RedisUtils.deleteObject(CacheConstants.CAPTCHA_CODE_KEY + phonenumber);
        }
        return result;
    }

    /**
     * 校验邮箱验证码
     */
    protected boolean validateEmailCode(String email, String emailCode) {
        String code = RedisUtils.getCacheObject(CacheConstants.CAPTCHA_CODE_KEY + email);
        if (StringUtils.isBlank(code)) {
            recordLogininfor(email, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire"));
            throw new CaptchaExpireException();
        }
        boolean result = code.equals(emailCode);
        if (result) {
            // 验证码只能使用一次
            RedisUtils.deleteObject(CacheConstants.CAPTCHA_CODE_KEY + email);
        }
        return result;
    }

    protected LoginAuthVO thirdAppLogin(ThirdAppLoginBody loginBody) {
        String phonenumber = loginBody.getMobile();
        String code = loginBody.getCode();
        LoginUser userInfo = null;
        Long userId = null;
        Object cacheObject = RedisUtils.getCacheObject(CacheEnum.SocialLogin.getRegion() + loginBody.getUuid());
        if (loginBody.getSocialType() == wechat_miniapp) {
            userId = loginBody.getUserId();
        } else {
            validateSmsCode(phonenumber, code);
            cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(cacheObject);
            String roleKey = jsonObject.getStr("roleKey");
            log.info("thirdAppLogin roleKey:{}", roleKey);
            SmsCodeLoginBo bo = BeanUtil.copyProperties(loginBody, SmsCodeLoginBo.class);
            bo.setPhoneNumber(phonenumber);
            if (StrUtil.isNotBlank(roleKey) && Objects.nonNull(PlatformRoleCodeEnum.map.get(roleKey.toLowerCase()))) {
                log.info("thirdAppLogin 1");
                userInfo = this.createUserByPhoneIfAbsent(bo);
            } else {
                log.info("thirdAppLogin 2");
                userInfo = this.createUserByPhoneIfAbsent(BeanUtil.copyProperties(loginBody, SmsCodeLoginBo.class));
            }
            userId = userInfo.getUserId();
        }
        if (Objects.isNull(cacheObject)) {
            throw new BaseException("");
        }
        log.info("thirdAppLogin cacheObject:" + cacheObject.toString());
        switch (loginBody.getSocialType()) {
            case wechat_miniapp:
                WxAccessToken toBean = JSONUtil.toBean(cacheObject.toString(), WxAccessToken.class);
                //APP-微信授权绑定用户
                SocialUserDomain socialUser = new SocialUserDomain();
                socialUser.setUserId(userId);
                socialUser.setSource(wechat_miniapp.name());
                socialUser.setUnionId(toBean.getUnionid());
                socialUser.setOpenId(toBean.getOpenid());
                remoteSocialUserService.bindThirdUser(socialUser);
                break;
            case wechat_app:
                WxAccessToken bean = JSONUtil.toBean(cacheObject.toString(), WxAccessToken.class);
                //APP-微信授权绑定用户
                SocialUserDomain socialUserApp = new SocialUserDomain();
                socialUserApp.setUserId(userId);
                socialUserApp.setSource(wechat_app.name());
                socialUserApp.setUnionId(bean.getUnionid());
                socialUserApp.setOpenId(bean.getOpenid());
                remoteSocialUserService.bindThirdUser(socialUserApp);
                break;
            case ios_app:
                cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(cacheObject);
                String sub = jsonObject.getStr("sub");
                //IOS-APP绑定用户
                //APP-微信授权绑定用户
                SocialUserDomain socialUserIos = new SocialUserDomain();
                socialUserIos.setUserId(userId);
                socialUserIos.setSource(ios_app.name());
                socialUserIos.setUnionId(sub);
                remoteSocialUserService.bindThirdUser(socialUserIos);
                break;
            case wechat_qr:
                WxAccessToken wxAccessToken = JSONUtil.toBean(cacheObject.toString(), WxAccessToken.class);
                //APP-微信授权绑定用户
                SocialUserDomain social = new SocialUserDomain();
                social.setUserId(userId);
                social.setSource(wechat_qr.name());
                social.setUnionId(wxAccessToken.getUnionid());
                social.setAccessToken(wxAccessToken.getAccess_token());
                social.setOpenId(wxAccessToken.getOpenid());
                social.setExpireIn(wxAccessToken.getExpires_in());
                remoteSocialUserService.bindThirdUser(social);
                break;
            default:
                throw new RuntimeException("");
        }
        if (loginBody.getSocialType() != wechat_miniapp) {
            // 获取登录token
            userInfo.setExpireTime(userPasswordProperties.getExpiresTime());
            LoginHelper.loginByDevice(userInfo, DeviceType.APP);
            return this.build(userInfo.getUserId(), StpUtil.getTokenValue(), new ArrayList<>(), userInfo);
        }
        return null;
    }

    public void updateUser(LoginUser loginUser) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(loginUser.getUserId());
        sysUser.setLoginIp(ServletUtils.getClientIP());
        sysUser.setLoginDate(DateUtils.getNowDate());
        sysUser.setUpdateBy(StringUtils.getValidString(loginUser.getNickname(), loginUser.getUsername()));
        remoteUserService.updateUser(sysUser);
        log.info("login success, userId: {}, username: {}, loginIp: {}, loginDate: {}",
                sysUser.getUserId(), sysUser.getUserName(), sysUser.getLoginIp(), sysUser.getLoginDate());
    }

}
