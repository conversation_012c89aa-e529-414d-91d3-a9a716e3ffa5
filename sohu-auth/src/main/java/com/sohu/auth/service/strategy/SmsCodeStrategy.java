package com.sohu.auth.service.strategy;

import cn.hutool.core.util.StrUtil;
import com.sohu.auth.enums.LoginTypeEnum;
import com.sohu.auth.form.v2.SmsCodeLoginBo;
import com.sohu.common.core.enums.LoginType;
import com.sohu.common.core.utils.SecretUtil;
import com.sohu.common.satoken.config.LoginUser;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/6/21
 */
@Component
public class SmsCodeStrategy extends AbsLoginStrategy<SmsCodeLoginBo> {
    @Override
    protected LoginTypeEnum getType() {
        return LoginTypeEnum.SMS_CODE;
    }

    @Override
    protected LoginUser prepareLogin(SmsCodeLoginBo req) {
        validateSmsCodeBeforeSave(req.getPhoneNumber(), req.getSmsCode());

        // 通过手机号查找用户
        //LoginUser userInfo = this.createUserByPhoneIfAbsent(req.getPhoneNumber(), req.getRoleKey(), req.getEmail());
        LoginUser userInfo = null;
        if (StrUtil.isEmpty(req.getPassword())) {
            userInfo = this.createUserByPhoneIfAbsent(req);
        } else {
            userInfo = this.createUserByPhoneIfAbsent(req);
        }

        checkLogin(LoginType.SMS, userInfo.getUsername(), () -> !validateSmsCode(req.getPhoneNumber(), req.getSmsCode()));
        return userInfo;
    }

    @Override
    protected LoginUser prepareRegister(SmsCodeLoginBo req) {
        if (StrUtil.isEmpty(req.getPassword())) {
            throw new RuntimeException("密码不能为空");
        }
        validateSmsCodeBeforeSave(req.getPhoneNumber(), req.getSmsCode());
        String reqPassword = SecretUtil.desEncrypt(req.getPassword(), SecretUtil.KEY);
        LoginUser userInfo = this.registerUserByPhoneIfAbsent(req);
        checkLogin(LoginType.SMS, userInfo.getUsername(), () -> !validateSmsCode(req.getPhoneNumber(), req.getSmsCode()));
        return userInfo;
    }
}
