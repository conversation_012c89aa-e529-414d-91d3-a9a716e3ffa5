package com.sohu.auth.service;

import com.sohu.auth.utils.WeixinUtil;
import com.sohu.auth.vo.WxAccessToken;
import com.sohu.auth.vo.WxShareVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * 微信分享实现
 *
 * @Author: leibo
 * @Date: 2025/2/27 12:00
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class WxShareService {

    // 微信appid---微信公众平台
    public static final String appId = "wx136b7ba99b6943a4";
    // 微信AppSecret---微信公众平台
    public static final String appSecret = "3acd8da1eb127355e54b00e67074e47b";

    public WxShareVo getsignature(String url) {
        try {
            WxAccessToken accessToken = WeixinUtil.getAccessToken(appId, appSecret);
            String jsapiTicket = WeixinUtil.getTicket(accessToken.getAccess_token());
            String noncestr = UUID.randomUUID().toString().replace("-", "").substring(0, 16);//随机字符串
            String timestamp = String.valueOf(System.currentTimeMillis()/ 1000);// 时间戳
            String params = "jsapi_ticket=" + jsapiTicket + "&noncestr=" + noncestr + "&timestamp=" + timestamp + "&url=" + url;
            //将字符串进行sha1加密
            String signature = WeixinUtil.getSHA1(params);
            //微信appId
            WxShareVo wxShare = new WxShareVo();
            wxShare.setAppId(appId);
            wxShare.setAccessToken(accessToken.getAccess_token());
            wxShare.setJsapiTicket(jsapiTicket);
            wxShare.setTimestamp(timestamp);
            wxShare.setNonceStr(noncestr);
            wxShare.setSignature(signature);
            log.info("微信分享成功");
            return wxShare;
        } catch (Exception e) {
            log.error("获取微信加密信息" + e.getMessage(), e);
            return null;
        }
    }
}
