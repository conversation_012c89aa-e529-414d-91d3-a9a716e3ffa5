<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.system.mapper.SysPlatformRoleMapper">
    <sql id="selectPlatformRolesVo">
        SELECT pr.*
        FROM sys_platform_role pr
                 INNER JOIN sys_platform_user pu on pu.platform_role_id = pr.id
                 INNER JOIN sys_user u on u.user_id = pu.user_id
        WHERE pr.del_flag = '0' AND u.del_flag = '0'
    </sql>

    <select id="selectListOfEnableByUserId" resultType="com.sohu.system.api.vo.SysPlatformRoleVo">
        <include refid="selectPlatformRolesVo"/>
        AND pr.`status`='ENABLE'
        AND u.user_id = #{userId}
    </select>

    <select id="selectListByUserId" resultType="com.sohu.system.api.vo.SysPlatformRoleVo">
        <include refid="selectPlatformRolesVo"/>
        AND u.user_id = #{userId}
    </select>
</mapper>
