<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.system.mapper.SohuNoticeMapper">

	<resultMap type="com.sohu.system.domain.SohuNotice" id="SohuNoticeResult">
		<result property="id" column="id"/>
		<result property="userId" column="user_id"/>
		<result property="name" column="name"/>
		<result property="image" column="image"/>
		<result property="msg" column="msg"/>
		<result property="info" column="info"/>
		<result property="type" column="type"/>
		<result property="state" column="state"/>
		<result property="onShelfTime" column="on_shelf_time"/>
		<result property="isDel" column="is_del"/>
		<result property="createTime" column="create_time"/>
		<result property="createBy" column="create_by"/>
		<result property="updateTime" column="update_time"/>
		<result property="updateBy" column="update_by"/>
	</resultMap>

</mapper>
