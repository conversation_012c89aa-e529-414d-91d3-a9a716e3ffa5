package com.sohu.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 公告管理对象 sohu_notice
 *
 * <AUTHOR>
 * @date 2024-09-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_notice")
public class SohuNotice extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 公告名称
     */
    private String name;
    /**
     * 公告封面
     */
    private String image;
    /**
     * 公告描述
     */
    private String msg;
    /**
     * 公告详情
     */
    private String info;
    /**
     * 应用生效端(all:总后台 agent:代理商后台 stationAgent:站长后台 project:任务方后台)
     */
    private String type;
    /**
     * 状态（OnShelf：上架，OffShelf：下架）
     */
    private String state;

    /**
     * 上架时间
     */
    private Date onShelfTime;

    /**
     * 是否删除
     */
    private Boolean isDel;

}
