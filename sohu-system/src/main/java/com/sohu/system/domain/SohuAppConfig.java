package com.sohu.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 移动端菜单配置对象 sohu_app_config
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_app_config")
public class SohuAppConfig extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * Icon 图标地址
     */
    private String icon;
    /**
     * 跳转链接
     */
    private String pageUrl;
    /**
     * 涉及类型  1.个人中心 2.导航栏 默认 1
     */
    private Long type;
    /**
     * 排序
     */
    private Long sort;
    /**
     * 是否删除 0.否 1.是
     */
    @TableLogic
    private Integer delFlag;
    /**
     * 是否启用 0.否 1.是 默认1
     */
    private Integer enable;

    /**
     * 是否可编辑 0.否 1.是 默认1
     */
    private Integer isEdit;

    /**
     * 配置附加字段
     */
    private String configExt;
    /**
     * 业务类型 menu菜单 common 通用 ai_task 智能商单  common_img 公共图片
     */
    private String busyType;

}
