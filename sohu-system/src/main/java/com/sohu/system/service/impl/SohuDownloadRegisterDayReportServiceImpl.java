package com.sohu.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.system.api.bo.SohuDownloadRegisterDayReportBo;
import com.sohu.system.api.bo.SohuDownloadRegisterDayReportQueryBo;
import com.sohu.system.api.vo.SohuDownloadRegisterDayReportVo;
import com.sohu.system.domain.SohuDownloadRegisterDayReport;
import com.sohu.system.mapper.SohuDownloadRegisterDayReportMapper;
import com.sohu.system.service.ISohuDownloadRegisterDayReportService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 下载注册(天)统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@RequiredArgsConstructor
@Service
public class SohuDownloadRegisterDayReportServiceImpl implements ISohuDownloadRegisterDayReportService {

    private final SohuDownloadRegisterDayReportMapper baseMapper;

    /**
     * 查询下载注册(天)统计列表
     */
    @Override
    public TableDataInfo<SohuDownloadRegisterDayReportVo> queryPageList(SohuDownloadRegisterDayReportQueryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuDownloadRegisterDayReport> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(SohuDownloadRegisterDayReport::getDate);
        Page<SohuDownloadRegisterDayReportVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询下载注册(天)统计列表
     */
    @Override
    public List<SohuDownloadRegisterDayReportVo> queryList(SohuDownloadRegisterDayReportQueryBo bo) {
        LambdaQueryWrapper<SohuDownloadRegisterDayReport> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(SohuDownloadRegisterDayReport::getDate);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuDownloadRegisterDayReport> buildQueryWrapper(SohuDownloadRegisterDayReportQueryBo bo) {
        LambdaQueryWrapper<SohuDownloadRegisterDayReport> lqw = Wrappers.lambdaQuery();
        lqw.le(StrUtil.isNotBlank(bo.getEndTime()), SohuDownloadRegisterDayReport::getDate, bo.getEndTime());
        lqw.ge(StrUtil.isNotBlank(bo.getStartTime()), SohuDownloadRegisterDayReport::getDate, bo.getStartTime());
        return lqw;
    }

    /**
     * 新增下载注册(天)统计
     */
    @Override
    public Boolean insertByBo(SohuDownloadRegisterDayReportBo bo) {
        SohuDownloadRegisterDayReport add = BeanUtil.toBean(bo, SohuDownloadRegisterDayReport.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改下载注册(天)统计
     */
    @Override
    public Boolean updateByBo(SohuDownloadRegisterDayReportBo bo) {
        SohuDownloadRegisterDayReport update = BeanUtil.toBean(bo, SohuDownloadRegisterDayReport.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuDownloadRegisterDayReport entity) {
    }

    @Override
    public Boolean saveByBo(SohuDownloadRegisterDayReportBo bo) {
        LambdaQueryWrapper<SohuDownloadRegisterDayReport> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuDownloadRegisterDayReport::getDate, bo.getDate());
        lqw.last("limit 1");
        SohuDownloadRegisterDayReport entity = this.baseMapper.selectOne(lqw);
        if (Objects.isNull(entity)) {
            return this.insertByBo(bo);
        } else {
            bo.setId(entity.getId());
            return this.updateByBo(bo);
        }
    }

}
