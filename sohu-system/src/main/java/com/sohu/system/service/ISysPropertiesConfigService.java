package com.sohu.system.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.system.api.bo.SysPropertiesConfigBo;
import com.sohu.system.api.vo.SysPropertiesConfigVo;

import java.util.Collection;
import java.util.List;

/**
 * 基础数据配置Service接口
 *
 * <AUTHOR>
 * @date 2023-08-08
 */
public interface ISysPropertiesConfigService {

    /**
     * 查询基础数据配置
     */
    SysPropertiesConfigVo queryById(String configKey);

    /**
     * 查询基础数据配置列表
     */
    TableDataInfo<SysPropertiesConfigVo> queryPageList(SysPropertiesConfigBo bo, PageQuery pageQuery);

    /**
     * 查询基础数据配置列表
     */
    List<SysPropertiesConfigVo> queryList(SysPropertiesConfigBo bo);

    /**
     * 修改基础数据配置
     */
    Boolean insertByBo(SysPropertiesConfigBo bo);

    /**
     * 修改基础数据配置
     */
    Boolean updateByBo(SysPropertiesConfigBo bo);

    /**
     * 校验并批量删除基础数据配置信息
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

}
