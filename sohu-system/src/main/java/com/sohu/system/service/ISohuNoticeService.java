package com.sohu.system.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.system.api.bo.SohuNoticeBo;
import com.sohu.system.api.bo.SohuNoticeInsertBo;
import com.sohu.system.api.bo.SohuNoticeUpdateBo;
import com.sohu.system.api.vo.SohuNoticeVo;

import java.util.Collection;
import java.util.List;

/**
 * 公告管理Service接口
 *
 * <AUTHOR>
 * @date 2024-09-02
 */
public interface ISohuNoticeService {

    /**
     * 查询公告管理
     */
    SohuNoticeVo queryById(Long id);

    /**
     * 查询公告管理列表
     */
    TableDataInfo<SohuNoticeVo> queryPageList(SohuNoticeBo bo, PageQuery pageQuery);

    /**
     * 查询公告管理列表
     */
    List<SohuNoticeVo> queryList(SohuNoticeBo bo);

    /**
     * 新增公告管理
     */
    Boolean insertByBo(SohuNoticeInsertBo bo);

    /**
     * 修改公告管理
     */
    Boolean updateByBo(SohuNoticeUpdateBo bo);

    /**
     * 校验并批量删除公告管理信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量上/下架
     */
    Boolean onOrOffShelf(SohuNoticeBo bo);
}
