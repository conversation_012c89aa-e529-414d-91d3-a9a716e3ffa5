package com.sohu.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.system.api.bo.SysPropertiesConfigBo;
import com.sohu.system.api.vo.SysPropertiesConfigVo;
import com.sohu.system.service.ISysPropertiesConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 基础数据配置控制器
 * 前端访问路由地址为:/system/sysPropertiesConfig
 *
 * <AUTHOR>
 * @date 2023-08-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/sysPropertiesConfig")
public class SysPropertiesConfigController extends BaseController {

    private final ISysPropertiesConfigService iSysPropertiesConfigService;

    /**
     * 查询基础数据配置列表
     */
    @SaCheckPermission("system:sysPropertiesConfig:list")
    @GetMapping("/page")
    public TableDataInfo<SysPropertiesConfigVo> page(SysPropertiesConfigBo bo, PageQuery pageQuery) {
        return iSysPropertiesConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出基础数据配置列表
     */
    @SaCheckPermission("system:sysPropertiesConfig:export")
    @Log(title = "基础数据配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysPropertiesConfigBo bo, HttpServletResponse response) {
        List<SysPropertiesConfigVo> list = iSysPropertiesConfigService.queryList(bo);
        ExcelUtil.exportExcel(list, "基础数据配置", SysPropertiesConfigVo.class, response);
    }

    /**
     * 获取基础数据配置详细信息
     *
     * @param configKey 主键
     */
    @SaCheckPermission("system:sysPropertiesConfig:query")
    @GetMapping("/{configKey}")
    public R<SysPropertiesConfigVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable String configKey) {
        return R.ok(iSysPropertiesConfigService.queryById(configKey));
    }

    /**
     * 新增基础数据配置
     */
    @SaCheckPermission("system:sysPropertiesConfig:add")
    @Log(title = "基础数据配置", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SysPropertiesConfigBo bo) {
        return toAjax(iSysPropertiesConfigService.insertByBo(bo));
    }

    /**
     * 修改基础数据配置
     */
    @SaCheckPermission("system:sysPropertiesConfig:edit")
    @Log(title = "基础数据配置", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SysPropertiesConfigBo bo) {
        bo.setUpdateTime(new Date());
        return toAjax(iSysPropertiesConfigService.updateByBo(bo));
    }

    /**
     * 删除基础数据配置
     *
     * @param configKeys 主键串
     */
    @SaCheckPermission("system:sysPropertiesConfig:remove")
    @Log(title = "基础数据配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{configKeys}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable String[] configKeys) {
        return toAjax(iSysPropertiesConfigService.deleteWithValidByIds(Arrays.asList(configKeys), true));
    }

    /**
     * 查询配置项
     */
    @SaCheckPermission("system:sysPropertiesConfig:list")
    @GetMapping("/list")
    public R<List<SysPropertiesConfigVo>> list(SysPropertiesConfigBo bo) {
        return R.ok(iSysPropertiesConfigService.queryList(bo));
    }

}
