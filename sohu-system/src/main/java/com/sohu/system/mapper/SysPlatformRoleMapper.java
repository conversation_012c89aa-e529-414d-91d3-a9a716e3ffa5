package com.sohu.system.mapper;

import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.system.api.vo.SysPlatformRoleVo;
import com.sohu.system.api.vo.SysRoleVo;
import com.sohu.system.domain.SysPlatformRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 平台角色Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-20
 */
public interface SysPlatformRoleMapper extends BaseMapperPlus<SysPlatformRoleMapper, SysPlatformRole, SysPlatformRoleVo> {

    /**
     * 根据用户id获取拥有的平台角色
     * @param userId
     * @return
     */
    List<SysPlatformRoleVo> selectListOfEnableByUserId(@Param("userId") Long userId);

    /**
     * 根据用户id获取拥有的平台角色
     * @param userId
     * @return
     */
    List<SysPlatformRoleVo> selectListByUserId(@Param("userId") Long userId);

}
