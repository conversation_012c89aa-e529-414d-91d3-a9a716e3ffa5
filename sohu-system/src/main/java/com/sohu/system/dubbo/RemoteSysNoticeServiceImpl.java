package com.sohu.system.dubbo;

import com.sohu.system.api.RemoteSysNoticeService;
import com.sohu.system.service.ISysNoticeService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Date;

@RequiredArgsConstructor
@Service
@DubboService
public class RemoteSysNoticeServiceImpl implements RemoteSysNoticeService {

    private final ISysNoticeService sysNoticeService;

    @Override
    public Boolean sendNotice(Long receUserId, String noticeRole, Date noticeDate) {
        return sysNoticeService.sendNotice(receUserId, noticeRole, noticeDate);
    }

    @Override
    public void sendUserIncomeStatisticsNotice() {
        sysNoticeService.sendUserIncomeStatisticsNotice();
    }

}
