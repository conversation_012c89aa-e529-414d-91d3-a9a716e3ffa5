package com.sohu.system.dubbo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sohu.common.core.constant.Constants;
import com.sohu.system.api.RemoteSysPropertiesConfigService;
import com.sohu.system.domain.SysPropertiesConfig;
import com.sohu.system.mapper.SysPropertiesConfigMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@Service
@DubboService
public class RemoteSysPropertiesConfigServiceImpl implements RemoteSysPropertiesConfigService {

    private final SysPropertiesConfigMapper sysPropertiesConfigMapper;


    @Override
    public Map<String, Boolean> countryConfigMap(Long countrySiteId) {
        LambdaQueryWrapper<SysPropertiesConfig> query = new LambdaQueryWrapper<>();
        query.eq(SysPropertiesConfig::getBusType, "AUDIT_COUNTRY");
        query.eq(SysPropertiesConfig::getSiteId, countrySiteId);
        List<SysPropertiesConfig> sysPropertiesConfigs = sysPropertiesConfigMapper.selectList(query);
        Map<String, Boolean> map = new HashMap<>();
        if (CollUtil.isEmpty(sysPropertiesConfigs)) {
            return null;
        }
        for (SysPropertiesConfig sysPropertiesConfig : sysPropertiesConfigs) {
            map.put(sysPropertiesConfig.getConfigKey(), StrUtil.equalsAnyIgnoreCase(Constants.TRUE, sysPropertiesConfig.getConfigValue()) ? Boolean.TRUE : Boolean.FALSE);
        }
        return map;
    }

    @Override
    public Map<String, Boolean> sysConfigMap() {
        LambdaQueryWrapper<SysPropertiesConfig> query = new LambdaQueryWrapper<>();
        query.eq(SysPropertiesConfig::getBusType, "AUDIT");
        query.eq(SysPropertiesConfig::getSiteId, 0L);
        List<SysPropertiesConfig> sysPropertiesConfigs = sysPropertiesConfigMapper.selectList(query);
        Map<String, Boolean> map = new HashMap<>();
        if (CollUtil.isEmpty(sysPropertiesConfigs)) {
            return map;
        }
        for (SysPropertiesConfig sysPropertiesConfig : sysPropertiesConfigs) {
            map.put(sysPropertiesConfig.getConfigKey(), StrUtil.equalsAnyIgnoreCase(Constants.TRUE, sysPropertiesConfig.getConfigValue()) ? Boolean.TRUE : Boolean.FALSE);
        }
        return map;
    }

}
