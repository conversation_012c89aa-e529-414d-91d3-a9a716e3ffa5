package com.sohu.resource.controller;


import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.QueryGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.encrypt.annotation.SkipApiEncrypt;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.resource.domain.bo.SysOssBo;
import com.sohu.resource.domain.vo.SysOssVo;
import com.sohu.resource.service.ISysOssService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.*;

/**
 * 文件上传 控制层
 *
 * <AUTHOR> Li
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/oss")
@Slf4j
public class SysOssController extends BaseController {

    private final ISysOssService iSysOssService;

    /**
     * 查询OSS对象存储列表
     */
    @SaCheckPermission("system:oss:list")
    @GetMapping("/list")
    public TableDataInfo<SysOssVo> list(@Validated(QueryGroup.class) SysOssBo bo, PageQuery pageQuery) {
//        boolean admin = LoginHelper.isAdmin();
//        if (!admin) {
//            throw new ServiceException("无权限查看");
//        }
        return iSysOssService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询OSS对象基于id串
     *
     * @param ossIds OSS对象ID串
     */
    @SaCheckPermission("system:oss:list")
    @GetMapping("/listByIds/{ossIds}")
    public R<List<SysOssVo>> listByIds(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ossIds) {
//        boolean admin = LoginHelper.isAdmin();
//        if (!admin) {
//            throw new ServiceException("无权限查看");
//        }
        List<SysOssVo> list = iSysOssService.listByIds(Arrays.asList(ossIds));
        return R.ok(list);
    }

    /**
     * 上传OSS对象存储-单个上传
     *
     * @param file    文件
     * @param quality 压缩率，值区间1到100，默认50
     */
    @SaCheckPermission("system:oss:upload")
    @Log(title = "OSS对象存储", businessType = BusinessType.INSERT)
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @SkipApiEncrypt
    public R<Map<String, String>> upload(@RequestPart("file") MultipartFile file,
                                         @RequestParam(name = "quality", required = false, defaultValue = "50") Integer quality) {
        if (ObjectUtil.isNull(file)) {
            return R.fail("上传文件不能为空");
        }
        // 文件大小
        String fileSize = FileUtils.byteCountToDisplaySize(file.getSize());
        try {
            long start = System.currentTimeMillis();
            SysOssVo oss = iSysOssService.upload(file, quality);
            Map<String, String> map = new HashMap<>();
            map.put("url", oss.getUrl());
            map.put("fileName", oss.getOriginalName());
            map.put("ossId", oss.getOssId().toString());
            map.put("fileSize", fileSize);
            if (oss.getVideoCover() != null) {
                map.put("videoCover", oss.getVideoCover());
            }
            log.info("V1版本大文件上传,耗时: {} ms", (System.currentTimeMillis() - start));
            return R.ok(map);
        } catch (IOException e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 上传OSS对象存储-批量上传
     *
     * @param fileList 文件列表
     * @param quality  压缩率，值区间1到100，默认50
     */
    @Log(title = "OSS批量上传对象存储", businessType = BusinessType.INSERT)
    @PostMapping(value = "/batch/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @SkipApiEncrypt
    public R<List<Map<String, String>>> batchUpload(@RequestPart("fileList") MultipartFile[] fileList,
                                                    @RequestParam(name = "quality", required = false, defaultValue = "50") Integer quality) {
        if (fileList == null || fileList.length == 0) {
            return R.fail("上传文件不能为空");
        }
        List<Map<String, String>> list = new ArrayList<>();
        for (MultipartFile file : fileList) {
            // 文件大小
            String fileSize = FileUtils.byteCountToDisplaySize(file.getSize());
            SysOssVo oss = null;
            try {
                oss = iSysOssService.upload(file, quality);
                Map<String, String> map = new HashMap<>();
                map.put("url", oss.getUrl());
                map.put("fileName", oss.getOriginalName());
                map.put("ossId", oss.getOssId().toString());
                map.put("fileSize", fileSize);
                if (oss.getVideoCover() != null) {
                    map.put("videoCover", oss.getVideoCover());
                }
                list.add(map);
            } catch (IOException e) {

            }
        }
        return R.ok(list);

    }

    /**
     * 下载OSS对象存储
     *
     * @param pathUrl OSS对象地址
     */
    //@SaCheckPermission("system:oss:download")
    @GetMapping("/download/url")
    @SkipApiEncrypt
    public void downloadUrl(@RequestParam String pathUrl, HttpServletResponse response) throws IOException {
        iSysOssService.download(pathUrl, response);
    }

    /**
     * 下载OSS对象存储
     *
     * @param ossId OSS对象ID
     */
    //@SaCheckPermission("system:oss:download")
    @GetMapping("/download/{ossId}")
    @SkipApiEncrypt
    public void download(@PathVariable Long ossId, HttpServletResponse response) throws IOException {
        iSysOssService.download(ossId, response);
    }

    /**
     * 删除OSS对象存储
     *
     * @param ossIds OSS对象ID串
     */
    @SaCheckPermission("system:oss:remove")
    @Log(title = "OSS对象存储", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ossIds}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ossIds) {
//        boolean admin = LoginHelper.isAdmin();
//        if (!admin) {
//            throw new ServiceException("无权限查看");
//        }
        return toAjax(iSysOssService.deleteWithValidByIds(Arrays.asList(ossIds), true));
    }

    /**
     * 列举S3文件列表
     *
     * @param bucketName 桶名称
     * @param prefix     文件前缀
     */
    @GetMapping("/file/list")
    public R list(@RequestParam String bucketName, @RequestParam(required = false) String prefix) throws IOException {
//        boolean admin = LoginHelper.isAdmin();
//        if (!admin) {
//            throw new ServiceException("无权限查看");
//        }
        return R.ok(iSysOssService.listOss(bucketName, prefix));
    }

}
