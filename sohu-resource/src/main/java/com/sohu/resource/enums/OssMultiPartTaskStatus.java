package com.sohu.resource.enums;

import com.sohu.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/4/22
 */
@Getter
@AllArgsConstructor
public enum OssMultiPartTaskStatus {


    UNDERWAY("UNDERWAY", "进行中"),

    COMPLETE("COMPLETE", "完成"),

    FAIL("FAIL", "失败"),

    ABORT("ABORT", "取消"),

    ;

    private final String code;
    private final String msg;

    public static OssMultiPartTaskStatus findByCode(String name){
        return Stream.of(OssMultiPartTaskStatus.values())
                .filter(status -> StringUtils.equals(name, status.code))
                .findFirst()
                .orElse(null);
    }
}
