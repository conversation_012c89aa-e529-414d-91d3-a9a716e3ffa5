package com.sohu.resource.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 短信、邮箱发送业务类型
 */
@Getter
@AllArgsConstructor
public enum SmsSendBizType {
    /**
     * 注册
     */
    REGISTER("register", "注册"),
    /**
     * 登录
     */
    LOGIN("login", "登录"),
    /**
     * 修改密码
     */
    UPDATEPWD("updatePwd", "修改密码"),
    /**
     * 修改手机号
     */
    UPDATEPHONE("updatePhone", "修改手机号"),
    /**
     * 账号注销
     */
    LOGOUT("logout", "账号注销"),
    /**
     * 角色入住
     */
    ENTRY("entry", "角色入驻"),
    /**
     * 店铺入驻
     */
    STOREENTRY("storeEntry", "店铺入驻"),
    /**
     * 代理商
     */
    AGENT("agent", "代理商"),
    /**
     * 拆单方入驻
     */
    RESOLVE("resolve", "拆单方入驻"),
    /**
     * 查看个人隐私
     */
    PRIVACY("privacy", "查看个人隐私"),
    /**
     * 绑定邮箱
     */
    BINDEMAIL("bindEmail", "绑定邮箱"),
    /**
     * 修改邮箱
     */
    UPDATEEMAIL("updateEmail", "修改邮箱"),
    /**
     * 绑定手机号
     */
    BINDPHONE("bindPhone", "绑定手机号"),
    /**
     * 找回密码
     */
    RETRIEVEPWD("retrievePwd", "找回密码"),
    /**
     * 转移达人
     */
    TRANSFER("transfer", "转移达人"),
    /**
     * 修改支付密码
     */
    UPDATEPAYPWD("updatePayPwd", "修改支付密码"),
    /**
     * 设置支付密码
     */
    SETPAYPWD("setPayPwd", "设置支付密码");

    /**
     * 编码
     */
    private final String code;
    /**
     * 描述
     */
    private final String msg;

}
