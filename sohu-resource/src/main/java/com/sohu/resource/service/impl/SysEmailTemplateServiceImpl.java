package com.sohu.resource.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.resource.domain.EmailTemplate;
import com.sohu.resource.mapper.SysEmailTemplateMapper;
import com.sohu.resource.service.ISysEmailTemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * SmsTemplateServiceImpl 接口实现
 */
@RequiredArgsConstructor
@Service
public class SysEmailTemplateServiceImpl implements ISysEmailTemplateService {

    private final SysEmailTemplateMapper baseMapper;

    /**
     * 获取详情
     * @param id 模板id
     * @return SmsTemplate
     */
    @Override
    public EmailTemplate getDetail(Long id) {
        EmailTemplate emailTemplate = baseMapper.selectById(id);
        if (ObjectUtil.isNull(emailTemplate)) {
            throw new ServiceException("Email template does not exist");
        }
        return emailTemplate;
    }
}

