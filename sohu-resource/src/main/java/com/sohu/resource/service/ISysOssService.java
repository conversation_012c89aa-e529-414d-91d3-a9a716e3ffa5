package com.sohu.resource.service;

import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.resource.domain.bo.SysOssBo;
import com.sohu.resource.domain.vo.SysOssVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

/**
 * 文件上传 服务层
 *
 * <AUTHOR> Li
 */
public interface ISysOssService {

    TableDataInfo<SysOssVo> queryPageList(SysOssBo sysOss, PageQuery pageQuery);

    List<SysOssVo> listByIds(Collection<Long> ossIds);

    String selectUrlByIds(String ossIds);

    SysOssVo getById(Long ossId);

    SysOssVo upload(MultipartFile file, Integer quality) throws IOException;

    SysOssVo upload(File file);

    Boolean insertByBo(SysOssBo bo);

    void download(Long ossId, HttpServletResponse response) throws IOException;

    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 列举S3文件列表
     *
     * @param bucketName 桶名称
     * @param prefix     文件前缀
     */
    List<S3ObjectSummary> listOss(String bucketName, String prefix);

    /**
     * s3文件创建预签名处理
     *
     * @param originUrl s3文件地址
     * @param day       有效期天数
     * @return 返回签名好的url地址
     */
    String createPreSignedGetUrl(String originUrl, Long day);

    /**
     * 下载文件
     *
     * @param pathUrl
     * @param response
     */
    void download(String pathUrl, HttpServletResponse response) throws IOException;
}
