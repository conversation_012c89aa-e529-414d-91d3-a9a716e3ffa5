package com.sohu.resource.domain.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 极光推送标签业务对象
 *
 * <AUTHOR>
 * @date 2024-04-24
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuJiguangTagBo extends BaseEntity {

    /**
     * id
     */
    @NotBlank(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 标签名称
     */
    @NotBlank(message = "标签名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 标签描述
     */
    @NotBlank(message = "标签描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String description;

}
