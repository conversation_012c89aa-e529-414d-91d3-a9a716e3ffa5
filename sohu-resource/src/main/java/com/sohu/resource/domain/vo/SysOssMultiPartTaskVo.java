package com.sohu.resource.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.List;


/**
 * OSS对象存储分片任务视图对象
 *
 * <AUTHOR>
 * @date 2024-04-19
 */
@Data
@ExcelIgnoreUnannotated
public class SysOssMultiPartTaskVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 原名
     */
    @ExcelProperty(value = "原名")
    private String originalName;

    /**
     * 文件后缀名
     */
    @ExcelProperty(value = "文件后缀名")
    private String fileSuffix;

    /**
     * 文件名
     */
    @ExcelProperty(value = "文件名")
    private String fileName;

    /**
     * 上传凭证id，三方返回
     */
    @ExcelProperty(value = "上传凭证id，三方返回")
    private String uploadId;

    /**
     * 内容md5值
     */
    @ExcelProperty(value = "内容md5值")
    private String md5;

    /**
     * 文件大小
     */
    @ExcelProperty(value = "文件大小")
    private Long fileSize;

    /**
     * 每个分片大小
     */
    @ExcelProperty(value = "每个分片大小")
    private Long partSize;

    /**
     * 总分片数
     */
    @ExcelProperty(value = "总分片数")
    private Integer totalPartCount;

    /**
     * 服务商
     */
    @ExcelProperty(value = "服务商")
    private String service;

    /**
     * 状态：UNDERWAY进行中 COMPLETE完成 FAIL失败 ABORT取消
     */
    @ExcelProperty(value = "状态: UNDERWAY进行中 COMPLETE完成 FAIL失败 ABORT取消")
    private String status;

    /**
     * URL地址
     */
    @ExcelProperty(value = "URL地址")
    private String url;

    /**
     * 失败原因
     */
    @ExcelProperty(value = "失败原因")
    private String failReason;

    /**
     * 分片信息
     */
    private List<SysOssMultiPartTaskExecuteVo> executeList;
}
