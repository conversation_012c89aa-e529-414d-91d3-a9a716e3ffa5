package com.sohu.pm;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 项目
 *
 * <AUTHOR>
 */
@EnableDubbo
@SpringBootApplication(scanBasePackages = {"com.sohu"})
public class SohuPMApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(SohuPMApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  项目模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
