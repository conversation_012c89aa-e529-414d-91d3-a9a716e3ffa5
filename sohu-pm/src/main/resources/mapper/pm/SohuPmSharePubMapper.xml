<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.pm.mapper.SohuPmSharePubMapper">

    <resultMap type="com.sohu.pm.domain.SohuPmSharePub" id="SohuPmSharePubResult">
        <result property="id" column="id"/>
        <result property="productId" column="product_id"/>
        <result property="title" column="title"/>
        <result property="endTime" column="end_time"/>
        <result property="remark" column="remark"/>
        <result property="profitRatio" column="profit_ratio"/>
        <result property="profitPrice" column="profit_price"/>
        <result property="independentRatio" column="independent_ratio"/>
        <result property="independentPrice" column="independent_price"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getOrderList" resultType="com.sohu.pm.api.vo.SohuPmSharePubVo">
        SELECT
            p.id,
            p.title,
            p.product_id AS productId,
            p.create_time AS createTime,
            p.end_time AS endTime,
            o.order_no as orderNo
        FROM
            `ry-pm`.sohu_pm_share_pub p
        LEFT JOIN `ry-cloud`.sohu_shop_order o ON o.pm_share_pub_id = p.id
        <where>
            <if test="bo.id != null and bo.id != ''">
                AND p.id like concat('%',#{bo.id},'%')
            </if>
            <if test="bo.independentUserId != null and bo.independentUserId != ''">
                AND o.independent_user_id = #{bo.independentUserId}
            </if>
        </where>
        ORDER BY o.id DESC
    </select>

    <select id="getMaterialList" resultType="com.sohu.pm.api.vo.SohuPmSharePubVo">
        SELECT
        DISTINCT
            b.id,
            b.title,
            b.remark,
            b.independent_price AS independentPrice,
            b.create_time AS createTime
        FROM
            `ry-pm`.sohu_pm_share_pub b
        LEFT JOIN `ry-cloud`.sohu_independent_material m ON m.material_code = b.id
        LEFT JOIN `ry-cloud`.sohu_independent_material_user u ON u.material_id = m.id
        <where>
            <if test="bo.materialType != null and bo.materialType != ''">
                AND m.material_type = #{bo.materialType}
            </if>
            <if test="bo.independentUserId != null and bo.independentUserId != ''">
                AND u.material_share_user_id = #{bo.independentUserId}
            </if>
        </where>
        ORDER BY b.id DESC
    </select>

</mapper>
