package com.sohu.novel.service;

import com.sohu.novel.api.vo.CourseClassificationVo;

/**
 * 小说分类接口
 *
 * @Author: leibo
 * @Date: 2024/11/19 17:12
 **/
public interface ICourseClassificationService {

    /**
     * 基于分类查询相关分类id(如果不存在则进行新增操作)
     *
     * @param classificationName
     * @return
     */
    String queryClassifyIdsOrSaveClassify(String classificationName);

    /**
     * 基于外部id查询对应分类信息
     *
     * @param outSideId
     * @return
     */
    CourseClassificationVo queryCategoryByOutSideId(String outSideId);

    /**
     * 基于分类名称及类型
     *
     * @param classificationName
     * @param classifyType
     * @return
     */
    CourseClassificationVo queryClassifyByNameAndType(String classificationName, Integer classifyType);

    /**
     * 新增分类
     *
     * @param classificationName
     * @param classifyType
     * @param sourceType
     * @param outsideId
     */
    void insert(String classificationName, Integer classifyType, Integer sourceType, String outsideId);

    /**
     * 编辑分类
     *
     * @param classificationId
     * @param classificationName
     * @param classifyType
     * @param outsideId
     */
    void update(Integer classificationId, String classificationName, Integer classifyType, String outsideId);
}
