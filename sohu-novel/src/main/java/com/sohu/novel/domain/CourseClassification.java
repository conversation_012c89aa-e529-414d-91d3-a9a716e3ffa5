package com.sohu.novel.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 分类
 *
 * @Author: leibo
 * @Date: 2024/11/19 15:52
 **/
@Data
@TableName("course_classification")
public class CourseClassification implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分类id
     */
    @TableId(value = "classification_id")
    private Integer classificationId;
    /**
     * 分类
     */
    private String classificationName;
    /**
     * 是否删除
     */
    private Integer isDelete;
    /**
     * 分类类型
     */
    private String classifyType;
    /**
     * 外部ID
     */
    private String outsideId;
    /**
     * 类型  1.系统默认  2.咪咕  3.颂书';
     */
    private Integer sourceType;
}
