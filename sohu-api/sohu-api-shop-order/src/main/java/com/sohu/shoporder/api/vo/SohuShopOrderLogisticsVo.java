package com.sohu.shoporder.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;

/**
 * 订单快递记录视图对象
 *
 * <AUTHOR>
 * @date 2023-06-29
 */
@Data
@ExcelIgnoreUnannotated
public class SohuShopOrderLogisticsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 商户订单编号
     */
    @ExcelProperty(value = "商户订单编号")
    private String orderNo;

    /**
     * 快递单号
     */
    @ExcelProperty(value = "快递单号")
    private String expNo;

    /**
     * 快递公司编号
     */
    @ExcelProperty(value = "快递公司编号")
    private String expCode;

    /**
     * 快递公司名称
     */
    @ExcelProperty(value = "快递公司名称")
    private String expName;

    /**
     * 快递员 或 快递站(没有则为空)
     */
    @ExcelProperty(value = "快递员 或 快递站(没有则为空)")
    private String courier;

    /**
     * 快递员电话 (没有则为空)
     */
    @ExcelProperty(value = "快递员电话 (没有则为空)")
    private String courierPhone;

    /**
     * 最后的轨迹时间
     */
    @ExcelProperty(value = "最后的轨迹时间")
    private String trackTime;

    /**
     * 快递物流轨迹（JSON）AcceptStation-快递中转站，终点站，AcceptTime-事件时间
     */
    @ExcelProperty(value = "快递物流轨迹", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "J=SON")
    private String logisticsInfo;

    /**
     * 物流状态：-1：单号或快递公司代码错误, 0：暂无轨迹， 1：快递收件(揽件)，2：在途中,3：签收,4：问题件 5.疑难件 6.退件签收
     */
    @ExcelProperty(value = "物流状态：-1：单号或快递公司代码错误, 0：暂无轨迹， 1：快递收件(揽件)，2：在途中,3：签收,4：问题件 5.疑难件 6.退件签收")
    private Integer state;

    /**
     * 提示信息
     */
    @ExcelProperty(value = "提示信息")
    private String reason;

}
