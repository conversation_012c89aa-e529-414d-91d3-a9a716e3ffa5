package com.sohu.shoporder.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商户退款单视图对象
 *
 * <AUTHOR>
 * @date 2023-06-29
 */
@Data
@ExcelIgnoreUnannotated
public class SohuShopRefundOrderVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @ExcelProperty(value = "订单ID")
    private Long id;

    /**
     * 退款订单号
     */
    @ExcelProperty(value = "退款订单号")
    private String refundOrderNo;

    /**
     * 商户订单号
     */
    @ExcelProperty(value = "商户订单号")
    private String shopOrderNo;

    /**
     * 主订单号
     */
    @ExcelProperty(value = "主订单号")
    private String masterOrderNo;

    /**
     * 第三方支付退款单号(唯一)
     */
    @ExcelProperty(value = "第三方支付退款单号(唯一)")
    private String refundId;

    /**
     * 商户-狐小店id
     */
    @ExcelProperty(value = "商户-狐小店id")
    private Long merId;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 订单来源 1.微信小程序 2.app端  3.抖音小程序 4.支付宝小程序 5.PC网站
     */
    @ExcelProperty(value = "退款订单来源")
    private Integer refundSource;

    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱")
    private String email;

    /**
     * 收货人姓名
     */
    @ExcelProperty(value = "收货人姓名")
    private String receiverName;

    /**
     * 收货人电话
     */
    @ExcelProperty(value = "收货人电话")
    private String userPhone;

    /**
     * 收货人详细地址
     */
    @ExcelProperty(value = "收货人详细地址")
    private String userDetailAddress;

    /**
     * 订单商品总数
     */
    @ExcelProperty(value = "订单商品总数")
    private Long totalNum;

    /**
     * 退款原因
     */
    @ExcelProperty(value = "退款原因")
    private String refundReasonWap;

    /**
     * 退款图片
     */
    @ExcelProperty(value = "退款图片")
    private String refundReasonWapImg;

    /**
     * 退款用户说明
     */
    @ExcelProperty(value = "退款用户说明")
    private String refundReasonWapExplain;

    /**
     * 退款状态：FALSE:-未退款 WAITAPPROVE:待审核-申请中  REFUNDREFUSE:审核未通过 REFUNDING：退款中 REFUNDSUCCESS:已退款 RETURNGOODS:待退货 WAITGOODS:待收货 RECEIVEGOODS:已收货
     */
    @ExcelProperty(value = "退款状态：FALSE:-未退款 WAITAPPROVE:待审核-申请中  REFUNDREFUSE:审核未通过 REFUNDING：退款中 REFUNDSUCCESS:已退款 RETURNGOODS:待退货 WAITGOODS:待收货 RECEIVEGOODS:已收货")
    private String refundStatus;

    /**
     * 拒绝退款说明
     */
    @ExcelProperty(value = "拒绝退款说明")
    private String refundReason;

    /**
     * 退款金额
     */
    @ExcelProperty(value = "退款金额")
    private BigDecimal refundPrice;

    /**
     * 退款兑换积分
     */
    @ExcelProperty(value = "退款兑换积分")
    private Integer refundIntegral;

    /**
     * 退款时间
     */
    @ExcelProperty(value = "退款时间")
    private Date refundTime;

    /**
     * 商户备注
     */
    @ExcelProperty(value = "商户备注")
    private String merRemark;

    /**
     * 平台备注
     */
    @ExcelProperty(value = "平台备注")
    private String platformRemark;

    // 自定义

    /**
     * 商户名称
     */
    private String merName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 售后方式:OnlyRefund=仅退款,ReturnRefund=退货退款
     */
    private String refundType;

    /**
     * 寄件人姓名
     */
    @NotBlank(message = "寄件人姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String returnUserName;

    /**
     * 寄件人电话
     */
    @NotBlank(message = "寄件人电话不能为空", groups = {AddGroup.class, EditGroup.class})
    private String returnUserPhone;

    /**
     * 寄件人详细地址
     */
    @NotBlank(message = "寄件人详细地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String returnUserDetailAddress;

    /**
     * 支付状态
     */
    @ExcelProperty(value = "支付状态")
    private Boolean paid;

    /**
     * 订单总价
     */
    @ExcelProperty(value = "订单总价")
    private BigDecimal totalPrice;

    /**
     * 支付邮费
     */
    @ExcelProperty(value = "支付邮费")
    private BigDecimal payPostage;

    /**
     * 优惠券金额
     */
    @ExcelProperty(value = "优惠券金额")
    private BigDecimal couponPrice;

    /**
     * 实际支付金额
     */
    @ExcelProperty(value = "实际支付金额")
    private BigDecimal payPrice;

    /**
     * 支付方式  wechat-jsapi-微信小程序支付、wechat-app-微信app支付、wechat-h5-微信h5支付、wechat-native-微信扫码支付、tiktok-抖音小程序支付、ali-pay-支付宝小程序支付、integral-积分支付、balance-余额支付、offline-pay-线下支付
     */
    private String payType;


    /**
     * 用户备注
     */
    @ExcelProperty(value = "用户备注")
    private String userRemark;

    /**
     * 物流单号
     */
    private String deliveryNo;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 物流信息
     */
    private SohuShopOrderLogisticsVo shopOrderLogisticsVo;

    /**
     * 开放平台三方客户端ID
     */
    private Long openClientId;

}
