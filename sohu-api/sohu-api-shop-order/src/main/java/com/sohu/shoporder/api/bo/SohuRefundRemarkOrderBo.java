package com.sohu.shoporder.api.bo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 商户订单备注对象
 *
 * <AUTHOR>
 * @date 2023-06-29
 */

@Data
public class SohuRefundRemarkOrderBo implements Serializable {

    /**
     * 退款订单号
     */
    @NotEmpty(message = "退款订单号不能为空")
    private String refundOrderNo;

    /**
     * 备注内容不能为空
     */
    @NotEmpty(message = "备注内容不能为空")
    private String remark;

}
