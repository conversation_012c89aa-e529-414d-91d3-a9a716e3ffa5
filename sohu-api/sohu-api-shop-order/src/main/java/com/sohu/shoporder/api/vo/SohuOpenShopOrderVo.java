package com.sohu.shoporder.api.vo;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商户订单对象
 */
@Data
public class SohuOpenShopOrderVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(title = "商户订单号",description = "商户订单号",example = "S317169529042521079205")
    private String orderNo;

    @Schema(title = "主订单号",description = "主订单号",example = "M528169529042521039307")
    private String masterOrderNo;

    /**
     * 商户-狐小店id
     */
    @Schema(title = "狐小店mer_id",example = "1")
    private Long merId;

    @Schema(title = "狐小店商户名称", description = "狐小店商户名称可以去店铺查询接口获取，没有的店铺请联系管理员添加", example = "酷酷的小店")
    private String merchantName;

    @Schema(title = "收货人姓名", description = "收货人姓名", example = "张三")
    private String receiverName;

    @Schema(title = "收货人电话", description = "收货人电话", example = "13212345678")
    private String userPhone;

    @Schema(title = "收货人详细地址", description = "收货人详细地址", example = "湖北省武汉市某某街道某某小区01栋101室")
    private String userDetailAddress;

    @Schema(title = "订单商品总数", description = "订单商品总数", example = "10")
    private Integer totalNum;

    @Schema(title = "商品总价", description = "商品总价", example = "100.00")
    private BigDecimal productTotalPrice;

    @Schema(title = "商品总价", description = "商品总价", example = "100.00")
    private BigDecimal totalPostage;

    @Schema(title = "订单总价",description = "订单总价",example = "100.00")
    private BigDecimal totalPrice;

    @Schema(title = "实际支付金额",description = "实际支付金额",example = "110.00")
    private BigDecimal payPrice;

    @Schema(title = "支付邮费",description = "支付邮费",example = "10.00")
    private BigDecimal payPostage;

    @Schema(title = "第三方支付服务费",description = "第三方支付机构所需服务费",example = "0.03")
    private BigDecimal chargePrice;

    @Schema(title = "支付状态",description = "true:已支付,false:未支付",example = "true")
    private Boolean paid;

    @Schema(title = "支付时间",description = "日期格式：yyyy-MM-dd HH:mm:ss",example = "2024-08-28 15:23:34")
    private Date payTime;

    @Schema(title = "订单状态",description = "订单状态（UNPAID：待支付,WAIT_SENT：待发货,WAIT_RECEIVE:待收货,COMPLETED：已完成，CANCEL：已取消）",example = "WAIT_SENT")
    private String status;

    @Schema(title = "退款状态",description = "退款状态：FALSE:-未退款,WAITAPPROVE:待审核-申请中,REFUNDREFUSE:审核未通过,REFUNDING：退款中,REFUNDSUCCESS:已退款",example = "FALSE")
    private String refundStatus;

    @Schema(title = "快递公司名称",description = "快递公司名称",defaultValue = "顺丰速运")
    private String deliveryName;

    @Schema(title = "快递公司简称",description = "快递公司简称",example = "SF")
    private String deliveryCode;

    @Schema(title = "快递单号",description = "快递单号",example = "SF3125165514354")
    private String deliveryNo;

    @Schema(title = "用户备注",description = "用户备注",example = "我是用户备注")
    private String userRemark;

    @Schema(title = "商户备注",description = "商户备注",example = "我是商户备注")
    private String merRemark;

    @Schema(title = "平台备注",description = "平台备注",example = "我是平台备注")
    private String platformRemark;

    @Schema(title = "是否用户取消",description = "true:是；false:否",example = "false")
    private Boolean isUserCancel;

    @Schema(title = "创建时间",description = "日期格式：yyyy-MM-dd HH:mm:ss",example = "2024-08-28 15:23:34")
    private Date createTime;

    @Schema(title = "更新时间",description = "日期格式：yyyy-MM-dd HH:mm:ss",example = "2024-08-28 15:23:34")
    private Date updateTime;

    @Schema(title = "商户订单详情",description = "商户订单详情")
    private List<SohuOpenShopOrderInfoVo> orderInfoList;

}
