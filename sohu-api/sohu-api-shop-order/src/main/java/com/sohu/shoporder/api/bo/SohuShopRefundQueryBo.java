package com.sohu.shoporder.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.shoporder.api.constants.RefundConstants;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 商户退款单查询业务对象
 *
 * <AUTHOR>
 * @date 2023-06-29
 */

@Data
public class SohuShopRefundQueryBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 退款订单号
     */
    private String refundOrderNo;

    /**
     * 商户订单号
     */
    private String shopOrderNo;

    /**
     * 第三方支付退款单号(唯一)
     */
    private String refundId;

    /**
     * 商户-狐小店id
     */
    private Long merId;

    /**
     * 订单来源 1.微信小程序 2.app端  3.抖音小程序 4.支付宝小程序 5.PC网站
     */
    private Integer refundSource;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人电话
     */
    private String userPhone;

    /**
     * 退款状态：FALSE:-未退款 WAITAPPROVE:待审核-申请中  REFUNDREFUSE:审核未通过 REFUNDING：退款中 REFUNDSUCCESS:已退款 {@link RefundConstants}
     */
    @NotBlank(message = "退款状态：FALSE:-未退款 WAITAPPROVE:待审核-申请中  REFUNDREFUSE:审核未通过 REFUNDING：退款中 REFUNDSUCCESS:已退款  不能为空", groups = {AddGroup.class, EditGroup.class})
    private String refundStatus;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 售后方式:ONLYREFUND=仅退款,RETURNREFUND=退货退款
     */
    private String refundType;

}
