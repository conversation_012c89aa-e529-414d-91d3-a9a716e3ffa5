package com.sohu.shoporder.api.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 申请退款订单响应对象
 *
 * @author: zc
 * @date: 2023/9/14 14:29
 * @version:
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SohuApplyRefundOrderInfoModel implements Serializable {

    /**
     * 订单id
     */
    private Integer id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 支付状态
     */
    private Boolean paid;

    /**
     * 是否是分销单-0不是 1是
     */
    private Boolean isIndependent;

    /**
     * 支付金额
     */
    private BigDecimal payPrice;

    /**
     * 订单商品总数
     */
    private Integer totalNum;

    /**
     * 订单详情对象列表
     */
    private List<SohuShopOrderInfoModel> orderInfoList;

}
