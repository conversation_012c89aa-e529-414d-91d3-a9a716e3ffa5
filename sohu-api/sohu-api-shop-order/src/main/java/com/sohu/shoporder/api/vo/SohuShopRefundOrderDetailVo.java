package com.sohu.shoporder.api.vo;

import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商户退款单对象 sohu_shop_refund_order
 *
 * <AUTHOR>
 * @date 2023-06-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SohuShopRefundOrderDetailVo extends BaseEntity {

    /**
     * 订单ID
     */
    private Long id;

    /**
     * 退款订单号
     */
    private String refundOrderNo;

    /**
     * 商户订单号
     */
    private String shopOrderNo;

    /**
     * 主订单号
     */
    private String masterOrderNo;

    /**
     * 第三方支付退款单号(唯一)
     */
    private String refundId;

    /**
     * 商户-狐小店id
     */
    private Long merId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人电话
     */
    private String userPhone;

    /**
     * 收货人详细地址
     */
    private String userDetailAddress;

    /**
     * 订单商品总数
     */
    private Integer totalNum;

    /**
     * 退款原因
     */
    private String refundReasonWap;

    /**
     * 退款图片
     */
    private String refundReasonWapImg;

    /**
     * 退款用户说明
     */
    private String refundReasonWapExplain;

    /**
     * 退款状态：FALSE:-未退款 WAITAPPROVE:待审核-申请中  REFUNDREFUSE:审核未通过 REFUNDING：退款中 REFUNDSUCCESS:已退款
     */
    private String refundStatus;

    /**
     * 拒绝退款说明
     */
    private String refundReason;

    /**
     * 退款金额
     */
    private BigDecimal refundPrice;

    /**
     * 退款时间
     */
    private Date refundTime;

    /**
     * 商户备注
     */
    private String merRemark;

    /**
     * 平台备注
     */
    private String platformRemark;

}
