package com.sohu.shoporder.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商户订单视图对象
 *
 * <AUTHOR>
 * @date 2023-06-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SohuShopOrderListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id", example = "1")
    private Long id;

    /**
     * 店铺id
     */
    @Schema(description = "店铺id", example = "12")
    private Long merId;

    /**
     * 商户订单号
     */
    @Schema(description = "商户订单号", example = "S319172543667805654696")
    private String orderNo;

    /**
     * 主订单号
     */
    @Schema(description = "主订单号", example = "M921172543667791215801")
    private String masterOrderNo;

    /**
     * 收货人姓名
     */
    @Schema(description = "收货人姓名", example = "张三")
    private String receiverName;

    /**
     * 实际支付金额
     */
    @Schema(description = "实际支付金额", example = "1.00")
    private BigDecimal payPrice;

    /**
     * 订单状态（UNPAID：待支付 WAIT_SENT：待发货：WAIT_RECEIVE 待收货,2：RECEIVE 已收货,COMPLETED：已完成，CANCEL：已取消））
     */
    @Schema(description = "订单状态", example = "UNPAID")
    private String status;

    /**
     * 退款状态：0 未退款 1 申请中 2 退款中 3 已退款
     * 退款状态：FALSE:-未退款 WAITAPPROVE:待审核-申请中  REFUNDREFUSE:审核未通过 REFUNDING：退款中 REFUNDSUCCESS:已退款
     */
    @Schema(description = "退款状态", example = "FALSE")
    private String refundStatus;

    /**
     * 下单时间
     */
    @Schema(description = "下单时间", example = "2023-06-29 12:00:00")
    private Date createTime;

    /**
     * 支付方式  wechat-jsapi-微信小程序支付、wechat-app-微信app支付、wechat-h5-微信h5支付、wechat-native-微信扫码支付、tiktok-抖音小程序支付、ali-pay-支付宝小程序支付、integral-积分支付、balance-余额支付、offline-pay-线下支付
     */
    @Schema(description = "支付方式", example = "微信app支付")
    private String payType;

    /**
     * 商户备注
     */
    @Schema(description = "商户备注", example = "备注信息")
    private String merRemark;

    /**
     * 平台备注
     */
    @Schema(description = "平台备注", example = "备注信息")
    private String platformRemark;

    /**
     * 商品标识（0普通商品，1返哺商品，2抖音虚拟商品，3拓展商品',
     */
    @Schema(description = "0普通商品，1返哺商品，2抖音虚拟商品，3拓展商品", example = "0")
    private Long productType;

}
