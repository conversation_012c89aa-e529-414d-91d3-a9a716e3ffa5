package com.sohu.shoporder.api.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 退款订单详情响应对象
 *
 * @author: zc
 * @date: 2023/9/18 19:18
 * @version: 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SohuRefundOrderInfoModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long id;

    /**
     * 退款订单号
     */
    private String refundOrderNo;

    /**
     * 商户订单号
     */
    private String merOrderNo;

    /**
     * 主订单号
     */
    private String masterOrderNo;

    /**
     * 商户ID
     */
    private Long merId;

    /**
     * 订单商品总数
     */
    private Integer totalNum;

    /**
     * 退款原因
     */
    private String refundReasonWap;

    /**
     * 退款图片
     */
    private String refundReasonWapImg;

    /**
     * 退款用户说明
     */
    private String refundReasonWapExplain;

    /**
     * 退款状态：FALSE:-未退款 WAITAPPROVE:待审核-申请中  REFUNDREFUSE:审核未通过 REFUNDING：退款中 REFUNDSUCCESS:已退款
     */
    private String refundStatus;

    /**
     * 拒绝退款说明
     */
    private String refundReason;

    /**
     * 退款金额
     */
    private BigDecimal refundPrice;

    /**
     * 退款时间
     */
    private Date refundTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 订单详情对象列表
     */
    private List<SohuShopOrderInfoModel> orderInfoList;

    /**
     * 商户名称
     */
    private String merName;

}
