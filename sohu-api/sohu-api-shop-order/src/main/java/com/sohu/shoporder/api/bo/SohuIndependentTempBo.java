package com.sohu.shoporder.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/29 11:52
 */
@Data
public class SohuIndependentTempBo implements Serializable {

    @Schema(name = "userId", description = "用户id", defaultValue = "0")
    private Long userId;

    @Schema(name = "independentObject", description = "分账对象", defaultValue = "0")
    private String independentObject;

    @Schema(name = "independentPrice", description = "分账金额", defaultValue = "0")
    private BigDecimal independentPrice;

    @Schema(name = "merchantId", description = "翼码商户ID", defaultValue = "0")
    private String merchantId;

    @Schema(name = "siteId", description = "站点id", defaultValue = "0")
    private Long siteId;

    @Schema(name = "siteType", description = "站点类型 1 城市站 2 行业站", defaultValue = "0")
    private Integer siteType;
}
