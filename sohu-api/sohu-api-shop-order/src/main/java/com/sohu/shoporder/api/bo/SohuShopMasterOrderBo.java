package com.sohu.shoporder.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 主订单业务对象
 *
 * <AUTHOR>
 * @date 2023-06-29
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuShopMasterOrderBo extends BaseEntity implements Serializable {

    /**
     * 订单ID
     */
    @NotBlank(message = "订单ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 站点id
     */
    private Long siteId;

    /**
     * 主订单号
     */
    @NotBlank(message = "主订单号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orderNo;

    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空", groups = {AddGroup.class})
    private Long userId;

    /**
     * 支付来源-1、商城商品 2、商单-支付酬金 3、商户码扫码支付
     */
    private Integer paySource;

    /**
     * 订单来源 1.微信小程序 2.app端  3.抖音小程序 4.支付宝小程序 5.PC网站
     */
    @NotBlank(message = "订单来源不能为空 1.微信小程序 2.app端  3.抖音小程序 4.支付宝小程序 5.PC网站", groups = {AddGroup.class})
    private Integer orderSource;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 收货人姓名
     */
    @NotBlank(message = "收货人姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String receiverName;

    /**
     * 收货人电话
     */
    @NotBlank(message = "收货人电话不能为空", groups = {AddGroup.class, EditGroup.class})
    private String userPhone;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String userDetailAddress;

    /**
     * 订单商品总数
     */
    @NotBlank(message = "订单商品总数不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long totalNum;

    /**
     * 商品总价
     */
    @NotNull(message = "商品总价不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal productTotalPrice;

    /**
     * 邮费
     */
    private BigDecimal totalPostage;

    /**
     * 订单总价
     */
    @NotNull(message = "订单总价不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal totalPrice;

    /**
     * 总兑换积分
     */
    private Long totalIntegral;

    /**
     * 支付邮费
     */
    private BigDecimal payPostage;

    /**
     * 实际支付金额
     */
    @NotNull(message = "实际支付金额不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal payPrice;

    /**
     * 分销人id-分销时必传
     */
    private Long independentUserId;

    /**
     * 第三方服务费-0.00
     */
    private BigDecimal chargePrice;

    /**
     * 分销人分账金额-0.00
     */
    private BigDecimal distributorPrice;

    /**
     * 拉新人分账金额-0.00
     */
    private BigDecimal invitePrice;

    /**
     * 城市站长分账金额-0.00
     */
    private BigDecimal cityPrice;

    /**
     * 国家站长分账金额-0.00
     */
    private BigDecimal countryPrice;

    /**
     * 平台分账金额-0.00
     */
    private BigDecimal adminPrice;


    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 优惠券金额
     */
    private BigDecimal couponPrice;

    /**
     * 支付状态
     */
    @NotBlank(message = "支付状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private Boolean paid;

    /**
     * 支付时间
     */
    @NotNull(message = "支付时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date payTime;

    /**
     * 支付方式  wechat-jsapi-微信小程序支付、wechat-app-微信app支付、wechat-h5-微信h5支付、wechat-native-微信扫码支付、tiktok-抖音小程序支付、ali-pay-支付宝小程序支付、integral-积分支付、balance-余额支付、offline-pay-线下支付
     */
    @NotBlank(message = "支付方式  wechat-jsapi-微信小程序支付、wechat-app-微信app支付、wechat-h5-微信h5支付、wechat-native-微信扫码支付、tiktok-抖音小程序支付、ali-pay-支付宝小程序支付、integral-积分支付、balance-余额支付、offline-pay-线下支付不能为空", groups = {AddGroup.class, EditGroup.class})
    private String payType;

    /**
     * 支付渠道：pc,mobile
     */
    @NotBlank(message = "支付渠道：pc,mobile不能为空", groups = {AddGroup.class, EditGroup.class})
    private String payChannel;

    /**
     * 用户备注
     */
    private String mark;

    /**
     * 是否取消
     */
    @NotBlank(message = "是否取消不能为空", groups = {AddGroup.class, EditGroup.class})
    private Boolean isCancel;

    /**
     * 主支付单号
     */
    @NotBlank(message = "主支付单号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String payMasterOrderNo;

    /**
     * 第三方支付单号,32个字符内
     */
    private String outTradeNo;

    /**
     * 第三方延时分账单号,32个字符内
     */
    private String delayTradeNo;

    /**
     * 第三方交易流水号(唯一)
     */
    private String transactionId;

    /**
     * 支付重定向地址-国外支付用
     */
    private String redirect;

    /**
     * 是否是分销单-0不是 1是
     */
    private Boolean isIndependent;

    /**
     * 下单开始时间
     */
    private String startTime;
    /**
     * 下单结束时间
     */
    private String endTime;

    /**
     * 分销人的拉新人金额-0.00
     */
    private BigDecimal distributorInvitePrice;
    /**
     * 代理人金额-0.00
     */
    private BigDecimal agencyAdminPrice;
    /**
     * 分销人的拉新人id-主动查询
     */
    private Long independentInviteUserId;

    /**
     * 用户性别
     */
    private Integer sex;

}
