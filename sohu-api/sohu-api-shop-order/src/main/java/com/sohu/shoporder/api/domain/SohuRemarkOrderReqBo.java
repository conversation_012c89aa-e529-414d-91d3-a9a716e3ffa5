package com.sohu.shoporder.api.domain;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 商户订单备注对象
 *
 * <AUTHOR>
 * @date 2023-06-29
 */

@Data
public class SohuRemarkOrderReqBo implements Serializable {

    /**
     * 商户订单号
     */
    @NotEmpty(message = "订单号不能为空")
    private String orderNo;

    /**
     * 备注内容不能为空
     */
    @NotEmpty(message = "备注内容不能为空")
    private String remark;

}
