package com.sohu.busyorder.api.bo;

import com.sohu.common.core.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商单退款业务对象
 *
 * <AUTHOR>
 * @date 2025-01-13
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuBusyTaskRefundBo extends BaseEntity {

    /**
     * 主键id
     */
    @Schema(name = "id", description = "主键id", defaultValue = "1")
    private Long id;

    /**
     * 商单任务编号
     */
    @Schema(name = "taskNumber", description = "商单任务编号", defaultValue = "1")
    private String taskNumber;

    /**
     * 发起退款人ID
     */
    @Schema(name = "userId", description = "发起退款人ID", defaultValue = "1")
    private Long userId;

    /**
     * 三方支付流水号
     */
    @Schema(name = "transactionId", description = "三方支付流水号", defaultValue = "1")
    private String transactionId;

    /**
     * 退单编码
     */
    @Schema(name = "refundOrderNo", description = "退单编码", defaultValue = "1")
    private String refundOrderNo;

    /**
     * 退款流水号
     */
    @Schema(name = "refundNumber", description = "退款流水号", defaultValue = "1")
    private String refundNumber;

    /**
     * 退款状态 success,fail
     */
    @Schema(name = "refundStatus", description = "退款状态 success,fail", defaultValue = "1")
    private String refundStatus;

    /**
     * 退款金额
     */
    @Schema(name = "refundAmount", description = "退款金额", defaultValue = "1")
    private BigDecimal refundAmount;

    @Schema(name = "refundReason", description = "退款原因", defaultValue = "1")
    private String refundReason;
}
