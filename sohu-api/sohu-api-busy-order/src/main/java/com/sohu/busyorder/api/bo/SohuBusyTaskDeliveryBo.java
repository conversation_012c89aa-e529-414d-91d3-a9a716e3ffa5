package com.sohu.busyorder.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商单阶段性交付业务对象
 *
 * <AUTHOR>
 * @date 2023-12-11
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuBusyTaskDeliveryBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 主任务商id
     */
    private String masterTaskNumber;

    /**
     * 子任务编号
     */
    @NotNull(message = "子任务编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String taskNumber;

    /**
     * 阶段截止日期
     */
    private Date endTime;

    /**
     * 阶段性要求描述
     */
    private String content;

    /**
     * 阶段进度占比
     */
    private BigDecimal percentage;

    /**
     * 优先级;值越高
     */
    private Integer level;

    /**
     * 状态;WaitApprove-审核中，Pass-通过，Refuse-拒绝
     */
    private String state;

    /**
     * 审核不通过原因
     */
    private String refuseMsg;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 支付状态;支付状态：WaitPay-待支付，Paid-已支付，Fail-支付失败，TimeOut-超时
     */
    private String payState;

    /**
     * 支付单号
     */
    private String transactionNo;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 排序值
     */
    private Integer sortIndex;

    /**
     * 执行记录对象
     */
    private SohuBusyTaskExecuteBo taskExecuteBo;

    /**
     * 是否一次性支付所有任务价值金额
     */
    private Boolean allAmount;

    /**
     * 是否是阶段性付款
     */
    private Boolean isDelivery;

    /**
     * 支付渠道：pc、mobile
     */
    private String payChannel;

    /**
     * 是否是佣金结算
     */
    private Boolean isIndependentAmount;

//    /**
//     * 支付对象
//     */
//    private SohuPrePayBo prePayBo;

    /**
     * 商单接单执行记录
     */
    private SohuBusyTaskExecuteBo busyTaskExecute;

    /**
     * 最后一次时间或者次数
     */
    private Long time;

}
