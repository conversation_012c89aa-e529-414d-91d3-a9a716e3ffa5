package com.sohu.busyorder.api;


import com.sohu.busyorder.api.bo.*;
import com.sohu.busyorder.api.domain.SohuBusyTaskAfterSaleExecuteBo;
import com.sohu.busyorder.api.domain.SohuBusyTaskReqBo;
import com.sohu.busyorder.api.domain.SohuBusyTaskSiteReqBo;
import com.sohu.busyorder.api.domain.SohuMcnBusyTaskSiteReqBo;
import com.sohu.busyorder.api.model.SohuBusyTaskModel;
import com.sohu.busyorder.api.model.SohuBusyTaskSiteModel;
import com.sohu.busyorder.api.vo.*;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface RemoteBusyTaskService {

    /**
     * 查询任务主体
     */
    SohuBusyTaskModel queryById(Long id);

    /**
     * 查询任务主体列表
     */
    List<SohuBusyTaskModel> queryList(SohuBusyTaskReqBo bo);

    /**
     * 查询任务主体列表-根据任务ID
     */
    List<SohuBusyTaskModel> queryList(List<Long> ids);

    /**
     * 查询任务主体列表-根据taskNumber
     */
    List<SohuBusyTaskModel> queryListByNumberList(Collection<? extends Serializable> taskNumberList);

    /**
     * 修改任务主体
     */
    Boolean insertByBo(SohuBusyTaskReqBo bo);

    /**
     * 修改任务主体
     */
    Long updateByBo(SohuBusyTaskReqBo bo);

    /**
     * 校验主任务是否可以编辑
     *
     * @param taskNumber
     */
    Boolean exitChildTask(String taskNumber);

    /**
     * 根据主任务单号查询主任务
     *
     * @param taskNumber
     */
    SohuBusyTaskVo getByTaskNo(String taskNumber);

    /**
     * 修改主任务状态
     *
     * @param taskNumber
     * @return
     */
    Boolean updateTaskState(String taskNumber, String state);

    /**
     * 支付后修改通用商单任务状态
     */
    Boolean updateTaskStateAfterPay(String taskNumber, String state, Long userId);

    /**
     * 根据主单号获取审核通过时间或接单时间
     */
    Date getTaskTimeWithMasterTaskNumber(String masterTaskNumber);

    /**
     * 根据主单号获取接单人id
     */
    Map<String, Long> getUserIdByMasterTaskNumber(List<String> masterTaskNumberList);

    /**
     * 校验并批量删除任务主体信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 审核
     *
     * @param bo {@link SohuBusyTaskReqBo}
     * @return {@link Boolean}
     */
    Boolean audit(SohuBusyTaskReqBo bo);

    /**
     * 上下架-主任务
     *
     * @param bo {@link SohuBusyTaskReqBo}
     * @return {@link Boolean}
     */
    Boolean shelf(SohuBusyTaskReqBo bo);

    /**
     * 主任务分页列表
     *
     * @param bo
     * @param pageQuery
     */
    TableDataInfo<SohuBusyTaskModel> queryPageLists(SohuBusyTaskReqBo bo, PageQuery pageQuery);

    /**
     * 子任务列表
     *
     * @param bo
     * @param pageQuery
     */
    TableDataInfo<SohuBusyTaskSiteModel> queryPageChildLists(SohuBusyTaskSiteReqBo bo, PageQuery pageQuery);

    /**
     * 子任务列表-待接单
     *
     * @param bo
     * @param pageQuery
     */
    TableDataInfo<SohuBusyTaskSiteVo> queryPageOfOnShelf(SohuBusyTaskSiteBo bo, PageQuery pageQuery);

    /**
     * 我的子任务订单列表
     *
     * @param bo
     * @param pageQuery
     */
    TableDataInfo<SohuBusyTaskSiteModel> queryPageMyChildLists(SohuBusyTaskSiteReqBo bo, PageQuery pageQuery);

    /**
     * 根据主任务编号查询所有子任务
     *
     * @param masterTaskNumber
     */
    List<SohuBusyTaskSiteModel> getChildList(String masterTaskNumber);

    /**
     * 获取子任务详情
     *
     * @param taskNumber
     */
    SohuBusyTaskSiteModel getChildInfo(String taskNumber);

    /**
     * 获取子任务详情
     *
     * @param taskId 子任务ID
     */
    SohuBusyTaskSiteModel getChildInfo(Long taskId);

    /**
     * 审核子任务
     *
     * @param bo
     */
    Boolean auditChild(SohuBusyTaskSiteReqBo bo);

    /**
     * 修改子任务
     *
     * @param bo
     */
    Boolean updateByChildBo(SohuBusyTaskSiteReqBo bo);

    /**
     * 上下架-子任务
     *
     * @param bo
     */
    Boolean childShelf(SohuBusyTaskSiteReqBo bo);

    /**
     * 任务审核校验接口
     *
     * @param taskNumber
     * @param state
     */
    Boolean exitAuditTask(String taskNumber, String state);

    /**
     * 任务广场
     *
     * @param bo
     * @param pageQuery
     */
    TableDataInfo<SohuBusyTaskSiteModel> queryPageTaskLists(SohuBusyTaskSiteReqBo bo, PageQuery pageQuery);

    /**
     * 任务广场
     *
     * @param bo
     * @param pageQuery
     */
    TableDataInfo<SohuBusyTaskSiteVo> queryPageTaskList(SohuBusyTaskSiteBo bo, PageQuery pageQuery);

    /**
     * mcn挑选任务列表
     *
     * @param bo
     * @param pageQuery
     */
    TableDataInfo<SohuBusyTaskSiteModel> queryPageMcnTaskList(SohuMcnBusyTaskSiteReqBo bo, PageQuery pageQuery);

    /**
     * mcn任务库管理列表
     *
     * @param bo
     * @param pageQuery
     */
    TableDataInfo<SohuBusyTaskSiteModel> queryPageMcnTaskWindowList(SohuMcnBusyTaskSiteReqBo bo, PageQuery pageQuery);

    /**
     * 获取用户发布任务数量统计
     *
     * @param userId
     * @return
     */
    Long getBusyTaskOfPublishStat(Long userId);

    /**
     * 获取用户昨日发布任务数量统计
     *
     * @param userId
     * @return
     */
    Long getYesterdayBusyTaskOfPublishStat(Long userId);

    /**
     * 获取用户完结任务数量统计
     *
     * @param userId
     * @return
     */
    Long getBusyTaskOfFinishStat(Long userId);

    /**
     * 获取用户昨日完结任务数量统计
     *
     * @param userId
     * @return
     */
    Long getYesterdayBusyTaskOfFinishStat(Long userId);

    /**
     * 获取任务浏览量
     *
     * @param userId
     * @return
     */
    Long getBusyTaskViewStat(Long userId, String startDate, String endDate);

    /**
     * 获取任务接单人数
     *
     * @param userId
     * @return
     */
    Long getBusyTaskWithReceiveUserStat(Long userId, String startDate, String endDate);

    /**
     * 获取接单执行数
     *
     * @param userId
     * @return
     */
    Long getBusyTaskOfExecuteStat(Long userId, String startDate, String endDate);

    /**
     * 获取任务完结数
     *
     * @param userId
     * @return
     */
    Long getBusyTaskOfFinishWithLimitDayStat(Long userId, String startDate, String endDate);

    /**
     * 获取任务总金额
     *
     * @param userId
     * @return
     */
    BigDecimal getBusyTaskAmountStat(Long userId, String startDate, String endDate);

    /**
     * 初始化内容物料数据
     *
     * @return
     */
    Boolean initAirecContentItems();

    /**
     * 执行中终止任务
     *
     * @return
     */

    Boolean abortTask(SohuBusyTaskAfterSaleExecuteBo bo);

    /**
     * 更换接单人
     *
     * @return
     */
    Boolean abortReplaceReceiveTask(SohuBusyTaskAfterSaleExecuteBo bo);

    /**
     * 撤销申请
     *
     * @return
     */
    Boolean revokeTask(SohuBusyTaskAfterSaleExecuteBo bo);

    /**
     * 任务收藏列表
     */
    TableDataInfo<SohuBusyTaskSiteModel> listCollect(String busyTitle, PageQuery pageQuery);

    /**
     * mcn任务库管理列表(已接单)
     */
    TableDataInfo<SohuBusyTaskSiteModel> queryReceiveWindowList(SohuMcnBusyTaskSiteReqBo bo, PageQuery pageQuery);

    /**
     * 根据子任务编号查询主任务编号集合
     *
     * @param taskNumberList 子任务编号
     * @return 主任务编号集合
     */
    List<SohuBusyTaskSiteModel> queryMasterTaskNumber(List<String> taskNumberList);

    /**
     * 获取im群任务子任务列表
     */
    TableDataInfo<SohuBusyTaskSiteVo> queryImGroupSiteTask(SohuBusyTaskSiteReqBo bo, PageQuery pageQuery);

    /**
     * 根据子任务编号查询
     *
     * @param taskNumber 子任务编号
     */
    SohuBusyTaskSiteModel queryMasterTaskNumber(String taskNumber);

    /**
     * 站点内容数
     */
    Long queryBusyTaskNumBySite(Long siteId, Long userId);

    /**
     * 按状态统计对应的商单数量
     *
     * @param state
     * @return
     */
    Integer countByState(String state);

    /**
     * 基于时间统计商单数量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    Long countByTime(String startTime, String endTime);

    /**
     * 根据时间范围统计任务订单数据
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return List<SohuTaskOrderReportVo>
     */
    List<SohuTaskOrderReportVo> getBusyTaskSalesTrendByTimeRange(Date startDate, Date endDate);

    /**
     * 新增任务订单统计数据
     */
    void saveDataByBo(SohuTaskOrderReportBo bo);

    /**
     * 编辑任务订单统计数据
     */
    void updateDataByBo(SohuTaskOrderReportBo bo);

    /**
     * 根据时间范围统计执行中销售额
     */
    BigDecimal countSalesAmountByTime(Date startTime, Date endTime);

    /**
     * 根据时间范围统计获取订单量(待接单任务)
     */
    Long countOrderByTime(Date startTime, Date endTime);

    /**
     * 根据时间范围统计接单量(执行中任务)
     */
    Long countAcceptByTime(Date startTime, Date endTime);

    /**
     * 基于时间查询记录
     *
     * @param dateTime
     * @return
     */
    SohuTaskOrderReportVo queryReportByTime(String dateTime);

    /**
     * 调整过期时间
     *
     * @param id
     * @param deliveryTime
     */
    void updateDeliveryTime(Long id, Long deliveryTime);

    /**
     * 新增商单支付记录
     *
     * @param bo
     * @return
     */
    Boolean saveBusyTaskPay(SohuBusyTaskPayBo bo);

    /**
     * 根据商单订单号查询商单支付记录
     *
     * @param outTradeNo
     * @return
     */
    SohuBusyTaskPayVo queryBusyTaskPay(String outTradeNo, String payStatus);

    /**
     * 根据商单订单号查询商单支付记录
     *
     * @param outTradeNos
     * @param payStatus
     * @return
     */
    List<SohuBusyTaskPayVo> queryBusyTaskPayList(List<String> outTradeNos, String payStatus);

    /**
     * 更新商单支付记录
     *
     * @param bo
     * @return
     */
    Boolean updateBusyTaskPay(SohuBusyTaskPayBo bo);

    /**
     * 根据商单号查询商单支付记录
     *
     * @param taskNumber
     * @return
     */
    List<SohuBusyTaskPayVo> queryByTaskNumber(String taskNumber, String payStatus);

    /**
     * 根据商单场景值查询商单支付记录
     *
     * @param taskNumber
     * @param payStatus
     * @param paySceneType
     * @return
     */
    SohuBusyTaskPayVo queryByPayScene(String taskNumber, String payStatus, Integer paySceneType);

    /**
     * 查询商单支付记录列表
     *
     * @param payStatus
     * @param paySceneType
     * @param isExecute
     * @return
     */
    List<SohuBusyTaskPayVo> payJobList(String payStatus, Integer paySceneType, Integer isExecute);

    /**
     * 新增商单退款记录
     *
     * @param bo
     * @return
     */
    Boolean saveBusyTaskRefund(SohuBusyTaskRefundBo bo);

    /**
     * 根据子任务编号绑定IM进群用户标签
     *
     * @param masterTaskNumber 主任务编号
     * @param userId           绑定用户id
     */
    void insertUserLabelByTaskNumber(String masterTaskNumber, Long userId);

    /**
     * 处理流量商单达标用户(补救任务)
     */
    void handleFlowBusyTask();

    /**
     * 查询流量商单截止时间内的任务
     *
     * @return
     */
    List<SohuBusyTaskVo> selectFlowBusyTaskOfState();

    /**
     * 调整排序和生效时间
     *
     * @param bo SohuBusyTaskSortBo
     */
    Boolean updateSortAndEffectiveTimeById(SohuBusyTaskSortBo bo);

    /**
     * 调整置顶商单任务
     */
    void resetTopBusyTaskHandler();

    /**
     * 根据国家站长获取全部商单列表
     */
    TableDataInfo<SohuBusyTaskAllListVo> getAllListWithRole(SohuBusyTaskAllListBo bo, PageQuery pageQuery);

    /**
     * 查询流量商单分页列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuPayBusyVo> busyFlowList(SohuPayBusyBo bo, PageQuery pageQuery);

    /**
     * 查询流量商单聚合分页列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuPayBusyVo> busyAggrPayList(SohuPayBusyBo bo, PageQuery pageQuery);

    /**
     * 一键结算订单
     *
     * @param taskNumber
     * @return
     */
    Boolean settleBusyTask(String taskNumber);

    /**
     * 结算订单(管理员审核)
     *
     * @param taskNumber
     * @param rejectReason
     * @return
     */
    Boolean applySettleBusyTask(String taskNumber, String rejectReason);

    /**
     * 获取分销任务列表
     *
     * @param busyTitle 任务名称
     * @param pageQuery PageQuery
     * @return TableDataInfo<SohuBusyTaskSiteModel>
     */
    TableDataInfo<SohuBusyTaskSiteModel> listDistribution(String busyTitle, PageQuery pageQuery);

    /**
     * 达标人数补救
     */
    void handleFlowBusyTaskPassNum(String masterTaskNumber);

    /**
     * 查询专题内容列表
     */
    TableDataInfo<SohuBusyTaskSiteVo> getTopicList(Long topicId, Long topicPid, Boolean isSortByViewCount, PageQuery pageQuery);

    /**
     * 查询置顶商单数据
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuBusyTaskSiteVo> queryPageTopOfTaskSite(SohuBusyTaskSiteReqBo bo, PageQuery pageQuery);

    /**
     * 获取发单分组列表
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<TaskGroupVo> getSendTaskGroupList(Date startTime, Date endTime);
}

