package com.sohu.busyorder.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import java.math.BigDecimal;
import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 任务商单订单业务对象
 *
 * <AUTHOR>
 * @date 2025-01-13
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuBusyTaskPayBo extends BaseEntity {

    @Schema(name = "id", description = "主键Id",defaultValue = "1")
    private Long id;

    @Schema(name = "userId", description = "用户id",defaultValue = "1")
    private Long userId;

    /**
     * 商单任务编号
     */
    @Schema(name = "taskNumber", description = "商单任务编号",defaultValue = "12556666")
    private String taskNumber;

    /**
     * 订单支付单号
     */
    @Schema(name = "orderNo", description = "订单编号",defaultValue = "12556666")
    private String orderNo;

    /**
     * 三方支付订单号
     */
    @Schema(name = "transactionId", description = "三方支付订单号",defaultValue = "12556666")
    private String transactionId;

    /**
     * 支付流水号
     */
    @Schema(name = "payNumber", description = "支付流水号",defaultValue = "12556666")
    private String payNumber;

    /**
     * 支付方式  wechat,alipay,paypal,yima
     */
    @Schema(name = "payType", description = "支付方式  wechat,alipay,paypal,yima",defaultValue = "12556666")
    private String payType;

    /**
     * 付状态 WaitPay-待支付，Paid-已支付，Fail-支付失败，TimeOut-超时取消，Refund-退款  CANCEL-取消支付
     */
    @Schema(name = "payStatus", description = "付状态 WaitPay-待支付，Paid-已支付，Fail-支付失败，TimeOut-超时取消，Refund-退款  CANCEL-取消支付",defaultValue = "12556666")
    private String payStatus;

    /**
     * 支付金额
     */
    @Schema(name = "payAmount", description = "支付金额",defaultValue = "12556666")
    private BigDecimal payAmount;

    /**
     * 手续费
     */
    @Schema(name = "chargeAmount", description = "手续费",defaultValue = "12556666")
    private BigDecimal chargeAmount;

    /**
     * 已退金额
     */
    @Schema(name = "refundAmount", description = "已退金额",defaultValue = "12556666")
    private BigDecimal refundAmount;

    /**
     * 退单流水号
     */
    @Schema(name = "refundId", description = "退单流水号",defaultValue = "12556666")
    private String refundId;

    /**
     * 业务类型
     */
    @Schema(name = "busyType", description = "业务类型",defaultValue = "12556666")
    private String busyType;

    /**
     * 支付渠道：pc、mobile
     */
    @Schema(name = "payChannel", description = "支付渠道：pc、mobile",defaultValue = "12556666")
    private String payChannel;

    @Schema(name = "payTime", description = "支付时间",defaultValue = "12556666")
    private Date payTime;

    @Schema(name = "receiveDistributorAmount", description = "接单方分账金额",defaultValue = "10.00")
    private BigDecimal receiveDistributorAmount;

    @Schema(name = "platformAmount", description = "平台分账金额",defaultValue = "10.00")
    private BigDecimal platformAmount;

    @Schema(name = "platformRatio", description = "平台服务费比例",defaultValue = "10.00")
    private BigDecimal platformRatio;

    @Schema(name = "adminRatio", description = "平台保底比例",defaultValue = "10.00")
    private BigDecimal adminRatio;

    @Schema(name = "consumerInviteRatio", description = "接单的拉新人比例",defaultValue = "10.00")
    private BigDecimal consumerInviteRatio;

    @Schema(name = "distributorRatio", description = "分销比例",defaultValue = "10.00")
    private BigDecimal distributorRatio;

    @Schema(name = "distributorInviteRatio", description = "分销人的拉新人比例",defaultValue = "10.00")
    private BigDecimal distributorInviteRatio;

    @Schema(name = "busyAmount", description = "任务商单金额",defaultValue = "10.00")
    private BigDecimal busyAmount;

    @Schema(name = "shareAmount", description = "分销金额",defaultValue = "10.00")
    private BigDecimal shareAmount;

    @Schema(name = "shareDistributorAmount", description = "分销商分账金额",defaultValue = "10.00")
    private BigDecimal shareDistributorAmount;

    @Schema(name = "platformShareAmount", description = "平台分销分账金额",defaultValue = "10.00")
    private BigDecimal platformShareAmount;

    /**
     * 手续费承担方 0 分账方 1 平台
     */
    @Schema(name = "chargeUndertake", description = "手续费承担方 0 分账方 1 平台",defaultValue = "1")
    private Integer chargeUndertake;

    @Schema(name = "paySceneType",description = "支付场景类型 0 聚合支付 1 商单金额支付 2 分销金额支付 3 保证金金额支付 4 阶段性金额支付",example = "0")
    private Integer paySceneType;

    @Schema(name = "settleTime", description = "结算时间",defaultValue = "12556666")
    private Date settleTime;

    @Schema(name = "receiveUserId", description = "接单用户id",defaultValue = "12556666")
    private Long receiveUserId;

    @Schema(name = "isExecute", description = "执行状态 0 未执行 1 已执行",defaultValue = "12556666")
    private Integer isExecute;
}
