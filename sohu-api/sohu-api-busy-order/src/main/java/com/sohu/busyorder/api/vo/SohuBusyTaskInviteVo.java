package com.sohu.busyorder.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 商单被邀请人列视图对象
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Data
@ExcelIgnoreUnannotated
public class SohuBusyTaskInviteVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 商单id
     */
    @ExcelProperty(value = "商单id")
    private Long busyTaskId;

    /**
     * 被邀请人ID
     */
    @ExcelProperty(value = "被邀请人ID")
    private Long userId;


}
