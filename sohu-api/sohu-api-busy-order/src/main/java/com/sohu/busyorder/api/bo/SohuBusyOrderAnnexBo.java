package com.sohu.busyorder.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商单附件业务对象
 *
 * <AUTHOR>
 * @date 2023-07-10
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuBusyOrderAnnexBo extends BaseEntity {

    /**
     * 商单附件主键
     */
    @NotNull(message = "商单附件主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * dict::商单业务类型:[BusinessOrder:商单,Take:接单,Execute:执行]
     */
    //@NotBlank(message = "dict::商单业务类型:[BusinessOrder:商单,Take:接单,Execute:执行]不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orderBusyType;

    /**
     * 业务id
     */
    //@NotNull(message = "业务id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long busyCode;

    /**
     * 附件类型（自定义，例如合同照片、合同电子文档、执行凭证、打款凭证）
     */
    @NotBlank(message = "附件类型（自定义，例如合同照片、合同电子文档、执行凭证、打款凭证）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String annexType;

    /**
     * 附件内容（附件链接）
     */
    @NotBlank(message = "附件内容（附件链接）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String annexContent;


}
