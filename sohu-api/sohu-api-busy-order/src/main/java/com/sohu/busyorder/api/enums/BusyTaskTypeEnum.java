package com.sohu.busyorder.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BusyTaskTypeEnum {

    SALE("SALE", "销售类型代理商"),
    FLOW("FLOW", "流量类型代理商"),
    SERVER("SERVER", "服务类型代理商"),
    RESOURCE("RESOURCE", "资源类型代理商"),
    GAME("GAME", "游戏"),
    NOVEL("NOVEL", "小说"),
    ARTICLE("ARTICLE", "图文"),
    VIDEO("VIDEO", "视频"),
    FLOW_TASK("FLOW_TASK", "流量"),
    COMMON_TASK("COMMON_TASK", "通用");

    private String code;
    private String msg;

}
