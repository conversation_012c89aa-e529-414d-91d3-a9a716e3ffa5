package com.sohu.busyorder.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;


/**
 * 商单附件视图对象
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@Data
@ExcelIgnoreUnannotated
public class SohuBusyOrderAnnexVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商单附件主键
     */
    @ExcelProperty(value = "商单附件主键")
    private Long id;

    /**
     * dict::商单业务类型:[BusinessOrder:商单,Take:接单,Execute:执行]
     */
    @ExcelProperty(value = "dict::商单业务类型:[BusinessOrder:商单,Take:接单,Execute:执行]")
    private String orderBusyType;

    /**
     * 业务id
     */
    @ExcelProperty(value = "业务id")
    private Long busyCode;

    /**
     * 附件类型（自定义，例如合同照片、合同电子文档、执行凭证、打款凭证）
     */
    @ExcelProperty(value = "附件类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "自=定义，例如合同照片、合同电子文档、执行凭证、打款凭证")
    private String annexType;

    /**
     * 附件内容（附件链接）
     */
    @ExcelProperty(value = "附件内容", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "附=件链接")
    private String annexContent;


}
