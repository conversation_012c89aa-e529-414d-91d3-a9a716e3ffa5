package com.sohu.busyorder.api.vo;

import com.sohu.busyorder.api.bo.SohuBusyTaskExecuteBo;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商单交付公共VO对象
 *
 * @Author: leibo
 * @Date: 2025/1/15 10:24
 **/
@Data
public class SohuBusyTaskDeliveryCommonVo {

    /**
     * 子单的商单编号 CT982171413093793161398
     */
    @NotNull(message = "子单的商单编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String taskNumber;

    /**
     * 附件列表
     */
    private String annex;

    /**
     * 描述
     */
    @NotBlank(message = "描述不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;
}
