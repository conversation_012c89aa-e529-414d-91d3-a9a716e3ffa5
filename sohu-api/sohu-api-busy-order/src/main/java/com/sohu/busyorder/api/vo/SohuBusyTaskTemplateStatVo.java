package com.sohu.busyorder.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 模板使用统计对象
 */
@Data
@ExcelIgnoreUnannotated
public class SohuBusyTaskTemplateStatVo implements Serializable {

    /**
     * 模板使用总次数
     */
    private Long usageStat;

    /**
     * 模板总收益
     */
    private BigDecimal templateBenefitsTotal = BigDecimal.ZERO;
}
