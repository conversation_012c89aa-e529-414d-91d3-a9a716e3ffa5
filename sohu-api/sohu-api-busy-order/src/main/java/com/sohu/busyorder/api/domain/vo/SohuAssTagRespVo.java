package com.sohu.busyorder.api.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;


/**
 * 站点业务关联视图对象
 */
@Data
@ExcelIgnoreUnannotated
public class SohuAssTagRespVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * dict::业务类型:[Role:角色,Topic:话题,Project:项目,BusyOrder:商单]
     */
    @ExcelProperty(value = "dict::业务类型:[Role:角色,Topic:话题,Project:项目,BusyOrder:商单]")
    private String type;

    /**
     * 站点id（国家）
     */
    @ExcelProperty(value = "站点id", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "国=家")
    private Long siteId;

    /**
     * 标签名
     */
    @ExcelProperty(value = "标签名")
    private String name;
    /**
     * 标签标识
     */
    private String ident;

    /**
     * 热度
     */
    @ExcelProperty(value = "热度")
    private Long hot;

    /**
     * 拓展字段
     */
    @ExcelProperty(value = "拓展字段")
    private String ext;

    /**
     * 标签英文名
     */
    @ExcelProperty(value = "标签英文名")
    private String enName;


}
