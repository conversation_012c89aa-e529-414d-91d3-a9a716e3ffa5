package com.sohu.busyorder.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商单关联业务对象
 *
 * <AUTHOR>
 * @date 2023-07-10
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuBusyOrderRelateBo extends BaseEntity {

    /**
     * 商单关联id
     */
    @NotNull(message = "商单关联id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 商单id
     */
    @NotNull(message = "商单id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long orderId;

    /**
     * dict::关联类型:[Project:项目,BusyModel:生意模式]
     */
    @NotBlank(message = "dict::关联类型:[Project:项目,BusyModel:生意模式]不能为空", groups = {AddGroup.class, EditGroup.class})
    private String relateType;

    /**
     * 业务编码
     */
    @NotNull(message = "业务编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long busyCode;


}
