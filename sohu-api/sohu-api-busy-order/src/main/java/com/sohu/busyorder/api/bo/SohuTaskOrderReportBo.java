package com.sohu.busyorder.api.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import java.math.BigDecimal;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 任务订单统计数据业务对象
 *
 * <AUTHOR>
 * @date 2024-12-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuTaskOrderReportBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 销售金额 (单位: 元)
     */
    @NotNull(message = "销售金额 (单位: 元)不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal salesAmount;

    /**
     * 订单数量
     */
    @NotNull(message = "订单数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderCount;

    /**
     * 发单量
     */
    @NotNull(message = "发单量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long dispatchCount;

    /**
     * 接单量
     */
    @NotNull(message = "接单量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long acceptCount;

    /**
     * 接单率 (单位: 百分比)
     */
    @NotNull(message = "接单率 (单位: 百分比)不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal acceptRate;

    /**
     * 统计日期
     */
    @NotNull(message = "统计日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private String recordDate;

    /**
     * 记录创建时间
     */
    @ExcelProperty(value = "记录创建时间")
    private Date createTime;

    /**
     * 记录更新时间
     */
    @ExcelProperty(value = "记录更新时间")
    private Date updateTime;

}
