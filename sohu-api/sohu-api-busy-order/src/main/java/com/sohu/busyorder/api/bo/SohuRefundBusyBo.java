package com.sohu.busyorder.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/3/10 16:31
 */
@Data
public class SohuRefundBusyBo implements Serializable {

    @Schema(name = "taskNumber", description = "主单编号", example = "12222222")
    private String taskNumber;

    @Schema(name = "refundAmount", description = "退款金额", example = "10.00")
    private BigDecimal refundAmount;

    @Schema(name = "refundReason", description = "退款原因", example = "退款原因")
    private String refundReason;

    @Schema(name = "busyTaskType", description = "任务类型 流量商单 BusyTaskFlow;通用商单 BusyTaskCommon", defaultValue = "BusyTaskFlow")
    private String busyTaskType;

    @Schema(name = "paySceneType", description = "支付场景类型 0 聚合支付 1 商单金额支付 2 分销金额支付 3 保证金金额支付 4 阶段性金额支付", example = "0")
    private Integer paySceneType;
}
