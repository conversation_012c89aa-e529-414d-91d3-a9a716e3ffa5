package com.sohu.busyorder.api;

import com.sohu.busyorder.api.domain.SohuBusyTaskDeliveryReqBo;
import com.sohu.busyorder.api.model.SohuBusyTaskDeliveryModel;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 任务服务
 */
public interface RemoteBusyTaskDeliveryService {

    /**
     * 查询商单阶段性交付
     */
    SohuBusyTaskDeliveryModel queryById(Long id);

    /**
     * 查询商单阶段性交付列表
     */
    TableDataInfo<SohuBusyTaskDeliveryModel> queryPageList(SohuBusyTaskDeliveryReqBo bo, PageQuery pageQuery);

    /**
     * 查询商单阶段性交付列表
     */
    List<SohuBusyTaskDeliveryModel> queryList(SohuBusyTaskDeliveryReqBo bo);

    /**
     * 修改商单阶段性交付
     */
    Boolean insertByBo(SohuBusyTaskDeliveryReqBo bo);

    /**
     * 新增商单完整交付交付
     *
     * @param bo
     */
    Boolean addOverByBo(SohuBusyTaskDeliveryReqBo bo);

    /**
     * 修改商单阶段性交付-审核并支付
     */
    String updateByBo(SohuBusyTaskDeliveryReqBo bo);

    /**
     * 校验并批量删除商单阶段性交付信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据任务id查询商单阶段性交付
     *
     * @param busyTaskId 商单ID
     * @return {@link List}
     */
    List<SohuBusyTaskDeliveryModel> queryByTaskId(String busyTaskId);

    /**
     * 根据子任单号务查询商单阶段性交付
     *
     * @param taskNumber
     */
    List<SohuBusyTaskDeliveryModel> queryByTaskNumber(String taskNumber);

    /**
     * 根据子任务编号查询阶段性交付信息-非阶段性
     *
     * @param taskNumber
     * @param sortIndex
     */
    SohuBusyTaskDeliveryModel queryByTaskNumber(String taskNumber, Integer sortIndex);

    /**
     * 根据子任务编号查询阶段性交付信息-阶段性
     *
     * @param taskNumber
     * @return
     */
    List<SohuBusyTaskDeliveryModel> listByTaskNo(String taskNumber);
}
