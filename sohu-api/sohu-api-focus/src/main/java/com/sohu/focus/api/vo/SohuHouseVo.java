package com.sohu.focus.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 房源主体视图对象 sohu_house
 *
 * <AUTHOR>
 * @date 2023-06-03
 */
@Data
@ExcelIgnoreUnannotated
public class SohuHouseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 房源名称
     */
    private String houseName;
    /**
     * 房子总价
     */
    private String totalPrice;
    /**
     * 价格单位-字典id
     */
    private Long unitName;
    /**
     * 价格单位-字典名称
     */
    private String unitNameDict;
    /**
     * 物业费
     */
    private String propertyFee;
    /**
     * 地理位置
     */
    private String address;
    /**
     * 房屋类型id
     */
    private Long houseType;
    /**
     * 房屋类型名称
     */
    private String houseTypeName;
    /**
     * 开发商名称
     */
    private String propertyDeveloper;
    /**
     * 房源照片-oss id
     */
    private String housePhoto;
    /**
     * 房源照片-oss id
     */
    private String housePhotoUrl;
    /**
     * 开盘时间
     */
    private String openTime;
    /**
     * 交房时间
     */
    private String completeTime;
    /**
     * 产权时间
     */
    private String propertyRightTime;
    /**
     * 建筑面积
     */
    private String acreage;
    /**
     * 最小建筑面积
     */
    private String minAcreage;
    /**
     * 最大建筑面积
     */
    private String maxAcreage;
    /**
     * 主力户型
     */
    private String mainHouseType;
    /**
     * 状态
     */
    private String state;
    /**
     * 驳回理由
     */
    private String rejectReason;
    /**
     * 排序值
     */
    private Long sortIndex;
    /**
     * 站点id
     */
    private Long siteId;
    /**
     * 站点
     */
    private String siteName;
    /**
     * 户型列表
     */
    private List<SohuHouseTypeVo> houseTypeList;
    /**
     * 周边配套列表
     */
    private SohuHouseAmbitusVo houseAmbitusList;
    /**
     * 状态名称
     */
    private String stateName;

    /**
     * 浏览量
     */
    private Long viewCount;

    /**
     * 房源别名
     */
    private String houseOtherName;

    /**
     * 城市站点id
     */
    private Integer citySiteId;
    /**
     * 城市站点id
     */
    private String citySiteName;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 发布人昵称
     */
    private String userNickName;

    /**
     * 每平方单价
     */
    private Integer squarePrice;

    /**
     * 车库车位数量
     */
    private Integer parkingNumber;

    /**
     * 总楼层数
     */
    private Integer totalFloor;

    /**
     * 所在楼层数
     */
    private Integer currentFloor;

    /**
     * 房源描述
     */
    private String houseInfo;

    /**
     * 联系方式
     */
    private String contactInformation;

    /**
     * 是否开启首页推荐
     */
    private Boolean homeRecommend;

    /**
     * 详细位置经纬度
     */
    private String location;

    /**
     * 发布时间
     */
    private Date updateTime;

    /**
     * 汇率转换后的总价
     */
    private String totalRatePrice;

    /**
     * 汇率转换后的单价
     */
    private String squareRatePrice;

    /**
     * 汇率转换后的物业费
     */
    private String propertyRatePrice;

    /**
     * h5跳转地址
     */
    private String h5Url;

    /**
     * 推荐说明，后端返回中文说明:
     * eg: 首页推荐，专栏推荐
     */
    private String recommendMsg;

}
