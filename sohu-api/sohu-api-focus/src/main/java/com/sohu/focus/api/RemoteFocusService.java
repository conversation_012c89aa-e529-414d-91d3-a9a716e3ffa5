package com.sohu.focus.api;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.focus.api.bo.SohuArticleContentBo;
import com.sohu.focus.api.vo.SohuArticleContentVo;

/**
 * 焦点海外接口
 */
public interface RemoteFocusService {
    /**
     * 查询资讯明细
     *
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuArticleContentVo> queryPageList(PageQuery pageQuery);

    /**
     * 更新资讯内容
     * @param contentBo
     * @return
     */
    Boolean updateByBo(SohuArticleContentBo contentBo);
}
