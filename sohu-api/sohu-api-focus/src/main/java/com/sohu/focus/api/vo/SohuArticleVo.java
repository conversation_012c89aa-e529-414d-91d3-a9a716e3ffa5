package com.sohu.focus.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import com.sohu.middle.api.vo.SohuVideoVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 内容主体视图对象 sohu_article
 *
 * <AUTHOR>
 * @date 2023-06-03
 */
@Data
@ExcelIgnoreUnannotated
public class SohuArticleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "内容ID")
    @Schema(name = "id", description = "文章ID", example = "1")
    private Long id;

    @ExcelProperty(value = "内容标题")
    @Schema(name = "title", description = "文章标题", example = "《哪吒2》爆火后，光线传媒12.2亿收购北京奥林NEO办公楼")
    private String title;

    @ExcelProperty(value = "摘要")
    @Schema(name = "abstracts", description = "文章摘要", example = "此次向光线传媒出售奥林NEO项目公司99%股权的光曜致新，背后就是东方资产")
    private String abstracts;

    @ExcelProperty(value = "是否原创", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1==是,0==否")
    @Schema(name = "original", description = "是否原创（1=是 0=否）", example = "1")
    private Long original;

    @ExcelProperty(value = "转载地址")
    @Schema(name = "originUrl", description = "文章转载地址", example = "")
    private String originUrl;

    @ExcelProperty(value = "内容封面图")
    @Schema(name = "coverImage", description = "文章封面图,OSS对象ID", example = "https://sohugloba.oss-accelerate.aliyuncs.com/2025/03/10/f2cdb3c34c6b494e8f018037c6b63753.png")
    private String coverImage;

    @ExcelProperty(value = "子图")
    @Schema(name = "image", description = "子图", example = "")
    private String image;

    @ExcelProperty(value = "状态：Edit-编辑，WaitApprove-待审核，OnShelf-上架，Refuse-审核拒绝，OffShelf-下架，CompelOff-强制下架，Del-删除")
    @Schema(name = "state", description = "状态：Edit-编辑，WaitApprove-待审核，OnShelf-上架，Refuse-审核拒绝，OffShelf-下架，CompelOff-强制下架，Del-删除", example = "OnShelf")
    private String state;

    @ExcelProperty(value = "驳回理由")
    @Schema(name = "rejectReason", description = "驳回理由", example = "涉政")
    private String rejectReason;

    @ExcelProperty(value = "管理员下架理由")
    @Schema(name = "displayReason", description = "管理员下架理由", example = "涉政")
    private String displayReason;

    @ExcelProperty(value = "类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "Article=文章,V=ideo=视频")
    @Schema(name = "type", description = "类型（Article=文章 Video=视频）", example = "Article")
    private String type;

    @ExcelProperty(value = "阅读数")
    @Schema(name = "viewCount", description = "阅读数", example = "2354")
    private Long viewCount;

    @ExcelProperty(value = "评论数")
    @Schema(name = "commentCount", description = "评论数", example = "12")
    private Long commentCount;

    @ExcelProperty(value = "点赞数")
    @Schema(name = "praiseCount", description = "点赞数", example = "3214")
    private Long praiseCount;

    @ExcelProperty(value = "收藏数")
    @Schema(name = "collectCount", description = "收藏数", example = "121")
    private Long collectCount;

    @ExcelProperty(value = "视频链接地址")
    @Schema(name = "videoUrl", description = "视频链接地址", example = "")
    private String videoUrl;

    @ExcelProperty(value = "推荐值", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "推荐值越大越靠前")
    @Schema(name = "recomm", description = "推荐值越大越靠前", example = "99")
    private Long recomm;

    @ExcelProperty(value = "创建ip")
    @Schema(name = "createIp", description = "创建ip", example = "*************")
    private String createIp;

    @ExcelProperty(value = "修改ip")
    @Schema(name = "updateIp", description = "修改ip", example = "*************")
    private String updateIp;

    @ExcelProperty(value = "排序")
    @Schema(name = "sortIndex", description = "排序值", example = "1")
    private Long sortIndex;

    @ExcelProperty(value = "分类id")
    @Schema(name = "category", description = "分类id", example = "1")
    private Long category;

    @ExcelProperty(value = "拓展字段，json")
    @Schema(name = "extend", description = "拓展字段，json", example = "")
    private String extend;

    @ExcelProperty(value = "站点id")
    @Schema(name = "siteId", description = "站点id", example = "2")
    private Long siteId;

    @ExcelProperty(value = "站点")
    @Schema(name = "siteName", description = "站点名称", example = "塞浦路斯")
    private String siteName;

    @ExcelProperty(value = "内容")
    @Schema(name = "content", description = "内容", example = "<p>此次向光线传媒出售奥林NEO项目公司99%股权的光曜致新，背后就是东方资产。</p>")
    private String content;

    private Date createTime;

    @Schema(name = "userId", description = "作者id", example = "1")
    private Long userId;

    @Schema(name = "tags", description = "标签id集合", example = "")
    private Long tags;

    @Schema(name = "authorName", description = "创建者名称", example = "CESHI")
    private String authorName;

    @Schema(name = "authorAvatar", description = "创建者头像", example = "")
    private String authorAvatar;

    @Schema(name = "categoryName", description = "分类名称", example = "国学文化")
    private String categoryName;

    @Schema(name = "tagName", description = "标签名称集合", example = "")
    private String tagName;

    @Schema(name = "stateName", description = "审核状态名称", example = "已上架")
    private String stateName;

    @Schema(name = "categoryList", description = "层级分类id", example = "")
    private List<Long> categoryList;

    @Schema(name = "homeRecommend", description = "首页推荐(allRecommend：全站推荐 allHeadlines：全站头条 columnRecommend：专栏推荐 columnHeadlines：专栏头条)", example = "allRecommend")
    private String homeRecommend;

    @Schema(name = "updateTime", description = "更新时间", example = "2025-03-10 15:37:32")
    private Date updateTime;

    @Schema(name = "updateTimeFormat", description = "更新时间格式化的", example = "2025-03-10")
    private String updateTimeFormat;

    @Schema(name = "ident", description = "内容标识,如，sinologyculture", example = "sinologyculture")
    private String ident;

    @Schema(name = "h5Url", description = "h5跳转地址", example = "http://***************:8083/#/pages/detail/detail?id=90&ident=interact")
    private String h5Url;

    @Schema(description = "是否开启专栏头条（0=否，1=是）", example = "true")
    private boolean columnHeadlinesEnable;

    @Schema(description = "专栏头条排序值（值越小越靠前）", example = "10")
    private Integer columnHeadlinesSort;

    @Schema(description = "是否开启子专栏头条（0=否，1=是）", example = "true")
    private boolean subcolumnHeadlinesEnable;

    @Schema(description = "子专栏头条排序值（值越小越靠前）", example = "100")
    private Integer subcolumnHeadlinesSort;

    @Schema(description = "是否有子类（0=否，1=是）", example = "true")
    private boolean hasChildCategory = false;

    @ExcelProperty(value = "内容封面图OssId")
    @Schema(name = "coverImageOssId", description = "文章封面图,OSS对象ID", example = "https://sohugloba.oss-accelerate.aliyuncs.com/2025/03/10/f2cdb3c34c6b494e8f018037c6b63753.png")
    private String coverImageOssId;

    @Schema(description = "视频封面图OssId")
    private String videoCoverImageOssId;

    @Schema(description = "视频封面图")
    private String videoCoverImage;

    /**
     * 视频推荐列表
     */
    private List<SohuVideoVo> videoList;
}
