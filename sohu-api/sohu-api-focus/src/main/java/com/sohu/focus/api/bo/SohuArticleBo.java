package com.sohu.focus.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 内容主体业务对象 sohu_article
 *
 * <AUTHOR>
 * @date 2023-06-03
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuArticleBo extends BaseEntity {

    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 内容标题
     */
    @NotBlank(message = "内容标题不能为空", groups = {AddGroup.class})
    private String title;

    /**
     * 摘要
     */
    private String abstracts;

    /**
     * 是否原创（1=是 0=否）
     */
    private Long original;

    /**
     * 转载地址
     */
    private String originUrl;

    /**
     * 内容封面图
     */
    //@NotBlank(message = "内容封面图不能为空", groups = {AddGroup.class, EditGroup.class})
    private String coverImage;

    /**
     * 子图
     */
    private String image;

    /**
     * 状态：Edit-编辑，WaitApprove-待审核，OnShelf-上架，Refuse-审核拒绝，OffShelf-下架，CompelOff-强制下架，Del-删除
     */
    private String state;

    /**
     * 驳回理由
     */
    private String rejectReason;

    /**
     * 管理员下架理由
     */
    private String displayReason;

    /**
     * 类型（Article=文章 Video=视频）
     */
    private String type;

    /**
     * 阅读数
     */
    private Long viewCount;

    /**
     * 评论数
     */
    private Long commentCount;

    /**
     * 点赞数
     */
    private Long praiseCount;

    /**
     * 收藏数
     */
    private Long collectCount;

    /**
     * 视频链接地址
     */
    private String videoUrl;

    /**
     * 推荐值（推荐值越大越靠前）
     */
    private Long recomm;

    /**
     * 创建ip
     */
    private String createIp;

    /**
     * 修改ip
     */
    private String updateIp;

    /**
     * 排序
     */
    private Long sortIndex;

    /**
     * 分类id
     */
    private Long category;

    /**
     * 拓展字段，json
     */
    private String extend;

    /**
     * 站点id
     */
    //@NotNull(message = "站点id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long siteId;

    /**
     * 内容
     */
    private String content;
    /**
     * 作者id
     */
    private Long userId;
    /**
     * 标签id集合
     */
    private Long tags;
    /**
     * 内容标识
     */
    private String ident;

    /**
     * 根据访问量排序标识， hot=1
     */
    private int hot;

    /**
     * 层级分类id
     */
    private List<Long> categoryList;

    /**
     * 首页推荐(allRecommend：全站推荐 allHeadlines：全站头条 columnRecommend：专栏推荐 columnHeadlines：专栏头条)
     */
    private String homeRecommend;

    /**
     * 是否需要封面图(0不需要 1需要)
     */
    private Long isCoverImage;

    private String platform;

    /**
     * 查询来源，后台-admin，官网-index
     */
    private String source;
    /**
     * 是否查头条
     */
    private Boolean queryToutiao;
    /**
     * 是否开启专栏头条（0=否，1=是）
     */
    private Boolean columnHeadlinesEnable;

    /**
     * 专栏头条排序值（值越小越靠前）
     */
    private Integer columnHeadlinesSort;

    /**
     * 是否开启子专栏头条（0=否，1=是）
     */
    private Boolean subcolumnHeadlinesEnable;

    /**
     * 子专栏头条排序值（值越小越靠前）
     */
    private Integer subcolumnHeadlinesSort;
    /**
     * 是否有子类
     */
    private boolean hasChildCategory = false;
    /**
     * 视频封面图
     */
    private String videoCoverImage;


}
