package com.sohu.app.api.domain.vo.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "用户统计")
public class UserStatVO implements Serializable {

    @Schema(description = "粉丝数", example = "1")
    private Long fansCount = 0L;

    @Schema(description = "关注数", example = "1")
    private Long followCount = 0L;

    @Schema(description = "好友数", example = "1")
    private Long friendCount = 0L;

    @Schema(description = "点赞数", example = "1")
    private Long praiseCount = 0L;

    @Schema(description = "收藏数", example = "1")
    private Long collectCount = 0L;

}
