package com.sohu.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class OrderTradeAnalysisVo implements Serializable {
    private static final long serialVersionUID = 1L;

     @Schema(name = "displayDate", description = "显示日期 (周粒度时为'年份第几周')", example = "2025第20周")
    private String displayDate;

    @Schema(name = "wishOrderNum", description = "心愿单订单数", example = "10")
    private Integer wishOrderNum;

    @Schema(name = "productOrderNum", description = "商品订单数", example = "10")
    private Integer productOrderNum;

    @Schema(name = "shortDramaOrderNum", description = "短视频订单数", example = "10")
    private Integer shortDramaOrderNum;

    @Schema(name = "novelOrderNum", description = "小说订单数", example = "10")
    private Integer novelOrderNum;

    @Schema(name = "wishIncome", description = "心愿单收益", example = "10")
    private BigDecimal wishIncome;

    @Schema(name = "productIncome", description = "商品收益", example = "10")
    private BigDecimal productIncome;

    @Schema(name = "shortDramaIncome", description = "短视频收益", example = "10")
    private BigDecimal shortDramaIncome;

    @Schema(name = "novelIncome", description = "小说收益", example = "10")
    private BigDecimal novelIncome;
}