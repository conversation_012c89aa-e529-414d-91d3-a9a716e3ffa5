package com.sohu.shopgoods.api.model;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.web.domain.SohuEntity;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 商品品牌视图对象
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@Data
@ExcelIgnoreUnannotated
public class SohuProductBrandModel extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @Schema(title = "ID",example = "123456789")
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 名称
     */
    @Schema(title = "名称",example = "红旗")
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * icon
     */
    @Schema(title = "icon", description = "单张，存储url", example = "https://xxx.oss-cn-beijing.aliyuncs.com/images/123.jpg")
    @ExcelProperty(value = "icon")
    private String icon;

    /**
     * 排序
     */
    @Schema(title = "排序",description = "排序",example = "1")
    @ExcelProperty(value = "排序")
    private Integer sort;

    /**
     * 显示状态
     */
    @Schema(title = "显示状态",description = "1:展示，0：隐藏",example = "1")
    @ExcelProperty(value = "显示状态")
    private Integer isShow;

    /**
     * 是否删除
     */
    @Hidden
    @ExcelProperty(value = "是否删除")
    private Boolean isDel;

    /**
     * 关联分类id（分号分隔）
     */
    @Hidden
    private String categoryIds;

}
