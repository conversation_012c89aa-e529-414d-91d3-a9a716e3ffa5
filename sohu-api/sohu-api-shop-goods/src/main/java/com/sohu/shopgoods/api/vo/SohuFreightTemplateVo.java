package com.sohu.shopgoods.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 运费模板视图对象
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@ExcelIgnoreUnannotated
public class SohuFreightTemplateVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 商户Id
     */
    private Long merId;

    /**
     * 模版名称
     */
    private String name;

    /**
     * 模板类型（DEFAULT：默认模版 CUSTOMIZE：自定义模板）
     */
    private String type;

    /**
     * 包邮区域
     */
    private String freeArea;

    /**
     * 买家付运费区域
     */
    private List<SohuFreightPayVo> freightPayList;

    /**
     * 不配送区域
     */
    private String notDeliveryArea;

    /**
     * 是否删除
     */
    private Boolean isDel;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

}
