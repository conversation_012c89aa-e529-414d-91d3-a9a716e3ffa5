package com.sohu.shopgoods.api.bo;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 商户-商品分类业务对象
 */
@Data
public class SohuOpenMerProductCategoryUpdateStatusBo implements Serializable {

    @Schema(title = "id", example = "123456789")
    @NotNull(message = "Id不能为空")
    private Long id;

    @Schema(title = "显示状态", description = "1:展示，0：隐藏", example = "1", defaultValue = "1")
    @NotNull(message = "显示状态不能为空")
    private Integer isShow;

    /**
     * 开放平台三方客户端ID
     */
    @Hidden
    private Long openClientId;
}
