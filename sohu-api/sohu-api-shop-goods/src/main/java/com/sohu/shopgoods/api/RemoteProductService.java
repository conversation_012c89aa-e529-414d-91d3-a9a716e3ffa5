package com.sohu.shopgoods.api;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.bo.*;
import com.sohu.shopgoods.api.domain.*;
import com.sohu.shopgoods.api.model.*;
import com.sohu.shopgoods.api.vo.*;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品服务
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
public interface RemoteProductService {

    /**
     * 首页商品列表
     *
     * @param shopProductReqBo
     * @param pageQuery
     * @return 分页列表
     */
    TableDataInfo<SohuIndexProductModel> getHomeList(ShopProductReqBo shopProductReqBo, PageQuery pageQuery);

    /**
     * 个人商品橱窗
     *
     * @param shopProductReqBo
     * @param pageQuery
     */
    TableDataInfo<SohuIndexProductModel> userShopWindowPage(ShopProductReqBo shopProductReqBo, PageQuery pageQuery);

    /**
     * MCN带货库分页
     *
     * @param bo
     * @param pageQuery
     */
    TableDataInfo<SohuIndexProductModel> mcnShopWindowPage(McnShopProductReqBo bo, PageQuery pageQuery);

    /**
     * 个人添加分销商品分页
     *
     * @param shopProductReqBo
     * @param pageQuery
     * @return 个人添加分销商品分页
     */
    TableDataInfo<SohuIndexProductModel> userShopWindowIndependentPage(ShopProductReqBo shopProductReqBo, PageQuery pageQuery);

    /**
     * MCN选品商城商品分页
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuIndexProductModel> mcnShopWindowIndependentPage(McnShopProductReqBo bo, PageQuery pageQuery);

    /**
     * 新增个人商品橱窗
     */
    Boolean insertByBo(SohuProductWindowModel bo);

    /**
     * 是否置顶个人商品橱窗
     */
    Boolean updateByBoTop(SohuProductWindowModel bo);

    /**
     * 获取首页商品详情
     *
     * @param id
     * @return 详情
     */
    SohuIndexProductInfoModel getIndexInfo(Long id, Boolean isIndependent);

    /**
     * 获取首页商品数量
     *
     * @param id
     * @return 数量
     */
    SohuProductReplyCountModel getReplyCount(Long id);

    /**
     * 商品详情评论
     *
     * @param id
     * @return SohuProductReplyInfoModel
     */
    SohuProductReplyInfoModel getReplyInfo(Long id);

    /**
     * 商品规格详情
     *
     * @param id
     * @return SohuProductInfoModel
     */
    SohuProductInfoModel getSkuInfo(Long id);

    /**
     * 商品排行榜
     *
     * @return 排行榜
     */
    List<SohuProductModel> getLevel();

    /**
     * 根据商品ids批量查询商品信息
     *
     * @param productIds
     * @return List<SohuProductModel>
     */
    List<SohuProductModel> listByIds(List<Long> productIds);

    /**
     * 根据商品id查询商品
     *
     * @param productId
     * @return SohuProductModel
     */
    SohuProductModel queryById(Long productId);

    /**
     * 普通商品扣库存
     *
     * @param productId
     * @param num
     * @param type
     */
    Boolean operationStock(Long productId, Integer num, String type);

    /**
     * app商户商品列表
     *
     * @param productReqBo
     * @param pageQuery
     */
    TableDataInfo<SohuIndexProductModel> getMerchantProductList(ShopProductReqBo productReqBo, PageQuery pageQuery);

    /**
     * 根据店铺id下架店铺下所有商品
     */
    Boolean forcedRemovalAll(Long id, Long siteId);

    /**
     * app分销商户商品列表-sotre
     *
     * @param productReqBo
     * @param pageQuery
     */
    TableDataInfo<SohuAppProductMerchantModel> getMerchantIndependentProductList(ShopProductReqBo productReqBo, PageQuery pageQuery);

    /**
     * 删除个人商品橱窗
     *
     * @param ids
     */
    boolean deleteWindow(List<Long> ids);

    /**
     * 根据userIds和productIds查询商品橱窗信息
     *
     * @param windowUserIds
     * @param windowProductIds
     */
    List<SohuProductWindowModel> selectByUserIdsAndProIds(List<Long> windowUserIds, List<Long> windowProductIds);

    /**
     * 根据userIds和productIds查询mcn商品橱窗信息
     *
     * @param windowMcnUserIds
     * @param windowMcnProductIds
     * @return
     */
    List<SohuProductWindowMcnModel> selectMcnByUserIdsAndProIds(List<Long> windowMcnUserIds, List<Long> windowMcnProductIds);

    /**
     * 根据个人商品橱窗集合对象修改
     *
     * @param productWindowModels
     */
    Boolean updateBatchWindow(List<SohuProductWindowModel> productWindowModels);

    /**
     * 根据mcn商品橱窗集合对象修改
     *
     * @param productWindowMcnModels
     */
    Boolean updateBatchWindowMcn(List<SohuProductWindowMcnModel> productWindowMcnModels);

    /**
     * 查询商品--MCN选品商城商品详情
     */
    SohuIndexProductInfoModel getMcnShopWindowInfo(Long id, Boolean isIndependent);

    /**
     * mcn选品批量添加
     *
     * @param bo
     * @return
     */
    Boolean insertByIds(SohuMcnShopProductBo bo);

    /**
     * MCN带货库批量添加
     *
     * @param ids
     * @return
     */
    Boolean productInsertByIds(List<Long> ids);

    /**
     * MCN带货库删除
     *
     * @param ids
     * @return
     */
    Boolean mcnShopWindowDeleteByIds(List<Long> ids);

    /**
     * MCN带货库批量修改
     *
     * @param bo
     * @return
     */
    Boolean mcnWindowUpdate(SohuMcnShopProductBo bo);

    /**
     * 根据用户id集合获取个人橱窗商品统计
     *
     * @param userIds
     * @param mcnId   MCN机构ID
     * @return
     */
    Map<Long, SohuProductWindowStatModel> getProductWindowStatByUserIdsAndMcnId(Collection<Long> userIds, Long mcnId);

    /**
     * 获取商品曝光量
     *
     * @param userId
     * @return
     */
    Long productExposureStatistics(Long userId, Long mcnId);

    /**
     * 获取商品浏览量
     *
     * @param userId
     * @return
     */
    Long productViewStatistics(Long userId, Long mcnId);

    /**
     * 获取商品性别分布
     *
     * @param userId
     * @param mcnId
     * @return
     */
    Map<Integer, Long> productSexStatistics(Long userId, Long mcnId);

    /**
     * 获取商品年龄分布
     *
     * @param userId
     * @param mcnId
     * @return
     */
    Map<String, Long> productAgeStatistics(Long userId, Long mcnId);

    /**
     * 获取商品购买人数统计
     *
     * @param userId
     * @param mcnId
     * @return
     */
    Long productBuyStatistics(Long userId, Long mcnId);

    /**
     * 查询MCN带货库商品--详情
     *
     * @param id
     * @param isIndependent
     * @return
     */
    SohuIndexProductModel getMcnIndexInfo(Long id, Boolean isIndependent);

    /**
     * 根据用户id获取前三带货商品
     *
     * @param userIds
     * @param mcnId
     * @return
     */
    Map<Long, List<SohuIndexProductModel>> getProductByUserIdAndMcnId(Collection<Long> userIds, Long mcnId);


//    /**
//     * MCN带货达人的商品排行榜
//     *
//     * @param bo
//     * @param pageQuery
//     * @return
//     */
//    @Deprecated
//    TableDataInfo<SohuIndexProductModel> getProductWindowTop(ShopProductWindowReqBo bo, PageQuery pageQuery);

//    /**
//     * 根据MCN机构id获取机构橱窗商品统计
//     *
//     * @param mcnId
//     * @return
//     */
//    @Deprecated
//    SohuProductWindowStatModel getProductWindowStatByMcnId(Long mcnId);

    /**
     * 查询商品列表
     */
    TableDataInfo<SohuProductModel> queryPageList(ShopProductReqBo bo, PageQuery pageQuery);

    /**
     * 查询商品列表-上架
     */
    TableDataInfo<SohuProductVo> queryPageListOfOnShelf(SohuProductAdBo bo, PageQuery pageQuery);

    /**
     * 查询商品列表
     */
    List<SohuProductModel> queryList(ShopProductReqBo bo);

    /**
     * 新增商品
     */
    Boolean insertByBo(SohuProductReqBo bo);

    /**
     * 修改商品
     */
    Boolean updateByBo(SohuProductReqBo bo);

    /**
     * 校验并批量删除商品信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 删除商品
     *
     * @param id   商品id
     * @param type 类型：recycle——回收站 delete——彻底删除
     * @return 删除结果
     */
    Boolean deleteByIdAndType(Long id, String type);

    /**
     * 恢复已删除商品
     *
     * @param id 商品id
     * @return 恢复结果
     */
    Boolean reStoreProduct(Long id);

    /**
     * 上下架商品
     *
     * @param bo 商品上下架操作对象
     */
    Boolean offOrPutShelf(SohuOffOrPutReqBo bo);

    /**
     * 商品分佣开关
     *
     * @param bo
     */
    Boolean independentIsShow(ShopProductReqBo bo);

    /**
     * 只查商品详情
     *
     * @param id 商品ID
     * @return {@link SohuProductModel}
     */
    SohuProductModel queryProductById(Long id);

    /**
     * 查询商品列表
     */
    List<SohuProductSyncVo> queryList();

    /**
     * 批量修改商品
     */
    Boolean updateBatchById(List<SohuProductSyncBo> productList);

    /**
     * 商品表同步一级分类
     *
     * @return 返回执行成功数量
     */
    int syncFirstCategoryToProduct();

    /**
     * 开放平台商品保存（开放平台）
     *
     * @param bo
     * @return
     */
    Boolean openProductSaveV1(SohuOpenProductSaveV1Bo bo);

    /**
     * 开放平台商品保存（开放平台）
     *
     * @param bo
     * @return
     */
    Boolean openProductSaveV2(SohuOpenProductSaveV2Bo bo);

    /**
     * 商品上下架修改（开放平台）
     *
     * @param bo
     * @return
     */
    Boolean openUpdateProductShelfV1(SohuOpenProductShelfEditV1Bo bo);

    /**
     * 商品上下架修改（开放平台）
     *
     * @param bo
     * @return
     */
    Boolean openUpdateProductShelfV2(SohuOpenProductShelfEditV2Bo bo);

    /**
     * 修改商品库存（开放平台）
     *
     * @param bo
     * @return
     */
    Boolean openUpdateProductStockV1(SohuOpenProductStockEditV1Bo bo);

    /**
     * 修改商品库存（开放平台）
     *
     * @param bo
     * @return
     */
    Boolean openUpdateProductStockV2(SohuOpenProductStockEditV2Bo bo);

    /**
     * 根据第三方商品唯一标识获取商品信息
     */
    SohuOpenProductV1Vo getBythirdProductIdV1(SohuOpenProductQueryV1Bo bo);

    /**
     * 根据第三方商品唯一标识获取商品信息
     */
    SohuOpenProductV2Vo getBythirdProductIdV2(SohuOpenProductQueryV2Bo bo);

    /**
     * 商品分佣费率计算
     */
    BigDecimal extractedGoodRation(SohuProductPmBo bo);

    /**
     * 获取商品规格列表
     */
    List<SohuProductAttrVo> getListByProductIdAndType(Long id, String type);

    /**
     * 短剧详情页小黄车商品列表
     */
    TableDataInfo<SohuProductVo> shopCartList(Long videoId, PageQuery pageQuery);

    /**
     * 上下架该剧的所有商品
     *
     * @param playletOperateBo
     * @return
     */
    Boolean operateBatchPlaylet(ShopOperateBatchBo playletOperateBo);


    /**
     * 短剧小黄车编辑集商品列表
     */
    TableDataInfo<SohuProductPlayletListVo> shopCartPlayletList(SohuPlayletShopCartQueryBo bo, PageQuery pageQuery);

    /**
     * 基于状态统计对应商品数量
     *
     * @param auditStatus
     * @return
     */
    Long countByAuditStatus(String auditStatus);

    /**
     * 商品总览统计定时任务
     */
    void productOverviewExecute(String day);


    /**
     * 通过id与状态查询商品
     *
     * @param ids
     * @param state
     * @return
     */
    List<SohuProductVo> queryGoodsByIds(List<Long> ids, String state);

    SohuProductVo get(Long id);

    /**
     * 基于商品id查询对应的分类id
     *
     * @param productId
     * @param level
     * @return
     */
    Long getCategoryIdByProductId(Long productId, Integer level);

    /**
     * 基于状态统计对应商品数量
     *
     * @param merId       商家id
     * @param auditStatus 审核状态
     * @return 商品数量
     */
    Long productCount(Long merId, String auditStatus);

    /**
     * 基于id查询商品明细
     *
     * @param id
     * @return
     */
    SohuProductVo getInfoById(Long id);

    /**
     * 处理机审结果
     *
     * @param busyCode
     * @param isPass
     * @param reason
     */
    void handleRobot(String busyCode, Boolean isPass, String reason);
}
