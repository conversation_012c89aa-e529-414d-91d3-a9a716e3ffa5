package com.sohu.shopgoods.api.bo;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 商品上下架修改
 *
 */
@Data
public class SohuOpenProductShelfEditV1Bo implements Serializable {

    @Schema(title = "第三方商品唯一标识",description = "用于查询修改商品操作",example = "12345678",maxLength = 64)
    @NotBlank(message = "第三方商品唯一标识不能为空")
    private String thirdProductId;

    @Schema(title = "狐小店商户名称", description = "狐小店商户名称可以去店铺查询接口获取，没有的店铺请联系管理员添加", example = "酷酷的小店")
    @NotNull(message = "狐小店商户名称不能为空")
    private String merchantName;

    @Schema(title = "是否上架",description = "true:上架，false:下架",example = "false")
    @NotNull(message = "是否上架不能为空")
    private Boolean isAdded;

    /**
     * 开放平台三方客户端ID
     */
    @Hidden
    private Long openClientId;

}
