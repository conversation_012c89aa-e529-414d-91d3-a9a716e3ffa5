package com.sohu.shopgoods.api.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品保障服务对象 sohu_product_guarantee
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_product_guarantee")
public class SohuProductGuaranteeModel extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @Schema(title = "id",example = "12345678")
    private Long id;
    /**
     * 保障条款名称
     */
    @Schema(title = "保障条款名称",example = "七天无理由退货")
    private String name;
    /**
     * 图标
     */
    @Schema(title = "icon", description = "单张，存储url", example = "https://xxx.oss-cn-beijing.aliyuncs.com/images/123.jpg")
    private String icon;
    /**
     * 条款内容
     */
    @Schema(title = "条款内容",example = "我是条款内容")
    private String content;
    /**
     * 排序
     */
    @Schema(title = "排序",example = "1")
    private Integer sort;
    /**
     * 显示状态
     */
    @Hidden
    private Integer isShow;
    /**
     * 是否删除
     */
    @Hidden
    private Boolean isDel;

    /**
     * 是否默认条款
     */
    @Schema(title = "是否默认条款",description = "默认条款为必选",example = "true")
    private Boolean isDefault;

    /**
     * 补充说明
     */
    @Schema(title = "补充说明",description = "富文本格式存储",example = "<p>我是补充说明<p>")
    private String description;

}
