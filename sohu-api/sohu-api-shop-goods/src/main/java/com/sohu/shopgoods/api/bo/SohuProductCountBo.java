package com.sohu.shopgoods.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 商品关联数量业务对象
 *
 * <AUTHOR>
 * @date 2023-06-26
 */

@Data
public class SohuProductCountBo implements Serializable {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 商品id
     */
    @NotNull(message = "商品id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 销量
     */
    @NotBlank(message = "销量不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long sales;

    /**
     * 库存
     */
    @NotBlank(message = "库存不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer stock;

    /**
     * 虚拟销量
     */
    @NotNull(message = "虚拟销量不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long varSales;

    /**
     * 浏览量
     */
    @NotNull(message = "浏览量不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long browse;

}
