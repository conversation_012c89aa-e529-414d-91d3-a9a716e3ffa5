package com.sohu.shopgoods.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/12/3 12:10
 */
@Data
public class SohuProductOverviewReportBo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "date",description = "日期",example = "2024-12-02")
    private String date;
    @Schema(name = "productNum",description = "新增商品数量",example = "1")
    private Integer productNum;
    @Schema(name = "productListingNum",description = "商品上架数量",example = "1")
    private Integer productListingNum;
    @Schema(name = "productDelistingNum",description = "商品下架数量",example = "1")
    private Integer productDelistingNum;
    @Schema(name = "productStockNum",description = "商品库存数量",example = "1")
    private Integer productStockNum;


}
