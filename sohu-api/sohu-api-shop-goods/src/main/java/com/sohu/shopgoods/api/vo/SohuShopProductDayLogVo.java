package com.sohu.shopgoods.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 商城商品每天数据日志视图对象
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@Data
@ExcelIgnoreUnannotated
public class SohuShopProductDayLogVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 日期
     */
    @ExcelProperty(value = "日期")
    private String date;

    /**
     * 新增商品数量
     */
    @ExcelProperty(value = "新增商品数量")
    private Long addProductNum;

    /**
     * 浏览量
     */
    @ExcelProperty(value = "浏览量")
    private Long pageView;

    /**
     * 收藏量
     */
    @ExcelProperty(value = "收藏量")
    private Long collectNum;

    /**
     * 加购件数
     */
    @ExcelProperty(value = "加购件数")
    private Long addCartNum;

    /**
     * 下单商品数
     */
    @ExcelProperty(value = "下单商品数")
    private Long orderProductNum;

    /**
     * 交易成功商品数
     */
    @ExcelProperty(value = "交易成功商品数")
    private Long orderSuccessProductNum;


}
