package com.sohu.shopgoods.api.domain;

import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * 商品pc后台修改业务对象
 *
 * <AUTHOR>
 * @date 2023-06-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuPcUpdateReqBo extends SohuEntity {

    private static final long serialVersionUID = -452373239606480650L;

    /**
     * 主键id--正常上下架必传
     */
    @NotNull(message = "商品id不能为空")
    private Long id;

    /**
     * 类型（add：添加虚拟销量，sub：减少虚拟销量，sort：修改排序）
     */
    @NotNull(message = "商品操作类型不能为空")
    private String type;

    /**
     * 变更虚拟商品数量或排序序号
     */
    @NotNull(message = "变更虚拟商品数量或排序序号不能为空")
    @Range(min = 1, max = 9999, message = "变更虚拟商品数量范围为1-9999")
    private Integer num;

}
