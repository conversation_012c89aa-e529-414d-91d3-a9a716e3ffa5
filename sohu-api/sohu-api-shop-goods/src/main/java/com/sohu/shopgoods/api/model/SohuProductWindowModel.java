package com.sohu.shopgoods.api.model;

import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 个人商品橱窗业务对象
 *
 * <AUTHOR>
 * @date 2023-11-15
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuProductWindowModel extends SohuEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 商品主键id集合-必传项
     */
    private List<Long> productIds;

    /**
     * 站点id
     */
    private Long siteId;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 狐小店商户mer_id
     */
    private Long merId;

    /**
     * mcn_id
     */
    private Long mcnId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 是否置顶 0不置顶 1置顶
     */
    private Boolean productTop;

    /**
     * 商品橱窗id
     */
    private Long windowId;

    /**
     * 销售量
     */
    private Integer saleCount;

    /**
     * 游览量
     */
    private Integer viewCount;

    /**
     * 曝光量
     */
    private Integer exposeCount;

    /**
     * 退货量
     */
    private Integer refundCount;

    /**
     * 个人商品橱窗业务对象集合
     */
    private List<SohuProductWindowModel> productWindowBoList;

}
