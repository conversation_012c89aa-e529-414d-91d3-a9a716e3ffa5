package com.sohu.shopgoods.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 分类与市场主体关联视图对象
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@ExcelIgnoreUnannotated
public class SohuCategoryMarketSubjectVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 分类id
     */
    @ExcelProperty(value = "分类id")
    private Long categoryId;

    /**
     * 市场类型   1. 个人店   2.个体户/企业店
     */
    @ExcelProperty(value = "市场类型   1. 个人店   2.个体户/企业店")
    private Integer marketType;

    /**
     * 1、开放展示  2、资质要求
     */
    @ExcelProperty(value = "1、开放展示  2、资质要求")
    private Integer type;


}
