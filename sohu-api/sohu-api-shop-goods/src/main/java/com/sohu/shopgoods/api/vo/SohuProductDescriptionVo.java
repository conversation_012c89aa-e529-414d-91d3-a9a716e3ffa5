package com.sohu.shopgoods.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 商品描述视图对象
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@Data
@ExcelIgnoreUnannotated
public class SohuProductDescriptionVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 商品ID
     */
    @ExcelProperty(value = "商品ID")
    private Long productId;

    /**
     * 商品详情
     */
    @ExcelProperty(value = "商品详情")
    private String description;

    /**
     * 商品类型
     */
    @ExcelProperty(value = "商品类型")
    private Integer type;


}
