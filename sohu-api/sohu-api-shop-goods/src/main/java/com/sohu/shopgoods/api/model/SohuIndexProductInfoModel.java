package com.sohu.shopgoods.api.model;

import com.sohu.common.core.vo.UserBaseVo;
import com.sohu.shopgoods.api.vo.SohuProductAttrVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

/**
 * 商品详情-首页
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SohuIndexProductInfoModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品属性
     */
    private List<SohuProductAttrVo> productAttr;

    /**
     * 商品属性详情
     */
    private HashMap<String, Object> productValue;

    /**
     * 商品信息
     */
    private SohuProductModel productInfo;

    /**
     * 商户信息
     */
    private SohuAppProductMerchantModel merchantInfo;

    /**
     * 收藏标识
     */
    private Boolean userCollect;

    /**
     * 保障服务
     */
    private List<SohuProductGuaranteeModel> guaranteeList;

    /**
     * 商户是否开启
     */
    private Boolean merchantStatus;

    /**
     * 商家用户信息
     */
    private UserBaseVo user;

    /**
     * 是否可售 0 否 1 是
     */
    private Boolean isSale;

}
