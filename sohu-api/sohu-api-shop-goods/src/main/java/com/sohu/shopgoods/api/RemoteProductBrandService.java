package com.sohu.shopgoods.api;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.bo.SohuProducrBrandPageQueryBo;
import com.sohu.shopgoods.api.domain.SohuProductBrandReqBo;
import com.sohu.shopgoods.api.vo.SohuBrandQualificationVo;
import com.sohu.shopgoods.api.vo.SohuProductBrandVo;

import java.util.Collection;
import java.util.List;

/**
 *  商品品牌
 * @author:<PERSON>
 * @create:2024/3/21 14:21
 **/
public interface RemoteProductBrandService {

//    /**
//     * 查询商品品牌列表
//     */
//    @Deprecated
//    TableDataInfo<SohuProductBrandModel> queryPageList(SohuProductBrandReqBo bo, PageQuery pageQuery);

    /**
     * 分页查询
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuProductBrandVo> queryPageList(SohuProducrBrandPageQueryBo bo, PageQuery pageQuery);

    /**
     * 新增商品品牌
     */
    Boolean insertByBo(SohuProductBrandReqBo bo);

    /**
     * 修改商品品牌
     */
    Boolean updateByBo(SohuProductBrandReqBo bo);

    /**
     * 校验并批量删除商品品牌信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询品牌列表
     *
     * @param brandIds
     * @return
     */
    List<SohuProductBrandVo> list(Collection<Long> brandIds);

    /**
     * 查询品牌对应的资质信息
     *
     * @param brandIds
     * @return
     */
    List<SohuBrandQualificationVo> listQualification(List<Long> brandIds);
}
