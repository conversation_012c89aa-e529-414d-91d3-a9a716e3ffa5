package com.sohu.shopgoods.api.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

/**
 * 商品详情
 *
 * <AUTHOR>
 * @date 2023-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SohuProductInfoModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品属性
     */
    private List<SohuProductAttrModel> productAttr;

    /**
     * 商品属性详情
     */
    private HashMap<String, Object> productValue;

    /**
     * 商品信息
     */
    private SohuProductModel productModel;

//    /**
//     *商户信息
//     */
//    private ProductMerchantResponse merchantInfo;

    /**
     * 收藏标识
     */
    private Boolean userCollect;

    /**
     * 保障服务
     */
    private List<SohuProductGuaranteeModel> guaranteeList;

}
