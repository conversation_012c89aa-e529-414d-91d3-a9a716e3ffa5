package com.sohu.shopgoods.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.math.BigDecimal;
import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 分类保证金关联业务对象
 *
 * <AUTHOR>
 * @date 2025-04-29
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuCategoryBondRelationBo extends SohuEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 类目id
     */
//    @NotNull(message = "类目id不能为空", groups = { EditGroup.class })
    private Long categoryId;

    /**
     * gmv金额
     */
    @NotNull(message = "gmv金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal gmvAmount;

    /**
     * 保证金金额
     */
    @NotNull(message = "保证金金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal bondAmount;

    /**
     * 是否删除  0.未删除 
     */
    @NotNull(message = "是否删除  0.未删除 不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isDel;


}
