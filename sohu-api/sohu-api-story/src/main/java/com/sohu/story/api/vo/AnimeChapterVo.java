package com.sohu.story.api.vo;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 小说章节对象
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Data
@ExcelIgnoreUnannotated
public class AnimeChapterVo {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private Long id;

    /**
     * 漫画小说ID
     */
    @ExcelProperty(value = "漫画小说ID")
    private Long anid;

    /**
     * 章节标题
     */
    @ExcelProperty(value = "章节标题")
    private String title;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String coverpic;

    /**
     * 第几章节
     */
    @ExcelProperty(value = "第几章节")
    private Long chaps;

    /**
     * 阅读人数
     */
    @ExcelProperty(value = "阅读人数")
    private Long readnums;

    /**
     * 点赞数量
     */
    @ExcelProperty(value = "点赞数量")
    private Long likes;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String info;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Date createdAt;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Date updatedAt;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Integer reviewStatus;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long userId;


}
