package com.sohu.middle.api.bo.risk;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/17 9:38
 */
@Data
public class RiskSyncCheckBo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "busyType", description = "用户-User", example = "User")
    private String busyType;
    @Schema(name = "busyCode", description = "业务码", example = "1000")
    private String busyCode;
    @Schema(name = "detectType", description = "检测类型  1.文本  2.图片 3.视频 4.音频 5.链接 6.文档", example = "1")
    private Integer detectType;
    @Schema(name = "fieldName", description = "业务场景名称", example = "昵称")
    private String fieldName;
    @Schema(name = "platform", description = "许愿狐-sohuglobal hi狐-hifocus", example = "sohuglobal")
    private String platform;
    @Schema(name = "content", description = "内容", example = "你好")
    private String content;
}
