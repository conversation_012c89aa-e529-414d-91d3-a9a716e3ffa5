package com.sohu.middle.api.bo.ai;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 用户模型业务对象
 *
 * <AUTHOR>
 * @date 2025-03-08
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuUserModelBo extends BaseEntity {

    /**
     * 用户模型关联id
     */
    @NotNull(message = "用户模型关联id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 对应的模型名称
     */
    @NotBlank(message = "对应的模型名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String modelName;

}
