package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.web.domain.SohuBaseVo;
import com.sohu.middle.api.aspect.RiskDetectionField;
import com.sohu.middle.api.enums.DetectTypeEnum;
import com.sohu.middle.api.enums.McnMediaContentTypeEnum;
import com.sohu.middle.api.enums.McnPublishMainStatusEnum;
import com.sohu.third.aliyun.airec.domain.AliyunAirecContentItem;
import com.sohu.third.aliyun.airec.domain.vo.ISohuAiRecResVo;
import com.sohu.third.aliyun.airec.domain.vo.SohuAiRecResultItemVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;


/**
 * 图文主体视图对象
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ExcelIgnoreUnannotated
public class SohuArticleVo extends SohuBaseVo implements Serializable, ISohuAiRecResVo {

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 作者id
     */
    @ExcelProperty(value = "作者id")
    private Long userId;

    /**
     * 站点ID
     */
    @ExcelProperty(value = "站点ID")
    private Long siteId;

    /**
     * 分类ID
     */
    @ExcelProperty(value = "分类ID")
    private Long categoryId;

    /**
     * 分类名
     */
    @ExcelProperty(value = "分类名")
    private String categoryName;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    @RiskDetectionField(detectType = DetectTypeEnum.TEXT)
    private String title;

    /**
     * 封面图
     */
    @ExcelProperty(value = "封面图")
    @RiskDetectionField(detectType = DetectTypeEnum.IMAGE)
    private String coverImage;

    /**
     * 子图
     */
    @ExcelProperty(value = "子图")
    private String images;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Long sortIndex;

    /**
     * {@link com.sohu.common.core.enums.CommonState}
     * 状态;Delete:删除，Edit:草稿，WaitApprove:待审核，OnShelf:上架，OffShelf:下架，CompelOff:强制下架，Private:仅自己可见，Refuse:已拒绝
     */
    @ExcelProperty(value = "状态;Delete:删除，Edit:草稿，WaitApprove：待审核,OnShelf：上架，OffShelf：下架，CompelOff：强制下架,Refuse：已拒绝")
    @Schema(name = "contentState", description = "作品状态", example = "WaitApprove-发布中,OnShelf-已发布,OffShelf-已下架(自主),CompelOff-已下架(强制),Delete-已删除(自主),ForceDelete-已删除(强制),Refuse-发布失败")
    private String state;

    /**
     * 审核状态（WaitApprove：审核中,Pass：已通过，Refuse：未通过）
     * {@link com.sohu.common.core.enums.CommonState}
     */
    private String auditState;

    /**
     * 驳回理由
     */
    private String rejectReason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 内容
     */
    @RiskDetectionField(detectType = DetectTypeEnum.RICH_TEXT)
    private String content;

    /**
     * 阅读数
     */
    private Integer viewCount = 0;

    /**
     * 评论数
     */
    private Integer commentCount = 0;

    /**
     * 点赞数
     */
    private Integer praiseCount = 0;

    /**
     * 收藏数
     */
    private Integer collectCount = 0;

    /**
     * 转发数
     */
    private Integer forwardCount = 0;

    /**
     * 关联项
     */
    private RelationRespVo relation;

    /**
     * 背景图片url
     */
    private String coverUrl;

    /**
     * 图文拓展数据
     */
    private SohuArticleInfoVo info;

    /**
     * 同步到草稿箱（蚁小二）：Y-是 ；N-否
     */
    private String syncDraft;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 是否点赞当前数据
     */
    private Boolean praiseObj = Boolean.FALSE;
    /**
     * 是否关注当前作者
     */
    private Boolean followObj = Boolean.FALSE;
    /**
     * 是否收藏当前数据
     */
    private Boolean collectObj = Boolean.FALSE;

    /**
     * 内容类型（蚁小二）:[Article-文章;MicroArticle-短内容]
     * {@link McnMediaContentTypeEnum}
     */
    private String mediaContentType;

    /**
     * 发布状态（蚁小二）[PushIng:同步中,AllSucceed:全部发布成功,PartSucceed:部分发布成功,Fail:全部发布失败]{@link McnPublishMainStatusEnum}
     */
    private String publishStatus;

    /**
     * 同步账号数（蚁小二）
     */
    private Integer platformNum;

    /**
     * 关联MCN(用户ID)
     */
    private Long mcnUserId;

    /**
     * 作品类型（普通视频-general，狐少少课堂-lesson）
     * {@link com.sohu.common.core.enums.VideoEnum}
     */
    private String type;

    /**
     * 初始学习人数
     */
    private Integer learnNum = 0;

    /**
     * 子任务编号
     */
    private String childTaskNumber;

    /**
     * 智能推荐物料信息
     */
    private AliyunAirecContentItem data;

    /**
     * 智能推荐返回结果详情(包含TraceId，TraceInfo)
     */
    private SohuAiRecResultItemVo resultItem;

    /**
     * 发布之前的唯一标识
     */
    private String publishMediaId;

    /**
     * 狐少少课堂标签id
     */
    private Long lessonLabelId;

    /**
     * 智能推荐返回结果详情(包含TraceId，TraceInfo)
     */
    private SohuAiRecResultItemVo aiResultItem;

    @Override
    public String getAiItemId() {
        return this.id == null ? null : String.valueOf(this.id);
    }

    /**
     * 作品可见类型(仅自己-only,公开-open)
     */
    private String visibleType;

    /**
     * 作品是否可分享:默认是true
     */
    private Boolean isShare = true;

    /**
     * 作品是否可下载:默认是true
     */
    private Boolean isDownload = true;


    /**
     * 提交次数
     */
    private Integer submitNum;

    /**
     * 提交场景
     */
    private String submitScene;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 作品状态
     */
    @Schema(description = "作品状态", example = "WaitApprove-审核中,OnShelf-已通过, Refuse-未通过")
    private String contentState;

    /**
     * 站点名称
     */
    @Schema(description = "站点名称", example = "武汉市")
    private String siteName;

    /**
     * 是否已申述
     */
    private Boolean appealStatus;

    /**
     * 申述原因
     */
    private String appealReason;

    /**
     * 删除时间
     */
    private Date delTime;

    /**
     * 作品类型(用于预览)
     */
    private String busyType;

    /**
     * 事件id
     */
    @Schema(description = "事件id", example = "CKSD-10-CT149173562657231742683-241231144155")
    private String eventId;

    /**
     * 移出时间
     */
    private Date removeTime;
}
