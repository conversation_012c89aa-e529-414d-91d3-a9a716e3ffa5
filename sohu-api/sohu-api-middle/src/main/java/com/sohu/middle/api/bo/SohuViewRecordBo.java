package com.sohu.middle.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 浏览记录业务对象
 *
 * <AUTHOR>
 * @date 2023-08-17
 */

@Data
public class SohuViewRecordBo implements Serializable {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;
    /**
     * 浏览人id
     */
    private Long userId;
    /**
     * 浏览表主键id
     */
    @NotNull(message = "浏览表主键id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long busyCode;

    /**
     * 浏览表类型
     */
    @NotBlank(message = "浏览表类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String busyType;
    /**
     * 业务归属人作者ID
     */
    private Long busyBelonger;
    /**
     * 浏览时间
     */
    private Date viewTime;
    /**
     * 是否是分销 {@link com.sohu.common.core.constant.Constants#Y}
     */
    private String distribution;
    /**
     * 分销人ID或分享人ID
     */
    private Long sharePerson;

}
