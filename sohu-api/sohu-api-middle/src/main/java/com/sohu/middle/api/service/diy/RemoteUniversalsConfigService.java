package com.sohu.middle.api.service.diy;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.diy.SohuUniversalsConfigBo;
import com.sohu.middle.api.vo.diy.SohuUniversalsConfigVo;

import java.util.List;

/**
 * 装修通用配置处理
 *
 * @Author: leibo
 * @Date: 2025/4/16 11:52
 **/
public interface RemoteUniversalsConfigService {
    
    
    TableDataInfo<SohuUniversalsConfigVo> list(SohuUniversalsConfigBo bo, PageQuery pageQuery);

    Long insertByBo(SohuUniversalsConfigBo bo);

    Long updateByBo(SohuUniversalsConfigBo bo);

    Boolean remove(List<Long> ids);
}
