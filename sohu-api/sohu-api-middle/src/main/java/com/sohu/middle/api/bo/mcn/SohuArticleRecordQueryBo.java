package com.sohu.middle.api.bo.mcn;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 蚁小二发布详情新增
 */
@Data
public class SohuArticleRecordQueryBo implements Serializable {

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 内容类型（蚁小二）:[Article-文章；Video-横版视频;MiniVideo-竖版视频；MicroArticle-短内容]
     * {@link McnMediaContentTypeEnum}
     */
    @Schema(description = "内容类型（蚁小二）:[Article-文章；Video-横版视频;MiniVideo-竖版视频；MicroArticle-短内容]")
    private String mediaContentType;

    @Schema(description = "用户id",hidden = true)
    private Long userId;

}
