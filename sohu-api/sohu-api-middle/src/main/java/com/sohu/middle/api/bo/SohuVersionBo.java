package com.sohu.middle.api.bo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 版本号业务对象
 *
 * <AUTHOR>
 * @date 2023-08-26
 */

@Data
public class SohuVersionBo implements Serializable {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 环境
     */
    private String env;

    /**
     * 环境版本
     */
    @NotBlank(message = "环境版本不能为空", groups = {AddGroup.class, EditGroup.class})
    private String envVersion;

    /**
     * 更新详细信息
     */
    @NotBlank(message = "更新详细信息不能为空", groups = {AddGroup.class, EditGroup.class})
    private String envVersionDesc;

    /**
     * 平台
     */
    @NotBlank(message = "平台不能为空", groups = {AddGroup.class, EditGroup.class})
    private String platform;

    /**
     * 是否强制弹窗(0:不强制，1:强制)
     */
    @NotNull(message = "是否强制弹窗(0:不强制，1:强制)不能为空", groups = {AddGroup.class, EditGroup.class})
    private Boolean popUp;

    /**
     * 是否强制更新(0:不强制，1:强制)
     */
    @NotNull(message = "是否强制更新(0:不强制，1:强制)不能为空", groups = {AddGroup.class, EditGroup.class})
    private Boolean renewal;

    /**
     * 下载链接
     */
    private String renewalUrl;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 环境版本编号
     */
    private String envVersionCode;

}
