package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 每日新增数据统计视图对象
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class SohuDailyReportVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private String id;

    /**
     * 统计时间(按天划分)
     */
    @ExcelProperty(value = "统计时间(按天划分)")
    private String date;

    /**
     * 新增任务数
     */
    @ExcelProperty(value = "新增任务数")
    private Long taskCount;

    /**
     * 任务趋势 大于0则上升  小于0则下降
     */
    private BigDecimal taskTrend;

    /**
     * 新增短视频数量
     */
    @ExcelProperty(value = "新增短视频数量")
    private Long videoCount;

    /**
     * 短视频趋势 大于0则上升  小于0则下降
     */
    private BigDecimal videoTrend;

    /**
     * 新增小说数量
     */
    @ExcelProperty(value = "新增小说数量")
    private Long novelCount;

    /**
     * 小说趋势 大于0则上升  小于0则下降
     */
    private BigDecimal novelTrend;

    /**
     * 新增短剧数量
     */
    @ExcelProperty(value = "新增短剧数量")
    private Long playletCount;

    /**
     * 短剧趋势 大于0则上升  小于0则下降
     */
    private BigDecimal playletTrend;

    /**
     * 新增诗文数量
     */
    @ExcelProperty(value = "新增诗文数量")
    private Long literatureCount;

    /**
     * 诗文趋势 大于0则上升  小于0则下降
     */
    private BigDecimal literatureTrend;

    public SohuDailyReportVo() {
    }

    public SohuDailyReportVo(String date, Long taskCount, BigDecimal taskTrend, Long videoCount, BigDecimal videoTrend, Long novelCount, BigDecimal novelTrend, Long playletCount, BigDecimal playletTrend, Long literatureCount, BigDecimal literatureTrend) {
        this.date = date;
        this.taskCount = taskCount;
        this.taskTrend = taskTrend;
        this.videoCount = videoCount;
        this.videoTrend = videoTrend;
        this.novelCount = novelCount;
        this.novelTrend = novelTrend;
        this.playletCount = playletCount;
        this.playletTrend = playletTrend;
        this.literatureCount = literatureCount;
        this.literatureTrend = literatureTrend;
    }
}
