package com.sohu.middle.api.service.finance;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.finance.SohuShareApplyAddBo;
import com.sohu.middle.api.bo.finance.SohuShareApplyBo;
import com.sohu.middle.api.bo.finance.SohuShareApplyQueryBo;
import com.sohu.middle.api.vo.finance.SohuShareApplyVo;
import com.sohu.middle.api.vo.finance.SohuShareSucceedVo;

import java.util.Collection;
import java.util.List;

/**
 * 金融分享-申请Service接口
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
public interface RemoteMiddleShareApplyService {

    /**
     * 查询金融分享-申请
     */
    SohuShareApplyVo queryById(Long id);

    /**
     * 查询金融分享-申请列表
     */
    TableDataInfo<SohuShareApplyVo> queryPageList(SohuShareApplyBo bo, PageQuery pageQuery);

    /**
     * 查询金融分享-状态申请中列表
     */
    TableDataInfo<SohuShareApplyVo> queryPageListOfApply(SohuShareApplyQueryBo bo, PageQuery pageQuery);

    /**
     * 查询金融分享-申请列表
     */
    List<SohuShareApplyVo> queryList(SohuShareApplyBo bo);

    /**
     * 修改金融分享-申请
     */
    Boolean insertByBo(SohuShareApplyAddBo bo);

    /**
     * 修改金融分享-申请
     */
    Boolean updateByBo(SohuShareApplyBo bo);

    /**
     * 校验并批量删除金融分享-申请信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 金融分享-订单及钱包-信用卡/贷款列表
     */
    TableDataInfo<SohuShareSucceedVo> shareSucceedList(String productType, PageQuery pageQuery);
}
