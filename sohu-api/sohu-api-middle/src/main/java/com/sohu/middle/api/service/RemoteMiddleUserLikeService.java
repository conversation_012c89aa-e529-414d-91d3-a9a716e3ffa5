package com.sohu.middle.api.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuBusyBO;
import com.sohu.middle.api.bo.SohuUserLikeAppBo;
import com.sohu.middle.api.bo.SohuUserLikeBo;
import com.sohu.middle.api.vo.SohuUserLikeVo;
import com.sohu.middle.api.vo.playlet.PlayletUserLikeAppVo;
import com.sohu.middle.api.vo.playlet.PlayletUserLikeVo;

import java.util.List;
import java.util.Map;

/**
 * 点赞
 */
public interface RemoteMiddleUserLikeService {

    /**
     * 批量删除用户的点赞
     */
    Boolean deleteWithValidByIds(List<Long> ids, Boolean isValid);

    //    /**
//     * 查询用户的点赞
//     */
//    SohuUserLikeVo queryById(Long id);
//
    /**
     * 查询用户的点赞列表
     */
    TableDataInfo<SohuUserLikeVo> queryPageList(SohuUserLikeBo bo, PageQuery pageQuery);

    /**
     * 查询用户的点赞列表
     */
    List<SohuUserLikeVo> queryList(SohuUserLikeBo bo);

    /**
     * 新增用户的点赞记录
     */
    Boolean insertByBo(SohuUserLikeBo bo);

    /**
     * 修改用户的点赞
     */
    Boolean updateByBo(SohuUserLikeBo bo);
//
//    /**
//     * 校验并批量删除用户的点赞信息
//     */
//    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 删除点赞记录
     *
     * @param bo
     * @return
     */
    Boolean deleteObject(SohuUserLikeBo bo);

    /**
     * 点赞与取消点赞
     * 返回结果说明
     * true=点赞成功
     * false=取消点赞成功
     */
    Boolean operate(SohuBusyBO bo);

    /**
     * 查询用户总的点赞数量
     *
     * @param userId 用户ID
     * @return {@link Long}
     */
    Long count(Long userId);

    /**
     * 查询数据，以map形式返回
     * key-value: Article_1:SohuUserLikeVo.class
     *
     * @param userId
     * @param busyCodes
     * @return
     */
    Map<String, SohuUserLikeVo> queryMap(Long userId, List<Long> busyCodes);

    /**
     * 查询数据，以map形式返回
     * key-value: 1:SohuUserLikeVo.class
     *
     * @param userId
     * @param busyType
     * @param busyCodes
     * @return
     */
    Map<Long, SohuUserLikeVo> queryMap(Long userId, String busyType, List<Long> busyCodes);

    /**
     * 查询数据，以map形式返回,查询busyType=Video,PlayletVideo
     * key-value: 1:SohuUserLikeVo.class
     *
     * @param userId
     * @param busyCodes
     * @return
     */
    Map<Long, SohuUserLikeVo> queryVideoMap(Long userId, List<Long> busyCodes);

    /**
     * 删除数据
     *
     * @param busyCode
     * @param busyType
     */
    void delete(Long busyCode, String busyType);
    /**
     * 点赞与取消点赞(短剧 该版本只做对整个剧的点赞)
     * 返回结果说明
     * true=点赞成功
     * false=取消点赞成功
     */
    Boolean like(Long id);

    /**
     * 点赞列表查询
     *
     * @return
     */
    TableDataInfo<PlayletUserLikeVo> getLikeList(PageQuery pageQuery);

    /**
     * 我的点赞-APP端
     *
     * @param bo        查询参数
     * @param pageQuery 分页
     * @return {@link TableDataInfo}
     */
    TableDataInfo<PlayletUserLikeAppVo> like(SohuUserLikeAppBo bo, PageQuery pageQuery);
}
