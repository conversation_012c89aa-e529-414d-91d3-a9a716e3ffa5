package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 版本号视图对象
 *
 * <AUTHOR>
 * @date 2023-08-26
 */
@Data
@ExcelIgnoreUnannotated
public class SohuVersionVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 环境
     */
    @ExcelProperty(value = "环境")
    private String env;

    /**
     * 环境版本
     */
    @ExcelProperty(value = "环境版本")
    private String envVersion;

    /**
     * 更新详细信息
     */
    @ExcelProperty(value = "更新详细信息")
    private String envVersionDesc;

    /**
     * 平台
     */
    @ExcelProperty(value = "平台")
    private String platform;

    /**
     * 是否强制弹窗(0:不强制，1:强制)
     */
    @ExcelProperty(value = "是否强制弹窗(0:不强制，1:强制)")
    private Boolean popUp;

    /**
     * 是否强制更新(0:不强制，1:强制)
     */
    @ExcelProperty(value = "是否强制更新(0:不强制，1:强制)")
    private Boolean renewal;

    /**
     * 下载链接
     */
    @ExcelProperty(value = "下载链接")
    private String renewalUrl;

    private Date createTime;

    /**
     * 环境版本编号
     */
    private String envVersionCode;

}
