package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.web.domain.SohuBaseVo;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 用户的收藏视图对象
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ExcelIgnoreUnannotated
public class SohuUserCollectVo extends SohuBaseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 内容id
     */
    @ExcelProperty(value = "内容id")
    private Long busyCode;

    /**
     * 收藏类型（Article,Video,Question,Answer,Project,BusyOrder）
     */
    @ExcelProperty(value = "收藏类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "Article,Video,Question,Answer,Project,BusyOrder")
    private String busyType;

}
