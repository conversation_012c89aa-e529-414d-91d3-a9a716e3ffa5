package com.sohu.middle.api.vo.im;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/13 17:47
 */
@Data
public class SohuImActivationTenantVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "id", description = "主键",example = "1")
    private Long id;

    @Schema(name = "activationCodeId", description = "激活码id",example = "1")
    private Long activationCodeId;


    @Schema(name = "userId", description = "用户id",example = "1")
    private Long userId;

    @Schema(name = "validStartTime", description = "有效开始时间",example = "2024-12-12")
    private Date validStartTime;

    @Schema(name = "validEndTime", description = "有效截止时间",example = "2024-12-12")
    private Date validEndTime;
}
