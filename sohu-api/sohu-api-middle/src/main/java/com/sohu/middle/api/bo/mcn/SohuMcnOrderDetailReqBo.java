package com.sohu.middle.api.bo.mcn;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * MCN下单详情请求对象
 */
@Data
public class SohuMcnOrderDetailReqBo implements Serializable {

    /**
     * 商品id（必填）
     */
    @NotNull(message = "商品ID不能为空")
    private Long productId;

    /**
     * 使用方用户ID(开通达人账号userId)（必填）
     */
    @NotNull(message = "达人账号userId不能为空")
    private Long useUserId;

}
