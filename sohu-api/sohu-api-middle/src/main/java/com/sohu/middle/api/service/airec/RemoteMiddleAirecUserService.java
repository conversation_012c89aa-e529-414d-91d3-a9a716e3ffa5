package com.sohu.middle.api.service.airec;

import com.sohu.middle.api.bo.airec.SohuAirecUserBo;
import com.sohu.middle.api.bo.airec.SohuAirecUserVisitorAddBo;

import java.util.Collection;

/**
 * 智能推荐用户表Service接口
 */
public interface RemoteMiddleAirecUserService{

    /**
     * 保存用户信息
     *
     * @param bo
     */
    void saveAirecUser(SohuAirecUserBo bo);

    /**
     * 未登录用户插入并推送至阿里云
     *
     * @param bo
     */
    void insertByNologin(SohuAirecUserVisitorAddBo bo);

    /**
     * 智能推荐-用户
     */
    Boolean initAirecUsers(Collection<SohuAirecUserBo> list);
}
