package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.middle.api.aspect.RiskDetectionField;
import com.sohu.middle.api.enums.DetectTypeEnum;
import lombok.Data;

import java.io.Serializable;


/**
 * 商户信息视图对象
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
@Data
@ExcelIgnoreUnannotated
public class SohuMerchantInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户信息ID
     */
    @ExcelProperty(value = "商户信息ID")
    private Long id;

    /**
     * 商户ID
     */
    @ExcelProperty(value = "商户ID")
    private Long merId;

    /**
     * 转账类型:bank-银行卡
     */
    @ExcelProperty(value = "转账类型:bank-银行卡")
    private String transferType;

    /**
     * 转账姓名
     */
    @ExcelProperty(value = "转账姓名")
    private String transferName;

    /**
     * 转账银行
     */
    @ExcelProperty(value = "转账银行")
    private String transferBank;

    /**
     * 转账银行卡号
     */
    @ExcelProperty(value = "转账银行卡号")
    private String transferBankCard;

    /**
     * 警戒库存
     */
    @ExcelProperty(value = "警戒库存")
    private Long alertStock;

    /**
     * 客服类型：H5-H5链接、phone-电话、message-Message,email-邮箱
     */
    @ExcelProperty(value = "客服类型：H5-H5链接、phone-电话、message-Message,email-邮箱")
    private String serviceType;

    /**
     * 客服H5链接
     */
    @ExcelProperty(value = "客服H5链接")
    @RiskDetectionField(detectType = DetectTypeEnum.Link)
    private String serviceLink;

    /**
     * 客服电话
     */
    @ExcelProperty(value = "客服电话")
    private String servicePhone;

    /**
     * 客服Message
     */
    @ExcelProperty(value = "客服Message")
    @RiskDetectionField(detectType = DetectTypeEnum.TEXT)
    private String serviceMessage;

    /**
     * 客服邮箱
     */
    @ExcelProperty(value = "客服邮箱")
    private String serviceEmail;


}
