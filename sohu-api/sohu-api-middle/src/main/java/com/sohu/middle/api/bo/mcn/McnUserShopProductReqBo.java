package com.sohu.middle.api.bo.mcn;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * MCN达人带货列表搜索
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class McnUserShopProductReqBo implements Serializable {

    private static final long serialVersionUID = 3481659942630712958L;

    /**
     * 关联MCN(用户ID)
     */
    private Long mcnUserId;

    /**
     * 达人（创作者）id
     */
    private String articleUserId;

    /**
     * 最低粉丝数
     */
    private Long minFansNum;

    /**
     * 最高粉丝数
     */
    private Long maxFansNum;

}
