package com.sohu.middle.api.vo.airec;

import lombok.Data;

import java.io.Serializable;

/**
 * 智能推荐用户对象
 * @author:<PERSON>
 * @create:2024/3/1 10:16
 **/
@Data
public class SohuAirecUserVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 用户唯一ID
     */
    private String userId;

    /**
     * 用户注册类型
     */
    private String userIdType;

    /**
     * 第三方用户名称
     */
    private String thirdUserName;

    /**
     * 第三方平台名称
     */
    private String thirdUserType;

    /**
     * 用户手机号的md5值
     */
    private String phoneMd5;

    /**
     * 用户设备ID
     */
    private String imei;

    /**
     * 用户内容
     */
    private String content;

    /**
     * 性别
     */
    private String gender;

    /**
     * 年龄
     */
    private String age;

    /**
     * 年龄段
     */
    private String ageGroup;

    /**
     * 国家
     */
    private String country;

    /**
     * 城市
     */
    private String city;

    /**
     * 最后登录IP
     */
    private String ip;

    /**
     * 设备型号
     */
    private String deviceModel;

    /**
     * 注册时间戳
     */
    private String registerTime;

    /**
     * 上次登录时间戳
     */
    private String lastLoginTime;

    /**
     * 用户信息的最后修改时间戳
     */
    private String lastModifyTime;

    /**
     * 用户tags
     */
    private String tags;

    /**
     * 用户来源
     */
    private String source;

    /**
     * 附加用户特征（字符串型）
     */
    private String features;

    /**
     * 附加用户特征（数值型）
     */
    private String numFeatures;
}
