package com.sohu.middle.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum InviteEnum {
    WAIT_JOIN("WAIT_JOIN", "待认证"),
    SUCCESS("SUCCESS", "邀请成功"),
    REFUSE("REFUSE", "邀请失败"),
    TIMEOUT("TIMEOUT", "超时未加入"),
    EXPIRED("EXPIRED", "已失效"),
    ;

    /**
     * 枚举代码值
     */
    private String code;

    /**
     * 枚举描述
     */
    private String msg;
}
