package com.sohu.middle.api.bo;

import com.sohu.common.core.web.domain.SohuBaseBo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 站点业务对象
 *
 * <AUTHOR>
 * @date 2023-06-21
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuSiteV2QueryBo extends SohuBaseBo {

    @Schema(name = "stationmasterPhone", description = "站长手机号", example = "13543211234")
    private String stationmasterPhone;

    @Schema(name = "platformIndustryId", description = "站长行业", example = "1")
    private Long platformIndustryId;

    @Schema(name = "id", description = "站长城市ID", example = "1")
    private Long id;

    @Schema(name = "stationmasterStatus", description = "站长状态:0=正常 1=禁用", example = "1")
    private Boolean stationmasterStatus;

}
