package com.sohu.middle.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 国际区号列表
 */
@Data
public class CountryCodeVo implements Serializable {

    @Schema(name = "name", description = "国家名称", example = "中国")
    private String name;

    @Schema(name = "code", description = "国家电话区号", example = "+86")
    private String code;

    @Schema(name = "countryAbbr", description = "国家缩写", example = "CN")
    private String countryAbbr;
}
