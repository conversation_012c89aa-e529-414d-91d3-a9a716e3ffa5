package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 用户签署协议详情
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
@Data
@ExcelIgnoreUnannotated
public class SohuUserAgreeInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 协议id
     */
    @Schema(name = "agreeId", description = "协议id", example = "1")
    private Long agreeId;

    /**
     * 协议名称
     */
    @Schema(name = "name", description = "协议名称", example = "平台服务协议")
    private String name;

    /**
     * 协议类型(SERVE=服务协议 PRIVACY=隐私协议 POWER=授权协议  BUSINESS=业务协议）
     */
    @Schema(name = "type", description = "协议类型", example = "SERVE")
    private String type;

    /**
     * 协议编号
     */
    @Schema(name = "agreeNumber", description = "协议编号", example = "xy101")
    private String agreeNumber;

    /**
     * 版本id
     */
    @Schema(name = "versionId", description = "版本id", example = "1")
    private Long versionId;


    /**
     * 协议备注
     */
    @Schema(name = "remark", description = "协议备注", example = "新增短剧付费业务")
    private String remark;

    /**
     * 版本号
     */
    @Schema(name = "versionNum", description = "versionNum", example = "v1.2")
    private String versionNum;

    /**
     * 版本说明
     */
    @Schema(name = "description", description = "description", example = "版本说明")
    private String description;

    /**
     * 协议内容
     */
    @Schema(name = "content", description = "content", example = "协议内容")
    private String content;

}
