package com.sohu.middle.api.bo;

import io.swagger.v3.oas.annotations.Hidden;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 用户标签关联业务对象保存
 *
 * <AUTHOR>
 * @date 2025-01-13
 */

@Data
public class SohuSaveUserLabelRelationBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @Hidden
    private Long userId;

    /**
     * 标签id(通用/行业)集合
     */
    @NotEmpty(message = "标签id(通用/行业)不能为空")
    private List<Long> labelIdList;

    /**
     * 标签类型(common-通用,industry-行业)
     */
    @NotBlank(message = "标签类型(common-通用,industry-行业)不能为空")
    private String labelType;

}
