package com.sohu.middle.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 新用户趋势图
 */
@Data
public class SohuNewUserReportVo implements Serializable {

    /**
     * 本条新增用户数
     */
    private Long newUserNum = 0L;

//    /**
//     * 上一条新增用户数
//     */
//    private Long lastUserNum = 0L;

    /**
     * 较上一条新增用户率
     */
    private BigDecimal newUserRatio = BigDecimal.ZERO;

    /**
     * 时间
     */
    private String time;

}
