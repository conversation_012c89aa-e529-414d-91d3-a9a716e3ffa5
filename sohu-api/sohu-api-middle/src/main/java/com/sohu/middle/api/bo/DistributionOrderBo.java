package com.sohu.middle.api.bo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/24 12:06
 **/
@Data
public class DistributionOrderBo implements Serializable {


    /**
     * 素材名称
     */
    private String title;

    /**
     * 狐少少系统单号,即订单号
     */
    private String payNumber;

    /**
     * 分账类型:
     * 商品-GOOD
     * 商单-BUSYTASK
     * 短剧-PLAYLET
     */
    private String tradeType;

    /**
     * 分账状态：0 未分账  1 已分账  2 分账处理中  3 分账异常  4 已退款
     */
    private Integer independentStatus;

    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 当前页数
     */
    private Integer pageNum;
    /**
     * 最小金额
     */
    private BigDecimal minAmount;
    /**
     * 最大金额
     */
    private BigDecimal maxAmount;
}
