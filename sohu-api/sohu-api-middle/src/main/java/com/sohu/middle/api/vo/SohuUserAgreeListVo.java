package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 用户协议视图对象
 *
 * <AUTHOR>
 * @date 2024-09-21
 */
@Data
@ExcelIgnoreUnannotated
public class SohuUserAgreeListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(name = "id", description = "主键id", example = "1")
    private Long id;

    /**
     * 协议编号
     */
    @Schema(name = "agreeNumber", description = "协议编号", example = "xy101")
    private String agreeNumber;

    /**
     * 协议名称
     */
    @Schema(name = "name", description = "协议名称", example = "平台服务协议")
    private String name;

    /**
     * 协议类型(SERVE=服务协议 PRIVACY=隐私协议 POWER=授权协议  BUSINESS=业务协议）
     */
    @Schema(name = "type", description = "协议类型", example = "SERVE")
    private String type;

    /**
     * 协议备注
     */
    @Schema(name = "remark", description = "协议备注", example = "新增短剧付费业务")
    private String remark;

    /**
     * 状态
     */
    @Schema(name = "state", description = "状态", example = "禁用")
    private String state;

    /**
     * 更新时间
     */
    @Schema(name = "updateTime", description = "更新时间", example = "2023-04-10 00:00:00")
    private Date updateTime;


}
