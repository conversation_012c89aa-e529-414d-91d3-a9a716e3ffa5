package com.sohu.middle.api.vo.playlet;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 海外单个短剧列表VO
 */
@Data
public class PlayletListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "playletVideoUrl", description = "短剧视频源", example = "default_avatar.mp4")
    private String playletVideoUrl;

    @Schema(name = "playletSortIndex", description = "短剧集数", example = "1")
    private Integer playletSortIndex;

//    @Schema(name = "playletIsPay", description = "是否需要付费 0不需要、1需要付费", example = "1")
//    private Boolean playletIsPay;

    @Schema(name = "paid", description = "是否已经付费，true=是，false=否", example = "false")
    private Boolean paid = false;

    @Schema(name = "playletEpisodePay", description = "当前集数是否需要付费 0不需要、1需要付费", example = "0")
    private Boolean playletEpisodePay;

    @Schema(name = "playetId", description = "剧集id", example = "1")
    private Long playetId;

    @Schema(name = "videoId", description = "视频ID", example = "1")
    private Long videoId;

    @Schema(name = "playetVideoKey", description = "视频加密key", example = "123dasda")
    private String playetVideoKey;

}
