package com.sohu.middle.api.vo.ai;

import lombok.Data;
import java.util.Date;



/**
 * 数据训练视图对象
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Data
public class SohuPracticeVo {

    private static final long serialVersionUID = 1L;

    /**
     * 训练数据id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 问题内容
     */
    private String question;

    /**
     * 需要训练的答案内容
     */
    private String answer;

    /**
     * 训练类型  1、OA相关 2、运营相关  3、其他
     */
    private Long practiceType;

    /**
     * 是否已训练 默认为0.未进行训练  1.已训练
     */
    private Integer isPractice;

    /**
     * 附加字段（提前预留）
     */
    private String practiceExt;


}
