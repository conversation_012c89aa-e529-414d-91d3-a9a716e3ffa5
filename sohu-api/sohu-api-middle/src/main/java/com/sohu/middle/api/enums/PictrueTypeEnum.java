package com.sohu.middle.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 图片类型枚举
 */
@Getter
@AllArgsConstructor
public enum PictrueTypeEnum {


    // 01：负责人或企业法人代表的身份证图片正面
    CertPhotoA("01", "身份证正面.jpg"),
    // 02：负责人或企业法人代表的身份证图片反面
    CertPhotoB("02", "身份证反面.jpg"),
    // 03：营业执照图片
    LicensePhoto("03", "营业执照图片.jpg"),
    // 05：开户许可证照片
    SettlePhoto("05", "开户许可证照片.jpg"),
    // 13：结算卡正面照
    SettlePhotoA("13", "结算卡正面.jpg"),
    // 14：结算卡反面照
    SettlePhotoB("14", "结算卡反面.jpg"),
    //15：法人护照人像面
    Passport("15", "法人护照人像面.jpg"),
    //17：法人港澳台通行证正面
    TaiwanPass("17", "法人港澳台通行证.jpg"),
    //19：法人其它证件照片(如外国人居留证)
    ForeignerPass("19", "外国人居留证.jpg"),
    //21：法人其它证件照片-港澳居民证
    MacaoResident("21", "港澳居民证.jpg"),
    //23：法人其它证件照片-台湾居民证
    TaiwanResident("23", "台湾居民证.jpg");

    private String code;
    private String name;

}
