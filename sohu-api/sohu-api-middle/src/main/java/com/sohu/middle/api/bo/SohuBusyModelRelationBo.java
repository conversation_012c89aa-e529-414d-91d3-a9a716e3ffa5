package com.sohu.middle.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.core.web.domain.SohuBaseBo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 生意模式关联业务对象
 *
 * <AUTHOR>
 * @date 2023-07-18
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuBusyModelRelationBo extends SohuBaseBo {

    /**
     * 扩展ID
     */
    @NotNull(message = "扩展ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 生意模式ID
     */
    @NotNull(message = "生意模式ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long modelId;

    /**
     * 业务类型
     */
    @NotBlank(message = "业务类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String busyType;

    /**
     * 行业ID
     */
    @NotNull(message = "行业ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long busyCode;


}
