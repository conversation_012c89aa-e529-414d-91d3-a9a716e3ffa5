package com.sohu.middle.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.io.Serializable;
import java.util.Date;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 用户标签关联业务对象
 *
 * <AUTHOR>
 * @date 2025-01-13
 */

@Data
public class SohuUserLabelRelationBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 标签id(通用/行业)
     */
    @NotNull(message = "标签id(通用/行业)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long labelId;

    /**
     * 标签类型(common-通用,industry-行业)
     */
    @NotBlank(message = "标签类型(common-通用,industry-行业)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String labelType;

}
