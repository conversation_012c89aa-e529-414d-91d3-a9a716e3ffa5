package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 广告标签关联视图对象
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@Data
@ExcelIgnoreUnannotated
public class SohuAdInfoLabelRelationVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 广告id
     */
    @ExcelProperty(value = "广告id")
    private Long adInfoId;

    /**
     * 标签id
     */
    @ExcelProperty(value = "标签id")
    private Long labelId;

    /**
     * 标签名称
     */
    private String labelName;

}
