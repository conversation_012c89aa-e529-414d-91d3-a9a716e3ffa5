package com.sohu.middle.api.vo.airec;

import lombok.Data;

import java.io.Serializable;

/**
 * 智能推荐物料视图对象
 *
 * <AUTHOR>
 * @date 2024-03-02
 */
@Data
public class SohuAirecContentItemVo implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 内容唯一标识ID
     */
    private String itemId;

    /**
     * 内容的类型
     */
    private String itemType;

    /**
     * 物品是否可以推荐出来;0不可1可
     */
    private String status;

    /**
     * 场景ID
     */
    private String sceneId;

    /**
     * 时长（秒级，大于等于0，小于 3600 * 10）
     */
    private String duration;

    /**
     * 内容发布时的时间戳（秒级）
     */
    private String pubTime;

    /**
     * 内容失效时间戳（秒级）
     */
    private String expireTime;

    /**
     * 内容信息的最后修改时间戳（秒级）
     */
    private String lastModifyTime;

    /**
     * 内容标题
     */
    private String title;

    /**
     * item的加权
     */
    private String weight;

    /**
     * 类目层级数，例如3级类目
     */
    private String categoryLevel;

    /**
     * 类目路径，下划线联接
     */
    private String categoryPath;

    /**
     * 标签，多个标签使用英文逗号分隔
     */
    private String tags;

    /**
     * 作者,多个作者使用英文逗号分隔
     */
    private String author;

    /**
     * 内容正文，通常为正文关键片段
     */
    private String content;

    /**
     * 内容对应的频道，为单值
     */
    private String channel;

    /**
     * 机构列表，多个标签使用英文逗号分隔
     */
    private String organization;

    /**
     * 一个月内曝光次数
     */
    private String pvCnt;

    /**
     * 一个月内点击次数
     */
    private String clickCnt;

    /**
     * 一个月内点赞次数
     */
    private String likeCnt;

    /**
     * 一个月内踩次数
     */
    private String unlikeCnt;

    /**
     * 一个月内评论次数
     */
    private String commentCnt;

    /**
     * 一个月内收藏次数
     */
    private String collectCnt;

    /**
     * 一个月内分享次数
     */
    private String shareCnt;

    /**
     * 一个月内下载次数
     */
    private String downloadCnt;

    /**
     * 一个月内打赏数
     */
    private String tipCnt;

    /**
     * 一个月内关注数
     */
    private String subscribeCnt;

    /**
     * 物料经由哪个平台进入场景
     */
    private String sourceId;

    /**
     * 国家编码
     */
    private String country;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 物料特征（字符串型）
     */
    private String features;

    /**
     * 物料特征（数值型）
     */
    private String numFeatures;

}
