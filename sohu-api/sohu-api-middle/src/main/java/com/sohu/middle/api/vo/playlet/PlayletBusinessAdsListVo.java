package com.sohu.middle.api.vo.playlet;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 短剧开屏广告vo
 *
 * <AUTHOR>
 */
@Data
public class PlayletBusinessAdsListVo implements Serializable {

    @Schema(name = "id", description = "主键id", example = "1")
    private Long id;

    @Schema(name = "playletId", description = "关联短剧id", example = "1")
    private Long playletId;

    @Schema(name = "playletTitle", description = "关联短剧标题", example = "1")
    private String playletTitle;

    @Schema(name = "episodeCount", description = "总集数", example = "1")
    private Integer episodeCount;

    @Schema(name = "episodeNumber", description = "广告位置", example = "1")
    private Integer episodeNumber;

    @Schema(name = "busyTaskId", description = "任务id", example = "1")
    private Long busyTaskId;

    @Schema(name = "busyTaskTitle", description = "任务名称", example = "【小红书】【【ICCU通告1⃣】小红书美妆护肤类达人寄拍需求】")
    private String busyTaskTitle;

    @Schema(name = "state", description = "发布状态", example = "OnShelf")
    private String state;

    @Schema(name = "startTime", description = "显示开始时间", example = "2023-04-10")
    private Date startTime;

    @Schema(name = "overTime", description = "显示结束时间", example = "2023-04-10")
    private Date overTime;

    @Schema(name = "userName", description = "发布人", example = "张小刚")
    private String userName;

    @Schema(name = "updateTime", description = "更新时间", example = "2023-04-10 00:00:00")
    private Date updateTime;
}
