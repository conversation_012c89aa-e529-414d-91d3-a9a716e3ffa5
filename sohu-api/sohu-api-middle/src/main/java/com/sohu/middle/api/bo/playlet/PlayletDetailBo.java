package com.sohu.middle.api.bo.playlet;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.common.core.web.domain.SohuBaseBo;
import com.sohu.middle.api.bo.SohuPlayletInfoBo;
import com.sohu.middle.api.bo.SohuVideoBo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.sohu.common.core.utils.DateUtils.TIME_ZONE_DEFAULT;

/**
 * 短剧详情bo
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PlayletDetailBo extends SohuBaseBo {

    @Schema(name = "id", description = "剧集id", example = "1")
    private Long id;

    @Schema(name = "title", description = "短剧标题", example = "大唐小神探")
    private String title;

    @Schema(name = "state", description = "状态", example = "Edit")
    private String state;

    @Schema(name = "categoryId", description = "分类id", example = "1")
    private Long categoryId;

    @Schema(name = "intro", description = "短剧简介", example = "Cao Yi is an ordinary factory worker his account. Although he didn't have much money, Cao Yi used it to live a better material life than before. Unexpectedly, shortly after he started living a good life, he encountered difficulties from the factory director.")
    private String intro;

    @Schema(name = "summary", description = "短剧概要", example = "Cao Yi is an ordinary factory worker his account. Although he didn't have much money, Cao Yi used it to live a better material life than before. Unexpectedly, shortly after he started living a good life, he encountered difficulties from the factory director.")
    private String summary;

    @Schema(name = "coverImage", description = "短剧封面图", example = "https://sohugloba.oss-cn-beijing.aliyuncs.com/2024/09/12/74dfa6640e65431d990d4afb0e70b504_232x310.jpg")
    private String coverImage;

    @Schema(name = "episodeCount", description = "总集数", example = "1")
    private Integer episodeCount;

    /**
     * 视频展示样式 0-横版 1-竖版
     */
    @Schema(name = "videoStyle", description = "视频展示样式", example = "0")
    private Integer videoStyle;

    /**
     * 默认语言 Chinese-中文、English-英文
     */
    @Schema(name = "language", description = "默认语言", example = "Chinese")
    private String language;

    @Schema(name = "startTime", description = "显示开始时间", example = "2023-04-10")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD, timezone = TIME_ZONE_DEFAULT)
    private Date startTime;

    @Schema(name = "overTime", description = "显示结束时间", example = "2023-04-10")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD, timezone = TIME_ZONE_DEFAULT)
    private Date overTime;

    @Schema(name = "isPayNumber", description = "开始收费集数", example = "1")
    private Integer isPayNumber;

    @Schema(name = "singlePrice", description = "单集价格", example = "10.00")
    private BigDecimal singlePrice;

    /**
     * 分销配置
     */
    @Schema(name = "isIndependent", description = "分销配置", example = "0")
    private Integer isIndependent;

    /**
     * 推广配置
     */
    @Schema(name = "sohuPlayletInfoBo", description = "推广配置", example = "")
    private SohuPlayletInfoBo sohuPlayletInfoBo;

    /**
     * 视频列表
     */
    @Schema(name = "videoBoList", description = "视频列表", example = "")
    private List<SohuVideoBo> videoBoList;
}
