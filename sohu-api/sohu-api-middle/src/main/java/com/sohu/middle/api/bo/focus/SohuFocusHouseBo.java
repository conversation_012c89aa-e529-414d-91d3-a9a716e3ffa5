package com.sohu.middle.api.bo.focus;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 房源主体业务对象 sohu_house
 *
 * <AUTHOR>
 * @date 2023-06-03
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuFocusHouseBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 房源名称
     */
    @NotBlank(message = "房源名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String houseName;

    /**
     * 房源别名
     */
    private String houseOtherName;

    /**
     * 房子总价
     */
    @NotNull(message = "房子总价不能为空", groups = {AddGroup.class, EditGroup.class})
    private String totalPrice;

    /**
     * 价格单位
     */
    @NotNull(message = "价格单位不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long unitName;

    /**
     * 物业费
     */
    private String propertyFee;

    /**
     * 地理位置
     */
    private String address;

    /**
     * 房屋类型id
     */
    @NotNull(message = "房屋类型id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long houseType;

    /**
     * 开发商名称
     */
    private String propertyDeveloper;

    /**
     * 房源照片
     */
    @NotNull(message = "房源照片不能为空", groups = {AddGroup.class, EditGroup.class})
    private String housePhoto;

    /**
     * 开盘时间
     */
    private String openTime;

    /**
     * 交房时间
     */
    private String completeTime;

    /**
     * 产权时间
     */
    @NotBlank(message = "产权时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private String propertyRightTime;

    /**
     * 建筑面积
     */
    private String acreage;

    /**
     * 主力户型
     */
    private String mainHouseType;

    /**
     * 状态
     */
    private String state;

    /**
     * 户型列表
     */
    private List<SohuFocusHouseTypeBo> houseTypeList;
    /**
     * 周边配套列表
     */
    private SohuFocusHouseAmbitusBo houseAmbitusList;
    /**
     * 站点id
     */
//    @NotNull(message = "站点id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long siteId;

    /**
     * 城市站点id
     */
//    @NotNull(message = "城市站点id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer citySiteId;

    /**
     * 驳回理由
     */
    private String rejectReason;

    /**
     * 根据访问量排序标识， hot=1
     */
    private int hot;

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 发布人昵称
     */
    private String userNickName;

    /**
     * 每平方单价
     */
    private Integer squarePrice;

    /**
     * 车库车位数量
     */
    private Integer parkingNumber;

    /**
     * 总楼层数
     */
    @NotNull(message = "总楼层数不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer totalFloor;

    /**
     * 所在楼层数
     */
    private Integer currentFloor;

    /**
     * 房源描述
     */
    @NotNull(message = "房源描述不能为空", groups = {AddGroup.class, EditGroup.class})
    private String houseInfo;

    /**
     * 联系方式
     */
    @NotNull(message = "联系方式不能为空", groups = {AddGroup.class, EditGroup.class})
    private String contactInformation;

    /**
     * 是否开启首页推荐
     */
    private Boolean homeRecommend;

    /**
     * 专栏排序
     */
    private Integer sortIndex;

    /**
     * 详细位置经纬度
     */
    private String location;

    /**
     * 汇率转换后的物业费
     */
    private String propertyRatePrice;

    /**
     * 汇率转换后的单价
     */
    private String squareRatePrice;
    /**
     * 推荐查询，未选择条件查询的话传0，首页推荐=1，专栏推荐=2
     */
    private int recommendQuery;
}
