package com.sohu.middle.api.bo.airec;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 智能推荐投流下单请求对象
 */
@Data
public class SohuAirecOrderReqBo implements Serializable {

    /**
     * 订单备注(预留字段)
     */
    private String mark;

    /**
     * 投流类型 BUSY_ORDER商单 VIDEO视频 ARTICLE图文 QUESTION问答 SHORT_VIDEO短剧
     */
    @NotBlank(message = "投流类型不能为空")
    private String type;

    /**
     * 投流二级类型 BUSY_ORDER商单 VIDEO视频 BUSINESS_VIDEO赚钱视频 ARTICLE图文 BUSINESS_ARTICLE赚钱图文 QUESTION问答 BUSINESS_QUESTION赚钱问答 SHORT_VIDEO短剧
     */
    @NotBlank(message = "投流二级类型不能为空")
    private String busyType;

    /**
     * 内容唯一标识ID
     */
    @NotNull(message = "内容唯一标识ID不能为空")
    private Long itemId;

    /**
     * 内容的类型 item商单任务 video视频（短剧） article图文 recipe问答
     */
    @NotBlank(message = "内容的类型不能为空")
    private String itemType;

    /**
     * 商品ID，自定义金融时不用传
     */
    private Long productId;

    /**
     * 投放金额,自定义金融时传
     */
    private BigDecimal price;

    /**
     * 投放人群类型： SMART-智能，CUSTOM-自定义
     */
    //@NotBlank(message = "投放人群类型不能为空")
    private String deliverPeopleType;

    /**
     * 性别:0-男，1-女 ，2-未知
     */
    private String gender;

    /**
     * 年龄段,多选,逗号分隔
     */
    private String ageGroup;

}
