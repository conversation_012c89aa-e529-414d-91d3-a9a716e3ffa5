package com.sohu.middle.api.vo.notice;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;



/**
 * 任务通知视图对象
 *
 * <AUTHOR>
 * @date 2024-08-13
 */
@Data
@ExcelIgnoreUnannotated
public class SohuTaskNoticeVo extends SohuEntity {

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 关联Id(任务详情跳转等)
     */
    @ExcelProperty(value = "关联Id(任务详情跳转等)")
    private String relateId;

    /**
     * 发送人Id
     */
    @ExcelProperty(value = "发送人Id")
    private Long senderId;

    /**
     * 接收人id
     */
    @ExcelProperty(value = "接收人id")
    private Long receiverId;

    /**
     * 通知标题(任务)
     */
    @ExcelProperty(value = "通知标题(任务)")
    private String title;

    /**
     * 通知内容
     */
    @ExcelProperty(value = "通知内容")
    private String content;

    /**
     * 通知类型(Push:发单方,Pull:接单方)
     */
    @ExcelProperty(value = "通知类型(Push:发单方,Pull:接单方)")
    private String noticeType;

    /**
     * 关联标题
     */
    @ExcelProperty(value = "关联标题")
    private String relateTitle;

    /**
     * 接单人昵称
     */
    private String receiverTaskNickName;

    /**
     * 附加参数
     */
    private String extValue;

}
