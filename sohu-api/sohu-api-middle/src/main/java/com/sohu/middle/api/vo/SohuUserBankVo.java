package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 用户银行卡列视图对象
 *
 * <AUTHOR>
 * @date 2023-07-04
 */
@Data
@ExcelIgnoreUnannotated
public class SohuUserBankVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 收款人名称
     */
    @ExcelProperty(value = "收款人名称")
    private String payeeName;

    /**
     * 开户行
     */
    @ExcelProperty(value = "开户行")
    private String openBankName;

    /**
     * 银行卡号
     */
    @ExcelProperty(value = "银行卡号")
    private String openBankNo;

    /**
     * 是否删除
     */
    @ExcelProperty(value = "是否删除")
    private String deleted;


}
