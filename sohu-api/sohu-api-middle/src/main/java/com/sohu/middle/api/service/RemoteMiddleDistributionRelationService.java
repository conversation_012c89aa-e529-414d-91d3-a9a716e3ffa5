package com.sohu.middle.api.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuDistributionRelationBo;
import com.sohu.middle.api.vo.SohuDistributionRelationVo;

import java.util.Collection;
import java.util.List;

public interface RemoteMiddleDistributionRelationService {

    /**
     * 查询分销关系
     */
    SohuDistributionRelationVo queryById(Long id);

    /**
     * 查询分销关系列表
     */
    TableDataInfo<SohuDistributionRelationVo> queryPageList(SohuDistributionRelationBo bo, PageQuery pageQuery);

    /**
     * 查询分销关系列表
     */
    List<SohuDistributionRelationVo> queryList(SohuDistributionRelationBo bo);

    /**
     * 修改分销关系
     */
    Boolean insertByBo(SohuDistributionRelationBo bo);

    /**
     * 修改分销关系
     */
    Boolean updateByBo(SohuDistributionRelationBo bo);

    /**
     * 校验并批量删除分销关系信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
