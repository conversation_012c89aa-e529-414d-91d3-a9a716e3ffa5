package com.sohu.middle.api.service.mcn;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.mcn.SohuMcnStarBo;
import com.sohu.middle.api.vo.mcn.SohuMcnStarVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

public interface RemoteMiddleMcnStarService {

    /**
     * 查询mcn达人采集信息
     */
    SohuMcnStarVo queryById(Long id);

    /**
     * 查询mcn达人采集信息列表
     */
    TableDataInfo<SohuMcnStarVo> queryPageList(SohuMcnStarBo bo, PageQuery pageQuery);

    /**
     * 查询mcn达人采集信息列表
     */
    List<SohuMcnStarVo> queryList(SohuMcnStarBo bo);

    /**
     * 修改mcn达人采集信息
     */
    Boolean insertByBo(SohuMcnStarBo bo);

    /**
     * 修改mcn达人采集信息
     */
    Boolean updateByBo(SohuMcnStarBo bo);

    /**
     * 校验并批量删除mcn达人采集信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 导入数据
     * @param file
     */
    void importData(MultipartFile file);
}
