package com.sohu.middle.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户协议业务对象
 *
 * <AUTHOR>
 * @date 2024-09-21
 */

@Data
public class SohuUserAgreeQueryBo implements Serializable {

    /**
     * 协议名称
     */
    @Schema(name = "name", description = "协议名称", example = "平台服务协议")
    private String name;

    /**
     * 协议类型(SERVE=服务协议 PRIVACY=隐私协议 POWER=授权协议  BUSINESS=业务协议）
     */
    @Schema(name = "type", description = "协议类型", example = "SERVE")
    private String type;

    /**
     * 状态 OnShelf-启用 OffShelf-禁用 Edit-待启用
     */
    @Schema(name = "state", description = "状态", example = "OnShelf")
    private String state;

}
