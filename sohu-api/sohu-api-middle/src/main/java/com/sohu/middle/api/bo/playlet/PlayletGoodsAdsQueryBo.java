package com.sohu.middle.api.bo.playlet;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 短剧开屏广告bo
 *
 * <AUTHOR>
 */
@Data
public class PlayletGoodsAdsQueryBo implements Serializable {

    @Schema(name = "playletId", description = "短剧id", example = "1")
    private Long playletId;

    @Schema(name = "playletTitle", description = "短剧名称", example = "1")
    private String playletTitle;

    @Schema(name = "episodeNumber", description = "选择集数", example = "1")
    private Integer episodeNumber;

    @Schema(name = "productName", description = "商品名称", example = "国潮印花T恤")
    private String productName;

    @Schema(name = "userName", description = "发布人", example = "张小刚")
    private String userName;

    @Schema(name = "state", description = "发布状态", example = "WaitShelf:待上架,OnShelf:上架,OffShelf:下架,Exceed:已过期")
    private String state;

    @Schema(name = "updateStartTime", description = "更新开始时间", example = "2023-04-10 00:00:00")
    private String updateStartTime;

    @Schema(name = "updateEndTime", description = "更新结束时间", example = "2023-04-10 00:00:00")
    private String updateEndTime;

    /**
     * 排序字段(state--最新发布,startTimeAsc--显示开始时间升序,startTimeDesc--显示开始时间降序,overTimeAsc--显示结束时间升序,overTimeDesc--显示结束时间降序，updateTimeAsc--更新时间升序,updateTimeDesc--更新时间降序)
     */
    @Schema(name = "sortBy", description = "排序字段", example = "2023-04-10 00:00:00")
    private String sortBy;
}
