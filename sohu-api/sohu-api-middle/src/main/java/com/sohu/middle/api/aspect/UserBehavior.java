package com.sohu.middle.api.aspect;

import com.sohu.middle.api.enums.behavior.BehaviorBusinessTypeEnum;
import com.sohu.middle.api.enums.behavior.OperaTypeEnum;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/2/28 10:25
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface UserBehavior {

    BehaviorBusinessTypeEnum busyType() default BehaviorBusinessTypeEnum.VIDEO;

    OperaTypeEnum operType() default OperaTypeEnum.LIST;
}
