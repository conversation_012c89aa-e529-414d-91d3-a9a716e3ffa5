package com.sohu.middle.api.service.mcn;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.mcn.SohuMcnProductBo;
import com.sohu.middle.api.vo.mcn.SohuMcnProductVo;

import java.util.Collection;
import java.util.List;

public interface RemoteMiddleMcnProductService {

    /**
     * 获取展示的MCN会员商品
     * @return
     */
    List<SohuMcnProductVo> getListOfShow();

    /**
     * 查询MCN会员套餐
     */
    SohuMcnProductVo queryById(Long id);

    /**
     * 查询MCN会员套餐列表
     */
    TableDataInfo<SohuMcnProductVo> queryPageList(SohuMcnProductBo bo, PageQuery pageQuery);

    /**
     * 查询MCN会员套餐列表
     */
    List<SohuMcnProductVo> queryList(SohuMcnProductBo bo);

    /**
     * 修改MCN会员套餐
     */
    Boolean insertByBo(SohuMcnProductBo bo);

    /**
     * 修改MCN会员套餐
     */
    Boolean updateByBo(SohuMcnProductBo bo);

    /**
     * 校验并批量删除MCN会员套餐信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
