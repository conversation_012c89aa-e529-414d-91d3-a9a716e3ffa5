package com.sohu.middle.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuBaseBo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import com.sohu.common.core.web.domain.BaseEntity;

import java.util.List;

/**
 * #文学拓展业务对象
 *
 * <AUTHOR>
 * @date 2024-11-22
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuLiteratureInfoBo extends SohuBaseBo {

    /**
     * 主键
     */
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 站点ID
     */
    @NotBlank(message = "站点ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long siteId;

    /**
     * 文学ID
     */
    @NotBlank(message = "文学ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long literatureId;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String content;

    /**
     * 阅读数
     */
    @NotBlank(message = "阅读数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer viewCount;

    /**
     * 评论数
     */
    @NotBlank(message = "评论数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer commentCount;

    /**
     * 点赞数
     */
    @NotBlank(message = "点赞数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer praiseCount;

    /**
     * 收藏数
     */
    @NotBlank(message = "收藏数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer collectCount;

    /**
     * 转发数
     */
    @NotBlank(message = "转发数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer forwardCount;
    /**
     * 文学ID集合
     */
    private List<Long> literatureIds;
}
