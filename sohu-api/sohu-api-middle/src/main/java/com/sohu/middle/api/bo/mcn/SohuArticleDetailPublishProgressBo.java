package com.sohu.middle.api.bo.mcn;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * (蚁小二)发布进度更新
 */
@Data
public class SohuArticleDetailPublishProgressBo implements Serializable {

    /**
     * 平台账号id
     */
    @NotNull(message = "平台账号id不能为空")
    @Schema(description = "平台账号id")
    private String platformAccountId;

    /**
     * 自媒体平台id
     */
    @NotNull(message = "自媒体平台id不能为空")
    @Schema(description = "自媒体平台id")
    private Long platformId;

    /**
     * 任务id 记录那一次发文
     */
    @NotNull(message = "任务id 不能为空")
    @Schema(description = "任务id 记录那一次发文")
    private String taskId;

    /**
     * 发布进度
     */
    @NotNull(message = "发布进度不能为空")
    @Schema(description = "发布进度")
    private Integer progress;

    /**
     * 进度描述
     */
    @Schema(description = "进度描述")
    private String msg;

}
