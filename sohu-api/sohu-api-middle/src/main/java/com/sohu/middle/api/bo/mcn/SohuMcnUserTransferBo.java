package com.sohu.middle.api.bo.mcn;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * MCN机构成员转移记录业务对象
 *
 * <AUTHOR>
 * @date 2024-04-08
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuMcnUserTransferBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     *数据来源id
     */
    @NotNull(message = "数据来源id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sourceId;

    /**
     * 关联MCN(用户ID)（原）
     */
    //@NotNull(message = "关联MCN(用户ID)（原）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long mcnId;

    /**
     * MCN名称（原）
     */
    //@NotBlank(message = "MCN名称（原）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mcnName;

    /**
     * 目标MCN机构登录账号
     */
    @NotBlank(message = "目标MCN机构登录账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String targetLoginAccount;

    /**
     * 目标MCNID（新）
     */
    //@NotNull(message = "目标MCNID（新）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long targetMcnId;

    /**
     * 目标MCN名称（新）
     */
    //@NotBlank(message = "目标MCN名称（新）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String targetMcnName;

//    /**
//     * 用户ID
//     */
//    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
//    private Long userId;
//
//    /**
//     * 用户昵称
//     */
//    @NotBlank(message = "用户昵称不能为空", groups = { AddGroup.class, EditGroup.class })
//    private String nickName;
//
//    /**
//     * 达人手机号码
//     */
//    @NotBlank(message = "达人手机号码不能为空", groups = { AddGroup.class, EditGroup.class })
//    private String phoneNumber;

    /**
     * 备注
     */
    //@NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;

    /**
     * 短信验证码
     */
    @NotBlank(message = "短信验证码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String smsCode;


}
