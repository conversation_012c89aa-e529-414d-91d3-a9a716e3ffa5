package com.sohu.middle.api.bo.mcn;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 更新账号登录状态
 */
@Data
public class SohuArticleAccountLoginStatusBo implements Serializable {

    /**
     * 蚁小二与自媒体绑定ID(账号id)
     */
    @NotBlank(message = "账号id不能为空")
    private String accountId;

    /**
     * 自媒体平台id
     */
    @NotNull(message = "自媒体平台id不能为空")
    private Long platformId;

    /**
     * 登录状态;登录成功:Login,登陆失效,LogOut {@link McnAccountLoginStatusEnum}
     */
    private String loginStatus;

    private static final long serialVersionUID = 1L;
}
