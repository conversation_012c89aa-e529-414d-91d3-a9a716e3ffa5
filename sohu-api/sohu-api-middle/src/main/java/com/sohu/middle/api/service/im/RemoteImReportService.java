package com.sohu.middle.api.service.im;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.im.SohuImReportBo;
import com.sohu.middle.api.bo.im.SohuImReportQueryBo;
import com.sohu.middle.api.vo.im.SohuImReportVo;

import java.util.Collection;
import java.util.List;

/**
 * IM举报信息Service接口
 *
 * <AUTHOR>
 * @date 2024-12-23
 */
public interface RemoteImReportService {

    /**
     * 查询IM举报信息
     */
    SohuImReportVo queryById(Long id);

    /**
     * 查询IM举报信息列表
     */
    TableDataInfo<SohuImReportVo> queryPageList(SohuImReportBo bo, PageQuery pageQuery);

    /**
     * 查询app举报信息列表-审核
     */
    TableDataInfo<SohuImReportVo> queryPageListOfAudit(SohuImReportQueryBo bo, PageQuery pageQuery);

    /**
     * 查询IM举报信息列表
     */
    List<SohuImReportVo> queryList(SohuImReportBo bo);

    /**
     * 修改IM举报信息
     */
    Boolean insertByBo(SohuImReportBo bo);

    /**
     * 修改IM举报信息
     */
    Boolean updateByBo(SohuImReportBo bo);

    /**
     * 保存IM举报信息
     * @param bo
     * @return
     */
    Boolean saveByBo(SohuImReportBo bo);

    /**
     * 校验并批量删除IM举报信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
