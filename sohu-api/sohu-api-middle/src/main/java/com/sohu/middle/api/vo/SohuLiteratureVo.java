package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuBaseVo;
import com.sohu.middle.api.aspect.RiskDetectionField;
import com.sohu.middle.api.enums.DetectTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 文学主体视图对象
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Data
@ExcelIgnoreUnannotated
public class SohuLiteratureVo extends SohuBaseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 作者id
     */
    @ExcelProperty(value = "作者id")
    private Long userId;

    /**
     * 分类ID
     */
    @ExcelProperty(value = "分类ID")
    private Long categoryId;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    @RiskDetectionField(detectType = DetectTypeEnum.TEXT)
    private String title;

    /**
     * 分类名
     */
    private String categoryName;
    /**
     * 封面图
     */
    @RiskDetectionField(detectType = DetectTypeEnum.IMAGE)
    private String coverImage;
    /**
     * 排序
     */
    private Long sortIndex;
    /**
     * {@link com.sohu.common.core.enums.CommonState}
     * 状态;Delete:删除，Edit:草稿，WaitApprove:待审核，OnShelf:上架，OffShelf:下架，CompelOff:强制下架，Private:仅自己可见，Refuse:已拒绝
     */
    private String state;
    /**
     * 审核状态（WaitApprove：审核中,Pass：已通过，Refuse：未通过）
     * {@link com.sohu.common.core.enums.CommonState}
     */
    private String auditState;
    /**
     * 驳回理由
     */
    private String rejectReason;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 内容
     */
    @RiskDetectionField(detectType = DetectTypeEnum.RICH_TEXT)
    private String content;
    /**
     * 阅读数
     */
    private Integer viewCount = 0;
    /**
     * 评论数
     */
    private Integer commentCount = 0;
    /**
     * 点赞数
     */
    private Integer praiseCount = 0;
    /**
     * 收藏数
     */
    private Integer collectCount = 0;
    /**
     * 转发数
     */
    private Integer forwardCount = 0;
    /**
     * 作品类型（诗歌-poetry，散文-prose）
     */
    private String type;
    /**
     * 文学拓展数据
     */
    private SohuLiteratureInfoVo info;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户头像
     */
    private String userAvatar;
    /**
     * 是否点赞当前数据
     */
    private Boolean praiseObj = Boolean.FALSE;
    /**
     * 是否关注当前作者
     */
    private Boolean followObj = Boolean.FALSE;
    /**
     * 是否收藏当前数据
     */
    private Boolean collectObj = Boolean.FALSE;
    /**
     * 站点ID
     */
    private Long siteId;
    /**
     * 提交次数
     */
    private Integer submitNum;

    /**
     * 提交场景
     */
    private String submitScene;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 是否已申述
     */
    private Boolean appealStatus;

    /**
     * 申述原因
     */
    private String appealReason;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 删除时间
     */
    private Date delTime;

}
