package com.sohu.middle.api.bo.mcn;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * mcn达人采集信息业务对象
 *
 * <AUTHOR>
 * @date 2024-06-13
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuMcnStarBo extends SohuEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 达人LOGO
     */
    @NotBlank(message = "达人LOGO不能为空", groups = { AddGroup.class, EditGroup.class })
    private String avatar;

    /**
     * 达人昵称
     */
    @NotBlank(message = "达人昵称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String nickName;

    /**
     * 达人ID
     */
    @NotBlank(message = "达人ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String authorId;

    /**
     * 粉丝总量
     */
    @NotBlank(message = "粉丝总量不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fansTotalNum;

    /**
     * 粉丝增量
     */
    @NotBlank(message = "粉丝增量不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fansAddNum;

    /**
     * 获赞总量
     */
    @NotBlank(message = "获赞总量不能为空", groups = { AddGroup.class, EditGroup.class })
    private String likeTotalNum;

    /**
     * 30天销售额
     */
    @NotBlank(message = "30天销售额不能为空", groups = { AddGroup.class, EditGroup.class })
    private String monthSaleAmount;

    /**
     * 直播场次
     */
    @NotBlank(message = "直播场次不能为空", groups = { AddGroup.class, EditGroup.class })
    private String liveNum;

    /**
     * 直播平均场观
     */
    @NotBlank(message = "直播平均场观不能为空", groups = { AddGroup.class, EditGroup.class })
    private String liveAudienceNum;

    /**
     * 场均带货数
     */
    @NotBlank(message = "场均带货数不能为空", groups = { AddGroup.class, EditGroup.class })
    private String liveGoodsNum;

    /**
     * 场均销售额
     */
    @NotBlank(message = "场均销售额不能为空", groups = { AddGroup.class, EditGroup.class })
    private String liveSaleAmount;

    /**
     * 达人简介
     */
    @NotBlank(message = "达人简介不能为空", groups = { AddGroup.class, EditGroup.class })
    private String description;

    /**
     * 主营类型
     */
    @NotBlank(message = "主营类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String meritoType;

    /**
     * 达人类型
     */
    @NotBlank(message = "达人类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String talentType;

    /**
     * MCN机构
     */
    @NotBlank(message = "MCN机构不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mcnName;

    /**
     * 认证信息
     */
    @NotBlank(message = "认证信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String authInfo;


}
