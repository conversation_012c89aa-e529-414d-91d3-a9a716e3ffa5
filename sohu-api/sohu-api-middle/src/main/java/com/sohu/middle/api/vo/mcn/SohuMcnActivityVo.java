package com.sohu.middle.api.vo.mcn;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * MCN活动视图对象
 *
 * <AUTHOR>
 * @date 2024-03-30
 */
@Data
@ExcelIgnoreUnannotated
public class SohuMcnActivityVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 活动标题
     */
    @ExcelProperty(value = "活动标题")
    private String title;

    /**
     * 活动封面
     */
    @ExcelProperty(value = "活动封面")
    private String coverUrl;

    /**
     * 活动内容
     */
    @ExcelProperty(value = "活动内容")
    private String content;

    /**
     * 活动开始时间
     */
    @ExcelProperty(value = "活动开始时间")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ExcelProperty(value = "活动结束时间")
    private Date endTime;

    /**
     * 状态（WAIT=即将开始，UNDER_WAY=进行中，END=已结束）
     * {@link com.sohu.dao.enums.McnActivityStateEnum}
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "WAIT=即将开始，UNDER_WAY=进行中，END=已结束")
    private String state;

    /**
     * 站点集合，0代表全部
     */
    private List<SohuMcnActivitySiteVo> siteList;

}
