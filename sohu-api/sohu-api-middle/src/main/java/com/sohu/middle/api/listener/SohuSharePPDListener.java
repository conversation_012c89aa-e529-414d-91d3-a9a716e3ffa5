package com.sohu.middle.api.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.sohu.middle.api.bo.finance.SohuSharePPDImportBo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 拍拍贷
 * 自定义监听器，对导入excel中的数据进行校验
 */
public class SohuSharePPDListener extends AnalysisEventListener {

    List<String> times = new ArrayList<>();
    List<String> customerPhones = new ArrayList<>();
    List<BigDecimal> amounts = new ArrayList<>();


    /**
     * 每解析一行，回调该方法
     *
     * @param data
     * @param context
     */
    @Override
    public void invoke(Object data, AnalysisContext context) {
        String time = ((SohuSharePPDImportBo) data).getTime();
        String customerPhone = ((SohuSharePPDImportBo) data).getCustomerPhone();
        BigDecimal amount = ((SohuSharePPDImportBo) data).getAmount();


        if (StrUtil.isBlank(time)) {
            throw new RuntimeException(String.format("第%s行日期为空，请核实", context.readRowHolder().getRowIndex() + 1));
        }
        if (StrUtil.isBlank(customerPhone)) {
            throw new RuntimeException(String.format("第%s行手机号为空，请核实", context.readRowHolder().getRowIndex() + 1));
        }
        if (Objects.isNull(amount)) {
            throw new RuntimeException(String.format("第%s行借款金额为空，请核实", context.readRowHolder().getRowIndex() + 1));
        } else {
            amounts.add(amount);
            customerPhones.add(customerPhone);
            times.add(customerPhone);
        }
    }


    /**
     * 出现异常回调
     *
     * @param exception
     * @param context
     * @throws Exception
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        if (exception instanceof ExcelDataConvertException) {
            /**从0开始计算*/
            Integer columnIndex = ((ExcelDataConvertException) exception).getColumnIndex() + 1;
            Integer rowIndex = ((ExcelDataConvertException) exception).getRowIndex() + 1;
            String message = "第" + rowIndex + "行，第" + columnIndex + "列" + "数据为空，请核实";
            throw new RuntimeException(message);
        } else if (exception instanceof RuntimeException) {
            throw exception;
        } else {
            super.onException(exception, context);
        }
    }

    /**
     * 解析完,全部回调
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        //解析完,全部回调逻辑实现
        amounts.clear();
        times.clear();
        customerPhones.clear();
    }
}


