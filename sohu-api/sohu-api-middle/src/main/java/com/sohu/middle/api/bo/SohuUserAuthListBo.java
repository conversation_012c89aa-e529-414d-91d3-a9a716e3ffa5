package com.sohu.middle.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.core.web.domain.SohuBaseBo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 用户认证管理业务对象
 *
 * <AUTHOR>
 * @date 2023-07-15
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuUserAuthListBo extends SohuBaseBo {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 真实姓名
     */
    @NotBlank(message = "真实姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String userName;

    /**
     * 资质号（如，身份证号，护照号）
     */
    @NotBlank(message = "资质号（如，身份证号，护照号）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String authNo;

    /**
     * 正面照片
     */
    @NotBlank(message = "正面照片不能为空", groups = {AddGroup.class, EditGroup.class})
    private String frontPhoto;

    /**
     * 背面照片
     */
    @NotBlank(message = "背面照片不能为空", groups = {AddGroup.class, EditGroup.class})
    private String backPhoto;

    /**
     * 认证资质类型（personal =个人  business=企业认证）
     */
    @NotBlank(message = "认证资质类型（personal =个人  business=企业认证）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String authType;

    /**
     * 认证子类型（idcard=身份证 =passport护照 pass=港澳通行证）
     */
    @NotBlank(message = "认证子类型（idcard=身份证 =passport护照 pass=港澳通行证）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String subAuthType;

    /**
     * 拓展项json
     */
    private String expand;

    /**
     * 状态（1=待审核 2=已审核 3=审核拒绝）
     */
    private Integer state;

    /**
     * 拒绝理由
     */
    private String rejectReason;

    /**
     * 操作时间
     */
    private Date operateDate;

    /**
     * 操作人
     */
    private Long operateUser;


}
