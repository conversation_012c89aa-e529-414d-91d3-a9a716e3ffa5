package com.sohu.middle.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: leibo
 * @Date: 2024/12/27 09:59
 **/
@Data
public class SohuTradeNovelRecordVo implements Serializable {

    /**
     * 订单编号
     */
    private String payNumber;

    /**
     * 购买类型
     */
    private String consumeType;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 文案
     */
    private String msg;

    /**
     * 付款状态：WaitPay-待支付，Paid-已支付，Fail-支付失败，TimeOut-超时取消，Refund-退款
     * {@link com.sohu.common.core.enums.PayStatus}
     */
    private String payStatus;
}
