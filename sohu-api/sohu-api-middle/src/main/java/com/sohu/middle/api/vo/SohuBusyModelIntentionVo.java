package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 生意模式意向视图对象
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Data
@ExcelIgnoreUnannotated
public class SohuBusyModelIntentionVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 生意模式-意向主键
     */
    @ExcelProperty(value = "生意模式-意向主键")
    private String id;

    /**
     * 生意模式id
     */
    @ExcelProperty(value = "生意模式id")
    private String modelId;

    /**
     * 意向人id
     */
    @ExcelProperty(value = "意向人id")
    private String applicant;

    /**
     * 资源名片id
     */
    @ExcelProperty(value = "资源名片id")
    private String cardId;

    /**
     * dict::资源名片状态:[Edit:编辑,WaitApprove:待审核,Refuse:审核失败,OnShelf:上架,OffShelf:下架,Del:删除]
     */
    @ExcelProperty(value = "dict::资源名片状态:[Edit:编辑,WaitApprove:待审核,Refuse:审核失败,OnShelf:上架,OffShelf:下架,Del:删除]")
    private String cardState;

    /**
     * dict::入群状态:[Edit:待审核,OnShelf:上架(已入群),Refuse:拒绝]
     */
    @ExcelProperty(value = "dict::入群状态:[Edit:待审核,OnShelf:上架(已入群),Refuse:拒绝]")
    private String groupState;


}
