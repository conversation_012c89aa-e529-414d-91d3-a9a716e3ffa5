package com.sohu.middle.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户站点圈子关联业务对象
 *
 * <AUTHOR>
 * @date 2023-09-11
 */

@Data
public class SohuSiteCircleRelationBo implements Serializable {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 站点id
     */
    @NotNull(message = "站点id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long siteId;

    /**
     * 状态（WaitApprove：待审核，Refuse：拒绝，Pass：通过，Delete：删除）
     */
    private String state;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 通过时间
     */
    private Date passTime;


}
