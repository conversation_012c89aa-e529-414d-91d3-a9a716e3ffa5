package com.sohu.middle.api.enums;

/**
 * @ClassName: AuditState
 * @Description: 审核状态枚举
 * @Author: 郑胜琦
 * @Date: 2023/3/20 19:05:08
 */
public enum AuditState {

	// 待审核
	WaitApprove,
	// 退款状态：WAITAPPROVE:待审核 REFUNDREFUSE:审核未通过 REFUNDING：退款中 REFUNDSUCCESS:已退款
	// 订单状态（0：待发货,1：待收货,2：已收货,3：已完成，9：已取消）
	// 退款状态：0 未退款 1 申请中 2 退款中 3 已退款


	// 拒绝
	Refuse,

	// 通过
	Pass,

	// 强制下架
	CompelOff,

	//已作废——子模块从通过状态变为其他状态时，审核模块变成此状态
	Delete
}
