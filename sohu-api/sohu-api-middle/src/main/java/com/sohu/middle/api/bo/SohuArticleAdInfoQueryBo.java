package com.sohu.middle.api.bo;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 视频-广告-请求
 *
 */
@Data
public class SohuArticleAdInfoQueryBo extends SohuArticleBo {

    @Hidden
    @Schema(title = "广告位编码",description = "广告位编码(MY_GUESS_YOU_LIKE：我的-猜你喜欢)",example = "MY_GUESS_YOU_LIKE")
    private String adPlace;

    @Hidden
    @Schema(title = "广告位端口",description = "广告位端口。ALL：全部；HSS_APP：狐少少app；HSS_PC：狐少少PC；HSS_H5：狐少少h5；HIH_APP：Hi狐app；HIH_PC：Hi狐PC；HIH_ALL：Hi狐",example = "HIH_APP")
    private String adPort;

}
