package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 商单模式主体视图对象
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Data
@ExcelIgnoreUnannotated
public class SohuBusyModelVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模式id
     */
    @ExcelProperty(value = "模式id")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 站点id
     */
    @ExcelProperty(value = "站点id")
    private Long siteId;

    /**
     * 模式标题
     */
    @ExcelProperty(value = "模式标题")
    private String title;

    /**
     * 内容
     */
    @ExcelProperty(value = "内容")
    private String content;

    /**
     * 封面图
     */
    @ExcelProperty(value = "封面图")
    private String coverImg;

    /**
     * Ip归属地
     */
    @ExcelProperty(value = "Ip归属地")
    private String ipAddress;


}
