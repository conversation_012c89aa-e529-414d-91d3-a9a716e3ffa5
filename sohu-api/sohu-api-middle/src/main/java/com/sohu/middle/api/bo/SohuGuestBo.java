package com.sohu.middle.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuBaseBo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 游客记录业务对象
 *
 * <AUTHOR>
 * @date 2024-09-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuGuestBo extends SohuBaseBo {

    public static final String GUEST_ZH_NAME = "游客";
    public static final String GUEST_EN_NAME = "Guest";
    public static final String CHANNEL_PC="PC";
    public static final String CHANNEL_MOBILE="MOBILE";

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 游客uuid
     */
    @NotBlank(message = "游客uuid不能为空", groups = {AddGroup.class, EditGroup.class})
    private String uuid;

    /**
     * 游客名称
     */
    @NotBlank(message = "游客名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String userName;

    /**
     * 游客头像
     */
    @NotBlank(message = "游客头像不能为空", groups = {AddGroup.class, EditGroup.class})
    private String userAvatar;

    /**
     * 游客ip
     */
    private String ip;

    /**
     * 平台用户ID
     */
    private Long userId;

    /**
     * 来源渠道（PC,Ios,Android）
     */
    private String channel;

    /**
     * 语言
     */
    private String lang;

}
