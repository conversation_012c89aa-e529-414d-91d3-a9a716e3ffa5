package com.sohu.middle.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuBaseBo;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 用户历史搜索记录业务对象
 *
 * <AUTHOR>
 * @date 2023-11-15
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuHistoryWordBo extends SohuBaseBo {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 关键字
     */
    @NotBlank(message = "关键字不能为空", groups = {AddGroup.class, EditGroup.class})
    private String keyword;

}
