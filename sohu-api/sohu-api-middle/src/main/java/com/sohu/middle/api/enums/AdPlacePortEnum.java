package com.sohu.middle.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 广告位端口枚举
 */
@Getter
@AllArgsConstructor
public enum AdPlacePortEnum {

    ALL("ALL", "全部"),
    HSS_APP("HSS_APP", "狐少少app"),
    HSS_PC("HSS_PC", "狐少少PC"),
    HSS_H5("HSS_H5", "狐少少h5"),
    HIH_APP("HIH_APP", "Hi狐app"),
    HIH_PC("HIH_PC", "Hi狐PC"),
    HIH_ALL("HIH_ALL", "Hi狐"),
    ;

    private String code;
    private String msg;

}
