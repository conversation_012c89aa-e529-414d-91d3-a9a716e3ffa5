package com.sohu.middle.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/12/19 17:01
 */
@Data
public class IncomeBusyOrderBo implements Serializable {

    private final static long serialVersionUID = 1L;

    @Schema(name = "orderConstants", description = "业务订单常量",example = "OB")
    private String orderConstants;

    @Schema(name = "userId", description = "用户id",example = "1")
    private Long userId;

    @Schema(name = "amount", description = "收入金额",example = "1")
    private BigDecimal amount;

    @Schema(name = "taskId", description = "商单id",example = "1")
    private Long taskId;

    @Schema(name = "taskNumber", description = "商单号",example = "1")
    private String taskNumber;

    @Schema(name = "transactionType", description = "交易类型",example = "BusyTaskPromise")
    private String transactionType;

    @Schema(name = "msg", description = "交易描述",example = "交易描述")
    private String msg;
}
