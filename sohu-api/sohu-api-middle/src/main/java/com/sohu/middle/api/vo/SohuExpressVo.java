package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 快递公司视图对象
 *
 * <AUTHOR>
 * @date 2023-09-05
 */
@Data
@ExcelIgnoreUnannotated
public class SohuExpressVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 快递公司id
     */
    @ExcelProperty(value = "快递公司id")
    private Long id;

    /**
     * 快递公司简称
     */
    @ExcelProperty(value = "快递公司简称")
    private String code;

    /**
     * 快递公司全称
     */
    @ExcelProperty(value = "快递公司全称")
    private String name;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Long sort;

    /**
     * 是否显示
     */
    @ExcelProperty(value = "是否显示")
    private Boolean isShow;

    /**
     * 是否可用
     */
    @ExcelProperty(value = "是否可用")
    private Integer status;


}
