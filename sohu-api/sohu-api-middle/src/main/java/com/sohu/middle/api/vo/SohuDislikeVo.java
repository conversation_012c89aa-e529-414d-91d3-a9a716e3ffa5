package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;

/**
 * 不感兴趣视图对象
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
@Data
@ExcelIgnoreUnannotated
public class SohuDislikeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 不喜欢类型（DislikeCategory代表不喜欢内容，DislikeAuthor代表不喜欢作者）
     */
    @ExcelProperty(value = "不喜欢类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "DislikeCategory代表不喜欢内容，DislikeAuthor代表不喜欢作者")
    private String dislikeType;

    /**
     * 不感兴趣id
     */
    @ExcelProperty(value = "不感兴趣id")
    private Long dislikeId;

    /**
     * 不感兴趣次数
     */
    @ExcelProperty(value = "不感兴趣次数")
    private Integer count;

    /**
     * 不感兴趣数据id
     */
    @ExcelProperty(value = "不感兴趣数据id")
    private Long busyCode;

    /**
     * Answer:回答,Article:图文,Video:视频
     */
    @ExcelProperty(value = "Answer:回答,Article:图文,Video:视频")
    private String busyType;

}
