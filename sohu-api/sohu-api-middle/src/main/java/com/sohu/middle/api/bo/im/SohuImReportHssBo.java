package com.sohu.middle.api.bo.im;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * IM举报信息业务对象
 *
 * <AUTHOR>
 * @date 2024-12-23
 */

@Data
public class SohuImReportHssBo implements Serializable {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 举报人id
     */
    @NotNull(message = "举报人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long reportUserId;

    /**
     * 举报人昵称
     */
    @NotBlank(message = "举报人昵称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String reportNickname;

    @Schema(title = "举报人账号", example = "hss123")
    private String reportUserName;

    /**
     * 状态;Delete:删除，WaitApprove：待审核,Pass：通过，Refuse：拒绝
     */
    @NotBlank(message = "状态;Delete:删除，WaitApprove：待审核,Pass：通过，Refuse：拒绝不能为空", groups = { AddGroup.class, EditGroup.class })
    private String state;

    /**
     * 举报类型
     */
    @NotBlank(message = "举报类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String reportType;

    /**
     * 举报信息
     */
    @NotBlank(message = "举报信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String reportInformation;

    /**
     * 对象id
     */
    @NotNull(message = "对象id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long objId;

    /**
     * dict::类型:[Question:问题,Answer:回答,Article:图文,Video:视频，BusyTask-任务]
     */
    @NotBlank(message = "dict::类型:[Question:问题,Answer:回答,Article:图文,Video:视频，BusyTask-任务]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String objType;

    /**
     * 被举报对象名称
     */
    @NotBlank(message = "被举报对象名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String objName;

    @Schema(title = "被投诉对象编码", description = "例如群ID、用户账号", example = "123")
    private String objCode;

    /**
     * 凭证图片
     */
    @NotBlank(message = "凭证图片不能为空", groups = { AddGroup.class, EditGroup.class })
    private String imageUrl;

    /**
     * 下架或驳回理由
     */
    @NotBlank(message = "下架或驳回理由不能为空", groups = { AddGroup.class, EditGroup.class })
    private String rejectReason;

    /**
     * 内容标题
     */
    @NotBlank(message = "内容标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    /**
     * 作者id
     */
    @NotNull(message = "作者id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long authorId;

    /**
     * 审核人id
     */
    @NotNull(message = "审核人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long auditUserId;

    /**
     * 审核时间
     */
    @NotNull(message = "审核时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date auditTime;

    /**
     * 聊天证据json
     */
    @NotBlank(message = "聊天证据json不能为空", groups = { AddGroup.class, EditGroup.class })
    private String chatEvidence;

    /**
     * 审核附件
     */
    private String auditUrl;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 聊天证据list
     */
    private List<ImChatReportEvidenceModel> chatEvidenceModel;

}
