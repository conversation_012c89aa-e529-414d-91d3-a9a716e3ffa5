package com.sohu.middle.api.vo.im;

import cn.hutool.extra.mail.MailAccount;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.middle.api.bo.im.SohuImJiguangConfigModel;
import com.sohu.middle.api.bo.im.SohuImMailConfigModel;
import com.sohu.middle.api.bo.im.SohuImOssConfigModel;
import com.sohu.middle.api.bo.im.SohuImSmsBlendConfigModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * IM租户服务配置视图对象
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
@Data
@ExcelIgnoreUnannotated
public class SohuImTenantConfigVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 租户ID
     */
    @ExcelProperty(value = "租户ID")
    private Long tenantId;

    /**
     * 邮箱配置json
     */
    @ExcelProperty(value = "邮箱配置json")
    private String mailConfig;

    /**
     * 邮箱配置
     */
    private SohuImMailConfigModel mailAccountModel;

    /**
     * 短信配置json
     */
    @ExcelProperty(value = "短信配置json")
    private String smsConfig;

    /**
     * 短信配置
     */
    private SohuImSmsBlendConfigModel smsBlendModel;

    /**
     * 极光推送配置json
     */
    @ExcelProperty(value = "极光推送配置json")
    private String jiguangConfig;

    /**
     * 极光推送配置
     */
    private SohuImJiguangConfigModel jiguangModel;

    /**
     * 音视频服务地址
     */
    @ExcelProperty(value = "音视频服务地址")
    private String avUrl;

    /**
     * 文件服务json
     */
    @ExcelProperty(value = "文件服务json")
    private String ossConfig;

    /**
     * 文件服务配置
     */
    private SohuImOssConfigModel ossConfigModel;

    /**
     * im服务地址
     */
    private String imServerUrl;

    @Schema(title = "im服务socket地址", description = "im服务socket地址", example = "**************:9326")
    private String imSocketUrl;

}
