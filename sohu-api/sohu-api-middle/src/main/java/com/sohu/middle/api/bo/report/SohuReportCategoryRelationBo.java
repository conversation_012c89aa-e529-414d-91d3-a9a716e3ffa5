package com.sohu.middle.api.bo.report;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 举报分类与业务关联业务对象
 *
 * <AUTHOR>
 * @date 2025-04-22
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuReportCategoryRelationBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 举报分类id
     */
    @NotNull(message = "举报分类id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long reportCategoryId;

    /**
     * 业务类型  user - 用户  busyTask - 商单 video - 视频  article - 图文  question - 问答  playlet - 短剧  comment - 评论 focus - 焦点海外 novel - 小说章节  novelComment - 小说书评  store - 店铺    product-商品  productComment - 商品评论  xyhUser - 许愿狐用户   xyhGroup - 许愿狐群聊  xyhMessage - 许愿狐消息   ad - 广告  game - 游戏 
     */
    @NotBlank(message = "业务类型  user - 用户  busyTask - 商单 video - 视频  article - 图文  question - 问答  playlet - 短剧  comment - 评论 focus - 焦点海外 novel - 小说章节  novelComment - 小说书评  store - 店铺    product-商品  productComment - 商品评论  xyhUser - 许愿狐用户   xyhGroup - 许愿狐群聊  xyhMessage - 许愿狐消息   ad - 广告  game - 游戏 不能为空", groups = { AddGroup.class, EditGroup.class })
    private String busyCode;

    /**
     * 排序序号  不设置则以默认的举报分类序号为准
     */
    @NotNull(message = "排序序号  不设置则以默认的举报分类序号为准不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sort;


}
