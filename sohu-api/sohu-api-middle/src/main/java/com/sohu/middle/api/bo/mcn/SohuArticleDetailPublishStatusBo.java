package com.sohu.middle.api.bo.mcn;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * (蚁小二)发布状态更新
 */
@Data
public class SohuArticleDetailPublishStatusBo implements Serializable {

    /**
     * 平台账号id
     */
    @NotNull(message = "平台账号id不能为空")
    @Schema(description = "平台账号id")
    private String platformAccountId;

    /**
     * 自媒体平台id
     */
    @NotNull(message = "自媒体平台id不能为空")
    @Schema(description = "自媒体平台id")
    private Long platformId;

    /**
     * 任务id 记录那一次发文
     */
    @NotNull(message = "任务id 不能为空")
    @Schema(description = "任务id 记录那一次发文")
    private String taskId;

    /**
     * 发布状态{@link McnPublishStatusEnum}
     */
    @NotNull(message = "发布状态不能为空")
    @Schema(description = "发布状态")
    private String publishStatus;

    @Schema(description = "发布错误信息")
    private String message;

    /**
     * 发布成功后平台生成的唯一作品id（注：视频号一个视频反复发布会产生相同的publishid,美拍没有publishId
     */
    private String publishId;

}
