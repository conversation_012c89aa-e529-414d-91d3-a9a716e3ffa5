package com.sohu.middle.api.bo.mcn;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * MCN会员套餐业务对象
 *
 * <AUTHOR>
 * @date 2024-04-23
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuMcnProductBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productName;

    /**
     * 商品简介
     */
    @NotBlank(message = "商品简介不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productInfo;

    /**
     * 商品价格
     */
    @NotNull(message = "商品价格不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal price;

    /**
     * 币种，默认CNY人名币—人民币（CNY）、美元（USD）、欧元（EUR）、日元（JPY）、英镑（GBP）、加拿大元（CAD）、澳大利亚元（AUD）、瑞士法郎（CHF）、瑞典克朗（SEK）、新西兰元（NZD）
     */
//    @NotBlank(message = "币种，默认CNY人名币—人民币（CNY）、美元（USD）、欧元（EUR）、日元（JPY）、英镑（GBP）、加拿大元（CAD）、澳大利亚元（AUD）、瑞士法郎（CHF）、瑞典克朗（SEK）、新西兰元（NZD）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String currency;

    /**
     * 会员类型:NORMAL-普通版，GOLD-黄金版，DIAMOND-钻石版，FLAGSHIP-旗舰版
     */
    @NotBlank(message = "会员类型:NORMAL-普通版，GOLD-黄金版，DIAMOND-钻石版，FLAGSHIP-旗舰版不能为空", groups = { AddGroup.class, EditGroup.class })
    private String memberType;

    /**
     * 有效期类型:MONTH-月，QUARTER-季，YEAR-年
     */
    @NotBlank(message = "有效期类型:MONTH-月，QUARTER-季，YEAR-年不能为空", groups = { AddGroup.class, EditGroup.class })
    private String useType;

    /**
     * 有效天数(30,90,365)
     */
    @NotNull(message = "有效天数(30,90,365)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long useDays;

    /**
     * 获得积分(预留字段)
     */
//    @NotNull(message = "获得积分(预留字段)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long giveIntegral;

    /**
     * 可绑定媒体账号数量
     */
    @NotNull(message = "可绑定媒体账号数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer accountNum;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sort;

    /**
     * 是否回收站
     */
//    @NotNull(message = "是否回收站不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer isRecycle;

    /**
     * 是否强制下架，0-否，1-是
     */
//    @NotBlank(message = "是否强制下架，0-否，1-是不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isForced;

    /**
     * 审核状态：WAITAPPROVE-待审核，PASS-审核成功，FALSE-审核拒绝
     */
//    @NotBlank(message = "审核状态：WAITAPPROVE-待审核，PASS-审核成功，FALSE-审核拒绝不能为空", groups = { AddGroup.class, EditGroup.class })
    private String auditStatus;

    /**
     * 拒绝原因
     */
//    @NotBlank(message = "拒绝原因不能为空", groups = { AddGroup.class, EditGroup.class })
    private String reason;

    /**
     * 状态（0：未上架，1：上架）
     */
//    @NotNull(message = "状态（0：未上架，1：上架）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer isShow;

    /**
     * 是否删除
     */
//    @NotBlank(message = "是否删除不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isDel;

}
