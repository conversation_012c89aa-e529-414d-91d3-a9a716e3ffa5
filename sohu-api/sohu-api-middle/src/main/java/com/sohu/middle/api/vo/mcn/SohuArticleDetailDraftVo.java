package com.sohu.middle.api.vo.mcn;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023/10/31 14:41
 * @description 蚁小二同步草稿箱记录视图对象
 */
@Data
public class SohuArticleDetailDraftVo implements Serializable {

    /**
     * ID
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 封面图
     */
    private String coverImage;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 同步账号数（蚁小二）
     */
    private Integer platformNum;

    /**
     * 主题类型：[Article:图文,Video:视频] {@link McnHandlerTypeEnum}
     */
    private String handlerType;

    /**
     * 内容类型（蚁小二）:[Article-文章;Video-横版视频;MiniVideo-竖版视频;MicroArticle-短内容]
     * {@link McnMediaContentTypeEnum}
     */
    private String mediaContentType;

}
