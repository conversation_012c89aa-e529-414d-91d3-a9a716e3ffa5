package com.sohu.middle.api.vo.playlet;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 短剧广告关联视图对象
 *
 * <AUTHOR>
 * @date 2024-09-06
 */
@Data
@ExcelIgnoreUnannotated
public class SohuPlayletRelateVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 关联id
     */
    @ExcelProperty(value = "关联id")
    private Long busyCode;

    /**
     * 关联类型（Video-剧集,GOODS-商品，BUSINESS-商单）
     */
    private String busyType;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 短剧id
     */
    @ExcelProperty(value = "短剧id")
    private Long playletId;

    /**
     * 视频id
     */
    @ExcelProperty(value = "视频id")
    private Long videoId;

    /**
     * WaitShelf-待上架,OnShelf-上架,OffShelf-下架，Exceed-已过期
     */
    @ExcelProperty(value = "WaitShelf-待上架,OnShelf-上架,OffShelf-下架，Exceed-已过期")
    private String state;

    /**
     * 广告投放开始时间
     */
    @ExcelProperty(value = "广告投放开始时间")
    private Date startTime;

    /**
     * 广告投放结束时间
     */
    @ExcelProperty(value = "广告投放结束时间")
    private Date overTime;

    /**
     * 播放时间
     */
    @ExcelProperty(value = "播放时间")
    private Long playTime;

    /**
     * 视频展示样式 0-横版 1-竖版
     */
    private Integer videoStyle;
}
