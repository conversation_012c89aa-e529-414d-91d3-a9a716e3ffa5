package com.sohu.middle.api.bo;

import com.sohu.common.core.web.domain.SohuBaseBo;
import com.sohu.third.aliyun.airec.domain.bo.ISohuAiRecReqBo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: leibo
 * @Date: 2025/6/3 14:15
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class SohuContentListBo extends SohuBaseBo implements ISohuAiRecReqBo {

    /**
     * 站点id
     */
    private Long siteId;

    /**
     * 标题
     */
    private String title;

    /**
     * 分类ID集合
     */
    private List<Long> categoryIds;

    /**
     * 是否智能推荐(支持智能推荐)
     */
    private Boolean aiRec = false;


    /**
     * 是否是推荐页
     */
    private Boolean recommend;

    /**
     * 用户设备id，安卓设备是（imei），IOS设备是（idfa）
     * (支持智能推荐)(已登录的用户可不填，未登录用户必填)
     */
    private String aiRecImei;

    /**
     * 需要推荐的目标用户,和imei至少一个不为空
     *
     * @return
     */
    @Schema(hidden = true)
    private String aiUserId;

    /**
     * 场景id
     * （智能推荐必传）
     */
    private String aiRecSceneId;

    /**
     * 智能推荐，单次请求返回的推荐结果数量，建议取值20,最大值50
     */
    @Schema(hidden = true)
    private Integer aiReturnCount;
}
