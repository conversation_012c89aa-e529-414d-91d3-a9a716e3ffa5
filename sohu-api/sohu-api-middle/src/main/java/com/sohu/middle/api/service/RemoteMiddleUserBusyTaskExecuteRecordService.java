package com.sohu.middle.api.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuUserBusyTaskExecuteRecordBo;
import com.sohu.middle.api.vo.SohuUserBusyTaskExecuteRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 用户执行商单记录服务接口
 *
 * <AUTHOR>
 */
public interface RemoteMiddleUserBusyTaskExecuteRecordService {

    /**
     * 查询用户执行商单记录
     */
    SohuUserBusyTaskExecuteRecordVo queryById(Long id);

    /**
     * 查询用户执行商单记录列表
     */
    TableDataInfo<SohuUserBusyTaskExecuteRecordVo> queryPageList(SohuUserBusyTaskExecuteRecordBo bo, PageQuery pageQuery);

    /**
     * 查询用户执行商单记录列表
     */
    List<SohuUserBusyTaskExecuteRecordVo> queryList(SohuUserBusyTaskExecuteRecordBo bo);

    /**
     * 修改用户执行商单记录
     */
    Boolean insertByBo(SohuUserBusyTaskExecuteRecordBo bo);

    /**
     * 修改用户执行商单记录
     */
    Boolean updateByBo(SohuUserBusyTaskExecuteRecordBo bo);

    /**
     * 校验并批量删除用户执行商单记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询最开始的访问记录
     *
     * @param taskNumber
     * @param userId
     * @return
     */
    SohuUserBusyTaskExecuteRecordVo queryBeginRecordByTaskNumberAndUserId(String taskNumber, Long userId);
}
