package com.sohu.middle.api.vo.notice;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 互动通知视图对象
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Data
@ExcelIgnoreUnannotated
public class SohuInteractNoticeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @ExcelProperty(value = "自增ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 互动通知类型（点赞-like，收藏-collect,我收到的评论-commentReceive，我发出的评论-commentSend）
     */
    @ExcelProperty(value = "互动通知类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "点=赞-like，收藏-collect,我收到的评论-commentReceive，我发出的评论-commentSend")
    private String type;

    /**
     * 对象ID
     */
    @ExcelProperty(value = "对象ID")
    private String objId;

    /**
     * 对象类型（图文-Article，视频-Video，问题-Question，回答-Answer,评论-Comment）
     */
    @ExcelProperty(value = "对象类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "图=文-Article，视频-Video，问题-Question，回答-Answer,评论-Comment")
    private String objType;

    /**
     * 对象的封面图片
     */
    private String objCoverImage;

    /**
     * 通知标题
     */
    @ExcelProperty(value = "通知标题")
    private String content;

    /**
     * 操作人ID
     */
    private Long operateUser;

    /**
     * 操作人名称
     */
    private String operatorUserName;

    /**
     * 操作人头像
     */
    private String operatorUserAvatar;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间的时间戳
     */
    private long time;
    /**
     * obj对象的归属类型,如，点赞视频的评论，那此值Video，上面obj_type等于Comment
     */
    private String sourceType;
    /**
     * obj对象的归属id,如，点赞视频的评论，那此值等于评论的视频的视频ID，上面obj_id等于评论ID
     */
    private Long sourceId;
    /**
     * obj对象的归属用户ID
     */
    private Long sourceUser;
    /**
     * 是否点赞
     */
    private Boolean praiseObj;
    /**
     * 是否已读(true=1,false=0)
     */
    private Boolean readState;
    /**
     * 最顶级对象ID
     */
    private Long topCode;
    /**
     * 最顶级对象类型（图文-Article，视频-Video，问题-Question，回答-Answer,评论-Comment）
     */
    private String topType;

}
