package com.sohu.middle.api.service;


import com.sohu.middle.api.bo.ai.SohuDialogBo;
import com.sohu.middle.api.bo.ai.SohuDialogRecordBo;
import com.sohu.middle.api.vo.ai.AiAnalysisVo;
import com.sohu.middle.api.vo.ai.SohuDialogInfoVo;
import com.sohu.middle.api.vo.ai.SohuDialogRecordVo;
import com.sohu.middle.api.vo.ai.SohuDialogVo;

import java.util.List;

/**
 * @Author: leibo
 * @Date: 2025/3/5 14:38
 **/
public interface RemoteDialogService {

    /**
     * 获取会话明细
     *
     * @return
     */
    SohuDialogInfoVo getDialogInfo(String busyType);

    /**
     * 获取单一会话记录明细
     *
     * @param dialogId
     * @return
     */
    List<SohuDialogRecordVo> listRecord(Long dialogId, Long userId);

    /**
     * 获取单一记录明细
     *
     * @param recordId
     * @return
     */
    SohuDialogRecordVo recordInfo(Long recordId);

    /**
     * 解析记录明细
     *
     * @param recordId
     * @param busyCode
     * @return
     */
    AiAnalysisVo analysisRecord(Long recordId, String busyCode);

    /**
     * 新增会话
     *
     * @param sohuDialog
     * @return
     */
    Boolean insertBo(SohuDialogBo sohuDialog);

    /**
     * 新增会话记录
     *
     * @param record
     * @return
     */
    Boolean insertRecord(SohuDialogRecordBo record);

    /**
     * 编辑会话记录
     *
     * @param record
     * @return
     */
    Boolean updateRecord(SohuDialogRecordVo record);

    /**
     * 查询最近的一条会话
     *
     * @return
     */
    SohuDialogVo getNearDialog(Long userId);

    /**
     * 查询会话明细
     *
     * @param dialogId
     * @return
     */
    List<SohuDialogRecordVo> listRecord(Long dialogId);

    /**
     * 查询用户当前会话的最近一条记录
     *
     * @param dialogId
     * @param userId
     * @return
     */
    SohuDialogRecordVo getNearDialogRecord(Long dialogId, Long userId);

    /**
     * 基于id删除相关会话数据
     *
     * @param ids
     * @return
     */
    Boolean deleteByIds(List<Long> ids);

    /**
     * 查询用户全部会话记录
     *
     * @param dialogName
     * @return
     */
    List<SohuDialogVo> listByDialogName(String dialogName, String busyType);
}
