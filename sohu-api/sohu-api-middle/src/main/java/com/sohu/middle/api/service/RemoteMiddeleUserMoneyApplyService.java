package com.sohu.middle.api.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuUserMoneyApplyBo;
import com.sohu.middle.api.vo.SohuUserMoneyApplyVo;

import java.util.Collection;
import java.util.List;

/**
 * 用户赚钱角色申请Service接口
 *
 * <AUTHOR>
 * @date 2023-08-17
 */
public interface RemoteMiddeleUserMoneyApplyService {

    /**
     * 查询用户赚钱角色申请
     */
    SohuUserMoneyApplyVo queryById(Long id);

    /**
     * 查询用户赚钱角色申请列表
     */
    TableDataInfo<SohuUserMoneyApplyVo> queryPageList(SohuUserMoneyApplyBo bo, PageQuery pageQuery);

    /**
     * 查询用户赚钱角色申请列表
     */
    List<SohuUserMoneyApplyVo> queryList(SohuUserMoneyApplyBo bo);

    /**
     * 修改用户赚钱角色申请
     */
    Boolean insertByBo(SohuUserMoneyApplyBo bo);

    /**
     * 修改用户赚钱角色申请
     */
    Boolean updateByBo(SohuUserMoneyApplyBo bo);

    /**
     * 校验并批量删除用户赚钱角色申请信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 创建用户赚钱角色申请
     */
    void createUserMoneyApply(SohuUserMoneyApplyBo roleApplyVo);
}
