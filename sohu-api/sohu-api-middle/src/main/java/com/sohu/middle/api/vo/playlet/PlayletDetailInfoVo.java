package com.sohu.middle.api.vo.playlet;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 整部短剧详情信息VO
 */
@Data
public class PlayletDetailInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "playletUserId", description = "短剧作者id，唯一", example = "1")
    private Long playletUserId;

    @Schema(name = "playletNickname", description = "短剧作者昵称", example = "张三")
    private String playletNickname;

    @Schema(name = "playletUserAvatar", description = "短剧作者头像", example = "default_avatar.png")
    private String playletUserAvatar;

    @Schema(name = "playletUserSignature", description = "短剧作者签名", example = "我是一个短剧用户")
    private String playletUserSignature;

    @Schema(name = "playletTitle", description = "短剧标题", example = "我是一个短剧")
    private String playletTitle;

    @Schema(name = "playletIntro", description = "短剧简介", example = "我是一个短剧")
    private String playletIntro;

    @Schema(name = "playletSummary", description = "短剧概要", example = "我是一个短剧")
    private String playletSummary;

    @Schema(name = "collectCount", description = "短剧收藏数", example = "1")
    private Long collectCount = 0L;

    @Schema(name = "praiseCount", description = "短剧点赞数", example = "1")
    private Long praiseCount = 0L;

    @Schema(name = "forwardCount", description = "短剧转发数", example = "1")
    private Long forwardCount = 0L;

    @Schema(name = "viewCount", description = "短剧播放量", example = "1")
    private Long viewCount = 0L;

    @Schema(name = "singlePrice", description = "单集价格", example = "1")
    private BigDecimal singlePrice;

    @Schema(name = "followObj", description = "是否关注当前作者", example = "1")
    private Boolean followObj = Boolean.FALSE;

    @Schema(name = "praiseObj", description = "是否点赞短剧", example = "1")
    private Boolean praiseObj = Boolean.FALSE;

    @Schema(name = "collectObj", description = "是否收藏短剧", example = "1")
    private Boolean collectObj = Boolean.FALSE;

    @Schema(name = "playetId", description = "剧集id", example = "1")
    private Long playetId;

    @Schema(name = "coverImage", description = "短剧封面图", example = "https://sohugloba.oss-cn-beijing.aliyuncs.com/app/default_avatar.png")
    private String coverImage;

    @Schema(name = "videoStyle", description = "视频展示样式", example = "1")
    private Integer videoStyle;

}
