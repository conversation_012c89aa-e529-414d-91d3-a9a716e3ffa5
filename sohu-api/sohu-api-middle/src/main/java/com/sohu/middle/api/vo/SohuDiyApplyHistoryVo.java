package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.core.web.domain.SohuBaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 定制审核历史记录视图对象
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ExcelIgnoreUnannotated
public class SohuDiyApplyHistoryVo extends SohuBaseVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private String id;

    /**
     * 定制审核id
     */
    @ExcelProperty(value = "定制审核id")
    private Long applyId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
