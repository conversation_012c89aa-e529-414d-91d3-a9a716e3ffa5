package com.sohu.middle.api.bo.diy;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 商户通用配置查询对象
 *
 * @Author: leibo
 * @Date: 2025/4/15 11:44
 **/
@Data
public class SohuMerchantCommonBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 通用配置key  底部导航栏： diy_bottom_nav    个人中心： diy_member_index_config
     */
    @NotBlank(message = "通用配置key不能为空")
    private String configKey;

    /**
     * 商户id
     */
    @NotBlank(message = "商户id不能为空")
    private Long merchantId;
}
