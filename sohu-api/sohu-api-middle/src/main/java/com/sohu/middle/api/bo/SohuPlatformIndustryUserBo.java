package com.sohu.middle.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 平台行业与用户关联业务对象
 *
 * <AUTHOR>
 * @date 2025-05-27
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuPlatformIndustryUserBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 平台行业id
     */
    @NotNull(message = "平台行业id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long platformIndustryId;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;


}
