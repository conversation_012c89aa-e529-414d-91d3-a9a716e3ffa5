package com.sohu.admin.api.bo;

import com.sohu.admin.api.bo.merchant.CategoryInfo;
import com.sohu.common.core.validate.EditGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/27 15:50
 */
@Data
public class SohuMerchantCateBo implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "商户ID不能为空", groups = {EditGroup.class})
    @Schema(name = "id", description = "商户ID", example = "1")
    private Long id;

    @Schema(name = "cateInfos", description = "经营类目/品牌信息", example = "1")
    private List<CategoryInfo> cateInfos;
}
