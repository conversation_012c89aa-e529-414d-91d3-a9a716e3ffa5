package com.sohu.admin.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 商户类目业务对象
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Data
public class SohuMerchantClassificationBo implements Serializable {

    @Schema(name = "id", description = "ID",defaultValue = "1")
    private Long id;

    @Schema(name = "merId", description = "商户ID",defaultValue = "1")
    private Long merId;

    @Schema(name = "parentId", description = "父级类目ID",defaultValue = "1")
    private Long parentId;

    @Schema(name = "cateId", description = "类目末级ID",defaultValue = "1")
    private Long cateId;

    @Schema(name = "state", description = "审核状态（WaitApprove=审核中，Pass=通过，Refuse=拒绝）",defaultValue = "Pass")
    private String state;

    @Schema(name = "relateNo",description = "关联编号", example = "123")
    private String relateNo;

    @Schema(name = "enable", description = "开启状态 false 关闭 true 开启",defaultValue = "true")
    private Boolean enable;

    @Schema(name = "denialReason", description = "拒绝原因", example = "绑定的银行账户信息与平台登记不一致，导致退款无法原路返回。")
    private String denialReason;

    @Schema(name = "type", description = "是否入驻新增 0 否 1是",defaultValue = "1")
    private Integer type;

}
