package com.sohu.admin.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * ai 模板视图对象
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
@Data
@ExcelIgnoreUnannotated
public class SohuAiTemplateVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 模板名称
     */
    @ExcelProperty(value = "模板名称")
    private String templateName;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String description;

    /**
     * logo
     */
    @ExcelProperty(value = "logo")
    private String logoUrl;

    /**
     * 提示语
     */
    @ExcelProperty(value = "提示语")
    private String prompts;

    /**
     * 来源类型 PLATFORM平台  USER用户
     */
    @ExcelProperty(value = "来源类型 PLATFORM平台  USER用户")
    private String sourceType;

    /**
     * 用户 id
     */
    private Long userId;

    /**
     * 使用次数
     */
    @ExcelProperty(value = "使用次数")
    private Long useTimes;

    /**
     * 排序
     */
    private Integer sortIndex;

    /**
     * 是否删除
     */
    @ExcelProperty(value = "是否删除")
    private Boolean deleted;

    /**
     * 模板字段
     */
    @ExcelProperty(value = "模板字段")
    private List<SohuAiTemplateFieldVo> fields;

}
