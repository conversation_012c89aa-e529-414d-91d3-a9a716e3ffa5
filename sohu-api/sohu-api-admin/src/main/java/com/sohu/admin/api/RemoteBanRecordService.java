package com.sohu.admin.api;

import com.sohu.admin.api.bo.SohuBanRecordsBo;
import com.sohu.admin.api.vo.SohuBanRecordsVo;

import java.util.List;

public interface RemoteBanRecordService {

    /**
     * 批量新增封禁记录
     */
    void insertBatch(List<SohuBanRecordsBo> banRecordsBos);

    /**
     * 更新封禁数据
     */
    void updateBanRecordForUnban(SohuBanRecordsBo updateRecord);

    /**
     * 根据用户id和类型查询封禁记录
     *
     * @param userId 用户id
     * @param status 状态
     * @return {@link SohuBanRecordsBo}
     */
    SohuBanRecordsVo findActiveBanByUserId(Long userId, String status);

    /**
     * 根据封禁主键id获取详情
     *
     * @param banRecordId 主键id
     * @return {@link SohuBanRecordsBo}
     */
    SohuBanRecordsVo findBanRecordById(Long banRecordId);

    /**
     * 新增封禁记录
     *
     * @param banRecord 封禁记录
     */
    Long insertRecord(SohuBanRecordsBo banRecord);

    /**
     * 根据用ip和类型查询封禁记录
     *
     * @param ip ip
     * @param type   封禁类型
     * @return {@link SohuBanRecordsBo}
     */
    SohuBanRecordsVo findActiveBanByIp(String ip, String type);

    /**
     * 根据设备号和类型查询封禁记录
     *
     * @param device 设备码
     * @param type   封禁类型
     * @return {@link SohuBanRecordsBo}
     */
    SohuBanRecordsVo findActiveBanByDevice(String device, String type);

    /**
     * 查询封禁记录列表
     */
    List<SohuBanRecordsVo> queryList(SohuBanRecordsBo bo);
}
