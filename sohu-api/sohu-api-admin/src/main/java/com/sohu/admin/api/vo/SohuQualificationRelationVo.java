package com.sohu.admin.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.web.domain.SohuEntity;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 资质关联视图对象
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Data
@ExcelIgnoreUnannotated
public class SohuQualificationRelationVo extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 资质id
     */
    @ExcelProperty(value = "资质id")
    private Long qualificationId;

    /**
     * 关联id(类目/品牌)
     */
    @ExcelProperty(value = "关联id(类目/品牌)")
    private Long relationId;

    /**
     * 关联类型(category-类目,brand-品牌)
     */
    @ExcelProperty(value = "关联类型(category-类目,brand-品牌)")
    private String relationType;

    /**
     * 是否删除  0.未删除 
     */
    @ExcelProperty(value = "是否删除  0.未删除 ")
    private Long isDel;


}
