package com.sohu.admin.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商户仓库关联业务对象
 *
 * <AUTHOR>
 * @date 2023-09-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuWarehouseMerchantBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 商户id
     */
    @NotNull(message = "商户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long merId;

    /**
     * 仓库id
     */
    @NotNull(message = "仓库id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long houseId;

    /**
     * 仓库名称
     */
    @NotBlank(message = "仓库名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String houseName;

    /**
     * 是否删除
     */
    private Boolean isDelete;


}
