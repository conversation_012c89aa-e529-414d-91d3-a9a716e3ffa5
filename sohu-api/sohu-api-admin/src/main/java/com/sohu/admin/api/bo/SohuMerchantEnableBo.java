package com.sohu.admin.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/10 11:06
 */
@Data
public class SohuMerchantEnableBo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "merchantId", description = "商户ID", example = "1")
    private Long merchantId;

    @Schema(name = "enable", description = "是否启用（0=禁用，1=启用）", example = "1")
    private Integer enable;

    @Schema(name = "cateId", description = "类目ID", example = "1")
    private Long cateId;

    @Schema(name = "brandId", description = "品牌ID", example = "1")
    private Long brandId;
}
