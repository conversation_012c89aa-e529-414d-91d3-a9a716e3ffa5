package com.sohu.admin.api.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/5/7
 */
@Data
public class SohuUserAiInfoVo implements Serializable {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 会员版本 month月度 quarter季度 year年度
     */
    private String version;
    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 截止时间
     */
    private Date endDate;
    /**
     * 会员状态 none未开通 normal正常 expire过期
     */
    private String status;

    /**
     * 对话的统计信息
     */
    private Stats chatStats;

    /**
     * 创建应用统计信息
     */
    private Stats createApp;


    @NoArgsConstructor
    @Data
    public static class Stats {

        /**
         * 总次数
         */
        private int totalCount;

        /**
         * 已使用次数
         */
        private int usedCount;

        /**
         * 剩余次数
         */
        private int residueCount;

    }
}
