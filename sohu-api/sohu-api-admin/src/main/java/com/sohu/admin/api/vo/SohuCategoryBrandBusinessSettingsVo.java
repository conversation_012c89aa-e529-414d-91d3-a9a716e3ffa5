package com.sohu.admin.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import com.sohu.shopgoods.api.vo.SohuCategoryBondRelationVo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 类目品牌业务设置视图对象
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@ExcelIgnoreUnannotated
public class SohuCategoryBrandBusinessSettingsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 业务对象id（如品牌、类目）
     */
    @ExcelProperty(value = "业务对象id", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=品牌、类目")
    private Long businessId;

    /**
     * 业务对象类型(Brand-品牌,Category-类目)
     */
    @ExcelProperty(value = "业务对象类型(Brand-品牌,Category-类目)")
    private String businessType;

    /**
     * 个人是否可展示 0.不可展示 1.可展示
     */
    @ExcelProperty(value = "个人是否可展示 0.不可展示 1.可展示")
    private Integer isOpenPersonal;

    /**
     * 企业是否可展示 0.不可展示 1.可展示
     */
    @ExcelProperty(value = "企业是否可展示 0.不可展示 1.可展示")
    private Integer isOpenBusiness;

    /**
     * 个人是否要求 0.不要求 1.要求
     */
    @ExcelProperty(value = "个人是否要求 0.不要求 1.要求")
    private Integer isRequiredPersonal;

    /**
     * 企业是否要求 0.不要求 1.要求
     */
    @ExcelProperty(value = "企业是否要求 0.不要求 1.要求")
    private Integer isRequiredBusiness;

    /**
     * 是否删除 0.未删除 1.已删除
     */
    @ExcelProperty(value = "是否删除 0.未删除 1.已删除")
    private Integer isDel;

    /**
     * 保证金列表
     */
    private List<SohuCategoryBondRelationVo> categoryBondRelationVos;

    /**
     * 资质列表
     */
    private List<SohuQualificationVo> qualificationVos;

}
