package com.sohu.admin.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * ai 模板个人视图对象
 *
 * <AUTHOR>
 * @date 2024-05-06
 */
@Data
@ExcelIgnoreUnannotated
public class SohuAiTemplatePersonallyVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private String id;

    /**
     * 模板名称
     */
    @ExcelProperty(value = "模板名称")
    private String templateName;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 行业
     */
    @ExcelProperty(value = "行业")
    private String profession;

    /**
     * 应用目标
     */
    @ExcelProperty(value = "应用目标")
    private String applicationTarget;

    /**
     * 风格
     */
    @ExcelProperty(value = "风格")
    private String style;

    /**
     * 状态 enable启用 disable禁用
     */
    @ExcelProperty(value = "状态 enable启用 disable禁用")
    private String status;

    /**
     * 使用次数
     */
    @ExcelProperty(value = "使用次数")
    private Long useTimes;
}
