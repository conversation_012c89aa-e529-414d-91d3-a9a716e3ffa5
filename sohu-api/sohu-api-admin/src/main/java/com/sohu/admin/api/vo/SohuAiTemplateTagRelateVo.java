package com.sohu.admin.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * ai 模板标签关联视图对象
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
@Data
@ExcelIgnoreUnannotated
public class SohuAiTemplateTagRelateVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 模板id
     */
    @ExcelProperty(value = "模板id")
    private Long templateId;

    /**
     * 标签id
     */
    @ExcelProperty(value = "标签id")
    private Long tagId;


}
