package com.sohu.admin.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/12/4 12:31
 */
@Data
public class SohuMerchantSalesReportVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(name = "date",description = "统计时间(按天划分",example = "2024-12-02")
    private String date;

    @Schema(name = "merId",description = "商户ID",example = "1")
    private Long merId;

    @Schema(name = "salesPrice",description = "销售额",example = "1")
    private BigDecimal salesPrice;

    @Schema(name = "salesNum",description = "销售量",example = "1")
    private Integer salesNum;

    @Schema(name = "userName",description = "用户名称",example = "1")
    private String userName;

    @Schema(name = "merName",description = "商户名称",example = "1")
    private String merName;
}
