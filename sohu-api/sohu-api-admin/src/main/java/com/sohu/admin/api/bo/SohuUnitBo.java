package com.sohu.admin.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuBaseBo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 单位站点业务对象
 *
 * <AUTHOR>
 * @date 2023-06-29
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuUnitBo extends SohuBaseBo {

    /**
     * 单位主键
     */
    @NotNull(message = "单位主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 单位站点id
     */
    @NotNull(message = "单位站点id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long siteId;

    /**
     * 单位名
     */
    @NotBlank(message = "单位名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String unitName;


}
