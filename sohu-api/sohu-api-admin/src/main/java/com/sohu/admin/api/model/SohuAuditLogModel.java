package com.sohu.admin.api.model;

import com.sohu.common.core.enums.CommonState;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

@Slf4j
@Data
public class SohuAuditLogModel implements Serializable {

    private Long id;

    /**
     * 审核业务类型:[Article:图文,Video视频,Project:项目,Question:问题,Answer:回答,Card:资源名片,BusyModel:生意模式,BusyOrder:商单]
     * {@link com.sohu.common.core.enums.BusyType}
     */
    private String busyType;

    /**
     * 业务id
     */
    private Long busyCode;

    /**
     * 业务标题（回答的标题指的是：对应问题的标题）
     */
    private String busyTitle;

    /**
     * 显示的文本，后端组合起来，eg:
     * 通过：2025-5-15 17:58:55 审核通过 操作人：admin
     * 拒绝：2025-5-15 17:59:15 审核驳回 驳回原因：涉政 操作人：admin
     * 待审核：待审核
     */
    private String busyText;

    /**
     * 业务封面图-ossUrl
     */
    private String busyCoverUrl;

    /**
     * 业务归属人
     */
    private Long busyBelonger;

    private Long operateUserId;

    private String state;
    /**
     * 拒绝理由
     */
    private String refuseReason;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date logTime;

    public static List<SohuAuditLogModel> logVos() {
        List<SohuAuditLogModel> list = new LinkedList<>();
        SohuAuditLogModel logVo = new SohuAuditLogModel();
        logVo.setId(1L);
        logVo.setBusyCode(1L);
        logVo.setBusyType("MerchantBrandAudit");
        logVo.setBusyTitle("2025-5-15 17:49:04 发起新增经验类目申请");
        logVo.setState(CommonState.WaitApprove.getCode());
        logVo.setLogTime(new Date());
        logVo.setBusyText("2025-5-15 17:59:15 审核驳回 驳回原因：涉政 操作人：admin");
        list.add(logVo);
        return list;
    }
}
