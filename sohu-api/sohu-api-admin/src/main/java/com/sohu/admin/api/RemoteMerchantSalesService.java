package com.sohu.admin.api;

import com.sohu.admin.api.bo.SohuMerchantSalesReportBo;
import com.sohu.admin.api.vo.SohuMerchantSalesReportVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/4 16:20
 */
public interface RemoteMerchantSalesService {

    /**
     * 同步商家销售报表
     * @param boList
     */
    void syncSalesReport(List<SohuMerchantSalesReportBo> boList);

    /**
     * 定时同步商家销售报表
     * @param salesReportVos
     */
    void excuteSalesReport(List<SohuMerchantSalesReportVo> salesReportVos,String day);
}
