package com.sohu.admin.api.bo.merchant;

import com.sohu.common.core.validate.AddGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/30 11:08
 */
@Data
public class QualificationInfo implements Serializable {

    @NotNull(message = "资质ID不能为空", groups = {AddGroup.class})
    @Schema(name = "qualificateId", description = "资质ID", example = "1")
    private Long qualificateId;

    @NotNull(message = "资质名称不能为空", groups = {AddGroup.class})
    @Schema(name = "qualificateName", description = "资质名称", example = "1")
    private String qualificateName;

    @NotBlank(message = "资质图片不能为空", groups = {AddGroup.class})
    @Schema(name = "proveUrl", description = "资质图片", example = "1")
    private String proveUrl;
}
