package com.sohu.admin.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 资质关联业务对象
 *
 * <AUTHOR>
 * @date 2025-05-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuQualificationRelationBo extends SohuEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 资质id
     */
    @NotNull(message = "资质id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long qualificationId;

    /**
     * 关联id(类目/品牌)
     */
    @NotNull(message = "关联id(类目/品牌)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long relationId;

    /**
     * 关联类型(category-类目,brand-品牌)
     */
    @NotBlank(message = "关联类型(category-类目,brand-品牌)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String relationType;

    /**
     * 是否删除  0.未删除 
     */
    @NotNull(message = "是否删除  0.未删除 不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isDel;


}
