package com.sohu.system.api.bo;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户对象查询
 *
 */

@Data
@NoArgsConstructor
public class SysUserQueryBo implements Serializable {

    /**
     * 角色id
     */
    //@NotNull(message = "角色id不能为空")
    private Long roleId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 手机号码
     */
    private String phoneNumber;

    private Long userId;

}
