package com.sohu.system.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 公告管理业务对象
 *
 * <AUTHOR>
 * @date 2024-09-02
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuNoticeInsertBo extends BaseEntity implements Serializable {

    /**
     * 公告名称
     */
    @NotBlank(message = "公告名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 公告封面
     */
    @NotBlank(message = "公告封面不能为空", groups = { AddGroup.class, EditGroup.class })
    private String image;

    /**
     * 公告描述
     */
    private String msg;

    /**
     * 公告详情
     */
    @NotBlank(message = "公告详情不能为空", groups = { AddGroup.class, EditGroup.class })
    private String info;

    /**
     * 应用生效端(all:总后台 agent:代理商后台 stationAgent:站长后台 project:任务方后台)
     */
    @NotBlank(message = "应用生效端不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 状态（OnShelf：上架，OffShelf：下架）
     */
    @NotBlank(message = "上架状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String state;

}
