package com.sohu.system.api;

import com.sohu.system.api.bo.SysUserVipBo;
import com.sohu.system.api.vo.SysUserVipVo;

/**
 * 用户会员
 *
 * @Author: leibo
 * @Date: 2024/12/4 11:49
 **/
public interface RemoteUserVipService {

    /**
     * 基于用户id、端口、业务类型查询相关用户会员信息
     *
     * @param userVipBo
     * @return
     */
    SysUserVipVo getUserVip(SysUserVipBo userVipBo);

    /**
     * 新增用户会员信息
     *
     * @param userVipBo
     * @return
     */
    Boolean insertBo(SysUserVipBo userVipBo);

    /**
     * 修改用户会员信息
     *
     * @param userVipBo
     * @return
     */
    Boolean updateBo(SysUserVipBo userVipBo);
}
