package com.sohu.system.api.bo;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 聊天记录缓存天数
 */
@Data
public class SysChatDataCacheConfigBo implements Serializable {

    /**
     * 聊天记录缓存天数key
     */
    public final static String CHAT_DATA_CACHE_DAY_KEY = "CHAT_DATA_CACHE_DAY";

    public final static String CHAT_DATA_CACHE_DAY_NAME = "聊天记录缓存天数";

    public final static String CHAT_DATA_CACHE_DAY_DEFAULT_VALUE = "30";

    /**
     * 聊天记录缓存天数值
     */
    @NotNull(message = "天数值不能为空")
    @Min(value = 1L)
    private Long dayValue;

}
