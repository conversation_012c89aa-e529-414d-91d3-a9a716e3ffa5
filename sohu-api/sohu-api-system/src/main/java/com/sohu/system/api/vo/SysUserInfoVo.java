package com.sohu.system.api.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.sohu.admin.api.vo.SohuMerchantSettledVo;
import com.sohu.common.core.annotation.Sensitive;
import com.sohu.common.core.enums.SensitiveStrategy;
import com.sohu.middle.api.vo.SohuMerchantVo;
import com.sohu.pay.api.vo.SohuAccountVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR> Li
 */

@Data
@NoArgsConstructor
public class SysUserInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "1")
    private Long userId;

    /**
     * 用户账号
     */
    @Schema(description = "用户账号", example = "test")
    private String userName;

    /**
     * 用户昵称
     */
    @Schema(description = "用户昵称", example = "test")
    private String nickName;

    /**
     * 用户类型（SYS_PLATFORM:总后台,DISTRIBUTION_USER:分销平台,CLIENT_USER:C端）
     * {@link com.sohu.common.core.enums.UserType}
     */
//    @Schema(description = "用户类型", example = "SYS_PLATFORM")
//    private String userType;

    /**
     * 用户邮箱
     */
    @Schema(description = "用户邮箱", example = "<EMAIL>")
    @Sensitive(strategy = SensitiveStrategy.EMAIL)
    private String email;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码", example = "138****8888")
    @Sensitive(strategy = SensitiveStrategy.PHONE)
    private String phoneNumber;

    /**
     * 用户性别
     */
    @Schema(description = "用户性别（0男 1女 2未知）", example = "1")
    private Integer sex;

    /**
     * 密码
     */
//    @Schema(description = "密码", example = "密文")
//    @Sensitive(strategy = SensitiveStrategy.PASSWORD)
//    private String password;

    /**
     * 帐号状态（0正常 1停用）
     * {@link com.sohu.common.core.enums.UserStatus}
     */
//    @Schema(description = "帐号状态（0正常 1停用）", example = "0")
//    private String status;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "备注")
    private String remark;

    /**
     * 归属部门名称
     */
//    @Schema(description = "归属部门名称", example = "技术部")
//    private String deptName;

    /**
     * 岗位
     */
//    @Schema(description = "岗位", example = "Java开发工程师")
//    private String postName;

    /**
     * 角色集合
     */
//    @Schema(description = "角色集合", example = "1,2")
//    private Long[] roleIds;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 设备码
     */
    private String deviceCode;

    /**
     * 店铺名称
     */
    private String merchantNames;

    /**
     * 店铺类型
     */
    private String merchantTypes;

    /**
     * 店铺关键字
     */
    private String merchantKeywords;

    /**
     * 商户分类
     */
    private String merchantCategorys;

    /**
     * 商品种类
     */
    private Long productCategoryCount;

    /**
     * 实名认证信息
     */
    private SohuAccountVo sohuAccountVo;

    /**
     * 商户信息
     */
    private List<SohuMerchantSettledVo> sohuMerchantSettledVos;

}

