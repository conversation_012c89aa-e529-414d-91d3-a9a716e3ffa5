package com.sohu.system.api;

import com.sohu.system.api.domain.SysDictData;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 字典服务
 *
 * <AUTHOR> Li
 */
public interface RemoteDictService {

    /**
     * 根据字典类型查询字典数据
     *
     * @param dictType 字典类型
     * @return 字典数据集合信息
     */
    List<SysDictData> selectDictDataByType(String dictType);

    /**
     * 获取dictValue集合
     *
     * @param dictType 字典类型
     * @return 返回dictValue集合
     */
    Set<String> getDictValues(String dictType);

    /**
     * 根据字典类型查询字典数据
     *
     * @param dictType 字典类型
     * @return 字典数据集合信息,key的值=dictValue字段,value的值=dictLabel字段
     */
    Map<String,String> mapVlDictDataByType(String dictType);

    /**
     * 根据字典类型查询字典数据
     *
     * @param dictType 字典类型
     * @param dictType 字典类型
     * @return 字典数据对象信息
     */
    SysDictData getDictData(String dictLabel, String dictType);

    /**
     * 根据字典类型查询字典数据,通过value
     *
     * @param dictValue 字典键值
     * @param dictType  字典类型
     * @return 字典数据对象信息
     */
    SysDictData getDictDataByValue(String dictValue, String dictType);

    /**
     * 根据字典类型查询字典数据,通过value
     *
     * @param dictValues 字典键值
     * @param dictType   字典类型
     * @return 字典数据对象信息
     */
    List<SysDictData> listDictDataByValues(Set<String> dictValues, String dictType);

}
