<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.sohu</groupId>
        <artifactId>sohu-dependency</artifactId>
        <version>1.0.0</version>
    </parent>

    <groupId>com.sohu</groupId>
    <artifactId>sohu-api</artifactId>
    <version>1.0.0</version>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.20</version>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-core</artifactId>
        </dependency>
    </dependencies>

    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>sohu-api-bom</module>
        <module>sohu-api-system</module>
        <module>sohu-api-resource</module>
        <module>sohu-api-admin</module>
        <module>sohu-api-pay</module>
        <module>sohu-api-busy-order</module>
        <module>sohu-api-shop-goods</module>
        <module>sohu-api-shop-order</module>
        <module>sohu-api-entry</module>
        <module>sohu-api-im</module>
        <module>sohu-api-app</module>
        <module>sohu-api-stream-rocketmq</module>
        <module>sohu-api-middle</module>
        <module>sohu-api-open</module>
        <module>sohu-api-pm</module>
        <module>sohu-api-novel</module>
        <module>sohu-api-focus</module>
        <module>sohu-api-story</module>
        <module>sohu-api-report</module>
    </modules>

    <packaging>pom</packaging>

    <description>
        sohu-api系统接口
    </description>

</project>
