package com.sohu.open.api.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * 开放者平台第三方成员
 */
@Data
public class SohuOpenUserBo implements Serializable {

    private Long id;

    /**
     * 关联团队id(用户ID)
     */
    private Long teamId;

    /**
     * 第三方客户端id
     */
    private Long clientId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * openId
     */
    private String openId;

}