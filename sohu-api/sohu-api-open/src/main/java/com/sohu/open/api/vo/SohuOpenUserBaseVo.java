package com.sohu.open.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class SohuOpenUserBaseVo implements Serializable {

    @Schema(name = "openId", description = "三方appId", example = "yougua")
    private String openId;

    @Schema(name = "avatar", description = "用户头像", example = "https://sohugloba.oss-cn-beijing.aliyuncs.com/2024/11/22/cc96fc7c72cb4c9a8fa7662c0efb3305_84x84.png")
    private String avatar;

    @Schema(name = "nickName", description = "用户昵称", example = "test")
    private String nickName;

    @Schema(name = "phoneNumber", description = "手机号", example = "18812345678")
    private String phoneNumber;

}
