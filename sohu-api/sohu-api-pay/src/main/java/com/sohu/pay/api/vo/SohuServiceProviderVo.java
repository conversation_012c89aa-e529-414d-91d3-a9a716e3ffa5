package com.sohu.pay.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/2/11 19:25
 */
@Data
public class SohuServiceProviderVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(name = "userName",description = "用户名称", example = "张三")
    private String userName;

    @Schema(name = "orderNo",description = "支付单号", example = "123456789")
    private String orderNo;

    @Schema(name = "transactionId",description = "流水号", example = "123456789")
    private String transactionId;

    @Schema(name = "payType",description = "支付方式", example = "线上 Online 线下 Offline")
    private String payType;

    @Schema(name = "status",description = "状态", example = "Paid")
    private String status;

    @Schema(name = "amount",description = "金额", example = "100")
    private String amount;

    @Schema(name = "voucherUrl",description = "凭证地址", example = "https://www.baidu.com")
    private String voucherUrl;

    @Schema(name = "operTime",description = "操作时间", example = "2023-02-10 12:00:00")
    private String operTime;

}
