package com.sohu.pay.api.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单分账金额对象
 *
 * @author: zc
 * @date: 2023/10/18 14:51
 * @version: 1.0.1
 */
@Data
@Accessors(chain = true)
public class SohuOrderIndependentPriceBo implements Serializable {

    /**
     * 分销人分账金额-0.00
     */
    private BigDecimal distributorPrice;

    /**
     * 拉新人分账金额-0.00
     */
    private BigDecimal invitePrice;

    /**
     * 城市站长分账金额-0.00
     */
    private BigDecimal cityPrice;

    /**
     * 国家站长分账金额-0.00
     */
    private BigDecimal countryPrice;

    /**
     * 平台分账金额-0.00
     */
    private BigDecimal adminPrice;

    /**
     * 分销人id
     */
    private Long independentUserId;

    /**
     * 是否是分销单
     */
    private Boolean independentOrder;

    /**
     * 商户订单号
     */
    private String shopOrderNo;

}
