package com.sohu.pay.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 支付订单视图对象
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
@Data
@ExcelIgnoreUnannotated
public class SohuPayOrderVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;
    /**
     * 主订单编码
     */
    private String masterOrderNo;
    /**
     * 子订单编码
     */
    private String orderNo;
    /**
     * 门店Id
     */
    @ExcelProperty(value = "门店Id")
    private Long merId;
    /**
     * 付款人ID，即用户ID
     */
    @ExcelProperty(value = "付款人ID")
    private Long userId;
    /**
     * 付款人名称
     */
    @ExcelProperty(value = "付款人名称")
    private String userName;
    /**
     * 付款人头像
     */
    private String userAvatar;

    /**
     * 支付来源 android-app-安卓app、 pc-网站、wechat-微信小程序、ios-app-苹果app、tiktok-抖音、alipay-支付宝小程序
     */
    @ExcelProperty(value = "支付来源")
    private String sourceType;

    /**
     * 终端编号
     */
    @ExcelProperty(value = "终端编号")
    private String terminalId;

    /**
     * 第三方支付流水号
     */
    @ExcelProperty(value = "第三方支付流水号")
    private String transactionId;

    /**
     * 退单流水号
     */
    @ExcelProperty(value = "退单流水号")
    private String refundId;

    /**
     * 支付流水号
     */
    @ExcelProperty(value = "支付流水号")
    private String payNumber;

    /**
     * 支付方式：
     * wechat-jsapi-微信小程序支付、
     * wechat-app-微信app支付、
     * wechat-h5-微信h5支付、
     * wechat-native-微信扫码支付、
     * tiktok-抖音小程序支付、
     * ali-pay-支付宝小程序支付、
     * integral-积分支付、
     * balance-余额支付、
     * offline-pay-线下支付
     * virtual-虚拟币账户支付
     * yi-ma-翼码支付
     * {@link com.sohu.common.core.enums.PayTypeEnum}
     */
    @ExcelProperty(value = "支付方式")
    private String payType;
    /**
     * 三方支付的支付类型（如当pay_type=翼码支付时候，此值为：502：支付宝支付，503：微信支付，512：银联二维码，515：小程序支付，516：数字人民币，519：会员余额支付）
     * {@link com.sohu.pay.api.enums.YiMaPayType}
     */
    private String childPayType;

    /**
     * 支付状态 WaitPay-未支付 Paid-支付成功 Fail-支付失败 TimeOut-超时，Refund-已退款,Cancel-取消
     * {@link com.sohu.common.core.enums.PayStatus}
     */
    @ExcelProperty(value = "支付状态")
    private String payStatus;

    /**
     * 实付金额
     */
    @ExcelProperty(value = "实付金额")
    private BigDecimal payAmount;

    /**
     * 应付金额
     */
    @ExcelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    /**
     * 支付补贴金额
     */
    @ExcelProperty(value = "支付补贴金额")
    private BigDecimal subsidyAmount;

    /**
     * 抵扣积分
     */
    @ExcelProperty(value = "抵扣积分")
    private Integer payIntegral;

    /**
     * 券码
     */
    @ExcelProperty(value = "券码")
    private String couponId;

    /**
     * 已退金额
     */
    @ExcelProperty(value = "已退金额")
    private BigDecimal refundAmount;

    /**
     * 付款时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;
    /**
     * 手续费
     */
    private BigDecimal chargeAmount;

}
