package com.sohu.pay.api.bo;

import com.sohu.common.core.constant.Constants;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 开户银行业务对象
 *
 * <AUTHOR>
 * @date 2023-10-11
 */

@Data
public class SohuAccountBankAddBo implements Serializable {

    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号不能为空")
    //@Pattern(regexp = "^(\\d{15}|\\d{19})$", message = "银行卡号必须是15或19位数字")
    @Pattern(regexp = Constants.BANK_NO_REGEXP, message = "银行卡号必须是15、16、17或19位数字")
    private String cardNo;

    /**
     * 结算开户银行名称
     */
    @NotBlank(message = "结算开户银行名称不能为空")
    private String parentBankName;

    /**
     * 结算开户银行号
     */
    @NotBlank(message = "结算开户银行号不能为空")
    private String parentBankCode;

    /**
     * 开户支行名称（对公时支付行号、开户支行名称二选一必填）
     */
    private String branchBankName;

    /**
     * 支付行号（对公时支付行号、开户支行名称二选一必填）
     */
    private String branchBankCode;

    /**
     * 开户行所在省
     */
    @NotBlank(message = "开户行所在省不能为空")
    private String province;

    /**
     * 开户行所在市
     */
    @NotBlank(message = "开户行所在市不能为空")
    private String city;

    /**
     * 结算卡正面
     */
    @NotBlank(message = "结算卡正面不能为空")
    private String settlePhotoFront;

    /**
     * 结算卡反面
     */
    @NotBlank(message = "结算卡反面不能为空")
    private String settlePhotoBack;

    /**
     * 支付密码
     */
    private String password;

}
