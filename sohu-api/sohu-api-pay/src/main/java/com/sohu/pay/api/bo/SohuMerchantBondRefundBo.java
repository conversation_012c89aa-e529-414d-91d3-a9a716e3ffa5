package com.sohu.pay.api.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 商户支付保证金退款业务对象
 *
 * <AUTHOR>
 * @date 2025-05-13
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuMerchantBondRefundBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotBlank(message = "主键id不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 退款人ID
     */
    @NotBlank(message = "退款人ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 商户保证金id
     */
    @NotBlank(message = "商户保证金id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long merchantBondId;

    /**
     * 订单支付单号
     */
    @NotBlank(message = "订单支付单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;

    /**
     * 订单退单号
     */
    @NotBlank(message = "订单退单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String refundOrderNo;

    /**
     * 三方支付流水号
     */
    @NotBlank(message = "三方支付流水号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transactionId;

    /**
     * 三方退单流水号
     */
    private String refundTransactionId;

    /**
     * 工单类型  CloseMerchant 闭店   CloseCategory  关闭类目
     */
    private String type;

    /**
     * 支付方式 Alipay 支付宝 WeChat 微信 Offline 对公转账
     */
    private String payType;

    /**
     * 退款方式 Alipay 支付宝 WeChat 微信 offline 对公转账
     */
    @NotBlank(message = "退款方式 Alipay 支付宝 WeChat 微信 offline 对公转账不能为空", groups = { AddGroup.class, EditGroup.class })
    private String refundType;

    /**
     * 退款状态 WAITAPPROVE-申请退款 REFUNDING-退款中 REFUNDSUCCESS-已退款 FALSE-未退款、REFUNDREFUSE-退款失败
     */
    @NotBlank(message = "退款状态 WAITAPPROVE-申请退款 REFUNDING-退款中 REFUNDSUCCESS-已退款 FALSE-未退款、REFUNDREFUSE-退款失败不能为空", groups = { AddGroup.class, EditGroup.class })
    private String refundStatus;

    /**
     * 应退金额
     */
    @NotNull(message = "应退金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal payableAmount;

    /**
     * 实退金额
     */
    @NotNull(message = "实退金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal refundAmount;

    /**
     * 实际退款时间
     */
    @ExcelProperty(value = "实际退款时间")
    private Date refundTime;

    /**
     * 凭证
     */
    private String voucherUrl;
    /**
     * 备注信息
     */
    private String remarks;

}
