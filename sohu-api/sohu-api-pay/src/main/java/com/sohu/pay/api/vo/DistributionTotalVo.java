package com.sohu.pay.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "累计佣金收入")
public class DistributionTotalVo implements Serializable {

    @Schema(description = "累计佣金收入", example = "100.00")
    private BigDecimal accumulateAmount = BigDecimal.ZERO;

    @Schema(description = "本月佣金收入", example = "50.00")
    private BigDecimal currentMonthAmount = BigDecimal.ZERO;

}
