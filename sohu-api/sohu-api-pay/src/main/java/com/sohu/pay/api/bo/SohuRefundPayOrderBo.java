package com.sohu.pay.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 退款单业务对象
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuRefundPayOrderBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 支付单号(sohu_pay_order的payNumber)
     */
    @NotBlank(message = "支付单号(sohu_pay_order的payNumber)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String payNumber;

    /**
     * 门店Id
     */
    @NotBlank(message = "门店Id不能为空", groups = {AddGroup.class, EditGroup.class})
    private String merId;

    /**
     * 付款人ID
     */
    @NotBlank(message = "付款人ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 第三方支付流水号
     */
    @NotBlank(message = "第三方支付流水号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String transactionId;

    /**
     * 主支付单号(sohu_pay_order的masterOrderNo)
     */
    @NotBlank(message = "主支付单号(sohu_pay_order的masterOrderNo)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String masterOrderNo;

    /**
     * 子单号(sohu_pay_order的orderNo)
     */
    @NotBlank(message = "子单号(sohu_pay_order的orderNo)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orderNo;

    /**
     * 退单编码(自己生成)
     */
    @NotBlank(message = "退单编码(自己生成)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String refundOrderNo;

    /**
     * 支付方式  wechat_jsapi-微信小程序支付、wechat-app-微信app支付、tiktok-抖音小程序支付、alipay-支付宝小程序支付、integral-积分支付、balance-余额支付
     */
    @NotBlank(message = "支付方式  wechat_jsapi-微信小程序支付、wechat-app-微信app支付、tiktok-抖音小程序支付、alipay-支付宝小程序支付、integral-积分支付、balance-余额支付不能为空", groups = {AddGroup.class, EditGroup.class})
    private String payType;

    /**
     * 退款状态 WAITAPPROVE-申请退款 REFUNDING-退款中 REFUNDSUCCESS-已退款 FALSE-未退款、REFUNDREFUSE-退款失败
     */
    @NotBlank(message = "退款状态 WAITAPPROVE-申请退款 REFUNDING-退款中 REFUNDSUCCESS-已退款 FALSE-未退款、REFUNDREFUSE-退款失败不能为空", groups = {AddGroup.class, EditGroup.class})
    private String refundStatus;

    /**
     * 应退金额
     */
    @NotNull(message = "应退金额不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal payableAmount;

    /**
     * 实退金额
     */
    @NotNull(message = "实退金额不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal refundAmount;

    /**
     * 支付补贴金额
     */
    @NotNull(message = "支付补贴金额不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal subsidyAmount;

    /**
     * 抵扣积分
     */
    @NotNull(message = "抵扣积分不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long refundIntegral;


}
