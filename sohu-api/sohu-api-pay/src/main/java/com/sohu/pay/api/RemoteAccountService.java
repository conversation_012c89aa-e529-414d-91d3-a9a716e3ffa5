package com.sohu.pay.api;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.pay.api.bo.SohuAccountBo;
import com.sohu.pay.api.bo.SohuAccountUpgradeBo;
import com.sohu.pay.api.model.SohuAccountBankModel;
import com.sohu.pay.api.vo.SohuAccountBankVo;
import com.sohu.pay.api.vo.SohuAccountVo;

import java.math.BigDecimal;
import java.util.*;

/**
 * 翼码账号
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
public interface RemoteAccountService {

//    /**
//     * 查询用户翼码账号id
//     *
//     * @param userId
//     */
//    @Deprecated
//    String userAccount(Long userId);

    /**
     * 查询用户翼码账号id
     *
     * @param userId
     * @return
     */
    String getMerchantId(Long userId);

//    /**
//     * 获取企业认证翼码id
//     */
//    @Deprecated
//    String getBusinessAccountByUserId(Long userId);

//    /**
//     * 根据userId查询成功开户账号信息
//     * @param userId
//     * @return
//     */
//    @Deprecated
//    SohuAccountModel selectAccountByUserId(Long userId);

//    /**
//     * 根据userIds查询开户账号信息
//     */
//    @Deprecated
//    SohuAccountModel queryAccountByUserId(Long userId);

    /**
     * 根据用户id查询开户账号
     */
    SohuAccountVo queryByUserId(Long userId);

    /**
     * 根据用户id查询开户账号(通过的)
     *
     * @param userId
     * @return
     */
    SohuAccountVo queryByUserIdOfPass(Long userId);

    /**
     * 根据用户id查询开户账号(通过的或审核中)
     *
     * @param userId
     * @return
     */
    SohuAccountVo queryByUserIdOfPassOrWaitApprove(Long userId);

    /**
     * 根据userIds查询开户账号信息
     *
     * @param userIds
     * @return
     */
    Map<Long, SohuAccountVo> selectAccountMapByUserIdsOfPass(Collection<Long> userIds);

    /**
     * 根据userIds查询开户账号信息
     *
     * @param userIds
     * @return
     */
    Map<Long, SohuAccountVo> selectAccountMapByUserIds(Collection<Long> userIds);

    /**
     * 批量获取用户银行卡数据
     */
    @Deprecated
    List<SohuAccountBankModel> queryListByUserId(List<Long> userIds);

    /**
     * 批量获取用户银行卡审核通过数据
     */
    List<SohuAccountBankVo> queryPassListByUserId(List<Long> userId);

    /**
     * 获取用户银行卡数据
     */
    SohuAccountBankModel queryAccountBankByUserId(Long userId);

    /**
     * 翼码业务入驻
     *
     * @return
     */
    Boolean YiMaBankAdd(String msg);

//    /**
//     * 保存账户信息
//     *
//     * @param sohuAccountModel SohuAccountModel
//     */
//    @Deprecated
//    void saveAccount(SohuAccountModel sohuAccountModel);

//    /**
//     * 获取审核通过的账户信息
//     * @param userId 用户id
//     * @return SohuAccountModel
//     */
//    @Deprecated
//    SohuAccountModel selectPassAccountByUserId(Long userId);

    /**
     * 获取审核通过的账户信息
     *
     * @return SohuAccountModel
     */
    @Deprecated
    TableDataInfo<SohuAccountVo> selectPassAccountList(PageQuery pageQuery);


    /**
     * 获取审核通过的账户数量
     *
     * @return SohuAccountModel
     */
    long selectPassAccountCount();


//    /**
//     * 根据用户id查询账户信息
//     *
//     * @param userId
//     * @return SohuAccountModel
//     */
//    @Deprecated
//    SohuAccountModel checkAccountTypeByUserId(Long userId);

    /**
     * 跟新过期状态
     *
     * @return
     */
    Boolean updateStateOfTimeout();

    /**
     * 修改开户账号
     */
    Boolean update(SohuAccountBo bo);

    /**
     * 修改开户账号
     */
    Boolean updateByBo(SohuAccountBo bo);

    /**
     * 保存开户账号
     */
    Boolean save(SohuAccountBo bo);

    /**
     * 保存开户账号
     */
    Boolean saveByBo(SohuAccountBo bo);

    /**
     * 审核-通过第三方
     *
     * @param bo
     * @return 返回错误信息。没有错误信息，则是成功
     */
    String auditByThirdParty(SohuAccountBo bo);

    /**
     * 发送提现申请
     *
     * @param userId
     * @param tradeNo
     * @param amount
     * @param remark
     * @return
     */
    Map<String, String> sendYmWithdrawal(Long userId, String tradeNo, BigDecimal amount, String remark, Long billId);

    /**
     * 查询提现结果
     *
     * @param tradeNo
     * @return
     */
    String queryWithdrawal(String tradeNo, Long billId);

    /**
     * 实名认证
     *
     * @param bo
     * @return
     */
    Long insertByBo(SohuAccountBo bo);

    /**
     * 实名认证升级(个人->企业)
     *
     * @param upgradeBo
     * @return
     */
    Boolean upgrage(SohuAccountUpgradeBo upgradeBo);

    /**
     * 账户升级定时任务
     *
     * @return
     */
    Boolean accountUpgradeJobHandler();

    /**
     * 查询账户列表
     *
     * @param bo
     * @return
     */
    List<SohuAccountVo> queryList(SohuAccountBo bo);

    /**
     * 判断账号是否处于冻结中，冻结后发生的交易不参与分销
     * @param userIds
     * @param freezeTime
     * @return
     */
    Map<Long, Boolean> afterFreezeMap(List<Long> userIds, Date freezeTime);
}
