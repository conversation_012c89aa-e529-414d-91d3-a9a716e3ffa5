package com.sohu.pay.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 分账模版业务对象
 *
 * <AUTHOR>
 * @date 2023-10-11
 */

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SohuIndependentPlayetTemplateBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 分销人比例-0.00
     */
    @NotNull(message = "分销人比例-0.00不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal distributorRatio;

    /**
     * 分销人的拉新人比例-0.00
     */
    @NotNull(message = "分销人的拉新人比例-0.00不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal distributorInviteRatio;

    /**
     * 消费者的拉新人比例-0.00
     */
    @NotNull(message = "消费者的拉新人比例-0.00不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal consumerInviteRatio;

    /**
     * 平台系数-0.00
     */
    @NotNull(message = "平台系数-0.00不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal platformRatio;


    /**
     * 平台成本-0.00
     */
    @NotNull(message = "平台比例-0.00不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal adminRatio;

    /**
     * 操作修改类型-json数据:{"distributorRatio":"20"} -- 分销人 20%
     */
    //private SohuIndependentTemplate types;


}
