package com.sohu.pay.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum YiMaPayType {

    aliPay("502", "支付宝支付"),
    wxPay("503", "微信支付"),
    unionPay("512", "银联二维码"),
    appletPay("515", "小程序支付"),
    digitalRmb("516", "数字人民币"),
    memberBalance("519", "会员余额支付");

    private String code;
    private String desc;

}
