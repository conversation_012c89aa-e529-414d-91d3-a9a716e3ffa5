package com.sohu.im.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 任务群角色标识
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ImGroupTaskRole {

    // 任务发布方
    taskPublish("taskPublish", "任务方", "https://sohugloba.oss-cn-beijing.aliyuncs.com/taskPublish.png"),
    // 任务接单方
    taskRece("taskRece", "接单方", "https://sohugloba.oss-cn-beijing.aliyuncs.com/taskRece.png"),
    ;

    private String code;
    private String desc;
    private String avatar;

    // 检查枚举是否存在的方法
    public static boolean isExist(String code) {
        for (ImGroupTaskRole role : ImGroupTaskRole.values()) {
            if (role.name().equals(code)) {
                return true;
            }
        }
        return false;
    }

}
