package com.sohu.im.api.bo;

import com.sohu.common.core.validate.AddGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 申请加群
 */
@Data
public class SohuImApplyGroupBo implements Serializable {

    @NotNull(message = "群ID不能为空", groups = {AddGroup.class})
    @Schema(name = "groupId", description = "群id，必传", example = "1")
    private Long groupId;

    @NotNull(message = "邀请人ID不能为空", groups = {AddGroup.class})
    @Schema(name = "inviteUserId", description = "邀请人ID(用户id)，必传", example = "1")
    private Long inviteUserId;

    @NotNull(message = "邀请人ID与指定数位运算结果不能为空", groups = {AddGroup.class})
    @Schema(name = "inviteCalculate", description = "邀请人ID与指定数位运算结果，防止破解，必传", example = "2314657324")
    private Long inviteCalculate;

    @Schema(name = "applyContent", description = "申请原因,非必传", example = "通过XXX邀请进群")
    private String applyContent;

    @Schema(name = "applyContent", description = "进群渠道code码,非必传", example = "sdf34523423")
    private String channelCode;
}
