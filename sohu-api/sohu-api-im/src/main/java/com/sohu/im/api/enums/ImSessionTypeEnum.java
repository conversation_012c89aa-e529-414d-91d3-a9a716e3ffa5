package com.sohu.im.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会话类型
 */
@Getter
@AllArgsConstructor
public enum ImSessionTypeEnum {

    single("single", "单聊"),
    group("group", "群聊"),
    groupTask("groupTask", "普通任务群"),
    groupTaskCustom("groupTaskCustom", "普通任务客户群"),
    merchant("merchant", "商城消息"),
    groupForm("groupForm", "表单任务主群"),
    groupFormCustom("groupFormCustom", "表单任务客户群"),
    groupFromGeneral("groupFromGeneral", "通用商单两人私密群"),
    groupAgent("groupAgent", "服务商专属群"),
    ;

    private String code;
    private String desc;
}
