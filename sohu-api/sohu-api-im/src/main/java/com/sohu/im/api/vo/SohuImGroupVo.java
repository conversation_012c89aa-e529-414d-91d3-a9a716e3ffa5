package com.sohu.im.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * im群组视图对象
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
@Data
@ExcelIgnoreUnannotated
public class SohuImGroupVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "群组id")
    @Schema(name = "id", description = "群主键ID", example = "2")
    private Long id;

    @Schema(name = "pid", description = "父群ID", example = "1")
    private Long pid;

    @ExcelProperty(value = "描述")
    @Schema(name = "description", description = "描述", example = "这是一个官方群")
    private String description;

    @ExcelProperty(value = "群名称")
    @Schema(name = "name", description = "群名称", example = "官方反馈群")
    private String name;

    @ExcelProperty(value = "群主")
    @Schema(name = "userId", description = "群主用户ID", example = "1")
    private Long userId;

    @ExcelProperty(value = "群logo")
    @Schema(name = "logo", description = "群logo", example = "https://sohugloba.oss-cn-beijing.aliyuncs.com/app/default_avatar.png")
    private String logo;

    @ExcelProperty(value = "进群招呼")
    @Schema(name = "groupGreet", description = "进群招呼", example = "您好！欢迎进入官方反馈群交流群！")
    private String groupGreet;

    @ExcelProperty(value = "群公告")
    @Schema(name = "groupNotice", description = "群公告", example = "1.本群提倡平等友好礼貌交流，积极和谐互动\n" +
            "2.禁止发布广告，恶意刷屏\n" +
            "3.不遵守规定，言论不当的成员会被踢出群聊")
    private String groupNotice;

    @Schema(name = "needConfirm", description = "进群是否需要确认（false=不需要 true=需要）", example = "true")
    private Boolean needConfirm;

    @Schema(name = "groupUserNum", description = "群成员数", example = "10")
    private int groupUserNum;

    @Schema(name = "createTime", description = "群创建时间", example = "2024-10-10 09:50:03")
    private Date createTime = new Date();

    @Schema(name = "updateTime", description = "群更新时间", example = "2024-10-10 09:50:03")
    private Date updateTime = new Date();

    @Schema(name = "forbid", description = "是否禁言（false=否 true=是）", example = "true")
    private Boolean forbid;

    @Schema(name = "maxGroupUserNum", description = "最大成员数", example = "500")
    private int maxGroupUserNum;

    @Schema(name = "maxGroupAdminNum", description = "最大群管理员人数", example = "5")
    private int maxGroupAdminNum;

    @Schema(name = "count", description = "群消息条数", example = "99", defaultValue = "0")
    private long count = 0;

    @Schema(name = "groupTransfer", description = "是否在转让中，true=在转让中 false=不在转让中", example = "false")
    private boolean groupTransfer;

    @Schema(name = "groupType", description = "群类型：group-普通群，groupTask-普通任务群,groupTaskCustom-任务客户群", example = "group")
    private String groupType;

    @Schema(name = "authorize", description = "是否需要授权手机号", example = "true")
    private Boolean authorize;

    @Schema(name = "authorizeExt", description = "授权扩展字段：{\n" +
            "      \"authorizeTitle\":\"授权标题\",\n" +
            "      \"authorizeContent\":\"授权内容\",\n" +
            "      \"authorizePhoto\":\"授权图片\"\n" +
            "}", example = "{\"authorizeTitle\":\"授权标题\",\"authorizeContent\":\"授权内容\",\"authorizePhoto\":\"授权图片\"}")
    private String authorizeExt;

    @Schema(name = "groupLink", description = "群链接", example = "1")
    private String groupLink;

    @Schema(name = "groupExt", description = "群扩展字段",
            example = "{\"groupTaskChildNumber\":\"CT779172852470952778957\",\"taskReceUserId\":2,\"taskPublishUserId\":146}")
    private String groupExt;

    @Schema(name = "unq", description = "群unq", example = "5556631442456277")
    private Long unq;

    @Schema(name = "bindUser", description = "进群是否绑定拉新关系（false=否 true=是）", example = "true")
    private Boolean bindUser;

    @Schema(name = "addFriend", description = "是否禁止添加好友（false=否 true=是）", example = "true")
    private Boolean addFriend;

    @Schema(name = "forbidTime", description = "是否按时间段禁言（false=否 true=是）", example = "true")
    private Boolean forbidTime;

    @Schema(name = "groupUserNickName", description = "用户在本群的昵称", example = "官方客服")
    private String groupUserNickName;

    @Schema(name = "timeList", description = "禁言时间段数组")
    private List<SohuImGroupForbidTimeVo> timeList;

    @Schema(name = "disturb", description = "该群是否设置了防骚扰规则", example = "true", defaultValue = "false")
    private Boolean disturb = Boolean.FALSE;

    @Schema(name = "groupUserPermissionType", example = "group_leader",
            description = "当前登录人在该群的身份(group_leader=群主 group_admin=管理员 group_user=普通成员)")
    private String groupUserPermissionType;

    @Schema(name = "specialRole", description = "特殊身份，taskPublish=发单方，taskRece=接单方", example = "taskPublish")
    private String specialRole;

    @Schema(name = "groupWord", description = "进群口令", example = "qwer1234")
    private String groupWord;

    @Schema(name = "planVo", description = "表单任务详情")
    private SohuImGroupFormPlanVo planVo;

    @Schema(name = "state", description = "群状态字段（默认0，0=正常 1=群禁用 2=通用商单结束）", example = "0")
    private Integer state;

    @Schema(title = "群主昵称", example = "菜菜")
    private String groupOwnerNickName;

    @Schema(title = "群主UID", example = "hss_123")
    private String groupOwnerUserName;

    @Schema(name = "inviteUserId", description = "邀请人ID(用户id)，必传", example = "1")
    private Long inviteUserId;

    @Schema(name = "inviteCalculate", description = "邀请人ID与指定数位运算结果，防止破解，必传", example = "2314657324")
    private Long inviteCalculate;
}
