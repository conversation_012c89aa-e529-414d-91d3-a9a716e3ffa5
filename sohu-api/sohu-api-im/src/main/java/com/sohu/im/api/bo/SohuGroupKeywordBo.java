package com.sohu.im.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 群聊关键词设置业务对象
 *
 * <AUTHOR>
 * @date 2024-05-11
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuGroupKeywordBo extends SohuEntity {

    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    @Schema(name = "id", description = "主键ID,新增不要传，编辑必传！", example = "1")
    private Long id;

    @Schema(name = "groupId", description = "群ID", example = "1")
    @NotNull(message = "群ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long groupId;

    @Schema(name = "keywordOpen", description = "关键词开关，默认关闭（true-1-开启）", example = "true")
    private Boolean keywordOpen;

    @Schema(name = "keyword", description = "关键词,逗号隔开", example = "广告,促销")
    private String keyword;

    @Schema(name = "whiteList", description = "白名单用户id,逗号隔开", example = "1,2,3")
    private String whiteList;

}
