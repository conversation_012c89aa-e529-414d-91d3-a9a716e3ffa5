package com.sohu.im.api.bo;

import com.sohu.common.core.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * im群组业务对象-禁用
 *
 * <AUTHOR>
 * @date 2023-08-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuImGroupDisableBo extends BaseEntity {

    @NotNull(message = "群组id不能为空")
    @Schema(title = "群组id", example = "2")
    private Long groupId;

    @Schema(title = "原因", example = "该群涉及诈骗")
    private String reason;

}
