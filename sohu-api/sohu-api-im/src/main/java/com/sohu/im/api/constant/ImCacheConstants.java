package com.sohu.im.api.constant;

/**
 * IM缓存常量
 */
public interface ImCacheConstants {

    /**
     * 消息未读-IM
     */
    String IM_UN_READ = "im_un_read";

    /**
     * 记录解散的群用户ID
     */
    String IM_UN_GROUP_USERS = "im_un_group";

    /**
     * im置顶
     */
    String IM_AS_TOP = "im_as_top";

    /**
     * 群
     */
    String GROUP = "group";

    /**
     * 群口令
     */
    String GROUP_WORD = "group_word";

    /**
     * 群用户
     */
    String GROUP_USER = "group_user";

    /**
     * 群的所有用户
     */
    String GROUP_ALL_USER = "group_all_user";

    /**
     * 群成员数量
     */
    String GROUP_USER_COUNT = "group_user_count";

    /**
     * 用户的所有群
     */
    String USER_ALL_GROUP = "user_all_group";

    /**
     * IM 检测数据
     */
    String IM_RISK_CHECK = "im_risk_check";
}
