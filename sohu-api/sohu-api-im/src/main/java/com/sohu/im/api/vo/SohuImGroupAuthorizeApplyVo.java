package com.sohu.im.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 群授权申请视图对象
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
@ExcelIgnoreUnannotated
public class SohuImGroupAuthorizeApplyVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 群ID
     */
    @ExcelProperty(value = "群ID")
    private Long groupId;

    /**
     * 申请理由
     */
    @ExcelProperty(value = "申请理由")
    private String applyMsg;

    /**
     * 关联对象ID
     */
    @ExcelProperty(value = "关联对象ID")
    private String relateObjId;

    /**
     * 关联对象类型
     */
    @ExcelProperty(value = "关联对象类型")
    private String relateObjType;

    /**
     * 申请状态
     */
    @ExcelProperty(value = "申请状态")
    private String state;

    /**
     * 拒绝理由
     */
    @ExcelProperty(value = "拒绝理由")
    private String rejectReason;


}
