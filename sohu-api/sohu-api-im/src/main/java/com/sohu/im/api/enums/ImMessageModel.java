package com.sohu.im.api.enums;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 聊天消息接收入参
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class ImMessageModel implements Serializable {

    /**
     * 消息类型;(如：text、image、voice、video、music、busyOrder、good,follow)
     * {@link com.sohu.common.core.enums.MsgTypeEnum}
     */
    private String msgType;
    /**
     * 消息标题
     * {@link com.sohu.common.core.enums.NotificationTypeEnum}
     */
    private String title;
    /**
     * 消息内容
     * {@link com.sohu.common.core.enums.NotificationTypeEnum}
     */
    private String content;
    /**
     * 群ID，私聊时，该字段等于0
     */
    private Long group;
    /**
     * 接收人ID，群聊时，该字段等于0
     */
    private Long receiverId;

    /**
     * 系统消息类型;(如：非系统消息-none,系统消息-system)
     * {@link com.sohu.common.core.enums.ImSystemTypeEnum}
     */
    private String systemType = "none";
    /**
     * 拓展数据
     */
    private String ext;
}
