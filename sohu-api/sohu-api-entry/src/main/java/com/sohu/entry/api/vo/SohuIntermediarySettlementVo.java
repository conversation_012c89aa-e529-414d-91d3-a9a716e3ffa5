package com.sohu.entry.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 中介入驻提交资料视图对象
 *
 * <AUTHOR>
 * @date 2023-08-30
 */
@Data
@ExcelIgnoreUnannotated
public class SohuIntermediarySettlementVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 关联入驻主表id
     */
    private Long enterId;

    /**
     * 行业标识
     */
    @Deprecated
    private String ident;

    /**
     * 注册信息
     */
    @ExcelProperty(value = "注册信息")
    private String companyRegisterInfo;
    /**
     * 汇款账户信息
     */
    @ExcelProperty(value = "汇款账户信息")
    private String remittanceAccountInfo;

    /**
     * 经纪人从业资格证
     */
    @ExcelProperty(value = "经纪人从业资格证")
    private String brokerCertification;

}
