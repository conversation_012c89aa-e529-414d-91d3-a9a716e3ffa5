package com.sohu.entry.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 角色入驻配置管理业务对象-修改入驻审核后的授予权限
 *
 */
@Data
public class SohuPlatformConfigUpdateRoleBo implements Serializable {

    /**
     * 主键id，修改传
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 入驻后授予的角色，功能角色key集合，英文逗号分开
     */
    @Schema(title = "功能角色key集合",description = "入驻后授予的角色，功能角色key集合，英文逗号分开",example = "article,common")
    @NotBlank(message = "功能角色key集合不能为空")
    private String roleKeysStr;

}
