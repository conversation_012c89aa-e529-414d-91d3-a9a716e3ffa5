package com.sohu.entry.api.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;


/**
 * <AUTHOR>
 */
@Schema(description = "内容关联项分页查询响应结果 Response VO")
@Data
@ToString(callSuper = true)
public class RelationRespModel implements Serializable {

    @Schema(description = "业务类型", example = "Project", required = true)
    private String busyType;

    @Schema(description = "业务标题", example = "塞浦路斯塞岛家园")
    private String busyTitle;

    @Schema(description = "业务ID", example = "56")
    private Long busyCode;

    @Schema(description = "封面图", example = "https://sohugloba.oss-cn-beijing.aliyuncs.com/20230511/35b98ee6637be1ef9982c7b94ab778fcf9dff2ae400b0585902dbf0cde6e0a1a.png")
    private String busyCoverImg;

    @Schema(description = "创作人头像", example = "https://sohugloba.oss-cn-beijing.aliyuncs.com/20230511/35b98ee6637be1ef9982c7b94ab778fcf9dff2ae400b0585902dbf0cde6e0a1a.png")
    private String belongerAvatar;

    @Schema(description = "创作人名称", example = "13026152281")
    private String belongerName;

    @Schema(description = "创作人ID", example = "195")
    private Long busyBelonger;

    @Schema(description = "发布时间", example = "2023-5-13 11:09:52")
    private String busyPublishTime;

    @Schema(description = "关联项单位", required = true, example = "1000万每套")
    private String busyUnit;
    /**
     * 关联项内容
     */
    @Schema(description = "关联项内容", example = "盛大开业")
    private String busyContent;

    /**
     * 关联项收藏数
     */
    @Schema(description = "关联项收藏数", example = "999")
    private Integer busyCollectCount;

    /**
     * 是否收藏了关联项
     */
    @Schema(description = "是否收藏了关联项", example = "true")
    private Boolean busyCollect = false;

    /**
     * 是否点赞了关联项
     */
    @Schema(description = "是否点赞了关联项", example = "true")
    private Boolean busyLike = false;

}
