package com.sohu.entry.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户行业资质认证资料业务对象
 *
 * <AUTHOR>
 * @date 2023-08-29
 */

@Data
public class SohuEntryAuthEditBo implements Serializable {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * sohu_industry表ID
     */
    @NotNull(message = "行业ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long industryId;

    /**
     * sohu_industry表行业全名称
     */
    @NotEmpty(message = "行业全名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String industryFullName;

    /**
     * 其它认证资料
     */
    private String ext;

}
