package com.sohu.entry.api.model;

import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;

import java.io.Serializable;


/**
 * 用户入驻行业认证资料视图对象
 */
@Deprecated
@Data
public class SohuEntryAuthModel extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 站点id
     */
    private Long siteId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户姓名
     */
    private String legalName;

    /**
     * 入驻角色权限字符串
     */
    private String roleKey;

//    /**
//     * 角色名称
//     */
//    private String roleName;

    /**
     * sohu_industry表ID
     */
    private Long industryId;

    /**
     * sohu_industry表行业全名称
     */
    private String industryFullName;

    /**
     * 其它认证资料
     */
    private String ext;

    /**
     * 认证状态【WaitApprove:审核中,OnShelf:通过,Refuse:审核拒绝】
     */
    private String state;

    /**
     * 拒绝理由或者取消认证理由
     */
    private String rejectReason;


}
