package com.sohu.entry.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 查询用户认证详情
 */
@Data
@ExcelIgnoreUnannotated
public class SohuEntryAuthInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 认证类型(personal:个人  business:企业认证)
     */
    private String accountType;

    /**
     * 认证行业(二级)
     */
    private String industryName;

    /**
     * 认证资质
     */
    private List<String> aptitudeList;

}
