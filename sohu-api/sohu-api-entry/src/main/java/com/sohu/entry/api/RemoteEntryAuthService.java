package com.sohu.entry.api;

import com.sohu.entry.api.bo.SohuEntryAuthAddBo;
import com.sohu.entry.api.bo.SohuEntryAuthAuditBo;
import com.sohu.entry.api.bo.SohuEntryAuthBo;
import com.sohu.entry.api.bo.SohuEntryAuthEditBo;
import com.sohu.entry.api.model.SohuEntryIndustryModel;
import com.sohu.entry.api.vo.SohuEntryAuthInfoVo;
import com.sohu.entry.api.vo.SohuEntryAuthStatVo;
import com.sohu.entry.api.vo.SohuEntryAuthVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.List;

/**
 * 用户入驻行业认证资料Service接口
 */
public interface RemoteEntryAuthService {

    /**
     * 查询用户入驻行业认证资料
     */
    SohuEntryAuthVo queryById(Long id);

    /**
     * 查询用户入驻行业认证列表
     */
    TableDataInfo<SohuEntryAuthVo> queryPageList(SohuEntryAuthBo bo, PageQuery pageQuery);

    /**
     * 修改用户入驻行业认证资料
     */
    void insertByBo(SohuEntryAuthAddBo bo);

    /**
     * 修改用户入驻行业认证资料
     */
    void updateByBo(SohuEntryAuthEditBo bo);

//    /**
//     * 查询当前用户已入驻的角色和已认证行业
//     * @return
//     */
//    @Deprecated
//    SohuEntryRoleAuthModel selectEntryRole();

//    /**
//     * 获取当前用户最新的一条行业认证数据
//     * @return
//     */
//    @Deprecated
//    SohuEntryAuthModel selectVoOfLastByUserId(Long userId);

    /**
     * 查询当前用户已认证行业（树形结构）
     */
    List<SohuEntryIndustryModel> selectEntryIndustryOfPass();

    /**
     * 查询当前用户未认证的行业（树形结构）
     *
     * @return
     */
    List<SohuEntryIndustryModel> selectEntryIndustryOfWait();

    /**
     * 审核
     *
     * @param bo
     */
    void audit(SohuEntryAuthAuditBo bo);

    /**
     * 查询当前用户已认证行业资质
     */
    List<SohuEntryAuthVo> selectEntryAuthOfPass();

    /**
     * 查询用户已认证的行业(二级)
     */
    List<String> getUserEntryAuth(Long userId);

    /**
     * 查询用户认证详情
     */
    List<SohuEntryAuthInfoVo> getUserEntryAuthInfo(Long userId);

    /**
     * 获取行业入驻统计-根节点
     * @return
     */
    List<SohuEntryAuthStatVo> getEntryAuthStatOfRoot();
    /**
     * 获取行业入驻统计-叶子节点
     * @return
     */
    List<SohuEntryAuthStatVo> getEntryAuthStatOfLeaf();

}
