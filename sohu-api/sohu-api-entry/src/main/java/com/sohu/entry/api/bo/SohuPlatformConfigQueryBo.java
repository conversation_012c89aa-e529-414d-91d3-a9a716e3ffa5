package com.sohu.entry.api.bo;

import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 角色入驻配置管理查询业务对象
 *
 * <AUTHOR>
 * @date 2024-09-20
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuPlatformConfigQueryBo extends BaseEntity implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色状态(ENABLE：启用，DISABLE：禁用)
     */
    private String roleStatus;

    /**
     * 身份认证类型(personal:个人 business:企业)
     */
    private String type;

    /**
     * 资质入驻(0停用 1启用)
     */
    private Boolean status;

}
