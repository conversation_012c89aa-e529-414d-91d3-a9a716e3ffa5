package com.sohu.entry.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 角色入驻配置管理视图对象
 *
 * <AUTHOR>
 * @date 2024-09-20
 */
@Data
@ExcelIgnoreUnannotated
public class SohuPlatformConfigVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 角色id
     */
    private String roleId;

    /**
     * 平台角色权限字符串
     */
    private String roleKey;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色状态(ENABLE：启用，DISABLE：禁用)
     */
    private String roleStatus;

    /**
     * 身份认证类型(personal:个人 business:企业)
     */
    private String type;

    /**
     * 资质入驻(0停用 1启用)
     */
    private Boolean status;

    /**
     * 入驻状态(0关闭 1开启)
     */
    private Boolean entryStatus;

    /**
     * 资质数量
     */
    private Long aptitudeCount;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 介绍页
     */
    private String introPage;

    /**
     * 协议id
     */
    private Long agreeId;

    /**
     * 版本id
     */
    private Long versionId;

    /**
     * 资质内容列表
     */
    private List<SohuPlatformConfigAptitudeVo> aptitudeList;

    /**
     * 入驻后授予的角色，功能角色key集合，英文逗号分开
     */
    private String roleKeysStr;

}
