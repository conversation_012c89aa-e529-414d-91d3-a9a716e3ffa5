# 基于 sys_user 表的开放平台认证系统实现

## 概述

本实现基于现有的 `sys_user` 表，通过 `user_type` 字段来区分开放平台用户和系统用户，避免了创建额外的用户表，充分利用了现有的用户管理基础设施。

## 设计思路

### 1. 用户类型区分
- **系统用户**: `user_type = 'sys_user'` (默认值)
- **开放平台用户**: `user_type = 'open_platform'`

### 2. 字段映射
开放平台用户在 `sys_user` 表中的字段使用：

| 开放平台概念 | sys_user字段 | 说明 |
|-------------|-------------|------|
| 邮箱 | email | 用户邮箱 |
| 用户名 | user_name | 使用邮箱作为用户名 |
| 昵称 | nick_name | 用户昵称 |
| 密码 | password | BCrypt加密密码 |
| 状态 | status | 0正常 1停用 |
| 用户类型 | user_type | 'open_platform' |
| 最后登录时间 | login_date | 最后登录时间 |
| 最后登录IP | login_ip | 最后登录IP |

### 3. 优势
1. **复用现有基础设施**: 利用现有的用户管理、权限控制等功能
2. **统一用户体系**: 开放平台用户和系统用户在同一个表中管理
3. **减少数据冗余**: 避免创建重复的用户表结构
4. **便于扩展**: 可以轻松为开放平台用户分配系统角色和权限

## 核心实现

### 1. 用户类型枚举
```java
public enum UserTypeEnum {
    SYS_USER("sys_user", "系统用户"),
    OPEN_PLATFORM("open_platform", "开放平台用户");
}
```

### 2. 服务层实现
```java
@Service
public class OpenPlatformUserServiceImpl implements IOpenPlatformUserService {
    
    // 登录时查询开放平台用户
    SysUser user = userMapper.selectOne(
        Wrappers.lambdaQuery(SysUser.class)
            .eq(SysUser::getEmail, loginBo.getEmail())
            .eq(SysUser::getUserType, UserTypeEnum.OPEN_PLATFORM.getCode())
            .eq(SysUser::getDelFlag, "0")
    );
    
    // 注册时创建开放平台用户
    SysUser user = new SysUser();
    user.setUserName(registerBo.getEmail()); // 使用邮箱作为用户名
    user.setUserType(UserTypeEnum.OPEN_PLATFORM.getCode());
    user.setEmail(registerBo.getEmail());
    // ... 其他字段设置
}
```

### 3. 数据查询策略
所有开放平台用户相关的查询都会添加以下条件：
- `user_type = 'open_platform'`
- `del_flag = '0'`

这确保了开放平台用户和系统用户的数据隔离。

## API接口

### 认证接口
| 方法 | 路径 | 功能 |
|------|------|------|
| POST | /open/auth/login | 邮箱密码登录 |
| POST | /open/auth/register | 用户注册 |
| POST | /open/auth/send-email-code | 发送邮箱验证码 |
| POST | /open/auth/forgot-password | 忘记密码 |
| POST | /open/auth/reset-password | 重置密码 |

### 请求示例

#### 用户注册
```json
{
  "email": "<EMAIL>",
  "password": "123456",
  "confirmPassword": "123456",
  "nickname": "用户昵称",
  "emailCode": "123456"
}
```

#### 用户登录
```json
{
  "email": "<EMAIL>",
  "password": "123456"
}
```

## 数据库表结构

### 主要存储
1. **sys_user**: 用户主表（现有）
2. **Redis**: 密码重置令牌存储（Key: `password_reset_token:{token}`, Value: 用户ID, TTL: 1小时）
3. **sohu_open_platform_email_code**: 邮箱验证码表

### sys_user 表关键字段
```sql
CREATE TABLE sys_user (
    user_id           bigint(20)      NOT NULL                   COMMENT '用户ID',
    user_name         varchar(30)     NOT NULL                   COMMENT '用户账号',
    nick_name         varchar(30)     NOT NULL                   COMMENT '用户昵称',
    user_type         varchar(10)     DEFAULT 'sys_user'         COMMENT '用户类型',
    email             varchar(50)     DEFAULT ''                 COMMENT '用户邮箱',
    password          varchar(100)    DEFAULT ''                 COMMENT '密码',
    status            char(1)         DEFAULT '0'                COMMENT '帐号状态',
    del_flag          char(1)         DEFAULT '0'                COMMENT '删除标志',
    login_ip          varchar(128)    DEFAULT ''                 COMMENT '最后登录IP',
    login_date        datetime                                   COMMENT '最后登录时间',
    -- 其他字段...
    PRIMARY KEY (user_id)
);
```

## 安全特性

### 1. 数据隔离
- 通过 `user_type` 字段确保开放平台用户和系统用户的数据隔离
- 所有查询都会添加用户类型条件

### 2. 密码安全
- 使用 BCrypt 进行密码加密
- 密码强度验证（6-20位字符）

### 3. 邮箱验证
- 注册时需要邮箱验证码
- 验证码10分钟有效期，一次性使用

### 4. 密码重置
- 通过邮件发送重置链接
- 重置令牌1小时有效期，一次性使用

## 扩展能力

### 1. 权限集成
由于开放平台用户存储在 `sys_user` 表中，可以轻松为其分配系统角色和权限：

```sql
-- 为开放平台用户分配角色
INSERT INTO sys_user_role (user_id, role_id) VALUES (?, ?);
```

### 2. 部门管理
可以为开放平台用户分配部门：

```sql
-- 更新用户部门
UPDATE sys_user SET dept_id = ? WHERE user_id = ? AND user_type = 'open_platform';
```

### 3. 统一管理
系统管理员可以在用户管理界面中统一管理所有类型的用户，只需要在查询条件中区分用户类型。

## 迁移和兼容性

### 1. 现有数据兼容
- 现有的系统用户不受影响
- `user_type` 字段默认值为 `'sys_user'`，确保向后兼容

### 2. 数据迁移
如果需要将现有的开放平台用户迁移到新系统：

```sql
-- 更新现有用户的用户类型
UPDATE sys_user SET user_type = 'open_platform' 
WHERE user_id IN (/* 开放平台用户ID列表 */);
```

## 监控和维护

### 1. 用户统计
```sql
-- 统计不同类型用户数量
SELECT user_type, COUNT(*) as user_count 
FROM sys_user 
WHERE del_flag = '0' 
GROUP BY user_type;
```

### 2. 登录统计
```sql
-- 统计开放平台用户登录情况
SELECT DATE(login_date) as login_date, COUNT(*) as login_count
FROM sys_user 
WHERE user_type = 'open_platform' 
  AND login_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(login_date);
```

## 总结

基于 `sys_user` 表的实现方案具有以下优势：

1. **架构简洁**: 复用现有用户表，减少系统复杂度
2. **功能完整**: 支持注册、登录、忘记密码等完整功能
3. **扩展性强**: 可以轻松集成现有的权限管理系统
4. **维护简单**: 统一的用户管理，降低维护成本
5. **性能优良**: 避免跨表查询，提高查询效率

这种设计既满足了开放平台用户的独特需求，又充分利用了现有的系统基础设施，是一个平衡性能、功能和维护成本的优秀方案。
