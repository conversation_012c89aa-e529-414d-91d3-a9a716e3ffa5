# 许愿狐开放平台认证API文档

## 概述

本文档描述了许愿狐开放平台的用户认证相关API，包括用户注册、登录、忘记密码等功能。

## API接口

### 1. 发送邮箱验证码

**接口地址：** `POST /open/auth/send-email-code`

**请求参数：**
```json
{
  "email": "<EMAIL>",
  "codeType": "register"  // register: 注册, reset_password: 重置密码
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

### 2. 用户注册

**接口地址：** `POST /open/auth/register`

**请求参数：**
```json
{
  "email": "<EMAIL>",
  "password": "123456",
  "confirmPassword": "123456",
  "nickname": "用户昵称",
  "emailCode": "123456"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

### 3. 用户登录

**接口地址：** `POST /open/auth/login`

**请求参数：**
```json
{
  "email": "<EMAIL>",
  "password": "123456"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 7200,
    "userInfo": {
      "id": 1,
      "email": "<EMAIL>",
      "nickname": "用户昵称",
      "avatar": "http://example.com/avatar.jpg",
      "emailVerified": true
    }
  }
}
```

### 4. 忘记密码

**接口地址：** `POST /open/auth/forgot-password`

**请求参数：**
```json
{
  "email": "<EMAIL>"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

### 5. 重置密码

**接口地址：** `POST /open/auth/reset-password`

**请求参数：**
```json
{
  "token": "reset-token-from-email",
  "newPassword": "newpassword",
  "confirmPassword": "newpassword"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 500 | 服务器内部错误 |

## 使用流程

### 注册流程
1. 调用发送邮箱验证码接口，codeType设置为"register"
2. 用户收到邮箱验证码
3. 调用注册接口，提供邮箱、密码、验证码等信息

### 登录流程
1. 调用登录接口，提供邮箱和密码
2. 获取访问令牌，用于后续API调用

### 忘记密码流程
1. 调用忘记密码接口，提供邮箱
2. 用户收到重置密码邮件，包含重置链接
3. 用户点击链接，调用重置密码接口

## 注意事项

1. 邮箱验证码有效期为10分钟
2. 密码重置令牌有效期为1小时
3. 访问令牌有效期为2小时
4. 所有接口都支持跨域访问
5. 邮箱地址必须是有效的邮箱格式
6. 密码长度必须在6-20个字符之间

## 数据库表结构

### sys_user (用户表)
基于现有的sys_user表，通过user_type字段区分用户类型：
- user_id: 主键ID
- user_name: 用户账号（开放平台用户使用邮箱）
- nick_name: 用户昵称
- user_type: 用户类型（'open_platform'为开放平台用户）
- email: 邮箱
- password: 密码（BCrypt加密）
- status: 状态（0正常 1停用）
- login_date: 最后登录时间
- login_ip: 最后登录IP

### sohu_open_platform_password_reset_token (密码重置令牌表)
- id: 主键ID
- user_id: 用户ID
- token: 重置令牌
- expire_time: 过期时间
- used: 是否已使用

### sohu_open_platform_email_code (邮箱验证码表)
- id: 主键ID
- email: 邮箱
- code: 验证码
- code_type: 验证码类型
- expire_time: 过期时间
- used: 是否已使用
