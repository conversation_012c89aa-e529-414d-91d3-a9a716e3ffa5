# 快速开始指南

## 概述

本指南将帮助您快速部署和使用基于 `sys_user` 表的许愿狐开放平台认证系统。

## 前置条件

- ✅ 已有若依框架项目
- ✅ MySQL 8.0+
- ✅ Redis 6.0+
- ✅ 邮件服务配置

## 快速部署

### 1. 执行数据库脚本

```bash
# 执行建表脚本
mysql -u root -p your_database < script/sql/sohu_open_platform.sql
```

### 2. 配置邮件服务

在 `application.yml` 中添加邮件配置：

```yaml
mail:
  enabled: true
  host: smtp.exmail.qq.com
  port: 465
  auth: true
  from: <EMAIL>
  user: <EMAIL>
  pass: your-password
  sslEnable: true
```

### 3. 启动应用

```bash
mvn clean package -DskipTests
java -jar ruoyi-admin/target/ruoyi-admin.jar
```

## API 测试

### 1. 发送注册验证码

```bash
curl -X POST http://localhost:8080/open/auth/send-email-code \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "codeType": "register"
  }'
```

### 2. 用户注册

```bash
curl -X POST http://localhost:8080/open/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "123456",
    "confirmPassword": "123456",
    "nickname": "测试用户",
    "emailCode": "123456"
  }'
```

### 3. 用户登录

```bash
curl -X POST http://localhost:8080/open/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "123456"
  }'
```

## 验证部署

### 1. 检查数据库表

```sql
-- 检查表是否创建成功
SHOW TABLES LIKE 'sohu_open_platform%';

-- 检查 sys_user 表结构
DESC sys_user;
```

### 2. 检查用户类型

```sql
-- 查看用户类型枚举值
SELECT DISTINCT user_type FROM sys_user;

-- 查询开放平台用户
SELECT user_id, user_name, nick_name, email, user_type 
FROM sys_user 
WHERE user_type = 'open_platform';
```

### 3. 检查邮件配置

查看应用日志，确认邮件服务配置正确：

```bash
tail -f logs/sys-info.log | grep -i mail
```

## 常见问题

### Q1: 邮件发送失败
**解决方案**:
1. 检查邮箱配置是否正确
2. 确认邮箱密码或授权码
3. 检查网络连接

### Q2: 用户注册失败
**解决方案**:
1. 检查验证码是否正确
2. 确认邮箱是否已注册
3. 检查密码格式是否符合要求

### Q3: 登录失败
**解决方案**:
1. 确认用户是否已注册
2. 检查密码是否正确
3. 确认用户状态是否正常

## 功能验证清单

- [ ] 数据库表创建成功
- [ ] 邮件服务配置正确
- [ ] 发送验证码功能正常
- [ ] 用户注册功能正常
- [ ] 用户登录功能正常
- [ ] 忘记密码功能正常
- [ ] 密码重置功能正常

## 下一步

### 1. 前端集成
参考 `docs/frontend-example.html` 文件，集成前端页面。

### 2. 权限配置
为开放平台用户配置相应的角色和权限：

```sql
-- 创建开放平台用户角色
INSERT INTO sys_role (role_name, role_key, role_sort, status) 
VALUES ('开放平台用户', 'open_platform_user', 3, '0');

-- 为用户分配角色
INSERT INTO sys_user_role (user_id, role_id) 
SELECT u.user_id, r.role_id 
FROM sys_user u, sys_role r 
WHERE u.user_type = 'open_platform' 
  AND r.role_key = 'open_platform_user';
```

### 3. 监控配置
设置用户注册、登录等关键指标的监控。

### 4. 安全加固
- 配置 HTTPS
- 设置防火墙规则
- 启用访问日志

## 技术支持

如遇到问题，请参考以下文档：
- [API文档](open-platform-auth-api.md)
- [部署指南](deployment-guide.md)
- [数据库查询示例](database-queries-examples.md)
- [基于sys_user的实现说明](sys-user-based-implementation.md)

或联系技术支持团队。
