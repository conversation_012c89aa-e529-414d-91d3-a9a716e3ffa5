# 许愿狐开放平台认证系统部署指南

## 概述

本文档描述了如何部署和配置许愿狐开放平台的用户认证系统，包括数据库配置、邮件服务配置等。

## 前置条件

1. Java 17+
2. MySQL 8.0+
3. Redis 6.0+
4. Maven 3.6+

## 部署步骤

### 1. 数据库初始化

执行以下SQL脚本创建必要的数据表：

```bash
mysql -u root -p your_database < script/sql/sohu_open_platform.sql
```

或者手动执行SQL文件中的建表语句。

### 2. 邮件服务配置

在 `application-dev.yml` 或 `application-prod.yml` 中配置邮件服务：

```yaml
# 邮件发送配置
mail:
  enabled: true
  host: smtp.exmail.qq.com  # SMTP服务器地址
  port: 465                 # SMTP端口
  auth: true               # 是否需要认证
  from: <EMAIL>  # 发送方邮箱
  user: <EMAIL>  # 用户名
  pass: your-email-password    # 密码或授权码
  starttlsEnable: true     # 启用STARTTLS
  sslEnable: true          # 启用SSL
  timeout: 0               # 超时时间
  connectionTimeout: 0     # 连接超时时间
```

### 3. 数据库配置

确保数据库连接配置正确：

```yaml
spring:
  datasource:
    url: ******************************************************************************************************************************************************
    username: your_username
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### 4. Redis配置

配置Redis连接：

```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      password: your_redis_password
      database: 0
```

### 5. 编译和打包

```bash
mvn clean package -DskipTests
```

### 6. 启动应用

```bash
java -jar ruoyi-admin/target/ruoyi-admin.jar
```

## 配置说明

### 邮件模板配置

系统支持自定义邮件模板，可以在数据库中配置不同类型的邮件模板：

1. 注册验证码邮件
2. 密码重置邮件

### 安全配置

1. **密码加密**: 使用BCrypt进行密码加密
2. **验证码过期**: 邮箱验证码默认10分钟过期
3. **重置令牌过期**: 密码重置令牌默认1小时过期
4. **访问令牌过期**: 访问令牌默认2小时过期

### API接口配置

所有认证相关的API接口都在 `/open/auth` 路径下，无需认证即可访问：

- `POST /open/auth/login` - 用户登录
- `POST /open/auth/register` - 用户注册
- `POST /open/auth/send-email-code` - 发送邮箱验证码
- `POST /open/auth/forgot-password` - 忘记密码
- `POST /open/auth/reset-password` - 重置密码

## 监控和日志

### 日志配置

系统会记录以下关键操作的日志：

1. 用户登录/注册
2. 邮件发送
3. 密码重置
4. 验证码生成和验证

### 性能监控

建议监控以下指标：

1. API响应时间
2. 邮件发送成功率
3. 用户注册/登录成功率
4. 数据库连接池状态

## 故障排除

### 常见问题

1. **邮件发送失败**
   - 检查SMTP配置是否正确
   - 确认邮箱密码或授权码是否有效
   - 检查网络连接

2. **数据库连接失败**
   - 检查数据库服务是否启动
   - 确认连接参数是否正确
   - 检查数据库用户权限

3. **验证码不生效**
   - 检查Redis连接是否正常
   - 确认系统时间是否正确

### 日志查看

```bash
# 查看应用日志
tail -f logs/sys-info.log

# 查看错误日志
tail -f logs/sys-error.log
```

## 安全建议

1. **生产环境配置**
   - 使用HTTPS协议
   - 配置防火墙规则
   - 定期更新密码
   - 启用访问日志

2. **数据库安全**
   - 使用专用数据库用户
   - 限制数据库访问权限
   - 定期备份数据

3. **邮件安全**
   - 使用专用邮箱账号
   - 启用两步验证
   - 定期更换密码

## 扩展配置

### 自定义邮件模板

可以通过修改邮件发送逻辑来支持HTML邮件模板：

```java
// 在SohuOpenPlatformUserServiceImpl中修改sendEmailCode方法
String htmlContent = String.format(
    "<html><body><h2>验证码</h2><p>您的验证码是：<strong>%s</strong></p><p>有效期10分钟</p></body></html>", 
    code
);
MailUtils.sendHtml(email, subject, htmlContent);
```

### 集成第三方登录

系统预留了扩展接口，可以集成微信、QQ等第三方登录方式。

### 多租户支持

如果需要支持多租户，可以在用户表中添加租户ID字段，并修改相关业务逻辑。

## 版本更新

### 数据库迁移

在版本更新时，请注意执行相应的数据库迁移脚本：

```bash
# 执行更新脚本
mysql -u root -p your_database < script/sql/update/update_xxx.sql
```

### 配置文件更新

检查新版本是否有配置文件变更，及时更新配置。

## 技术支持

如遇到问题，请联系技术支持团队或查看项目文档。
