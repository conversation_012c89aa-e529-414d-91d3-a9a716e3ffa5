# Redis 密码重置令牌存储方案

## 概述

本项目使用 Redis 来存储密码重置令牌，替代传统的数据库表存储方式。这种方案具有更好的性能和更简单的维护特性。

## 设计方案

### 1. 存储结构
```
Key: password_reset_token:{token}
Value: {user_id}
TTL: 3600 秒（1小时）
```

### 2. 示例
```
Key: password_reset_token:abc123def456ghi789
Value: 1001
TTL: 3600
```

## 核心优势

### 1. 自动过期清理
- **无需定时任务**: Redis 的 TTL 机制自动清理过期令牌
- **内存管理**: 过期数据自动释放内存空间
- **零维护成本**: 不需要编写清理脚本

### 2. 高性能
- **内存存储**: 读写速度远超数据库
- **原子操作**: Redis 的原子操作保证数据一致性
- **减少数据库压力**: 避免频繁的数据库读写

### 3. 简化架构
- **减少表结构**: 不需要创建专门的令牌表
- **简化查询**: 直接通过 Key 获取，无需复杂查询
- **降低复杂度**: 减少数据库关联和索引维护

## 实现细节

### 1. 令牌生成
```java
public String generateToken(Long userId) {
    String token = IdUtil.fastSimpleUUID();
    String key = PASSWORD_RESET_TOKEN_PREFIX + token;
    
    // 存储到 Redis，设置1小时过期
    RedisUtils.setCacheObject(key, userId.toString(), Duration.ofHours(1));
    
    return token;
}
```

### 2. 令牌验证
```java
public Long validateAndGetUserId(String token) {
    String key = PASSWORD_RESET_TOKEN_PREFIX + token;
    String userIdStr = RedisUtils.getCacheObject(key);
    
    if (userIdStr != null) {
        return Long.parseLong(userIdStr);
    }
    
    return null;
}
```

### 3. 令牌使用（一次性）
```java
public Long useToken(String token) {
    String key = PASSWORD_RESET_TOKEN_PREFIX + token;
    String userIdStr = RedisUtils.getCacheObject(key);
    
    if (userIdStr != null) {
        // 删除令牌，确保一次性使用
        RedisUtils.deleteObject(key);
        return Long.parseLong(userIdStr);
    }
    
    return null;
}
```

## 安全特性

### 1. 一次性使用
- 令牌验证成功后立即删除
- 防止令牌重复使用
- 提高安全性

### 2. 自动过期
- 1小时自动过期
- 减少令牌泄露风险
- 无需手动清理

### 3. 随机生成
- 使用 UUID 生成随机令牌
- 难以预测和暴力破解
- 足够的随机性保证

## 监控和维护

### 1. 令牌统计
```bash
# 查看当前活跃的重置令牌数量
redis-cli --scan --pattern "password_reset_token:*" | wc -l

# 查看特定令牌的剩余时间
redis-cli TTL password_reset_token:abc123def456
```

### 2. 内存使用
```bash
# 查看 Redis 内存使用情况
redis-cli INFO memory

# 查看特定模式的内存使用
redis-cli --bigkeys --pattern "password_reset_token:*"
```

### 3. 性能监控
```bash
# 监控 Redis 操作
redis-cli MONITOR | grep "password_reset_token"

# 查看慢查询
redis-cli SLOWLOG GET 10
```

## 配置建议

### 1. Redis 配置
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      password: your_password
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
```

### 2. 内存优化
```bash
# Redis 配置文件优化
maxmemory 256mb
maxmemory-policy allkeys-lru
```

### 3. 持久化配置
```bash
# 如果不需要持久化令牌数据，可以关闭持久化
save ""
appendonly no
```

## 扩展功能

### 1. 令牌限制
可以为每个用户限制同时存在的重置令牌数量：

```java
public void deleteUserTokens(Long userId) {
    var keys = RedisUtils.keys(PASSWORD_RESET_TOKEN_PREFIX + "*");
    
    for (String key : keys) {
        String userIdStr = RedisUtils.getCacheObject(key);
        if (userId.toString().equals(userIdStr)) {
            RedisUtils.deleteObject(key);
        }
    }
}
```

### 2. 令牌使用记录
可以记录令牌的使用情况：

```java
public void recordTokenUsage(String token, Long userId) {
    String logKey = "token_usage_log:" + token;
    String logValue = userId + ":" + System.currentTimeMillis();
    RedisUtils.setCacheObject(logKey, logValue, Duration.ofDays(7));
}
```

### 3. 频率限制
可以限制用户申请重置令牌的频率：

```java
public boolean canRequestToken(Long userId) {
    String rateLimitKey = "reset_rate_limit:" + userId;
    String count = RedisUtils.getCacheObject(rateLimitKey);
    
    if (count == null) {
        RedisUtils.setCacheObject(rateLimitKey, "1", Duration.ofMinutes(5));
        return true;
    }
    
    return Integer.parseInt(count) < 3; // 5分钟内最多3次
}
```

## 对比分析

### Redis 方案 vs 数据库方案

| 特性 | Redis 方案 | 数据库方案 |
|------|------------|------------|
| 性能 | 极高（内存） | 中等（磁盘） |
| 过期清理 | 自动 | 需要定时任务 |
| 维护成本 | 低 | 中等 |
| 数据持久化 | 可选 | 默认 |
| 查询复杂度 | 简单 | 中等 |
| 内存使用 | 少量 | 无 |
| 扩展性 | 高 | 中等 |

## 最佳实践

### 1. 键命名规范
- 使用有意义的前缀
- 包含业务标识
- 便于监控和管理

### 2. 过期时间设置
- 根据业务需求设置合理的过期时间
- 平衡安全性和用户体验
- 考虑时区和网络延迟

### 3. 错误处理
- 处理 Redis 连接异常
- 提供降级方案
- 记录关键操作日志

### 4. 安全考虑
- 使用强随机性的令牌
- 实现一次性使用机制
- 限制令牌申请频率

## 总结

使用 Redis 存储密码重置令牌是一个优秀的设计选择，它提供了：

1. **更好的性能**: 内存存储，读写速度快
2. **更简单的维护**: 自动过期，无需手动清理
3. **更低的复杂度**: 减少数据库表和查询
4. **更高的可扩展性**: 支持分布式部署

这种方案特别适合高并发的 Web 应用，能够有效提升系统性能和用户体验。
