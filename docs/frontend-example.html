<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>许愿狐开放平台 - 用户认证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"], input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .tab {
            display: flex;
            margin-bottom: 20px;
        }
        .tab button {
            flex: 1;
            background-color: #f8f9fa;
            color: #333;
            border: 1px solid #ddd;
            margin-right: 5px;
        }
        .tab button.active {
            background-color: #007bff;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .code-btn {
            width: auto;
            margin-left: 10px;
            padding: 8px 15px;
        }
        .code-group {
            display: flex;
            align-items: center;
        }
        .code-group input {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>许愿狐开放平台</h2>
        
        <div class="tab">
            <button class="tab-btn active" onclick="showTab('login')">登录</button>
            <button class="tab-btn" onclick="showTab('register')">注册</button>
            <button class="tab-btn" onclick="showTab('forgot')">忘记密码</button>
        </div>

        <!-- 登录表单 -->
        <div id="login" class="tab-content active">
            <form id="loginForm">
                <div class="form-group">
                    <label for="loginEmail">邮箱:</label>
                    <input type="email" id="loginEmail" name="email" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">密码:</label>
                    <input type="password" id="loginPassword" name="password" required>
                </div>
                <button type="submit">登录</button>
            </form>
        </div>

        <!-- 注册表单 -->
        <div id="register" class="tab-content">
            <form id="registerForm">
                <div class="form-group">
                    <label for="registerEmail">邮箱:</label>
                    <input type="email" id="registerEmail" name="email" required>
                </div>
                <div class="form-group">
                    <label for="registerCode">验证码:</label>
                    <div class="code-group">
                        <input type="text" id="registerCode" name="emailCode" required>
                        <button type="button" class="code-btn" onclick="sendEmailCode('register')">发送验证码</button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="registerNickname">昵称:</label>
                    <input type="text" id="registerNickname" name="nickname">
                </div>
                <div class="form-group">
                    <label for="registerPassword">密码:</label>
                    <input type="password" id="registerPassword" name="password" required>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">确认密码:</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required>
                </div>
                <button type="submit">注册</button>
            </form>
        </div>

        <!-- 忘记密码表单 -->
        <div id="forgot" class="tab-content">
            <form id="forgotForm">
                <div class="form-group">
                    <label for="forgotEmail">邮箱:</label>
                    <input type="email" id="forgotEmail" name="email" required>
                </div>
                <button type="submit">发送重置链接</button>
            </form>
        </div>

        <div id="message"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/open/auth';

        function showTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有按钮的active类
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');
            
            // 添加active类到对应按钮
            event.target.classList.add('active');
            
            // 清除消息
            document.getElementById('message').innerHTML = '';
        }

        function showMessage(message, type = 'success') {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="message ${type}">${message}</div>`;
        }

        async function sendEmailCode(type) {
            const email = type === 'register' ? 
                document.getElementById('registerEmail').value : 
                document.getElementById('forgotEmail').value;
            
            if (!email) {
                showMessage('请先输入邮箱', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/send-email-code`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        codeType: type
                    })
                });

                const result = await response.json();
                if (result.code === 200) {
                    showMessage('验证码已发送到您的邮箱');
                } else {
                    showMessage(result.msg || '发送失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
            }
        }

        // 登录表单提交
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);

            try {
                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                if (result.code === 200) {
                    showMessage('登录成功！');
                    // 保存token
                    localStorage.setItem('accessToken', result.data.accessToken);
                    // 这里可以跳转到其他页面
                } else {
                    showMessage(result.msg || '登录失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
            }
        });

        // 注册表单提交
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);

            if (data.password !== data.confirmPassword) {
                showMessage('两次输入的密码不一致', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                if (result.code === 200) {
                    showMessage('注册成功！请登录');
                    showTab('login');
                } else {
                    showMessage(result.msg || '注册失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
            }
        });

        // 忘记密码表单提交
        document.getElementById('forgotForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);

            try {
                const response = await fetch(`${API_BASE}/forgot-password`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                if (result.code === 200) {
                    showMessage('重置链接已发送到您的邮箱');
                } else {
                    showMessage(result.msg || '发送失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
            }
        });
    </script>
</body>
</html>
