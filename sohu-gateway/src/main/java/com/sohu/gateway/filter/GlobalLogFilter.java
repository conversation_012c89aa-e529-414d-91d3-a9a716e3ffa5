package com.sohu.gateway.filter;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.sohu.common.core.utils.JsonUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.gateway.config.properties.CustomGatewayProperties;
import com.sohu.gateway.utils.WebFluxUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import com.sohu.middle.api.bo.SohuUserBehaviorRecordBo;
import com.sohu.middle.api.service.RemoteMiddleUserBehaviorRecordService;
import org.apache.dubbo.config.annotation.DubboReference;

/**
 * 全局日志过滤器
 * <p>
 * 用于打印请求执行参数与响应时间等等
 *
 * <AUTHOR> Li
 */
@Slf4j
@Component
public class GlobalLogFilter implements GlobalFilter, Ordered {

    @Autowired
    private CustomGatewayProperties customGatewayProperties;

//    @DubboReference
//    private RemoteMiddleUserBehaviorRecordService remoteMiddleUserBehaviorRecordService;

    private static final String START_TIME = "startTime";

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        if (!customGatewayProperties.getRequestLog()) {
            return chain.filter(exchange);
        }
        ServerHttpRequest request = exchange.getRequest();
        String path = WebFluxUtils.getOriginalRequestUrl(exchange);
        String url = request.getMethod().name() + " " + path;

        String jsonParam = "";
        // 打印请求参数
        if (WebFluxUtils.isJsonRequest(exchange)) {
            jsonParam = WebFluxUtils.resolveBodyFromCacheRequest(exchange);
            if (StrUtil.isBlankIfStr(jsonParam)) {
                log.info("[PLUS]开始请求 => URL[{}],参数类型[json]", url);
            } else {
                log.info("[PLUS]开始请求 => URL[{}],参数类型[json],参数:[{}]", url, jsonParam);
            }
        } else {
            MultiValueMap<String, String> parameterMap = request.getQueryParams();
            if (MapUtil.isNotEmpty(parameterMap)) {
                jsonParam = JsonUtils.toJsonString(parameterMap);
                log.info("[PLUS]开始请求 => URL[{}],参数类型[param],参数:[{}]", url, jsonParam);
            } else {
                log.info("[PLUS]开始请求 => URL[{}],无参数", url);
            }
        }

//        HttpMethod method = request.getMethod();
//        HttpHeaders headers = request.getHeaders();
//
//        String authorization  = headers.getFirst("Authorization");
//        String platform = headers.getFirst("Platform");
//        if (StrUtil.isNotEmpty(authorization)){
//            String token = authorization.replace("Bearer ", "");
//            LoginUser loginUser = LoginHelper.getLoginUser(token);
//            if (ObjectUtil.isNotNull(loginUser)){
//                Long userId = loginUser.getUserId();
//                String methodName = method.toString();
//                SohuUserBehaviorRecordBo bo = new SohuUserBehaviorRecordBo();
//                bo.setOperUrl(path);
//                bo.setOperParam(getOperParam(methodName,jsonParam));
//                bo.setUserId(userId);
//                bo.setRequestMethod(methodName);
//                bo.setOperIp(loginUser.getIpaddr());
//                bo.setOperaSource(getOpertype(platform));
//                remoteMiddleUserBehaviorRecordService.insertByBo(bo);
//            }
//
//        }


        exchange.getAttributes().put(START_TIME, System.currentTimeMillis());
        return chain.filter(exchange).then(Mono.fromRunnable(() -> {
            Long startTime = exchange.getAttribute(START_TIME);
            if (startTime != null) {
                long executeTime = (System.currentTimeMillis() - startTime);
                if (executeTime > 200) {
                    log.warn("[PLUS]结束请求 => URL[{}],耗时:[{}]毫秒", url, executeTime);
                } else {
                    log.info("[PLUS]结束请求 => URL[{}],耗时:[{}]毫秒", url, executeTime);
                }
            }
        }));
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }

    private Long getOpertype(String platform){
        if (StrUtil.isNotEmpty(platform) && platform.toLowerCase().equals("android")){
            return 2L;
        }
        if (StrUtil.isNotEmpty(platform) && platform.toLowerCase().equals("ios")){
            return 3L;
        }
        return 1L;
    }

    private String getOperParam(String methodName,String param){
        try{
            if (!methodName.equals("GET")){
                JSONObject object = new JSONObject();
                JSONObject json = JSONObject.parseObject(param);
                if (json.containsKey("id")){
                    object.put("id",json.get("id"));
                    return object.toJSONString();
                }
            }
            return param;
        }catch (Exception e){
            return param;
        }
    }

}
