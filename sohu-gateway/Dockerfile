FROM openjdk:11

MAINTAINER sohu

RUN mkdir -p /sohu/gateway/logs  \
    /sohu/gateway/temp  \
    /sohu/skywalking/agent

WORKDIR /sohu/gateway

ENV SERVER_PORT=8080

EXPOSE ${SERVER_PORT}

ADD *.jar ./app.jar

ENTRYPOINT ["java", \
            "-Djava.security.egd=file:/dev/./urandom", \
            "-Dserver.port=${SERVER_PORT}", \
#            "-Dskywalking.agent.service_name=sohu-gateway", \
#           "-javaagent:/sohu/skywalking/agent/skywalking-agent.jar", \
            "-jar", "app.jar"]



