package com.sohu.stream.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.stream.mapper.MqMsgLogMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.sohu.streamrocketmq.api.bo.MqMsgLogBo;
import com.sohu.streamrocketmq.api.vo.MqMsgLogVo;
import com.sohu.stream.domain.MqMsgLog;
import com.sohu.stream.service.IMqMsgLogService;

import java.util.*;

/**
 * MQ消费消息记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
@RequiredArgsConstructor
@Service
public class MqMsgLogServiceImpl implements IMqMsgLogService {

    private final MqMsgLogMapper baseMapper;

    /**
     * 查询MQ消费消息记录
     */
    @Override
    public MqMsgLogVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询MQ消费消息记录列表
     */
    @Override
    public TableDataInfo<MqMsgLogVo> queryPageList(MqMsgLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MqMsgLog> lqw = buildQueryWrapper(bo);
        Page<MqMsgLogVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询MQ消费消息记录列表
     */
    @Override
    public List<MqMsgLogVo> queryList(MqMsgLogBo bo) {
        LambdaQueryWrapper<MqMsgLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MqMsgLog> buildQueryWrapper(MqMsgLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MqMsgLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getMsgId()), MqMsgLog::getMsgId, bo.getMsgId());
        lqw.like(StringUtils.isNotBlank(bo.getMsgText()), MqMsgLog::getMsgText, bo.getMsgText());
        lqw.between(params.get("beginProduceTime") != null && params.get("endProduceTime") != null,
            MqMsgLog::getProduceTime, params.get("beginProduceTime"), params.get("endProduceTime"));
        lqw.between(params.get("beginConsumeTime") != null && params.get("endConsumeTime") != null,
            MqMsgLog::getConsumeTime, params.get("beginConsumeTime"), params.get("endConsumeTime"));
        lqw.eq(bo.getState() != null, MqMsgLog::getState, bo.getState());
        return lqw;
    }

    /**
     * 新增MQ消费消息记录
     */
    @Override
    public Boolean insertByBo(MqMsgLogBo bo) {
        MqMsgLog add = BeanUtil.toBean(bo, MqMsgLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改MQ消费消息记录
     */
    @Override
    public Boolean updateByBo(MqMsgLogBo bo) {
        MqMsgLog update = BeanUtil.toBean(bo, MqMsgLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MqMsgLog entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除MQ消费消息记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean saveMqMsg(String msgId, String msgText, Long delay) {
        MqMsgLog msgLog = new MqMsgLog();
        msgLog.setMsgId(msgId);
        msgLog.setMsgText(msgText);
        msgLog.setProduceTime(new Date());
        msgLog.setDelay(delay);
        baseMapper.insert(msgLog);
        return Boolean.TRUE;
    }

    @Override
    public Boolean hasConsumeMqMsg(String msgId) {
        MqMsgLog msgLog = this.baseMapper.selectOne(MqMsgLog::getMsgId, msgId);
        if (Objects.nonNull(msgLog) && msgLog.getState()) {
            return true;
        }
        return false;
    }

    @Override
    public void updateConsumeTime(String msgId) {
        this.baseMapper.updateConsumeTime(msgId);
    }
}
