package com.sohu.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 群授权申请对象 sohu_im_group_authorize_apply
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_im_group_authorize_apply")
public class SohuImGroupAuthorizeApply extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 群ID
     */
    private Long groupId;
    /**
     * 申请理由
     */
    private String applyMsg;
    /**
     * 关联对象ID
     */
    private String relateObjId;
    /**
     * 关联对象类型
     */
    private String relateObjType;
    /**
     * 申请状态
     */
    private String state;
    /**
     * 拒绝理由
     */
    private String rejectReason;

}
