package com.sohu.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * im工具对象 sohu_im_tool
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_im_tool")
public class SohuImTool extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 群ID
     */
    private Long groupId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 内容
     */
    private String content;
    /**
     * 类型（快捷回复=quickReply）
     */
    private String type;
    /**
     * 状态
     */
    private String state;

    /**
     * 排序值
     */
    private int sortIndex;

}
