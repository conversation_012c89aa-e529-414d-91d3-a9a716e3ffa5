package com.sohu.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 群组禁言时间段表
 *
 * <AUTHOR>
 * @date 2024-11-21 14:43:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_im_group_forbid_time")
public class SohuImGroupForbidTime extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 群id
     */
    private Long groupId;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
}
