package com.sohu.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 群防骚扰设置对象 sohu_im_group_disturb
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Data
@TableName("sohu_im_group_disturb")
public class SohuImGroupDisturb extends SohuEntity {

    private static final long serialVersionUID = 1L;

    public transient static final long DEFAULT_SCREEN_LIMIT =800L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 规则名称
     */
    private String title;
    /**
     * 防广告开关(开启=true,关闭=false)
     */
    private Boolean enableAd;
    /**
     * 广告关键词列表
     */
    private String adWordList;
    /**
     * 防刷屏开关(开启=true,关闭=false)
     */
    private Boolean enableScreen;
    /**
     * 防刷屏消息长度字数，默认800
     */
    private Long screenLimit;
    /**
     * 触发规则后的处理，1=踢出群聊，2=不展示消息
     */
    private Integer punishType;
    /**
     * 使用于哪些群聊，此值为群id组合，以英文逗号间隔
     */
    private String groupList;
    /**
     * 创建人id
     */
    private Long userId;

}
