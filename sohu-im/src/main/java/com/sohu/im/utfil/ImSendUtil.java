package com.sohu.im.utfil;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.SpringUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.im.api.bo.ImChatRequestBo;
import com.sohu.im.api.enums.ImMessageTypeEnum;
import com.sohu.im.api.enums.ImSessionTypeEnum;
import com.sohu.im.api.vo.ImChatResponseVo;
import com.sohu.im.api.vo.SohuImGroupVo;
import com.sohu.im.server.ImServerConfig;
import com.sohu.im.server.WebsocketStarter;
import com.sohu.im.server.enums.ImChatHandleEnum;
import com.sohu.im.service.ISohuBatchSendService;
import com.sohu.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.tio.core.Tio;
import org.tio.server.ServerTioConfig;
import org.tio.websocket.common.WsResponse;

import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ImSendUtil {

    private static final AtomicInteger counter = new AtomicInteger(0);
    private static long lastTimestamp = -1;

    private final ISohuBatchSendService sohuBatchSendService;

    /**
     * 发送系统消息
     *
     * @param imGroup       群vo对象
     * @param content       发送内容
     * @param groupHeaderId 群主id
     * @param commandType   命令类型 {@link com.sohu.im.api.enums.ImCommandTypeEnum}
     */
    public void sendSystemMessage(SohuImGroupVo imGroup, String content, Long groupHeaderId, String commandType) {
        if (StrUtil.isBlankIfStr(content)) {
            return;
        }
        ImChatRequestBo requestBo = new ImChatRequestBo();
        requestBo.setReceiverId(imGroup.getId());
        requestBo.setSessionType(imGroup.getGroupType());
        requestBo.setMessageType(ImMessageTypeEnum.Command.getCode());
        requestBo.setCommandType(commandType);
        requestBo.setContent(content);
        requestBo.setLocalId(RandomUtil.randomString(32));
        requestBo.setChatId(System.nanoTime());
        requestBo.setLocalId(RandomUtil.randomString(16));
        // 发送socket 消息
        serverActiveSend(CalUtils.isNullOrZero(groupHeaderId) ? imGroup.getUserId() : groupHeaderId, requestBo, true);
    }

    /**
     * 服务端主动发送socket消息
     *
     * @param sendUserId 发送人id
     * @param bo
     * @param saveDb     是否保存到数据库
     */
    public void serverActiveSend(Long sendUserId, ImChatRequestBo bo, Boolean saveDb) {
        log.info("主动发送消息开始，发送人：{}，发送体：{}", sendUserId, JSONUtil.toJsonStr(bo));
        if (sendUserId == null || sendUserId <= 0L) {
            return;
        }
        ServerTioConfig serverTioConfig = WebsocketStarter.serverTioConfig;
        RemoteUserService remoteUserService = SpringUtils.getBean(RemoteUserService.class);
        LoginUser sendUser = remoteUserService.queryById(sendUserId);
        if (CalUtils.isNullOrZero(bo.getNanoTime())) {
            bo.setNanoTime(generateNanoTime(null));
        }
        if (CalUtils.isNullOrZero(bo.getRealReceiverId())) {
            bo.setRealReceiverId(bo.getReceiverId());
        }
        if (CalUtils.isNullOrZero(bo.getRealSenderId())) {
            bo.setRealSenderId(sendUserId);
        }

        // 发送者消息体
        ImChatResponseVo responseVoForSender = ImSocketResponseUtil.buildResponseVo(bo, sendUser);
        WsResponse wsResponseForReceiver = WsResponse.fromText(JSONUtil.toJsonStr(responseVoForSender), ImServerConfig.CHARSET);
        if (StrUtil.equalsAnyIgnoreCase(bo.getSessionType(), ImSessionTypeEnum.single.getCode(), ImSessionTypeEnum.merchant.getCode())) {
            // 单聊
            log.info("主动发送单聊消息成功，发送人：{}，发送体：{}", sendUserId, JSONUtil.toJsonStr(bo));
            Tio.sendToUser(serverTioConfig, String.valueOf(bo.getReceiverId()), wsResponseForReceiver);
            if (BooleanUtil.isFalse(bo.getHidden())) {
                ImUnReadUtil.inrUnReadCount(bo.getSessionType(), bo.getRealSenderId(), bo.getRealReceiverId());
            }
        } else {
            log.info("主动发送群聊消息成功，发送人：{}，发送体：{}", sendUserId, JSONUtil.toJsonStr(bo));
            Tio.sendToGroup(serverTioConfig, String.valueOf(bo.getReceiverId()), wsResponseForReceiver);
            // 异步增加未读消息数
            sohuBatchSendService.asyncInrUnRead(bo.getReceiverId(), bo.getSessionType(), bo.getRealSenderId());
        }
        if (BooleanUtil.isTrue(saveDb)) {
            log.info("主动发送消息入库操作");
            ImChatHandleEnum.valueOf(bo.getSessionType().toUpperCase()).sendMessage(sendUserId, bo, null);
        }
    }


    public synchronized long generateNanoTime(Long time) {
        long currentMillis = CalUtils.isNullOrZero(time) ? System.currentTimeMillis() : time;
        if (currentMillis == lastTimestamp) {
            counter.incrementAndGet();
        } else {
            counter.set(0);
            lastTimestamp = currentMillis;
        }
        return currentMillis * 1000 + counter.get(); // 毫秒后面补上微小自增
    }

    public long generateNanoTime(long createMillis, Map<Long, AtomicInteger> timestampCounterMap) {
        AtomicInteger counter = timestampCounterMap.computeIfAbsent(createMillis, k -> new AtomicInteger(0));
        return createMillis * 1_000 + counter.getAndIncrement(); // 每个毫秒补充微小增量，确保唯一
    }

}
