package com.sohu.im.utfil;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONUtil;
import com.sohu.common.core.constant.Constants;
import com.sohu.middle.api.bo.risk.RiskSyncCheckBo;
import com.sohu.im.api.enums.ImRiskTypeEnum;
import com.sohu.middle.api.service.RemoteRiskService;
import com.sohu.third.aliyun.audit.constants.AliyunAuditLabelEnum;
import com.sohu.third.aliyun.audit.service.AliyunAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * IM检测工具类
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ImRiskCheckUtil {

    @DubboReference
    private RemoteRiskService remoteRiskService;
    @Resource
    private AliyunAuditService aliyunAuditService;

    /**
     * 易盾同步检测
     *
     * @param content      检测内容
     * @param busyCode     检测对象，对应表主键ID或者其它唯一值
     * @param riskTypeEnum {@link ImRiskTypeEnum}
     * @return {@link Boolean} true-通过，false-不通过
     */
    public boolean syncCheck(String content, String busyCode, ImRiskTypeEnum riskTypeEnum) {
        RiskSyncCheckBo syncCheckBo = new RiskSyncCheckBo();
        syncCheckBo.setPlatform(Constants.SOHUGLOBAL);
        syncCheckBo.setBusyCode(busyCode);
        syncCheckBo.setContent(content);
        syncCheckBo.setDetectType(riskTypeEnum.getDetectType());
        syncCheckBo.setBusyType(riskTypeEnum.getBusyType());
        syncCheckBo.setFieldName(riskTypeEnum.getDesc());
        log.info("易盾同步检测，入参：{}", JSONUtil.toJsonStr(syncCheckBo));
        return remoteRiskService.syncCheck(syncCheckBo);
    }


    /**
     * 易盾异步检测
     *
     * @param content      检测内容
     * @param busyCode     检测对象，对应表主键ID或者其它唯一值
     * @param riskTypeEnum {@link ImRiskTypeEnum}
     */
    public void asyncCheck(String content, String busyCode, ImRiskTypeEnum riskTypeEnum) {
        RiskSyncCheckBo syncCheckBo = new RiskSyncCheckBo();
        syncCheckBo.setPlatform(Constants.SOHUGLOBAL);
        syncCheckBo.setContent(content);
        syncCheckBo.setBusyCode(busyCode);
        syncCheckBo.setDetectType(riskTypeEnum.getDetectType());
        syncCheckBo.setBusyType(riskTypeEnum.getBusyType());
        syncCheckBo.setFieldName(riskTypeEnum.getDesc());
        log.info("易盾异步检测，入参：{}", JSONUtil.toJsonStr(syncCheckBo));
        remoteRiskService.asyncCheck(syncCheckBo);
    }

    /**
     * 阿里云文本检测
     *
     * @param content 检测内容
     * @return 有返回则表示违规
     */
    public String aliyunTextCheck(String content) {
        if (StrUtil.isBlankIfStr(content)) {
            return null;
        }
        return aliyunAuditService.scanText(HtmlUtil.cleanHtmlTag(content), AliyunAuditLabelEnum.textCheck);
    }
}
