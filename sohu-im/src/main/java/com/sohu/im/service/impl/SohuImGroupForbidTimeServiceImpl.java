package com.sohu.im.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.bo.SohuImGroupForbidTimeBo;
import com.sohu.im.api.enums.ImCommandTypeEnum;
import com.sohu.im.api.enums.ImSystemMessageEnum;
import com.sohu.im.api.vo.SohuImGroupForbidTimeVo;
import com.sohu.im.api.vo.SohuImGroupVo;
import com.sohu.im.domain.SohuImGroupForbidTime;
import com.sohu.im.mapper.SohuImGroupForbidTimeMapper;
import com.sohu.im.mapper.SohuImGroupMapper;
import com.sohu.im.service.ISohuImCommonService;
import com.sohu.im.service.ISohuImGroupForbidTimeService;
import com.sohu.im.utfil.ImErrorUtil;
import com.sohu.im.utfil.ImGroupUtil;
import com.sohu.im.utfil.ImSendUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 群禁言时间段接口实现
 *
 * @Author: leibo
 * @Date: 2024/11/21 16:06
 **/
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuImGroupForbidTimeServiceImpl implements ISohuImGroupForbidTimeService {

    private final ISohuImCommonService sohuImCommonService;

    private final SohuImGroupForbidTimeMapper baseMapper;
    private final SohuImGroupForbidTimeMapper sohuImGroupForbidTimeMapper;
    private final ImGroupUtil imGroupUtil;
    private final ImSendUtil imSendUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByBo(SohuImGroupForbidTimeBo bo) {
        SohuImGroupVo sohuImGroupVo = sohuImCommonService.queryGroup(bo.getGroupId());
        ImErrorUtil.checkGroupExist(sohuImGroupVo);
        if (!CollectionUtils.isEmpty(bo.getTimeList())) {
            for (SohuImGroupForbidTimeBo.Time time : bo.getTimeList()) {
                if (StringUtils.isEmpty(time.getStartTime())
                        || StringUtils.isEmpty(time.getEndTime())) {
                    throw new ServiceException(MessageUtils.message("时间段开始时间或结束时间不能为空"));
                }
                if (!isValidTime(time.getStartTime()) || !isValidTime(time.getEndTime())) {
                    throw new ServiceException(MessageUtils.message("时间段格式异常,请检查后再试"));
                }
            }
        }
        // 先删除禁言时间段
        sohuImGroupForbidTimeMapper.delete(Wrappers.<SohuImGroupForbidTime>lambdaQuery()
                .eq(SohuImGroupForbidTime::getGroupId, bo.getGroupId()));
        // 判断是否有传禁言时间段
        if (!CollectionUtils.isEmpty(bo.getTimeList())) {
            List<SohuImGroupForbidTime> timeList = new ArrayList<>();
            StringBuffer timeStr = new StringBuffer();
            for (SohuImGroupForbidTimeBo.Time time : bo.getTimeList()) {
                SohuImGroupForbidTime sohuImGroupForbidTime = new SohuImGroupForbidTime();
                sohuImGroupForbidTime.setGroupId(bo.getGroupId());
                sohuImGroupForbidTime.setStartTime(time.getStartTime());
                sohuImGroupForbidTime.setEndTime(time.getEndTime());
                timeList.add(sohuImGroupForbidTime);
                timeStr.append(time.getStartTime()).append("-").append(time.getEndTime()).append(" ");
            }
            if (!CollectionUtils.isEmpty(timeList)) {
                sohuImGroupForbidTimeMapper.insertBatch(timeList);
            }
            if (sohuImGroupVo.getForbidTime()) {
                String content = imGroupUtil.exchangeUser(sohuImGroupVo.getId(), LoginHelper.getUserId()) +
                        String.format(ImSystemMessageEnum.GROUP_FORBID_TIME_OPEN.getContent(), timeStr.toString());
                imSendUtil.sendSystemMessage(sohuImGroupVo, content, LoginHelper.getUserId(),
                        ImCommandTypeEnum.groupForbidTime.getCode());
            }
        }
        return 1;
    }

    @Override
    public List<SohuImGroupForbidTimeVo> selectListByGroupId(Long groupId) {
        LambdaQueryWrapper<SohuImGroupForbidTime> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImGroupForbidTime::getGroupId, groupId);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public Map<Long, List<SohuImGroupForbidTimeVo>> selectMapByGroupIds(Collection<Long> groupIds) {
        LambdaQueryWrapper<SohuImGroupForbidTime> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuImGroupForbidTime::getGroupId, groupIds);
        List<SohuImGroupForbidTimeVo> forbidTimeVos = baseMapper.selectVoList(lqw);
        return CollUtil.isEmpty(forbidTimeVos) ? new HashMap<>() : forbidTimeVos.stream().collect(Collectors.groupingBy(SohuImGroupForbidTimeVo::getGroupId));
    }

    @Override
    public Boolean deleteByGroupId(Long groupId) {
        return sohuImGroupForbidTimeMapper.delete(Wrappers.<SohuImGroupForbidTime>lambdaQuery().eq(SohuImGroupForbidTime::getGroupId, groupId)) > 0;
    }

    /**
     * 校验时间格式
     *
     * @param time
     * @return
     */
    private boolean isValidTime(String time) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        try {
            // 尝试将字符串解析为 LocalTime 类型
            LocalTime.parse(time, formatter);
            // 如果没有抛出异常，说明格式有效
            return true;
        } catch (DateTimeParseException e) {
            // 如果抛出异常，说明格式无效
            return false;
        }
    }

}
