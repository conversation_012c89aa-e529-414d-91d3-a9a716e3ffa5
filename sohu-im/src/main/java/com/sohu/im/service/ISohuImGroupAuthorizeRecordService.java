package com.sohu.im.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.im.api.bo.SohuImGroupAuthorizeRecordBo;
import com.sohu.im.api.vo.SohuImGroupAuthorizeRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 群授权用户记录Service接口
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
public interface ISohuImGroupAuthorizeRecordService {

    /**
     * 查询群授权用户记录
     */
    SohuImGroupAuthorizeRecordVo queryById(Long id);

    /**
     * 查询群授权用户记录列表
     */
    TableDataInfo<SohuImGroupAuthorizeRecordVo> queryPageList(SohuImGroupAuthorizeRecordBo bo, PageQuery pageQuery);

    /**
     * 查询群授权用户记录列表
     */
    List<SohuImGroupAuthorizeRecordVo> queryList(SohuImGroupAuthorizeRecordBo bo);

    /**
     * 修改群授权用户记录
     */
    Boolean insertByBo(SohuImGroupAuthorizeRecordBo bo);

    /**
     * 修改群授权用户记录
     */
    Boolean updateByBo(SohuImGroupAuthorizeRecordBo bo);

    /**
     * 校验并批量删除群授权用户记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
