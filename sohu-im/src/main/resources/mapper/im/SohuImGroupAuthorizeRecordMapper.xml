<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.im.mapper.SohuImGroupAuthorizeRecordMapper">

    <resultMap type="com.sohu.im.domain.SohuImGroupAuthorizeRecord" id="SohuImGroupAuthorizeRecordResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="groupId" column="group_id"/>
        <result property="inviteUserId" column="invite_user_id"/>
        <result property="channelId" column="channel_id"/>
        <result property="authorizeIp" column="authorize_ip"/>
        <result property="state" column="state"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
