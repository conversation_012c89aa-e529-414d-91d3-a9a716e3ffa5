<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.im.mapper.SohuImGroupTransferMapper">

    <resultMap type="com.sohu.im.domain.SohuImGroupTransfer" id="SohuImGroupTransferResult">
        <result property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="userId" column="user_id"/>
        <result property="state" column="state"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
