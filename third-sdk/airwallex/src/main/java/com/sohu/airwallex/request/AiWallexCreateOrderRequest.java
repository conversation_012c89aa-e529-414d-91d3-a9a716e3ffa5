package com.sohu.airwallex.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AiWallexCreateOrderRequest implements Serializable {

    /**
     * 付款金额，必传字段
     */
    @JSONField(name = "amount")
    private BigDecimal amount;

    /**
     * AirWallex为连接实体（与平台链接的另一个帐户）分配了帐户标识符。平台将使用它来指示交易中的关联实体，其中平台是交易的所有者。注意：这不能与标头的x-on-behavio一起使用。非必传字段
     */
    @JSONField(name = "connected_account_id")
    private String connectedAccountId;
    /**
     * 币种单位，必传字段，如:USD
     */
    @JSONField(name = "currency")
    private String currency;

    /**
     * 消费者消息
     */
    @JSONField(name = "customer")
    private CustomerDTO customer;
    /**
     * 消费者ID
     */
    @JSONField(name = "customer_id")
    private String customerId;

    @JSONField(name = "descriptor")
    private String descriptor;

    @JSONField(name = "device_data")
    private DeviceDataDTO deviceData;

    @JSONField(name = "funds_split_data")
    private List<FundsSplitDataDTO> fundsSplitData;

    /**
     * 自己平台的订单id,必传
     */
    @JSONField(name = "merchant_order_id")
    private String merchantOrderId;

    @JSONField(name = "metadata")
    private MetadataDTO metadata;

    @JSONField(name = "order")
    private OrderDTO order;

    @JSONField(name = "payment_method")
    private PaymentMethodDTO paymentMethod;

    @JSONField(name = "payment_method_options")
    private PaymentMethodOptionsDTO paymentMethodOptions;

    /**
     * Unique request ID specified by the merchant. Maximum length is 64.
     */
    @JSONField(name = "request_id")
    private String requestId;

    /**
     * The web page URL or application scheme URI to redirect the customer after payment authentication.
     */
    @JSONField(name = "return_url")
    private String returnUrl;

    @JSONField(name = "risk_control_options")
    private RiskControlOptionsDTO riskControlOptions;

    @lombok.NoArgsConstructor
    @lombok.Data
    public static class CustomerDTO {
        @JSONField(name = "additional_info")
        private CustomerDTO.AdditionalInfoDTO additionalInfo;
        @JSONField(name = "address")
        private CustomerDTO.AddressDTO address;
        @JSONField(name = "business_name")
        private String businessName;
        @JSONField(name = "email")
        private String email;
        @JSONField(name = "first_name")
        private String firstName;
        @JSONField(name = "last_name")
        private String lastName;
        @JSONField(name = "merchant_customer_id")
        private String merchantCustomerId;
        @JSONField(name = "phone_number")
        private String phoneNumber;

        @lombok.NoArgsConstructor
        @lombok.Data
        public static class AdditionalInfoDTO {
            @JSONField(name = "account_type")
            private String accountType;
            @JSONField(name = "first_successful_order_date")
            private String firstSuccessfulOrderDate;
            @JSONField(name = "last_login_ip_address")
            private String lastLoginIpAddress;
            @JSONField(name = "last_modified_at")
            private String lastModifiedAt;
            @JSONField(name = "linked_social_networks")
            private List<LinkedSocialNetworksDTO> linkedSocialNetworks;
            @JSONField(name = "purchase_summaries")
            private List<PurchaseSummariesDTO> purchaseSummaries;
            @JSONField(name = "registered_via_social_media")
            private Boolean registeredViaSocialMedia;
            @JSONField(name = "registration_date")
            private String registrationDate;
            @JSONField(name = "registration_ip_address")
            private String registrationIpAddress;

            @lombok.NoArgsConstructor
            @lombok.Data
            public static class LinkedSocialNetworksDTO {
                @JSONField(name = "email")
                private String email;
                @JSONField(name = "name")
                private String name;
                @JSONField(name = "profile_id")
                private String profileId;
            }

            @lombok.NoArgsConstructor
            @lombok.Data
            public static class PurchaseSummariesDTO {
                @JSONField(name = "currency")
                private String currency;
                @JSONField(name = "first_successful_purchase_date")
                private String firstSuccessfulPurchaseDate;
                @JSONField(name = "last_successful_purchase_date")
                private String lastSuccessfulPurchaseDate;
                @JSONField(name = "payment_method_type")
                private String paymentMethodType;
                @JSONField(name = "successful_purchase_amount")
                private Double successfulPurchaseAmount;
                @JSONField(name = "successful_purchase_count")
                private Integer successfulPurchaseCount;
            }
        }

        @lombok.NoArgsConstructor
        @lombok.Data
        public static class AddressDTO {
            @JSONField(name = "city")
            private String city;
            @JSONField(name = "country_code")
            private String countryCode;
            @JSONField(name = "postcode")
            private String postcode;
            @JSONField(name = "state")
            private String state;
            @JSONField(name = "street")
            private String street;
        }
    }

    @lombok.NoArgsConstructor
    @lombok.Data
    public static class DeviceDataDTO {
        @JSONField(name = "accept_header")
        private String acceptHeader;
        @JSONField(name = "browser")
        private DeviceDataDTO.BrowserDTO browser;
        @JSONField(name = "device_id")
        private String deviceId;
        @JSONField(name = "ip_address")
        private String ipAddress;
        @JSONField(name = "language")
        private String language;
        @JSONField(name = "location")
        private DeviceDataDTO.LocationDTO location;
        @JSONField(name = "mobile")
        private DeviceDataDTO.MobileDTO mobile;
        @JSONField(name = "screen_color_depth")
        private Integer screenColorDepth;
        @JSONField(name = "screen_height")
        private Integer screenHeight;
        @JSONField(name = "screen_width")
        private Integer screenWidth;
        @JSONField(name = "timezone")
        private String timezone;

        @lombok.NoArgsConstructor
        @lombok.Data
        public static class BrowserDTO {
            @JSONField(name = "java_enabled")
            private Boolean javaEnabled;
            @JSONField(name = "javascript_enabled")
            private Boolean javascriptEnabled;
            @JSONField(name = "user_agent")
            private String userAgent;
        }

        @lombok.NoArgsConstructor
        @lombok.Data
        public static class LocationDTO {
            @JSONField(name = "lat")
            private String lat;
            @JSONField(name = "lon")
            private String lon;
        }

        @lombok.NoArgsConstructor
        @lombok.Data
        public static class MobileDTO {
            @JSONField(name = "device_model")
            private String deviceModel;
            @JSONField(name = "os_type")
            private String osType;
            @JSONField(name = "os_version")
            private String osVersion;
        }
    }

    @lombok.NoArgsConstructor
    @lombok.Data
    public static class MetadataDTO {
        @JSONField(name = "id")
        private Integer id;
    }

    @lombok.NoArgsConstructor
    @lombok.Data
    public static class OrderDTO {
        @JSONField(name = "discount")
        private OrderDTO.DiscountDTO discount;
        @JSONField(name = "itineraries")
        private List<ItinerariesDTO> itineraries;
        @JSONField(name = "products")
        private List<ProductsDTO> products;
        @JSONField(name = "sellers")
        private List<SellersDTO> sellers;
        @JSONField(name = "shipping")
        private OrderDTO.ShippingDTO shipping;
        @JSONField(name = "supplier")
        private OrderDTO.SupplierDTO supplier;
        @JSONField(name = "travelers")
        private List<TravelersDTO> travelers;
        @JSONField(name = "type")
        private String type;

        @lombok.NoArgsConstructor
        @lombok.Data
        public static class DiscountDTO {
            @JSONField(name = "coupon_code")
            private String couponCode;
        }

        @lombok.NoArgsConstructor
        @lombok.Data
        public static class ShippingDTO {
            @JSONField(name = "address")
            private AddressDTO address;
            @JSONField(name = "fee_amount")
            private Double feeAmount;
            @JSONField(name = "first_name")
            private String firstName;
            @JSONField(name = "last_name")
            private String lastName;
            @JSONField(name = "phone_number")
            private String phoneNumber;
            @JSONField(name = "shipping_delayed_at")
            private String shippingDelayedAt;
            @JSONField(name = "shipping_method")
            private String shippingMethod;

            @lombok.NoArgsConstructor
            @lombok.Data
            public static class AddressDTO {
                @JSONField(name = "city")
                private String city;
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "postcode")
                private String postcode;
                @JSONField(name = "state")
                private String state;
                @JSONField(name = "street")
                private String street;
            }
        }

        @lombok.NoArgsConstructor
        @lombok.Data
        public static class SupplierDTO {
            @JSONField(name = "address")
            private AddressDTO address;
            @JSONField(name = "business_name")
            private String businessName;
            @JSONField(name = "email")
            private String email;
            @JSONField(name = "first_name")
            private String firstName;
            @JSONField(name = "last_name")
            private String lastName;
            @JSONField(name = "phone_number")
            private String phoneNumber;

            @lombok.NoArgsConstructor
            @lombok.Data
            public static class AddressDTO {
                @JSONField(name = "city")
                private String city;
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "postcode")
                private String postcode;
                @JSONField(name = "state")
                private String state;
                @JSONField(name = "street")
                private String street;
            }
        }

        @lombok.NoArgsConstructor
        @lombok.Data
        public static class ItinerariesDTO {
            @JSONField(name = "airline_carrier_code")
            private String airlineCarrierCode;
            @JSONField(name = "arrival_airport_code")
            private String arrivalAirportCode;
            @JSONField(name = "arrival_city")
            private String arrivalCity;
            @JSONField(name = "arrive_at")
            private String arriveAt;
            @JSONField(name = "depart_at")
            private String departAt;
            @JSONField(name = "departure_airport_code")
            private String departureAirportCode;
            @JSONField(name = "departure_city")
            private String departureCity;
            @JSONField(name = "insurance")
            private List<InsuranceDTO> insurance;
            @JSONField(name = "price")
            private Integer price;
            @JSONField(name = "service_class")
            private String serviceClass;
            @JSONField(name = "traveler_identifier")
            private String travelerIdentifier;

            @lombok.NoArgsConstructor
            @lombok.Data
            public static class InsuranceDTO {
                @JSONField(name = "company")
                private String company;
                @JSONField(name = "price")
                private Integer price;
                @JSONField(name = "type")
                private String type;
            }
        }

        @lombok.NoArgsConstructor
        @lombok.Data
        public static class ProductsDTO {
            @JSONField(name = "category")
            private String category;
            @JSONField(name = "code")
            private String code;
            @JSONField(name = "desc")
            private String desc;
            @JSONField(name = "effective_end_at")
            private String effectiveEndAt;
            @JSONField(name = "effective_start_at")
            private String effectiveStartAt;
            @JSONField(name = "image_url")
            private String imageUrl;
            @JSONField(name = "name")
            private String name;
            @JSONField(name = "quantity")
            private Integer quantity;
            @JSONField(name = "seller")
            private SellerDTO seller;
            @JSONField(name = "sku")
            private String sku;
            @JSONField(name = "type")
            private String type;
            @JSONField(name = "unit_price")
            private Double unitPrice;
            @JSONField(name = "url")
            private String url;

            @lombok.NoArgsConstructor
            @lombok.Data
            public static class SellerDTO {
                @JSONField(name = "identifier")
                private String identifier;
                @JSONField(name = "name")
                private String name;
            }
        }

        @lombok.NoArgsConstructor
        @lombok.Data
        public static class SellersDTO {
            @JSONField(name = "additional_info")
            private AdditionalInfoDTO additionalInfo;
            @JSONField(name = "business_info")
            private BusinessInfoDTO businessInfo;
            @JSONField(name = "identifier")
            private String identifier;
            @JSONField(name = "name")
            private String name;

            @lombok.NoArgsConstructor
            @lombok.Data
            public static class AdditionalInfoDTO {
                @JSONField(name = "address_updated_at")
                private String addressUpdatedAt;
                @JSONField(name = "email_updated_at")
                private String emailUpdatedAt;
                @JSONField(name = "password_updated_at")
                private String passwordUpdatedAt;
                @JSONField(name = "products_updated_at")
                private String productsUpdatedAt;
                @JSONField(name = "sales_summary")
                private OrderDTO.SellersDTO.AdditionalInfoDTO.SalesSummaryDTO salesSummary;

                @lombok.NoArgsConstructor
                @lombok.Data
                public static class SalesSummaryDTO {
                    @JSONField(name = "currency")
                    private String currency;
                    @JSONField(name = "period")
                    private String period;
                    @JSONField(name = "sales_amount")
                    private Integer salesAmount;
                    @JSONField(name = "sales_count")
                    private Integer salesCount;
                }
            }

            @lombok.NoArgsConstructor
            @lombok.Data
            public static class BusinessInfoDTO {
                @JSONField(name = "email")
                private String email;
                @JSONField(name = "phone_number")
                private String phoneNumber;
                @JSONField(name = "postcode")
                private String postcode;
                @JSONField(name = "rating")
                private Double rating;
                @JSONField(name = "registration_date")
                private String registrationDate;
            }
        }

        @lombok.NoArgsConstructor
        @lombok.Data
        public static class TravelersDTO {
            @JSONField(name = "identifier")
            private String identifier;
            @JSONField(name = "name")
            private String name;
            @JSONField(name = "title")
            private String title;
        }
    }

    @lombok.NoArgsConstructor
    @lombok.Data
    public static class PaymentMethodDTO {
        @JSONField(name = "card")
        private PaymentMethodDTO.CardDTO card;
        @JSONField(name = "card_present")
        private PaymentMethodDTO.CardPresentDTO cardPresent;
        @JSONField(name = "type")
        private String type;

        @lombok.NoArgsConstructor
        @lombok.Data
        public static class CardDTO {
            @JSONField(name = "additional_info")
            private AdditionalInfoDTO additionalInfo;
            @JSONField(name = "billing")
            private BillingDTO billing;
            @JSONField(name = "cryptogram")
            private String cryptogram;
            @JSONField(name = "cvc")
            private String cvc;
            @JSONField(name = "expiry_month")
            private String expiryMonth;
            @JSONField(name = "expiry_year")
            private String expiryYear;
            @JSONField(name = "external_three_ds")
            private ExternalThreeDsDTO externalThreeDs;
            @JSONField(name = "name")
            private String name;
            @JSONField(name = "number")
            private String number;
            @JSONField(name = "number_type")
            private String numberType;
            @JSONField(name = "three_ds")
            private ThreeDsDTO threeDs;

            @lombok.NoArgsConstructor
            @lombok.Data
            public static class AdditionalInfoDTO {
                @JSONField(name = "merchant_verification_value")
                private String merchantVerificationValue;
                @JSONField(name = "token_requestor_id")
                private String tokenRequestorId;
            }

            @lombok.NoArgsConstructor
            @lombok.Data
            public static class BillingDTO {
                @JSONField(name = "address")
                private PaymentMethodDTO.CardDTO.BillingDTO.AddressDTO address;
                @JSONField(name = "email")
                private String email;
                @JSONField(name = "first_name")
                private String firstName;
                @JSONField(name = "last_name")
                private String lastName;
                @JSONField(name = "phone_number")
                private String phoneNumber;

                @lombok.NoArgsConstructor
                @lombok.Data
                public static class AddressDTO {
                    @JSONField(name = "city")
                    private String city;
                    @JSONField(name = "country_code")
                    private String countryCode;
                    @JSONField(name = "postcode")
                    private String postcode;
                    @JSONField(name = "state")
                    private String state;
                    @JSONField(name = "street")
                    private String street;
                }
            }

            @lombok.NoArgsConstructor
            @lombok.Data
            public static class ExternalThreeDsDTO {
                @JSONField(name = "authentication_value")
                private String authenticationValue;
                @JSONField(name = "ds_transaction_id")
                private String dsTransactionId;
                @JSONField(name = "eci")
                private String eci;
                @JSONField(name = "three_ds_exemption")
                private String threeDsExemption;
                @JSONField(name = "three_ds_server_transaction_id")
                private String threeDsServerTransactionId;
                @JSONField(name = "version")
                private String version;
            }

            @lombok.NoArgsConstructor
            @lombok.Data
            public static class ThreeDsDTO {
                @JSONField(name = "acs_response")
                private String acsResponse;
                @JSONField(name = "device_data_collection_res")
                private String deviceDataCollectionRes;
                @JSONField(name = "ds_transaction_id")
                private String dsTransactionId;
                @JSONField(name = "return_url")
                private String returnUrl;
            }
        }

        @lombok.NoArgsConstructor
        @lombok.Data
        public static class CardPresentDTO {
            @JSONField(name = "card_sequence_number")
            private String cardSequenceNumber;
            @JSONField(name = "cardholder_verification_method")
            private String cardholderVerificationMethod;
            @JSONField(name = "emv_tags")
            private String emvTags;
            @JSONField(name = "encrypted_pin")
            private String encryptedPin;
            @JSONField(name = "expiry_month")
            private String expiryMonth;
            @JSONField(name = "expiry_year")
            private String expiryYear;
            @JSONField(name = "fallback")
            private Boolean fallback;
            @JSONField(name = "fallback_reason")
            private String fallbackReason;
            @JSONField(name = "number")
            private String number;
            @JSONField(name = "pan_entry_mode")
            private String panEntryMode;
            @JSONField(name = "terminal_info")
            private TerminalInfoDTO terminalInfo;
            @JSONField(name = "track1")
            private String track1;
            @JSONField(name = "track2")
            private String track2;

            @lombok.NoArgsConstructor
            @lombok.Data
            public static class TerminalInfoDTO {
                @JSONField(name = "mobile_device")
                private Boolean mobileDevice;
                @JSONField(name = "pin_entry_capability")
                private String pinEntryCapability;
                @JSONField(name = "supported_pan_entry_modes")
                private List<String> supportedPanEntryModes;
                @JSONField(name = "terminal_id")
                private String terminalId;
                @JSONField(name = "use_embedded_reader")
                private Boolean useEmbeddedReader;
            }
        }
    }

    @lombok.NoArgsConstructor
    @lombok.Data
    public static class PaymentMethodOptionsDTO {
        @JSONField(name = "card")
        private PaymentMethodOptionsDTO.CardDTO card;

        @lombok.NoArgsConstructor
        @lombok.Data
        public static class CardDTO {
            @JSONField(name = "authorization_type")
            private String authorizationType;
            /**
             * Specifies whether the funds should be requested automatically after the payment is authorized. Only applicable when payment_method is provided. Default to true.
             */
            @JSONField(name = "auto_capture")
            private Boolean autoCapture;
            @JSONField(name = "risk_control")
            private RiskControlDTO riskControl;
            @JSONField(name = "three_ds_action")
            private String threeDsAction;

            @lombok.NoArgsConstructor
            @lombok.Data
            public static class RiskControlDTO {
                @JSONField(name = "skip_risk_processing")
                private Boolean skipRiskProcessing;
                @JSONField(name = "three_domain_secure_action")
                private String threeDomainSecureAction;
                @JSONField(name = "three_ds_action")
                private String threeDsAction;
            }
        }
    }

    @lombok.NoArgsConstructor
    @lombok.Data
    public static class RiskControlOptionsDTO {
        @JSONField(name = "skip_risk_processing")
        private Boolean skipRiskProcessing;
        @JSONField(name = "tra_applicable")
        private Boolean traApplicable;
    }

    @lombok.NoArgsConstructor
    @lombok.Data
    public static class FundsSplitDataDTO {
        @JSONField(name = "amount")
        private Double amount;
        @JSONField(name = "destination")
        private String destination;
    }
}
