package novel;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.sohu.third.migu.novel.constants.MiguNovelConstant;
import com.sohu.third.migu.novel.enums.MiguNovelApiUrlEnum;
import com.sohu.third.migu.novel.response.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.List;

/**
 * 小说接口调用测试
 *
 * @Author: leibo
 * @Date: 2024/11/19 10:05
 **/
@Slf4j
public class NovelTest {

    @Test
    void testBookList() {
        String sign = SecureUtil.md5(MiguNovelConstant.APP_ID + "&" + MiguNovelConstant.APP_KEY);
        String url = MiguNovelConstant.URL_PREFIX
                + MiguNovelApiUrlEnum.BOOK_LIST.getUrl()
                + "?appId=" + MiguNovelConstant.APP_ID + "&sign=" + sign;
        String body = HttpRequest.get(url).execute().body();
        log.info("NovelBookList response:{}", body);
        JSONObject json = JSONObject.parseObject(body);
        if (!json.get("statusCode").equals("0")) {
            log.error("调用失败");
        }
        List<NovelBookListResponse> data = JSONUtil.toList(json.get("data").toString(), NovelBookListResponse.class);
        System.out.println(JSONUtil.toJsonStr(data));
    }

    @Test
    void testBookInfo() {
        String sign = SecureUtil.md5(MiguNovelConstant.APP_ID + "&"
                + "13692755" + "&"
                + MiguNovelConstant.APP_KEY);
        String url = MiguNovelConstant.URL_PREFIX
                + MiguNovelApiUrlEnum.BOOK_INFO.getUrl()
                + "?appId=" + MiguNovelConstant.APP_ID
                + "&bookId=" + "13692755"
                + "&sign=" + sign;
        String body = HttpRequest.get(url).execute().body();
        log.info("NovelBookInfo response:{}", body);
        JSONObject json = JSONObject.parseObject(body);
        if (!json.get("statusCode").equals("0")) {
            log.error("调用失败");
        }
        NovelBookInfoResponse data = JSONUtil.toBean(json.get("data").toString(), NovelBookInfoResponse.class);
        System.out.println(JSONUtil.toJsonStr(data));
    }

    @Test
    void testVolume() {
        String sign = SecureUtil.md5(MiguNovelConstant.APP_ID + "&"
                + "13692755" + "&"
                + MiguNovelConstant.APP_KEY);
        String url = MiguNovelConstant.URL_PREFIX
                + MiguNovelApiUrlEnum.VOLUME_LIST.getUrl()
                + "?appId=" + MiguNovelConstant.APP_ID
                + "&bookId=" + "13692755"
                + "&sign=" + sign;
        String body = HttpRequest.get(url).execute().body();
        log.info("NovelVolume response:{}", body);
        JSONObject json = JSONObject.parseObject(body);
        if (!json.get("statusCode").equals("0")) {
            log.error("调用失败");
        }
        List<NovelVolumeResponse> data = JSONUtil.toList(json.get("data").toString(), NovelVolumeResponse.class);
        System.out.println(JSONUtil.toJsonStr(data));
    }

    @Test
    void testChapterList() {
        String sign = SecureUtil.md5(MiguNovelConstant.APP_ID + "&"
                + "13692755" + "&"
                + MiguNovelConstant.APP_KEY);
        String url = MiguNovelConstant.URL_PREFIX
                + MiguNovelApiUrlEnum.CHAPTER_LIST.getUrl()
                + "?appId=" + MiguNovelConstant.APP_ID
                + "&bookId=" + "13692755"
                + "&sign=" + sign;
        String body = HttpRequest.get(url).execute().body();
        log.info("NovelChapterList response:{}", body);
        JSONObject json = JSONObject.parseObject(body);
        if (!json.get("statusCode").equals("0")) {
            log.error("调用失败");
        }
        List<NovelChapterListResponse> data = JSONUtil.toList(json.get("data").toString(),
                NovelChapterListResponse.class);
        System.out.println(JSONUtil.toJsonStr(data));
    }

    @Test
    void testChapterInfo() {
        String sign = SecureUtil.md5(MiguNovelConstant.APP_ID + "&"
                + "13692755" + "&" + "16270601" + "&"
                + MiguNovelConstant.APP_KEY);
        String url = MiguNovelConstant.URL_PREFIX
                + MiguNovelApiUrlEnum.CHAPTER_INFO.getUrl()
                + "?appId=" + MiguNovelConstant.APP_ID
                + "&bookId=" + "13692755"
                + "&chapterId=" + "16270601"
                + "&sign=" + sign;
        String body = HttpRequest.get(url).execute().body();
        log.info("chapterInfo response:{}", body);
        JSONObject json = JSONObject.parseObject(body);
        if (!json.get("statusCode").equals("0")) {
            log.error("调用失败");
        }
        NovelChapterInfoResponse data = JSONUtil.toBean(json.get("data").toString(), NovelChapterInfoResponse.class);
        System.out.println(JSONUtil.toJsonStr(data));
    }
}
