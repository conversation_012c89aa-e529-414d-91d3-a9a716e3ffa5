package com.sohu.oa.vc.service;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.sohu.oa.vc.constants.VcOaUrlEnum;
import com.sohu.oa.vc.request.OaVcHrmSearchRequest;
import com.sohu.oa.vc.response.*;
import lombok.extern.slf4j.Slf4j;

import java.net.HttpCookie;
import java.util.List;
import java.util.Objects;

@Slf4j
public class OaVcService {

    public static void main(String[] args) {
        OaVcHrmSearchRequest searchRequest = new OaVcHrmSearchRequest();
        searchRequest.setLoginName("kezhen");
        searchRequest.setLoginPwd("kz123456");
        searchRequest.setResourceName("柯真");
        queryStaff(searchRequest);
    }

    public static OaVcHrmSearchResultKeyResponse getHrmSearchResultKey(OaVcHrmSearchRequest request) {
        OaVcHrmSearchModuleListResponse moduleListResponse = queryHrmModule(request.getLoginName(), request.getLoginPwd());
        if (Objects.isNull(moduleListResponse)) {
            return null;
        }
        String all = "全部人员";
        String typeid = "";
        for (OaVcHrmSearchModuleListResponse.TabsDTO tab : moduleListResponse.getTabs()) {
            if (StrUtil.equalsAnyIgnoreCase(tab.getTitle(), all)) {
                typeid = tab.getTypeid();
                break;
            }
        }
        if (StrUtil.isBlankIfStr(typeid)) {
            return null;
        }
        StringBuilder url = new StringBuilder(VcOaUrlEnum.OA_DOMAIN + "/api/hrm/search/getHrmSearchResult?tabkey=" + typeid);
        if (StrUtil.isNotBlank(request.getResourceName())) {
            url.append("&resourcename=" + request.getResourceName());
        }
        if (StrUtil.isNotBlank(request.getMobile())) {
            url.append("&mobile=" + request.getMobile());
        }
        HttpResponse response = HttpRequest.post(url.toString()).header("cookie", moduleListResponse.getCookie()).execute();
        OaVcHrmSearchResultKeyResponse resultKeyResponse = JSONObject.parseObject(response.body(), OaVcHrmSearchResultKeyResponse.class);
        resultKeyResponse.setCookie(moduleListResponse.getCookie());
        return resultKeyResponse;
    }

    public static OaVcHrmSearchModuleListResponse queryHrmModule(String name, String password) {
        OaVcLoginResponse loginResponse = login(name, password);
        if (Objects.isNull(loginResponse)) {
            return null;
        }
        String ur = VcOaUrlEnum.OA_DOMAIN + "/api/hrm/search/getHrmSearchMoudleList";
        HttpResponse response = HttpRequest.get(ur).header("cookie", loginResponse.getCookie()).execute();
        OaVcHrmSearchModuleListResponse responseParse = JSONObject.parseObject(response.body(), OaVcHrmSearchModuleListResponse.class);
        if (Objects.isNull(responseParse) || !StrUtil.equalsAnyIgnoreCase(responseParse.getStatus(), "1")) {
            log.error("VC OA查询人员组织架构响应：{}", response.body());
            return null;
        }
        responseParse.setCookie(loginResponse.getCookie());
        return responseParse;
    }

    /***
     * 查询员工
     * @return
     */
    public static OaVcStaffResponse queryStaff(OaVcHrmSearchRequest searchRequest) {
        OaVcHrmSearchResultKeyResponse keyResponse = getHrmSearchResultKey(searchRequest);
        if (Objects.isNull(keyResponse) || StrUtil.isBlankIfStr(keyResponse.getSessionKey())) {
            return null;
        }
        String ur = VcOaUrlEnum.OA_DOMAIN + "/api/ec/dev/table/datas?current=1&dataKey=" + keyResponse.getSessionKey();
        HttpResponse response = HttpRequest.post(ur).header("cookie", keyResponse.getCookie()).execute();
        OaVcStaffResponse responseParse = JSONObject.parseObject(response.body(), OaVcStaffResponse.class);
        if (BooleanUtil.isFalse(responseParse.getStatus())) {
            log.error("oa vc查询员工：{}", response.body());
            return null;
        }
        return responseParse;
    }

    /**
     * 打卡
     *
     * @param name
     * @param password
     * @return
     */
    public static OaVcPunchResponse oaPunch(String name, String password) {
        OaVcLoginResponse loginResponse = login(name, password);
        if (Objects.isNull(loginResponse)) {
            return null;
        }
        String punchUrl = VcOaUrlEnum.OA_DOMAIN + VcOaUrlEnum.PUNCH.getUrlPath();
        HttpResponse punchPost = HttpRequest.post(punchUrl).header("cookie", loginResponse.getCookie()).execute();
        // 获取响应体
        String punchPostResponse = punchPost.body();
        OaVcPunchResponse response = JSONObject.parseObject(punchPostResponse, OaVcPunchResponse.class);
        if (Objects.isNull(response) || !StrUtil.equalsAnyIgnoreCase(response.getSuccess(), "0")) {
            log.info("OA 打卡请求 Response Body: {}", punchPostResponse);
            return null;
        }
        response.setCookie(loginResponse.getCookie());
        return response;
    }

    /**
     * 登录
     *
     * @param name
     * @param password
     * @return
     */
    public static OaVcLoginResponse login(String name, String password) {
        String loginUrl = VcOaUrlEnum.OA_DOMAIN + VcOaUrlEnum.LOGIN.getUrlPath() + VcOaUrlEnum.OA_LOGIN_COMMON_PARAM;
        if (StrUtil.isBlankIfStr(name)) {
            loginUrl += "&loginid=kezhen&userpassword=kz123456";
        } else {
            loginUrl += "&loginid=" + name + "&userpassword=" + password;
        }
        log.info("OA 登录请求URL：{}", loginUrl);

        HttpResponse response = HttpRequest.post(loginUrl).execute();
        // 获取响应体
        String responseBody = response.body();
        OaVcLoginResponse vaLoginResponse = JSONObject.parseObject(responseBody, OaVcLoginResponse.class);
        if (Objects.isNull(vaLoginResponse) || !StrUtil.equalsAnyIgnoreCase(vaLoginResponse.getMsgCode(), "0")) {
            log.info("OA 登录请求 Response Body: {}", responseBody);
            return null;
        }
        // 获取Cookie列表
        List<HttpCookie> cookies = response.getCookies();
        // 将Cookie列表转换为 a=1;b=2;c=3 格式的字符串
        String cookieString = convertCookiesToString(cookies);
        vaLoginResponse.setCookie(cookieString);
        return vaLoginResponse;
    }


    /**
     * 将List<HttpCookie>转换为 a=1;b=2;c=3 格式的字符串
     *
     * @param cookies Cookie列表
     * @return 拼接后的Cookie字符串
     */
    public static String convertCookiesToString(List<HttpCookie> cookies) {
        StringBuilder cookieBuilder = new StringBuilder();
        for (HttpCookie cookie : cookies) {
            if (cookieBuilder.length() > 0) {
                cookieBuilder.append(";"); // 用分号分隔多个Cookie
            }
            cookieBuilder.append(cookie.getName()).append("=").append(cookie.getValue());
        }
        return cookieBuilder.toString();
    }

}
