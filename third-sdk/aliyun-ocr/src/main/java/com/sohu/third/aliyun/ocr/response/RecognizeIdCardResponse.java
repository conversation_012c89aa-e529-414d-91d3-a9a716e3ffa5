package com.sohu.third.aliyun.ocr.response;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 阿里云的身份证识别结果转化后
 */
@NoArgsConstructor
@Data
public class RecognizeIdCardResponse {


    /**
     * 身份证号
     */
    private String idCardNumber;
    /**
     * 姓名
     */
    private String name;
    /**
     * 性别
     */
    private String sex;
    /**
     * 出生日期
     */
    private String birthDate;
    /**
     * 地址
     */
    private String address;
    /**
     * 公安局，武汉市公安局洪山分局
     */
    private String issueAuthority;
    /**
     * 有效期开始日期，如：2018.04.03
     */
    private String validBeginDate;
    /**
     * 有效期结束日期，如：2028.04.03
     */
    private String validEndDate;

    /**
     * 阿里云正面识别结果转换
     *
     * @param response
     * @return {@link RecognizeIdCardResponse}
     */
    public RecognizeIdCardResponse build(AliyunOcrRecognizeIdCardFrontResponse response) {
        RecognizeIdCardResponse result = new RecognizeIdCardResponse();
        AliyunOcrRecognizeIdCardFrontResponse.DataDTO.FaceDTO.DataDetail dataDetail = response.getData().getFace().getData();
        result.setName(dataDetail.getName());
        result.setIdCardNumber(dataDetail.getIdNumber());
        result.setBirthDate(dataDetail.getBirthDate());
        result.setSex(dataDetail.getSex());
        result.setAddress(dataDetail.getAddress());
        return result;
    }

    /**
     * 阿里云正面识别结果转换
     *
     * @param response
     * @return {@link RecognizeIdCardResponse}
     */
    public RecognizeIdCardResponse build(RecognizeIdCardResponse result, AliyunOcrRecognizeIdCardBackResponse response) {
        AliyunOcrRecognizeIdCardBackResponse.DataBody.BackDetail.DataDTO dataDTO = response.getData().getBack().getData();
        result.setIssueAuthority(dataDTO.getIssueAuthority());
        List<String> split = StrUtil.split(dataDTO.getValidPeriod(), "-");
        result.setValidBeginDate(DateUtil.format(DateUtil.parse(split.get(0)), "yyyyMMdd"));
        if (!split.get(1).equals("长期")){
            result.setValidEndDate(DateUtil.format(DateUtil.parse(split.get(1)), "yyyyMMdd"));
        }else {
            result.setValidEndDate(split.get(1));
        }
        return result;
    }

}
