package com.sohu.third.wechat.pay.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 商家转账-发起商家转账
 *
 * @author: fzy
 * @date: 2023/5/6 17:13
 * @version:
 */
@Data
public class WechatPayMerchantTransferUnifiedResponse extends BaseWechatPayResponse implements Serializable {
    private static final long serialVersionUID = 8241891654782412789L;

    /**
     * 商家批次单号
     */
    @JsonProperty(value = "out_batch_no")
    private String outBatchNo;

    /**
     * 微信批次单号
     */
    @JsonProperty(value = "batch_id")
    private String batchId;

    /**
     * 批次创建时间
     */
    @JsonProperty(value = "create_time")
    private String createTime;

}
