package com.sohu.third.wechat.pay.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sohu.third.wechat.pay.annotation.Required;
import lombok.Data;

import java.io.Serializable;

/**
 * 受理转账明细电子回单返回 及 查询转账明细电子回单受理结果返回
 *
 * @author: lyw
 * @date: 2023/5/6 19:46
 * @version: 1.0.0
 */
@Data
public class WechatPayAcceptTheTransferResponse extends BaseWechatPayResponse implements Serializable {

    private static final long serialVersionUID = 5392369423225328754L;

    /**
     * 受理类型
     */
    @Required
    @JsonProperty(value = "accept_type")
    private String acceptType;

    /**
     * 商家转账批次单号
     */
    @JsonProperty(value = "out_batch_no")
    private String outBatchNo;

    /**
     * 商家转账明细单号
     */
    @Required
    @JsonProperty(value = "out_detail_no")
    private String outDetailNo;

    /**
     * 电子回单受理单号
     */
    @Required
    @JsonProperty(value = "signature_no")
    private String signatureNo;

    /**
     * 电子回单状态
     */
    @JsonProperty(value = "signature_status")
    private String signatureStatus;

    /**
     * 电子回单文件的hash方法
     */
    @JsonProperty(value = "hash_type")
    private String hashType;

    /**
     * 电子回单文件的hash值
     */
    @JsonProperty(value = "hash_value")
    private String hashValue;

    /**
     * 电子回单文件的下载地址
     */
    @JsonProperty(value = "download_url")
    private String downloadUrl;

}
