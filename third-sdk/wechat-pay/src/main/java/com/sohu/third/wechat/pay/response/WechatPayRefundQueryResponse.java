package com.sohu.third.wechat.pay.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 微信支付-退款查询返回结果
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/02 10:40
 */
@Data
public class WechatPayRefundQueryResponse extends BaseWechatPayResponse implements Serializable {

    private static final long serialVersionUID = 5392369423225328754L;

    /**
     * 微信支付退款单号.
     */
    @JsonProperty(value = "refund_id")
    private String refundId;

    /**
     * 商户退款单号
     */
    @JsonProperty(value = "out_refund_no")
    private String outRefundNo;

    /**
     * 微信支付订单号
     */
    @JsonProperty(value = "transaction_id")
    private String transactionId;

    /**
     * 商户订单号
     */
    @JsonProperty(value = "out_trade_no")
    private String outTradeNo;

    /**
     * 退款渠道
     * 枚举值：
     * ORIGINAL：原路退款
     * BALANCE：退回到余额
     * OTHER_BALANCE：原账户异常退到其他余额账户
     * OTHER_BANKCARD：原银行卡异常退到其他银行卡
     */
    private String channel;

    /**
     * 退款入账账户
     * 取当前退款单的退款入账方，有以下几种情况：
     * 1）退回银行卡：{银行名称}{卡类型}{卡尾号}
     * 2）退回支付用户零钱:支付用户零钱
     * 3）退还商户:商户基本账户商户结算银行账户
     * 4）退回支付用户零钱通:支付用户零钱通
     */
    @JsonProperty(value = "user_received_account")
    private String userReceivedAccount;

    /**
     * 退款成功时间
     * 当退款状态为退款成功时有返回。
     */
    @JsonProperty(value = "success_time")
    private String successTime;

    /**
     * 退款创建时间
     * 退款受理时间
     */
    @JsonProperty(value = "create_time")
    private String createTime;

    /**
     * 退款状态
     * 退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，可前往服务商平台-交易中心，手动处理此笔退款。
     * 枚举值：
     * SUCCESS：退款成功
     * CLOSED：退款关闭
     * PROCESSING：退款处理中
     * ABNORMAL：退款异常
     */
    private String status;

    /**
     * 资金账户
     * 退款所使用资金对应的资金账户类型
     * 枚举值：
     * UNSETTLED : 未结算资金
     * AVAILABLE : 可用余额
     * UNAVAILABLE : 不可用余额
     * OPERATION : 运营户
     * BASIC : 基本账户（含可用余额和不可用余额）
     */
    @JsonProperty(value = "funds_account")
    private String fundsAccount;

    /**
     * 金额信息
     * 金额详细信息
     */
    private Amount amount;

    /**
     * 优惠退款信息
     */
    @JsonProperty(value = "promotion_detail")
    private List<PromotionDetail> promotionDetailList;

    /**
     * 订单金额
     *
     * <AUTHOR>
     * @version 1.0.0
     * @date 2022/12/14 11:53
     */
    @Data
    public static class Amount implements Serializable {

        private static final long serialVersionUID = 3896217803255511617L;
        /**
         * 订单总金额，单位为分。
         */
        private Integer total;

        /**
         * 退款金额
         */
        private Integer refund;

        /**
         * 退款出资账户及金额
         */
        private List<AmountFrom> from;

        /**
         * 现金支付金额，单位为分，只能为整数
         */
        @JsonProperty(value = "payer_total")
        private Integer payerTotal;

        /**
         * 用户退款金额
         * 退款给用户的金额，不包含所有优惠券金额
         */
        @JsonProperty(value = "payer_refund")
        private Integer payerRefund;

        /**
         * 应结退款金额
         * 去掉非充值代金券退款金额后的退款金额，单位为分，
         * 退款金额=申请退款金额-非充值代金券退款金额，退款金额<=申请退款金额
         */
        @JsonProperty(value = "settlement_refund")
        private Integer settlementRefund;

        /**
         * 应结订单金额
         * 应结订单金额=订单金额-免充值代金券金额，应结订单金额<=订单金额，单位为分
         */
        @JsonProperty(value = "settlement_total")
        private Integer settlementTotal;

        /**
         * 优惠退款金额
         * 优惠退款金额<=退款金额，退款金额-代金券或立减优惠退款金额为现金
         * 说明详见代金券或立减优惠，单位为分
         */
        @JsonProperty(value = "discount_refund")
        private Integer discountRefund;

        /**
         * 货币类型 CNY：人民币
         */
        private String currency;

        /**
         * 手续费退款金额
         * 单位为分
         */
        @JsonProperty(value = "refund_fee")
        private Integer refundFee;

        /**
         * 订单金额初始化
         */
        public Amount() {
            this.currency = "CNY";
        }
    }

    /**
     * 退款出资的账户类型及金额信息
     *
     * <AUTHOR>
     * @version 1.0.0
     * @date 2022/12/14 11:53
     */
    @Data
    public static class AmountFrom implements Serializable {

        private static final long serialVersionUID = -1711721766629575921L;
        /**
         * 出资账户类型
         */
        private String account;

        /**
         * 出资金额
         */
        private Integer amount;
    }

    /**
     * 优惠功能
     *
     * <AUTHOR>
     * @version 1.0.0
     * @date 2022/12/14 13:34
     */
    @Data
    public static class PromotionDetail implements Serializable {
        private static final long serialVersionUID = -8822597319569525831L;

        /**
         * 券ID
         */
        @JsonProperty(value = "promotion_id")
        private String promotionId;

        /**
         * 优惠名称
         * GLOBAL-全场代金券
         * SINGLE-单品优惠
         */
        private String scope;

        /**
         * 优惠类型
         * 枚举值：
         * COUPON-代金券，需要走结算资金的充值型代金券
         * DISCOUNT-优惠券，不走结算资金的免充值型优惠券
         */
        private String type;

        /**
         * 优惠券面额
         * 优惠券面额
         * 示例值：100
         */
        private Integer amount;

        /**
         * 优惠退款金额
         */
        @JsonProperty(value = "refund_amount")
        private Integer refundAmount;

        /**
         * 单品列表
         */
        @JsonProperty(value = "goods_detail")
        private List<GoodsDetail> goodsDetail;
    }

    /**
     * 单品
     */
    @Data
    public static class GoodsDetail implements Serializable {

        private static final long serialVersionUID = 6164071002618561090L;

        /**
         * 商户侧商品编码
         */
        @JsonProperty(value = "merchant_goods_id")
        private String goodsId;

        /**
         * 微信支付商品编码
         */
        @JsonProperty(value = "wechatpay_goods_id")
        private String platformGoodsId;

        /**
         * 商品名称
         */
        @JsonProperty(value = "goods_name")
        private String goodsName;

        /**
         * 商品单价，单位为分
         */
        @JsonProperty(value = "unit_price")
        private Integer unitPrice;

        /**
         * 商品退款金额
         */
        @JsonProperty(value = "refund_amount")
        private Integer refundAmount;

        /**
         * 商品退货数量
         * 单品的退款数量
         */
        @JsonProperty(value = "refund_quantity")
        private Integer refundQuantity;
    }

}
