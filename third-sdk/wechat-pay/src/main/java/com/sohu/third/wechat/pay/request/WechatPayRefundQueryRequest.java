package com.sohu.third.wechat.pay.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sohu.third.wechat.pay.response.WechatPayRefundQueryResponse;
import lombok.Data;

/**
 * <pre>
 * Created by <PERSON><PERSON> on 2016-11-24.
 * </pre>
 *
 * <AUTHOR> href="https://github.com/binarywang">Binary <PERSON></a>
 */
@Data
public class WechatPayRefundQueryRequest extends BaseWechatPayRequest<WechatPayRefundQueryResponse> {
    private static final long serialVersionUID = 859585105481201993L;

    /**
     * <pre>
     * 商户退款单号
     * out_refund_no
     * String(32)
     * 1217752501201407033233368018
     * 商户侧传给微信的退款单号
     * </pre>
     */
    @JsonIgnore
    private String outRefundNo;

    @Override
    public boolean ignoreAppId() {
        return Boolean.TRUE;
    }

    @Override
    public boolean ignoreMchId() {
        return Boolean.TRUE;
    }

//    @Override
//    public boolean ignoreSubAppId() {
//        return Boolean.TRUE;
//    }
}
