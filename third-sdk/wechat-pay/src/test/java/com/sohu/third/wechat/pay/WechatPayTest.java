package com.sohu.third.wechat.pay;

import cn.hutool.json.JSONUtil;
import com.sohu.third.wechat.pay.bean.WechatPayConfig;
import com.sohu.third.wechat.pay.exception.WechatPayException;
import com.sohu.third.wechat.pay.request.WechatPayOrderQueryRequest;
import com.sohu.third.wechat.pay.request.WechatPayRefundQueryRequest;
import com.sohu.third.wechat.pay.request.WechatPayRefundRequest;
import com.sohu.third.wechat.pay.request.WechatPayUnifiedOrderRequest;
import com.sohu.third.wechat.pay.response.*;
import com.sohu.third.wechat.pay.response.notify.WechatPayOrderNotifyResponse;
import com.sohu.third.wechat.pay.response.notify.WechatPayRefundNotifyResponse;
import com.wechat.pay.contrib.apache.httpclient.notification.NotificationRequest;
import com.sohu.third.wechat.pay.constant.WechatPayConstants;
import com.sohu.third.wechat.pay.service.WechatPayService;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 13:43
 */
public class WechatPayTest {

    /**
     * 支付宝配置
     *
     * @return
     */
    private static WechatPayConfig wechatConfig() {
        WechatPayConfig config = new WechatPayConfig();
        config.setAppId("wxfc2c052f0e3befd8");
        config.setMchId("1615698323");
        config.setMchKey("hushaoshao1615698323hushaoshao16");
//        config.setAppId("wx6cff519a5888b91d");
//        config.setMchId("1501584331");
//        config.setMchKey("hfztf20804091436kaixinbalalalala");
        config.setTradeType(WechatPayConstants.TradeType.JSAPI);
        config.setSignType("MD5");
//        config.setKeyPath("http://midstage.oss-cn-hangzhou.aliyuncs.com/10000000/494f3385-df2f-4d18-b74b-82e3b17e87d9.p12");
//        config.setPrivateKeyPath("http://midstage.oss-cn-hangzhou.aliyuncs.com/10000000/a17245b0-a978-4ce9-9242-415d6b700813.pem");
//        config.setPrivateCertPath("http://midstage.oss-cn-hangzhou.aliyuncs.com/10000000/4efeb041-eb81-4695-b09e-57b0d28b6c73.pem");
        config.setKeyPath("https://sohuglobal.oss-cn-hongkong.aliyuncs.com/20230424/06cb6681e8f833cc00fe250cdcf5d9bb4e2232e9821cdb4bbc9ae4c2cb7ccb37.p12");
        config.setPrivateKeyPath("https://sohuglobal.oss-cn-hongkong.aliyuncs.com/20230424/1250c7a819cbd6e573ed06e077d3a34ad839940ddef3a84b26bfa3f38b3296ca.pem");
        config.setPrivateCertPath("https://sohuglobal.oss-cn-hongkong.aliyuncs.com/20230424/7d55401879025a2f2dffbf3b2d463486d06c0423dd3818487ac6270dd74ca9bd.pem");
//        config.setSubMchId("1532970801");
//        config.setSubAppId("wx5748977cf1ba0359");
        config.setNotifyUrl("https://whale.zwztf.net/api/message/pay-callback/93635611966901");
//        config.setApiV3Key("hfztf20804091436kaixinbalalalala");
        config.setApiV3Key("hushaoshao1615698323hushaoshao16");
        return config;
    }

    @Test
    public void unifiedOrder() {
        WechatPayUnifiedOrderRequest request = new WechatPayUnifiedOrderRequest();
        request.setDescription("Image形象店-深圳腾大-QQ公仔");
        request.setOutTradeNo("PT243168340522658936405");
        request.setNotifyUrl(wechatConfig().getNotifyUrl() + "/a");
        WechatPayUnifiedOrderRequest.Amount wechatAmount = new WechatPayUnifiedOrderRequest.Amount();
        wechatAmount.setTotal(1);
        request.setAmount(wechatAmount);
        WechatPayUnifiedOrderRequest.Payer wechatPayer = new WechatPayUnifiedOrderRequest.Payer();
//        wechatPayer.setSubOpenid("oTS675HxxJey8lGIxGkaGx9hqQc8");
        // 方的
//        wechatPayer.setOpenid("o0Dsn5BqvsfTa6GWnZLRzWOJjzI0");
        // 我的
//        wechatPayer.setOpenid("o0Dsn5Hcvcpk6ag0khkVGv8ZO3Y8");
        // 新人
        wechatPayer.setOpenid("o0Dsn5MRN7GRKcnh4gncL8yc8A_0");
        request.setPayer(wechatPayer);
        try {
            System.out.println(JSONUtil.toJsonStr(request));
            System.out.println(JSONUtil.toJsonStr(wechatConfig()));
            WechatPaymentResponse response = WechatPayService.unifiedOrder(request, wechatConfig());
            System.out.println(JSONUtil.toJsonStr(response));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void closeOrder() {
        String outTradeNo = "97621415091444";
        try {
            WechatPayOrderCloseResponse response = WechatPayService.closeOrder(outTradeNo, wechatConfig());
            System.out.println(JSONUtil.toJsonStr(response));
        } catch (Exception e) {

        }
    }

    @Test
    public void queryOrder() {
        WechatPayOrderQueryRequest request = new WechatPayOrderQueryRequest();
        request.setOutTradeNo("PT407168265064266271914");
        try {
            WechatPayQueryResponse response = WechatPayService.queryOrder(request, wechatConfig());
            System.out.println(JSONUtil.toJsonStr(response));
        } catch (Exception e) {

        }
    }

    @Test
    public void refundTest() {
        WechatPayRefundRequest request = new WechatPayRefundRequest();
        request.setTransactionId("4200001903202309218298007581");
        request.setOutRefundNo("M528169529042521039307");
        WechatPayRefundRequest.Amount amount = new WechatPayRefundRequest.Amount();
        amount.setRefund(2);
        amount.setTotal(2);
        request.setAmount(amount);
        request.setNotifyUrl(wechatConfig().getNotifyUrl() + "/wxrefund/callback");
        try {
            WechatPayRefundResponse response = WechatPayService.refund(request, wechatConfig());
            System.out.println(JSONUtil.toJsonStr(response));
        } catch (Exception e) {

        }
    }

    @Test
    public void refundQuery() {
        String outRefundNo = "PT552168663013288564929";
        WechatPayConfig wechatPayConfig = wechatConfig();
        WechatPayRefundQueryRequest request = new WechatPayRefundQueryRequest();
        request.setOutRefundNo(outRefundNo);
        try {
            WechatPayRefundQueryResponse response = WechatPayService.refundQuery(request, wechatPayConfig);
            System.out.println(JSONUtil.toJsonStr(response));
        } catch (WechatPayException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void refundCallbackTest() {
        String serialNumber = "604E6FDF1DA0A23146EDE0C26528D39BD5D2D0B1";
        String signature = "no4y1+XR92yz59kKxAXTnHhxvGfBeYHpryssb7nuBaC9d06fsCFzVzuNBx5XgmfxVvfxsphVPSrYP+yX8cFscd2RuZVb1azSRewWwd7K8ROLfDcscVBCWAeMWqlXuZxuNMQhHBlNz3gDrcauuYYJfhBvhrxX+e/XcJgNZgeXoKxKVm/sbG0trixEyV+iTsVV5EWCXiO2/CizzlivhsO6jXfe/XWrq4UnEq0HfEgzYaHUotzHeR6br6wEUH9X+jgU1lQN3SbMO5Cf/rmcFV7tAKsvm+bTMjC3pReUwRzYxI+ZSL/4bwd3JlehGpb94Kk+MrNCix1KRNAVCzsmp1gfiw==";
        String nonce = "bdAiSbGYiqbrHjOaCjx7aYqncgpH2Jaa";
        String timestamp = "1671864353";
        String body = "{\"id\":\"3485a3fb-196f-5af0-b092-569f76bb1451\",\"create_time\":\"2022-12-24T14:45:47+08:00\",\"resource_type\":\"encrypt-resource\",\"event_type\":\"REFUND.SUCCESS\",\"summary\":\"退款成功\",\"resource\":{\"original_type\":\"refund\",\"algorithm\":\"AEAD_AES_256_GCM\",\"ciphertext\":\"WIVLXOd/XFUOxMZY3wJkk0cC7Hp0sukInDx+JFO08u9S4HDbLlYOw6HzDpwx/7IWUUTzxYK/duiZatSjn00N6WDIOChUk7NugXh5X15wMEWgKgh6Lzex9hTSp6XOoPDC1INtzDUxdVbmAjXbdtDCaJJfXY6CaOXC4FfscZwy9oOE/8kDI3n7MPea7sVHtOvfGAQ3sDYm+LtV5W8XP3hlZFtsP8GbFfW9a10C+mXQ4Gaj2Tdla48tzD1SK0jwotY3FWNc9Xp1PcHWBgtQZ9IhLb6szCIy0IuFjN+NvIT5KG5OF+unLGvFdQ6TXnwhZHfrSnNceN8olaCJTr8+Vn1YvMcJZuW/y68xRGQSspOAPJMJF6XmSBhySzLnhwtnfg3aYfu3yXRf45pLi/HUwNDD73S7jJvILkDByL7mbvUPkfFrd0f22OOuGlYX5cXhaOwuEGi5aesvqmpzvJwSwRghZQM2axsX8ETGOzd+zZZ8ICyYWFsMDvgDWxphBTM6KTXBI+expxkij3VIuLMAr/noBjz4aCDUu/ERIOPVSW2DFA==\",\"associated_data\":\"refund\",\"nonce\":\"TtiRfh3nGd3y\"}}";
        NotificationRequest notificationRequest = new NotificationRequest.Builder()
                .withSerialNumber(serialNumber)
                .withSignature(signature)
                .withNonce(nonce)
                .withTimestamp(timestamp)
                .withBody(body)
                .build();
        try {
            WechatPayRefundNotifyResponse response = WechatPayService.parseRefundNotifyResult(notificationRequest, wechatConfig());
            System.out.println(JSONUtil.toJsonStr(response));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void queryCallbackTest() {
        String serialNumber = "6461C5956DBD812E6C09FECBA76B07DD2250ADD9";
        String signature = "v2/YEgVdj8tY3PMx+jM9dUCQg6Qw5dJhO2E0AnJk2cyN7lSNDzhS6AoEq4NqwPlKoxauOHq+onLPwYX2hbfX01LpFUeVK7krpl/JN+sankmTRKsbSv3hzSDAAT9L/8+rF5h1zH18Z+M58CFwLoqVB6bnB265r8bIJUYZxNNib+IleBocWbLxgTGVTn63xnC2ejO9bdlCt2S59Tdg8Dk97O5xTCWfNFdDRrp/d4QsYuCHRAN3hSh0Ihn2OQLC6EPExFpDtc7PCMuZ/de9Xk8yBXqLRYESuthVT2Z3DgZ04mRcD4EkzkxbpFL0PBsoRa5xDKm6l0KrcEQ42mv22LnUyw==";
        String timestamp = "1683450464";
//        String nonce = "bdAiSbGYiqbrHjOaCjx7aYqncgpH2Jaa";
        String nonce = "HyWvfYTg4zaB";
        String body = "{\"id\":\"8e436e79-9a57-57cd-a069-33f36b2e274b\",\"create_time\":\"2023-05-07T17:07:44+08:00\",\"resource_type\":\"encrypt-resource\",\"event_type\":\"TRANSACTION.SUCCESS\",\"summary\":\"支付成功\",\"resource\":{\"original_type\":\"transaction\",\"algorithm\":\"AEAD_AES_256_GCM\",\"ciphertext\":\"cq8f8IbF82qMt1FOrW5f+557/CDZLuBSXs2zP0CEy70ZNNnwMUphVWIcg/XlWRJamQ/Xy9u1T2NEvz10hEHpiPpwFiA8Ny27Osb2AnrdmI1RgUkVbngkWJMcPmN6C8xnkJFxAy79fsH8uKs8NPd9p4sJ+KC251Nle3dG3vwlutwJrJxkovLVkkMfnboNQIuorJBRvvjynDsbQJ+LI40/zJ2UH3ADt2xGl6tD2IZbQrtXmD1oNM8x+bhxX1qOMLiy4L7RvcH0aHbZF0gl57xiO6KD9AzmYUNZ0SbqpWCruAZSf6KizOGrDd481/gB9n3o+kXBjnXciDaAzB0T7c9XtqoS15Nmy/Wa/nCutoxZJm2e6o53bEIUADvd/mBaado/XkyIMULD1zddO9YO4KlxHX9TJVX95baD0wmdSaJ7Fdt2/SA3ayxdxI2ytqkdScNqGpw/BVZNfIZbS0A/tWP9SQpqrTa2wIc69oiM1Tw3YeTqpVFL5xhB61XEGtQAQQaOGh/bYYZiflakkIp9TgCy5zv9/LDl862hTt0JcZNqKWwXZlfbO3/kzhmXSUVAzlqTxYTABAK4weM7YtMXiw==\",\"associated_data\":\"transaction\",\"nonce\":\"HyWvfYTg4zaB\"}}";
        NotificationRequest notificationRequest = new NotificationRequest.Builder()
                .withSerialNumber(serialNumber)
                .withSignature(signature)
                .withNonce(nonce)
                .withTimestamp(timestamp)
                .withBody(body)
                .build();
        try {
            WechatPayOrderNotifyResponse response = WechatPayService.parseOrderNotifyResult(notificationRequest, wechatConfig());
            System.out.println(JSONUtil.toJsonStr(response));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
