package com.sohu.third.ai.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class AiSiliconFlowChatStreamTextResponse {

    /**
     * 对话ID
     */
    @JsonProperty("id")
    private String id;
    @JsonProperty("object")
    private String object;
    /**
     * 对话时间
     */
    @JsonProperty("created")
    private Integer created;
    @JsonProperty("model")
    private String model;
    @JsonProperty("choices")
    private List<ChoicesDTO> choices;
    @JsonProperty("system_fingerprint")
    private String systemFingerprint;
    @JsonProperty("usage")
    private UsageDTO usage;

    @NoArgsConstructor
    @Data
    public static class UsageDTO {
        @JsonProperty("prompt_tokens")
        private Integer promptTokens;
        @JsonProperty("completion_tokens")
        private Integer completionTokens;
        @JsonProperty("total_tokens")
        private Integer totalTokens;
    }

    @NoArgsConstructor
    @Data
    public static class ChoicesDTO {
        @JsonProperty("index")
        private Integer index;
        @JsonProperty("delta")
        private DeltaDTO delta;
        @JsonProperty("finish_reason")
        private Object finishReason;
        @JsonProperty("content_filter_results")
        private ContentFilterResultsDTO contentFilterResults;

        @NoArgsConstructor
        @Data
        public static class DeltaDTO {
            /**
             * 对话内容
             */
            @JsonProperty("content")
            private Object content;
            /**
             * 推理内容:只有deepseek-R1系列和Qwen/QwQ-32B型号支持推理内容
             */
            @JsonProperty("reasoning_content")
            private String reasoningContent;
            /**
             * 对话角色 {@link com.sohu.third.ai.constants.AiRoleEnum}
             */
            @JsonProperty("role")
            private String role;
        }

        @NoArgsConstructor
        @Data
        public static class ContentFilterResultsDTO {
            @JsonProperty("hate")
            private HateDTO hate;
            @JsonProperty("self_harm")
            private SelfHarmDTO selfHarm;
            @JsonProperty("sexual")
            private SexualDTO sexual;
            @JsonProperty("violence")
            private ViolenceDTO violence;

            @NoArgsConstructor
            @Data
            public static class HateDTO {
                @JsonProperty("filtered")
                private Boolean filtered;
            }

            @NoArgsConstructor
            @Data
            public static class SelfHarmDTO {
                @JsonProperty("filtered")
                private Boolean filtered;
            }

            @NoArgsConstructor
            @Data
            public static class SexualDTO {
                @JsonProperty("filtered")
                private Boolean filtered;
            }

            @NoArgsConstructor
            @Data
            public static class ViolenceDTO {
                @JsonProperty("filtered")
                private Boolean filtered;
            }
        }
    }
}
