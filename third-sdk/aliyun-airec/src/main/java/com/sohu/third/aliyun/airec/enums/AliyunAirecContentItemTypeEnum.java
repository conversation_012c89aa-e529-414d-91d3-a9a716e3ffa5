package com.sohu.third.aliyun.airec.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 内容行业-内容类型枚举
 * 此处由于阿里云只提供了这几个固定的类型，当想要的类型没有时，则占用没有被使用的类型来解决
 * 参考文档：https://help.aliyun.com/zh/airec/airec/getting-started/content-industry?spm=a2c4g.11186623.0.0.1b48342dirTNn0#title-xtn-r39-0x2
 * https://help.aliyun.com/zh/airec/airec/support/faq-related-to-data-access?spm=a2c4g.11186623.0.0.2c022475r7ukcx#h4-f6s-p4e-ecp
 */
@Getter
@AllArgsConstructor
public enum AliyunAirecContentItemTypeEnum {
    /**
     * 待使用
     */
    IMAGE("image","IMAGE"),
    /**
     * 图文
     */
    ARTICLE("article","图文"),
    /**
     * 视频
     */
    VIDEO("video","视频"),
    /**
     * 待使用
     */
    SHORTVIDEO("shortvideo","待使用"),
    /**
     * 任务（占用）
     */
    ITEM("item","任务"),
    /**
     * 问答（占用）
     */
    RECIPE("recipe","问答"),
    /**
     * 待使用
     */
    AUDIO("audio","AUDIO");

    private String code;
    private String msg;

    public static AliyunAirecContentItemTypeEnum getByCode(String code) {
        for (AliyunAirecContentItemTypeEnum type : AliyunAirecContentItemTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

}
