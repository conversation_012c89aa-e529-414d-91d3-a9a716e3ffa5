package com.sohu.third.aliyun.airec.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 *  组合过滤规则
 * 参考文献：https://help.aliyun.com/zh/airec/airec/use-cases/use-the-recommendation-filtering-feature-to-customize-the-filtering-of-feed-streams#div-hmi-twt-w7m
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AliyunAirecJoinFilterRule extends AliyunAirecBaseFilterRule{
    /**
     * 组合过滤类型
     * {@link com.sohu.third.aliyun.airec.enums.AliyunAirecQueryJoinEnum}
     */
    public String join;
    public List<AliyunAirecBaseFilterRule> filters = new ArrayList<>();

}
