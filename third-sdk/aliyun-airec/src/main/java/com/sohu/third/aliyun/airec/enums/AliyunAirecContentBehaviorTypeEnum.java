package com.sohu.third.aliyun.airec.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 内容行业-行为类型枚举
 * 文档链接  https://help.aliyun.com/zh/airec/airec/getting-started/content-industry?spm=a2c4g.11186623.0.0.1f57342dNGVKmE#h2-behavior5
 */
@Getter
@AllArgsConstructor
public enum AliyunAirecContentBehaviorTypeEnum {

    /**
     * 必填行为，且曝光行为数要大于点击行为数
     */
    EXPOSE("expose", "曝光"),
    /**
     * 必填行为
     */
    CLICK("click", "点击"),
    LIKE("like", "点赞"),
    UNLIKE("unlike", "踩"),
    COMMENT("comment", "评论"),
    COLLECT("collect", "收藏"),
    STAY("stay", "停留时长"),
    SHARE("share", "分享"),
    DOWNLOAD("download", "下载"),
    TIP("tip", "打赏"),
    SUBSCRIBE("subscribe", "关注"),
    /**
     * 具体使用参考负反馈功能详解
     */
    DISLIKE("dislike", "负反馈"),
    /**
     * bhv_value填1即可
     */
    PAGE_NEXT("page_next", "翻章");

    private String code;
    private String msg;

    public static AliyunAirecContentBehaviorTypeEnum getByCode(String code) {
        for (AliyunAirecContentBehaviorTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
