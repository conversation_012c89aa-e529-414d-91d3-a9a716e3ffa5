package com.sohu.third.yidun.service;

import com.netease.yidun.sdk.antispam.AntispamRequester;
import com.netease.yidun.sdk.antispam.file.v2.FileSolutionClient;
import com.netease.yidun.sdk.antispam.file.v2.submit.request.FileSubmitV2Request;
import com.netease.yidun.sdk.antispam.file.v2.submit.response.FileSubmitV2Response;
import com.sohu.third.yidun.object.TransformObjet;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/6/17 17:51
 */
@Component
public class FileCheckService extends AbstractService {

    public static FileSubmitV2Response.FileSubmitV2Resp asyncFile(String secretId, String secretKey, String businessId, TransformObjet object, String callbackUrl) {
        // 实例化一个requester，入参需要传入易盾内容安全分配的secretId，secretKey
        AntispamRequester antispamRequester = createAntispamRequester(secretId, secretKey);

        // 实例化发起请求的client对象
        FileSolutionClient fileSolutionClient = FileSolutionClient.getInstance(antispamRequester);

        // 实例化请求对象
        FileSubmitV2Request request = new FileSubmitV2Request();
        request.setUrl(object.getContent());
        request.setDataId(object.getDataId());
        request.setCallbackUrl(callbackUrl);
        request.setCheckFlag(15);

        // 请求对象中的其他参数如果有需要，请参考官方接口文档中字段说明，按需添加
        FileSubmitV2Response response = null;
        try {
            // 发起同步检测的请求
            response = fileSolutionClient.check(request);
        } catch (Exception e) {
            return null;
        }

        if (response != null && response.getCode() == 200) {
            if (response.getResult() != null) {
                return response.getResult();
            }
        }
        return null;
    }
}
