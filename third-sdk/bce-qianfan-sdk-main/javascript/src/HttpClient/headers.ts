// Copyright (c) 2024 Baidu, Inc. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

export const CONTENT_TYPE = 'Content-Type';
export const CONTENT_LENGTH = 'Content-Length';
export const CONTENT_MD5 = 'Content-MD5';
export const CONTENT_ENCODING = 'Content-Encoding';
export const CONTENT_DISPOSITION = 'Content-Disposition';
export const ETAG = 'ETag';
export const CONNECTION = 'Connection';
export const HOST = 'Host';
export const USER_AGENT = 'User-Agent';
export const CACHE_CONTROL = 'Cache-Control';
export const EXPIRES = 'Expires';
export const ORIGIN = 'Origin';
export const ACCESS_CONTROL_REQUEST_METHOD = 'Access-Control-Request-Method';
export const ACCESS_CONTROL_REQUEST_HEADERS = 'Access-Control-Request-Headers';

/** BOS 相关headers */
export const AUTHORIZATION = 'Authorization';
export const X_BCE_DATE = 'x-bce-date';
export const X_BCE_ACL = 'x-bce-acl';
export const X_BCE_GRANT_READ = 'x-bce-grant-read';
export const X_BCE_GRANT_FULL_CONTROL = 'x-bce-grant-full-control';
export const X_BCE_REQUEST_ID = 'x-bce-request-id';
export const X_BCE_CONTENT_SHA256 = 'x-bce-content-sha256';
export const X_BCE_OBJECT_ACL = 'x-bce-object-acl';
export const X_BCE_OBJECT_GRANT_READ = 'x-bce-object-grant-read';
export const X_BCE_STORAGE_CLASS = 'x-bce-storage-class';
export const X_BCE_SERVER_SIDE_ENCRYPTION = 'x-bce-server-side-encryption';
export const X_BCE_RESTORE_DAYS = 'x-bce-restore-days';
export const X_BCE_RESTORE_TIER = 'x-bce-restore-tier';
export const X_BCE_SYMLINK_TARGET = 'x-bce-symlink-target';
export const X_BCE_FORBID_OVERWRITE = 'x-bce-forbid-overwrite';
export const X_BCE_TRAFFIC_LIMIT = 'x-bce-traffic-limit';
export const X_BCE_FETCH_SOURCE = 'x-bce-fetch-source';
export const X_BCE_FETCH_MODE = 'x-bce-fetch-mode';
export const X_BCE_CALLBACK_ADDRESS = 'x-bce-callback-address';
export const X_BCE_FETCH_REFERER = 'x-bce-fetch-referer';
export const X_BCE_FETCH_USER_AGENT = 'x-bce-fetch-user-agent';
export const X_BCE_PROCESS = 'x-bce-process';

export const X_HTTP_HEADERS = 'http_headers';
export const X_BODY = 'body';
export const X_STATUS_CODE = 'status_code';
export const X_MESSAGE = 'message';
export const X_CODE = 'code';
export const X_REQUEST_ID = 'request_id';

export const SESSION_TOKEN = 'x-bce-security-token';

export const X_VOD_MEDIA_TITLE = 'x-vod-media-title';
export const X_VOD_MEDIA_DESCRIPTION = 'x-vod-media-description';
export const ACCEPT_ENCODING = 'accept-encoding';
export const ACCEPT = 'accept';
