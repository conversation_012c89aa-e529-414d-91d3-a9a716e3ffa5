// Copyright (c) 2024 Baidu, Inc. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import RateLimiter from '../index';

describe('RateLimiter', () => {

    it('should enforce rate limiting', async () => {
        const rateLimiter = new RateLimiter(1); // 1 QPS
        const start = Date.now();

        await rateLimiter.schedule(() => Promise.resolve('first'));
        await rateLimiter.schedule(() => Promise.resolve('second'));

        const end = Date.now();
        expect(end - start).toBeGreaterThanOrEqual(1000);
    });

    it('should update limits correctly', async () => {
        const rateLimiter = new RateLimiter(1);
        rateLimiter.updateLimits(2);

        const start = Date.now();

        await rateLimiter.schedule(() => Promise.resolve('first'));
        await rateLimiter.schedule(() => Promise.resolve('second'));

        const end = Date.now();
        expect(end - start).toBeLessThan(1500);
    });
});
