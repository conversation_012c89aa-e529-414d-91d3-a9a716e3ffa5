{"name": "@baiducloud/qianfan", "version": "0.0.8-alpha.0", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "repository": {"type": "git", "url": "https://github.com/baidubce/bce-qianfan-sdk"}, "description": "", "main": "dist/bundle.js", "type": "commonjs", "scripts": {"build": "npx rollup -c rollup.config.mjs", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "publish:main": "npm version patch && npm run build && npm publish --access public", "publish:beta": "npm version prerelease --preid=beta && npm run build && npm publish --access public"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/node-fetch": "^2.6.11", "async-mutex": "^0.5.0", "bottleneck": "^2.19.5", "debug": "^3.1.0", "dotenv": "^16.4.1", "node-fetch": "2.7.0", "rollup": "^3.29.4", "tslib": "^2.6.2", "typescript": "^5.3.3", "underscore": "^1.9.1", "urlencode": "^1.1.0"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/eslint-parser": "^7.23.10", "@babel/eslint-plugin": "^7.23.5", "@babel/preset-env": "^7.24.0", "@babel/preset-typescript": "^7.23.3", "@ecomfe/eslint-config": "^8.0.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-eslint": "^9.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.6", "@types/jest": "^29.5.11", "@types/node": "^20.11.13", "@types/underscore": "^1.11.15", "@typescript-eslint/parser": "^7.1.0", "babel-jest": "^29.7.0", "eslint": "^8.57.0", "jest": "^29.7.0", "prettier": "^3.2.5", "ts-node": "^10.9.2"}}