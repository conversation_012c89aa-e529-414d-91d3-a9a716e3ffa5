#%% md
# 如何使用千帆 Python SDK 搭配预置大模型服务进行批量推理

在进行模型评估或其他任务时，通常需要对大量数据进行预测。然而，模型推理过程往往耗时较长，通过循环串行执行会增加整体时间成本，而并行执行则需要额外的开发工作。

SDK 提供了多种解决方案来应对这一场景，其中包括：

- [本地并行推理](https://github.com/baidubce/bce-qianfan-sdk/blob/main/cookbook/batch_prediction.ipynb)：利用 SDK 内置的批量推理功能，在本地通过并行调用模型接口实现高效的批量预测。
- [数据集评估](https://github.com/baidubce/bce-qianfan-sdk/blob/main/cookbook/dataset/batch_inference_using_dataset.ipynb)：利用 SDK 的 Dataset 模块，调用平台提供的数据集评估功能，以便快速而有效地完成任务。
- [离线批量推理](https://github.com/baidubce/bce-qianfan-sdk/blob/main/cookbook/offline_batch_inference.ipynb)：对于时间要求不那么严格的场景，可以考虑利用平台提供的离线批量预测能力，以降低实时推理的负载压力。

本文将介绍第二种解决方案，即使用 Dataset 进行评估，在 0.2.8 版本中，千帆 Python SDK 增加了对该功能的支持，使用该功能需要视情况开通千帆的模型服务，确保您的账号可以调用您想进行批量推理的服务。

# 准备工作

在开始之前，请确保你的千帆 Python SDK 已经升级到了 0.2.8 及以上版本。
nest_asyncio 是一个异步库，用于支持 Python SDK 的异步推理功能。
#%%
#-# cell_skip
! pip install -U "qianfan>=0.2.8"
! pip install nest_asyncio
#%% md
并且在环境变量中设置好 Access Key 与 Secret Key
#%%
import logging
import os

from qianfan.utils import enable_log

# os.environ['QIANFAN_ACCESS_KEY'] = 'your_access_key'
# os.environ['QIANFAN_SECRET_KEY'] = 'your_secret_key'

os.environ["QIANFAN_QPS_LIMIT"] = "1"
os.environ['QIANFAN_LLM_API_RETRY_COUNT'] = "3"

# 选择打印出来的日志等级，目前打印出 info 级别
enable_log(logging.INFO)
#%% md
# 正文

为了开始批量推理，我们首先需要获取到用于做批量推理输入的数据集文件，并且指定用做推理输入的列名
#%%
from qianfan.dataset import Dataset

dataset_file_path = "data_file/qa_pair.csv"
dataset_input_column_list = ["prompt"]

# 预期输出列列名，当数据集为对话类数据集时必填，为非对话数据集时选填。
# 对应列的数据会在推理结果中出现
reference_column = "response"

ds = Dataset.load(data_file=dataset_file_path, input_columns=dataset_input_column_list, reference_column=reference_column)

# 预览数据格式
print(ds.list(0))
#%% md
在导入之后，用户可以根据自己的需求，传入不同的参数来使用不同的方式进行推理
#%%
#-# cell_skip
# 用户可以设置 service_model 为自己想要的模型名，来直接对数据进行批量推理，以 EB 4 为例
result = ds.test_using_llm(service_model="ERNIE-Bot-4")

# 用户还可以设置 service_endpoint 来使用预置或自己的服务。
result = ds.test_using_llm(service_endpoint="completions_pro")
#%% md
如果用户有异步请求的需求，还可以使用 `atest_using_llm` 来进行异步批量推理
#%%
import asyncio
import nest_asyncio

nest_asyncio.apply()

result = asyncio.run(ds.atest_using_llm(service_endpoint="completions_pro"))
#%% md
拿到的 `result` 对象也是一个 `Dataset` 对象，可以继续使用千帆 Python SDK 进行后续处理，或者直接保存到本地。
#%%
print(result.list(0))

dataset_save_file_path = "output_file.csv"

result.save(data_file=dataset_save_file_path)
#%% md
## 对模型进行批量推理

除了对 `Service` 进行批量推理，我们也可以对 `Model` 进行批量推理

在对 `Model` 进行批量推理时，请先确认用到的数据集已经在千帆平台上发布
#%%
#-# cell_skip
cloud_dataset_id = "dataset_id"

qianfan_ds = Dataset.load(qianfan_dataset_id=cloud_dataset_id)

result = qianfan_ds.test_using_llm(model_version_id="amv-qb8ijukaish3")
print(result[0])
#%% md
# 进阶能力

在调用 `test_using_llm` 时，用户还可以传入一些额外参数来支持额外的功能，比如设置 Prompt 模板，设置人设字段，或者传入大模型调用时的超参数

当对服务进行非对话类推理时，用户可以传入 `prompt_template` 参数来传递一个 Prompt 模板。`prompt_template` 是一个千帆 Python SDK 的 `Prompt` 对象，用户可以通过设置 `Prompt` 对象的 `template` 成员来自定义被用于推理的模板，模板渲染出来的内容将会被作为最终输入提交给大模型。以示例数据集为例，我们可以这么指定一个模板：
#%%
from qianfan.common import Prompt

prompt = Prompt(template="请你就以下问题进行回答: {prompt}")

# 传递给函数
result = ds.test_using_llm(service_model="ERNIE-Bot-4", prompt_template=prompt)
#%% md
除此之外，用户还可以传入 `system_prompt` 参数来指定对话中大模型需要遵守的人设
#%%
result = ds.test_using_llm(service_model="ERNIE-Bot-4", system_prompt="人设 prompt")
#%% md
用户在进行批量推理时，还可以直接在 test_using_llm 中传入模型支持的超参数，例如我们可以这么设置模型的 `temperature` :
#%%
result = ds.test_using_llm(service_model="ERNIE-Bot-4", system_prompt="人设 prompt", temperature=0.1)