#%% md
# Semantic Kernel Planner

在[chatbot][./chatbot_with_sk.ipynb]中我们介绍
在Semantic-Kernel（以下简称SK）中，有若干可以用于实现Plan的Planner类型：
- SequentialPlanner：整体规划多步plan，在串联进行执行
- ActionPlanner：创建单个Action的Planner。
- StepwisePlanner：根据大模型的响应，逐步的进行计划和执行，类似于ReAct
#%% md
接下来我们来了解如何结合qianfan + planner实现一个简单的demo
#%% md
## 前置准备
### 安装依赖：
本文基于semantic-kernel `0.4.5dev0` 版本，由于 SK持续迭代的原因，原来的Skill正在迁移成Plugin，如碰到不兼容问题请检查依赖版本。
使用以下命令可以安装我们所需要的`qianfan` 以及 `semantic-kernel`：
#%%
#-# cell_skip
! pip install "qianfan>=0.3.0" -U
! pip install semantic-kernel=='0.4.5.dev0'
#%% md
与直接调用千帆SDK类似，我们需要先初始化鉴权(以下以使用IAM鉴权为例)：
#%%

import os

os.environ["QIANFAN_AK"] = "your_ak"
os.environ["QIANFAN_SK"] = "your_sk"
#%% md
初始化一个SK `kernel`， kernel是 SK中的一个重要类型，通过Kernel，我们可以把众多Plugin，LLM，以及Memory等进行注册组合，最终实现一键式的规划调用。
#%%
import semantic_kernel as sk
kernel = sk.Kernel()
#%% md
以下是使用`ActionPlanner`实现的计算的一个例子：
#%%
# Copyright (c) Microsoft. All rights reserved.
import semantic_kernel as sk
from qianfan.extensions.semantic_kernel import QianfanChatCompletion
from semantic_kernel.core_skills import FileIOSkill, MathSkill, TextSkill, TimeSkill
from semantic_kernel.planning import ActionPlanner


kernel = sk.Kernel()

kernel.add_chat_service("erniebot", QianfanChatCompletion("ERNIE-Bot-4"))
kernel.import_skill(MathSkill(), "math")
kernel.import_skill(FileIOSkill(), "fileIO")
kernel.import_skill(TimeSkill(), "time")
kernel.import_skill(TextSkill(), "text")

# create an instance of action planner.
planner = ActionPlanner(kernel)

# the ask for which the action planner is going to find a relevant function.
ask = "What is the sum of 110 and 990?"

# ask the action planner to identify a suitable function from the list of functions available.
plan = await planner.create_plan_async(goal=ask)

# ask the action planner to execute the identified function.
result = await plan.invoke_async()
print(result)
"""
Output:
1100
"""