#%% md
# function call

## 简介

function_call，顾名思义，通过给大模型提供 function 的说明描述，以及对应的入参出参 schema，让大模型输出 function 调用策略，结合多轮对话，以最终实现一个复杂的任务。
以下将以天气获取为例子，通过千帆 Python SDK提供的 ERNIE-Bot 大模型以实现通过大模型得到对应城市的天气情况。

本教程可使用的最低 langchain 版本为 0.1.10

## 准备

本文使用了 Langchain 中的的 agent 以及 tool 。首先安装千帆 Python SDK 和 Langchain：
#%%
#-# cell_skip
!pip install qianfan
!pip install langchain
#%% md
如果你已经安装了千帆 Python SDK 和 Langchain，我们建议进行一次升级
#%%
#-# cell_skip
!pip install -U qianfan
!pip install -U langchain
#%% md
并且初始化我们所需要的凭证
#%%
# 初始化LLM
import os

os.environ['QIANFAN_AK'] = ''
os.environ['QIANFAN_SK'] = ''
#%% md
### 定义工具

这一步需要定义使用的工具，以及它的参数和描述，以上信息会影响到大模型对工具的使用
#%%
from enum import Enum

from langchain.agents import tool
from langchain.pydantic_v1 import BaseModel, Field


class TemperatureUnitEnum(str, Enum):
    celsius: str = "摄氏度"
    fahrenheit: str = "华氏度"


class WeatherToolSchema(BaseModel):
    """获得指定地点的天气"""

    location: str = Field(description="省，市名，例如：河北省，石家庄")
    unit: TemperatureUnitEnum = Field(description="温度单位")

@tool(args_schema=WeatherToolSchema)
def get_current_weather(location: str, unit: TemperatureUnitEnum) -> str:
    """获得指定地点的天气"""
    return "25"

tools = [get_current_weather]
#%% md
### 在 Agent 中使用工具
#%%
from langchain.agents import AgentExecutor

from langchain.chat_models import QianfanChatEndpoint
from qianfan.extensions.langchain.agents import QianfanSingleActionAgent


llm = QianfanChatEndpoint(endpoint="ernie-3.5-8k-0205")
agent = QianfanSingleActionAgent.from_system_prompt(tools, llm)
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

agent_executor.invoke("今天上海的气温是多少摄氏度")