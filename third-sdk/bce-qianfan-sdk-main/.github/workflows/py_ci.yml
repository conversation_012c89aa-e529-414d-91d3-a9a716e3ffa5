on:
  push:
    branches: ['main']
  pull_request:
    paths:
      - "python/**"
      - ".github/workflows/py_ci.yml"
  workflow_dispatch:

name: Python CI

defaults:
  run:
    shell: bash
    working-directory: ./python

env:
  RUFF_OUTPUT_FORMAT: github

jobs:
  build:
    name: Unit tests
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os:
          - 'ubuntu-latest'
          - 'macos-latest'
          - 'windows-latest'
        python-version:
          - '3.7'
          - '3.8'
          - '3.9'
          - '3.10'
          - '3.11'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{matrix.python-version}}
      - name: Install Poetry
        run: |
          pip install poetry
          echo "$HOME/.poetry/bin" >> $GITHUB_PATH
          poetry lock
      - name: Get Poetry version
        run: poetry --version
      - name: Setup Python Cache
        uses: actions/setup-python@v5
        with:
          python-version: ${{matrix.python-version}}
          cache: "poetry"
      - name: Install deps
        run: |
          make install
      - name: run lint
        run: make lint
      - name: Disable ntp on MacOS
        if: runner.os == 'macOS'
        run: |
          sudo systemsetup -setusingnetworktime off
          sudo rm -rf /etc/ntp.conf
      - name: Run tests
        run: make test
      - name: Build artifacts
        run: make build
      - uses: actions/upload-artifact@v4
        if: ${{ matrix.python-version == '3.11' && matrix.os == 'ubuntu-latest' }}
        with:
          path: ${{ github.workspace }}/python/output/**
