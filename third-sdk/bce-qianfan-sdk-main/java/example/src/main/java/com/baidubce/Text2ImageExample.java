package com.baidubce;

import com.baidubce.qianfan.Qianfan;
import com.baidubce.qianfan.core.auth.Auth;
import com.baidubce.qianfan.model.image.Text2ImageResponse;

import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 本示例实现了Text2Image调用流程，并将生成的图片保存到本地
 */
public class Text2ImageExample {
    public static void main(String[] args) throws IOException {
        Qianfan qianfan = new Qianfan(Auth.TYPE_OAUTH,"5W9dFLCns7XBPFMlaYn65ebp","1pqLwJPyl7aDczuD2yCmS1uydBqbzvr5");
        Text2ImageResponse response = qianfan.text2Image()
                .prompt("百达翡丽手表")
                .execute();
        byte[] rawImage = response.getData().get(0).getImage();
        try (FileOutputStream fos = new FileOutputStream("Rolex_watch.png")) {
            fos.write(rawImage);
        }
    }
}
