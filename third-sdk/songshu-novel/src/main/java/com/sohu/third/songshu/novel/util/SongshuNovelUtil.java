package com.sohu.third.songshu.novel.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import com.sohu.third.songshu.novel.constants.SongshuNovelConstant;

import java.util.Date;

/**
 * @Author: leibo
 * @Date: 2024/12/16 10:35
 **/
public class SongshuNovelUtil {

    /**
     * 处理秘钥
     *
     * @return
     */
    public static String handleSign() {
        String dateTime = DateUtil.format(new Date(), "yyyyMMdd");
        String signStr = dateTime + SongshuNovelConstant.MCH_ID + SongshuNovelConstant.KEY;
        String sign = SecureUtil.md5(signStr).toUpperCase();
        return sign;
    }
}
