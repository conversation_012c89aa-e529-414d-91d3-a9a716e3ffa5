package com.sohu.third.songshu.novel.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.sohu.third.songshu.novel.constants.SongshuNovelConstant;
import com.sohu.third.songshu.novel.enums.SongshuNovelApiUrlEnum;
import com.sohu.third.songshu.novel.request.SongshuNovelBookInfoRequest;
import com.sohu.third.songshu.novel.request.SongshuNovelChapterInfoRequest;
import com.sohu.third.songshu.novel.request.SongshuNovelChapterListRequest;
import com.sohu.third.songshu.novel.response.*;
import com.sohu.third.songshu.novel.util.SongshuNovelUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * 小说查询实现
 *
 * @Author: leibo
 * @Date: 2024/11/18 18:06
 **/
@Slf4j
public class SongshuNovelService {

    /**
     * 颂书小说书籍列表查询
     *
     * @return
     */
    public static List<SongshuNovelBookListResponse> songShuBookList() {
        String url = SongshuNovelConstant.URL_PREFIX
                + SongshuNovelApiUrlEnum.BOOK_LIST.getUrl()
                + "?mch_id=" + SongshuNovelConstant.MCH_ID
                + "&sign=" + SongshuNovelUtil.handleSign();
        String body = HttpRequest.get(url).execute().body();
        log.info("songshu NovelBookList response:{}", body);
        JSONObject json = JSONObject.parseObject(body);
        if (!json.get("code").equals(0)) {
            log.error(json.get("msg").toString());
            return Collections.emptyList();
        }
        if (StrUtil.isEmpty(json.get("data").toString())) {
            log.error("songshu NovelBookList返回结果为空");
            return Collections.emptyList();
        }
        return JSONUtil.toList(json.get("data").toString(), SongshuNovelBookListResponse.class);
    }

    /**
     * 小说单本图书详情查询
     *
     * @param request
     * @return
     */
    public static SongshuNovelBookInfoResponse bookInfo(SongshuNovelBookInfoRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getBookId())) {
            log.error("songshu bookInfo request bookId is null");
            return null;
        }
        String url = SongshuNovelConstant.URL_PREFIX
                + SongshuNovelApiUrlEnum.BOOK_INFO.getUrl()
                + "?mch_id=" + SongshuNovelConstant.MCH_ID
                + "&book_id=" + request.getBookId()
                + "&sign=" + SongshuNovelUtil.handleSign();
        String body = HttpRequest.get(url).execute().body();
        log.info("songshu NovelBookInfo response:{}", body);
        JSONObject json = JSONObject.parseObject(body);
        if (!json.get("code").equals(0)) {
            log.error(json.get("msg").toString());
            return null;
        }
        if (StrUtil.isEmpty(json.get("data").toString())) {
            log.error("songshu NovelBookInfo返回结果为空");
            return null;
        }
        return JSONUtil.toBean(json.get("data").toString(), SongshuNovelBookInfoResponse.class);
    }

    /**
     * 小说章节目录列表查询
     *
     * @param request
     * @return
     */
    public static List<SongshuNovelChapterListResponse> chapterList(SongshuNovelChapterListRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getBookId())) {
            log.error("songshu chapterList request bookId is null");
            return Collections.emptyList();
        }
        String url = SongshuNovelConstant.URL_PREFIX
                + SongshuNovelApiUrlEnum.CHAPTER_LIST.getUrl()
                + "?mch_id=" + SongshuNovelConstant.MCH_ID
                + "&book_id=" + request.getBookId()
                + "&sign=" + SongshuNovelUtil.handleSign();
        String body = HttpRequest.get(url).execute().body();
        log.info("songshu NovelChapterList response:{}", body);
        JSONObject json = JSONObject.parseObject(body);
        if (!json.get("code").equals(0)) {
            log.error(json.get("msg").toString());
            return Collections.emptyList();
        }
        if (StrUtil.isEmpty(json.get("data").toString())) {
            log.error("songshu NovelChapterList返回结果为空");
            return Collections.emptyList();
        }
        return JSONUtil.toList(json.get("data").toString(), SongshuNovelChapterListResponse.class);
    }

    /**
     * 小说章节详情查询
     *
     * @param request
     * @return
     */
    public static SongshuNovelChapterInfoResponse chapterInfo(SongshuNovelChapterInfoRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getBookId()) || Objects.isNull(request.getChapterId())) {
            log.error("songshu chapterList request bookId is null or chapterId is null");
            return null;
        }
        String url = SongshuNovelConstant.URL_PREFIX
                + SongshuNovelApiUrlEnum.CHAPTER_INFO.getUrl()
                + "?mch_id=" + SongshuNovelConstant.MCH_ID
                + "&book_id=" + request.getBookId()
                + "&chapter_id=" + request.getChapterId()
                + "&sign=" + SongshuNovelUtil.handleSign();
        String body = HttpRequest.get(url).execute().body();
        log.info("songshu chapterInfo response:{}", body);
        JSONObject json = JSONObject.parseObject(body);
        if (!json.get("code").equals(0)) {
            log.error(json.get("msg").toString());
            return null;
        }
        if (StrUtil.isEmpty(json.get("data").toString())) {
            log.error("songshu chapterInfo返回结果为空");
            return null;
        }
        return JSONUtil.toBean(json.get("data").toString(), SongshuNovelChapterInfoResponse.class);
    }

    /**
     * 小说分类列表查询
     *
     * @return
     */
    public static List<SongshuNovelCategoryListResponse> categoryList() {
        String url = SongshuNovelConstant.URL_PREFIX
                + SongshuNovelApiUrlEnum.CATEGORY.getUrl()
                + "?mch_id=" + SongshuNovelConstant.MCH_ID
                + "&sign=" + SongshuNovelUtil.handleSign();
        String body = HttpRequest.get(url).execute().body();
        log.info("songshu categoryList response:{}", body);
        JSONObject json = JSONObject.parseObject(body);
        if (!json.get("code").equals(0)) {
            log.error(json.get("msg").toString());
            return null;
        }
        if (StrUtil.isEmpty(json.get("data").toString())) {
            log.error("songshu categoryList");
            return null;
        }
        return JSONUtil.toList(json.get("data").toString(), SongshuNovelCategoryListResponse.class);
    }

}
