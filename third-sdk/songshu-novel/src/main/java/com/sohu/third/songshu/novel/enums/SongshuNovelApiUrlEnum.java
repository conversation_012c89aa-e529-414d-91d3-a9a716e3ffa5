package com.sohu.third.songshu.novel.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

/**
 * 颂书小说相关接口地址
 *
 * @Author: leibo
 * @Date: 2024/12/16 09:46
 **/
@Getter
@AllArgsConstructor
public enum SongshuNovelApiUrlEnum implements Serializable {

    BOOK_LIST("/api/book-list","获取书籍列表", "GET"),
    BOOK_INFO("/api/book-info","获取书籍信息", "GET"),
    CHAPTER_LIST("/api/chapter-list","获取章节列表", "GET"),
    CHAPTER_INFO("/api/chapter-info","获取章节内容", "GET"),
    CATEGORY("/api/category-list","获取分类列表","GET");

    private String url;
    private String desc;
    private String method;
}
