package com.sohu.third.wechat.auth.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sohu.third.wechat.auth.response.WechatJscodeSessionResponse;
import lombok.Data;

/**
 * WechatJscodeRequest
 *
 * <AUTHOR>
 * @version 1.1
 * @date 2020-11-16 19:57
 */
@Data
public class WechatJscodeRequest extends WechatBaseRequest<WechatJscodeSessionResponse> {

    private static final long serialVersionUID = -7344440075200336705L;
    /**
     * 应用ID
     */
    @JsonProperty(value = "appid")
    protected String appId;

    /**
     * 秘钥
     */
    protected String secret;

    /**
     * code码
     */
    @JsonProperty(value = "js_code")
    protected String jsCode;

    /**
     * 授权类型
     */
    @JsonProperty(value = "grant_type")
    protected String grantType;
}
