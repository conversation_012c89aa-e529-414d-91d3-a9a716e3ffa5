package com.sohu.third.wechat.auth.util;

import cn.hutool.http.HttpUtil;
import com.sohu.third.core.util.JsonUtils;
import com.sohu.third.wechat.auth.bean.WechatMiniAppConfig;
import com.sohu.third.wechat.auth.constant.WechatAuthConstant;
import com.sohu.third.wechat.auth.constant.WechatError;
import com.sohu.third.wechat.auth.exception.WechatMessageErrorException;
import com.sohu.third.wechat.auth.exception.WechatMessageException;
import com.sohu.third.wechat.auth.request.WechatBaseRequest;
import com.sohu.third.wechat.auth.response.WechatBaseResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;

/**
 * 微信基础请求类
 *
 * <AUTHOR>
 * @version 1.1
 * @date 2020-11-16 19:54
 */
@Slf4j
public class BaseWechatRequest {

    /**
     * 阻塞时长
     */
    private static final int RETRY_SLEEP_MILLIS = 1000;
    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_TIMES = 5;

    /**
     * get请求
     *
     * @param url
     * @param request
     * @param config
     * @param <T>
     * @return
     * @throws WechatMessageErrorException
     */
    public static <T extends WechatBaseResponse> T get(String url, WechatBaseRequest<T> request,
                                                       WechatMiniAppConfig config) throws WechatMessageErrorException {
        return execute("GET", url, request, config);
    }

    /**
     * post请求
     *
     * @param url
     * @param request
     * @param config
     * @param <T>
     * @return
     * @throws WechatMessageErrorException
     */
    public static <T extends WechatBaseResponse> T post(String url, WechatBaseRequest<T> request,
                                                        WechatMiniAppConfig config) throws WechatMessageErrorException {
        return execute("POST", url, request, config);
    }

    /**
     * 执行http请求
     *
     * @param httpMethod
     * @param url
     * @param request
     * @param config
     * @return T
     * @throws WechatMessageErrorException
     */
    public static <T extends WechatBaseResponse> T execute(String httpMethod, String url, WechatBaseRequest<T> request,
                                                           WechatMiniAppConfig config) throws WechatMessageErrorException {
        int retryTimes = 0;
        do {
            try {
                return BaseWechatRequest.executeInternal(httpMethod, url, request, config);
            } catch (WechatMessageErrorException e) {
                if (retryTimes + 1 > MAX_RETRY_TIMES) {
                    log.warn("重试达到最大次数【{}】", MAX_RETRY_TIMES);
                    // 最后一次重试失败后，直接抛出异常，不再等待
                    throw new WechatMessageException("微信服务端异常，超出重试次数");
                }

                WechatError error = e.getError();
                // -1 系统繁忙, 1000ms后重试
                if (error.getErrorCode() == -1) {
                    int sleepMillis = RETRY_SLEEP_MILLIS * (1 << retryTimes);
                    try {
                        log.warn("微信系统繁忙，{} ms 后重试(第{}次)", sleepMillis, retryTimes + 1);
                        Thread.sleep(sleepMillis);
                    } catch (InterruptedException interruptedException) {
                        throw new WechatMessageException(interruptedException);
                    }
                } else {
                    throw e;
                }
            }
        } while (retryTimes++ < MAX_RETRY_TIMES);
        log.warn("重试达到最大次数【{}】", MAX_RETRY_TIMES);
        throw new WechatMessageException("微信服务端异常，超出重试次数");
    }

    /**
     * 处理请求
     *
     * @param httpMethod
     * @param url
     * @param request
     * @param config
     * @return
     * @throws WechatMessageErrorException
     */
    private static <T extends WechatBaseResponse> T executeInternal(String httpMethod, String url,
                                                                    WechatBaseRequest<T> request,
                                                                    WechatMiniAppConfig config) throws WechatMessageErrorException {
        String uriWithAccessToken;
        if (Objects.nonNull(config)) {
            if (url.contains("access_token=")) {
                throw new IllegalArgumentException("uri参数中不允许有access_token: " + url);
            }
            uriWithAccessToken = url + (url.contains("?") ? "&" : "?") + "access_token=" + config.getAccessToken();
        } else {
            uriWithAccessToken = url;
        }
        String result;
        if ("GET".equals(httpMethod)) {
            Map<String, Object> param = JsonUtils.objectToMap(request);
            result = HttpUtil.get(uriWithAccessToken, param);
        } else {
            result = HttpUtil.post(uriWithAccessToken, JsonUtils.toJsonString(request));
        }
        return JsonUtils.parseObject(handleResponse(result), request.findResponseClass());
    }

    /**
     * 处理返回值
     *
     * @param responseContent
     * @return
     * @throws WechatMessageErrorException
     */
    private static String handleResponse(String responseContent) throws WechatMessageErrorException {
        log.debug("微信接口返回 {}", responseContent);
        if (responseContent.isEmpty()) {
            throw new WechatMessageErrorException("无响应内容");
        }
        if (responseContent.startsWith("<xml>")) {
            // xml格式输出直接返回
            return responseContent;
        }
        WechatError error = WechatError.fromJson(responseContent, WechatAuthConstant.WxType.MiniApp);
        if (error.getErrorCode() != 0) {
            throw new WechatMessageErrorException(error);
        }
        return responseContent;
    }

}
