package com.sohu.third.wechat.profitsharing.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sohu.third.wechat.profitsharing.exception.WechatProfitSharingException;
import com.sohu.third.wechat.profitsharing.bean.WechatProfitSharingConfig;
import com.sohu.third.wechat.profitsharing.response.BaseWechatProfitSharingResponse;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.lang.reflect.ParameterizedType;

/**
 * 微信支付请求对象共用的参数存放类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 13:43
 */
@Data
public abstract class BaseWechatProfitSharingRequest<T extends BaseWechatProfitSharingResponse> implements Serializable {
    private static final long serialVersionUID = -4766915659779847060L;

    /**
     * <pre>
     * 字段名：公众账号ID.
     * 变量名：appid
     * 是否必填：是
     * 类型：String(32)
     * 示例值：wxd678efh567hg6787
     * 描述：微信分配的公众账号ID（企业号corpid即为此appId）
     * </pre>
     */
    @JsonProperty(value = "appid")
    protected String appId;
    /**
     * <pre>
     * 字段名：商户号.
     * 变量名：mch_id
     * 是否必填：是
     * 类型：String(32)
     * 示例值：1230000109
     * 描述：微信支付分配的商户号
     * </pre>
     */
    @JsonProperty(value = "mch_id")
    protected String mchId;
    /**
     * <pre>
     * 字段名：服务商模式下的子商户公众账号ID.
     * 变量名：sub_appid
     * 是否必填：是
     * 类型：String(32)
     * 示例值：wxd678efh567hg6787
     * 描述：微信分配的子商户公众账号ID
     * </pre>
     */
    @JsonProperty(value = "sub_appid")
    protected String subAppId;
    /**
     * <pre>
     * 字段名：服务商模式下的子商户号.
     * 变量名：sub_mch_id
     * 是否必填：是
     * 类型：String(32)
     * 示例值：1230000109
     * 描述：微信支付分配的子商户号，开发者模式下必填
     * </pre>
     */
    @JsonProperty(value = "sub_mchid")
    protected String subMchId;

    /**
     * 是否忽略 appid.
     *
     * @return the boolean
     */
    public boolean ignoreAppId() {
        return Boolean.FALSE;
    }

    /**
     * 是否忽略 mchId.
     *
     * @return the boolean
     */
    public boolean ignoreMchId() {
        return Boolean.FALSE;
    }

    /**
     * 是否忽略 subAppid.
     *
     * @return the boolean
     */
    public boolean ignoreSubAppId() {
        return Boolean.TRUE;
    }

    /**
     * 是否忽略 subMchId.
     *
     * @return the boolean
     */
    public boolean ignoreSubMchId() {
        return Boolean.TRUE;
    }

    /**
     * 返回response的class
     *
     * @return
     */
    public Class<T> findResponseClass() {
        try {
            return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
        } catch (Exception e) {
            return (Class<T>) BaseWechatProfitSharingResponse.class;
        }
    }

    /**
     * <pre>
     * 检查参数，并设置签名.
     * 1、检查参数（注意：子类实现需要检查参数的而外功能时，请在调用父类的方法前进行相应判断）
     * 2、补充系统参数，如果未传入则从配置里读取
     * 3、生成签名，并设置进去
     * </pre>
     *
     * @param config 支付配置对象，用于读取相应系统配置信息
     * @throws WechatProfitSharingException the wx pay exception
     */
    public void checkAndSign(WechatProfitSharingConfig config) throws WechatProfitSharingException {
//        this.checkFields();

        if (!ignoreAppId() && StringUtils.isBlank(getAppId())) {
            this.setAppId(config.getAppId());
        }

        if (!ignoreMchId() && StringUtils.isBlank(getMchId())) {
            this.setMchId(config.getMchId());
        }

        if (!ignoreSubAppId() && StringUtils.isBlank(getSubAppId())) {
            this.setSubAppId(config.getSubAppId());
        }

        if (!ignoreSubMchId() && StringUtils.isBlank(getSubMchId())) {
            this.setSubMchId(config.getSubMchId());
        }
    }
}
