package com.sohu.third.alipay.pay.request.trade;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 业务扩展参数
 *
 * <AUTHOR>
 * @version :1.0.0
 * @since 2021-11-22
 */
@Data
public class AlipayTradeExtendParams implements Serializable {

    private static final long serialVersionUID = -4686106078902421370L;
    /**
     * 系统商编号,该参数作为系统商返佣数据提取的依据，请填写系统商签约协议的PID
     */
    @JsonProperty(value = "sys_service_provider_id")
    private String sysServiceProviderId;

    /**
     * 行业数据回流信息
     */
    @JsonProperty(value = "industry_reflux_info")
    private String industryRefluxInfo;

    /**
     * 卡类型
     */
    @JsonProperty(value = "card_type")
    private String cardType;

    /**
     * 特殊场景下，允许商户指定交易展示的卖家名称
     */
    @JsonProperty(value = "specified_seller_name")
    private String specifiedSellerName;

}
