package com.sohu.third.alipay.pay.service;

import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.sohu.third.alipay.pay.bean.AlipayBaseConfig;
import com.sohu.third.alipay.pay.request.AlipayOrderSyncRequest;
import com.sohu.third.alipay.pay.util.BaseAlipayRequest;
import com.sohu.third.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 支付宝订单同步
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/12 16:37
 */
@Slf4j
public class AlipayOrderSyncService {

    /**
     * 订单同步接口
     *
     * @param syncRequest
     * @param config
     * @return
     */
    public static AlipayMerchantOrderSyncResponse syncOrder(AlipayOrderSyncRequest syncRequest, AlipayBaseConfig config) {
        AlipayMerchantOrderSyncRequest request = new AlipayMerchantOrderSyncRequest();
        request.setBizContent(JsonUtils.toJsonString(syncRequest));
        AlipayMerchantOrderSyncResponse response = null;
        try {
            response = (AlipayMerchantOrderSyncResponse) BaseAlipayRequest.execute(request, config);
        } catch (Exception e) {
            log.warn("订单同步异常 {}", request);
        }
        return response;
    }
}
