package com.sohu.third.paypal.request;


import cn.hutool.json.JSONUtil;
import com.paypal.http.HttpResponse;
import com.paypal.payments.Capture;
import com.paypal.payments.CapturesGetRequest;
import com.paypal.payments.LinkDescription;
import com.sohu.third.paypal.config.PayPalClient;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * 查询扣款详情
 */
@Slf4j
public class CapturesGet extends PayPalClient {

    public void capturesGetRequest() throws IOException {
        CapturesGetRequest request = new CapturesGetRequest("扣款id， CaptureOrder生成");

        HttpResponse<Capture> response = client().execute(request);
        for (LinkDescription link : response.result().links()) {
            System.out.println("\t" + link.rel() + ": " + link.href() + "\tCall Type: " + link.method());
        }
        log.info("CapturesGet response body:{}", JSONUtil.toJsonStr(response));
    }

    public static void main(String[] args) {
        try {
            new CapturesGet().capturesGetRequest();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
 