package com.sohu.third.apple.pay.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 狐少少
 *
 * <AUTHOR>
 * @since 2024/5/29
 */
@Data
@ConfigurationProperties(prefix = "apple.pay")
public class ApplePayProperties {

    /**
     * appAppleId – The unique identifier of the app in the App Store.
     */
    private Long appleId;

    /**
     * keyId – Your private key ID from App Store Connect
     */
    private String keyId;

    /**
     * issuerId – Your issuer ID from the Keys page in App Store Connect
     */
    private String issuerId;

    /**
     * bundleId – Your app’s bundle ID
     */
    private String bundleId;

    /**
     * 环境 Sandbox(沙盒)、Production(线上)
     */
    private String environment;

    /**
     * Your private key downloaded from App Store Connect
     */
    private String p8CertClassPath;

    /**
     * 以下为苹果根证书
     * 从https://www.apple.com/certificateauthority/下载
     */
    private String computerRootClassPath;

    private String incRootClassPath;

    private String rootCAG2ClassPath;

    private String rootCAG3ClassPath;
}
