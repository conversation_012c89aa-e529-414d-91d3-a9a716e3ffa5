package com.sohu.baidu.translate.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 默认返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/07/28 14:48
 */
@NoArgsConstructor
@Data
public class BaiduTranslateResponse implements Serializable {

    /**
     * 源语言
     */
    @JsonProperty("from")
    private String from;
    /**
     * 目标语言
     */
    @JsonProperty("to")
    private String to;

    /**
     * 错误码
     */
    @JsonProperty("error_code")
    private String errorCode;

    /**
     * 返回翻译结果，包括 src 和 dst 字段
     */
    @JsonProperty("trans_result")
    private List<TransResultDTO> transResult;

    /**
     * 返回翻译结果，包括 src 和 dst 字段
     */
    @Data
    public static class TransResultDTO {
        /**
         * 原文
         */
        @JsonProperty("src")
        private String src;
        /**
         * 译文
         */
        @JsonProperty("dst")
        private String dst;
    }

}
