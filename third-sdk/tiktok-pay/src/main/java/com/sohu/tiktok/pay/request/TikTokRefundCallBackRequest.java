package com.sohu.tiktok.pay.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 退款结果回调
 *
 * @author: lyw
 * @date: 2023/6/5 12:47
 * @version: 1.0.0
 */
@NoArgsConstructor
@Data
public class TikTokRefundCallBackRequest {

    /**
     * Unix 时间戳，字符串类型
     * 1644399124
     */
    @JsonProperty("timestamp")
    private String timestamp;

    /**
     * 随机数
     * 797
     */
    @JsonProperty("nonce")
    private String nonce;

    /**
     * 订单信息的 json 字符串
     */
    @JsonProperty("msg")
    private String msg;

    /**
     * 回调类型标记，退款成功回调为"refund"
     *
     * refund
     */
    @JsonProperty("msg_signature")
    private String msgSignature;

    /**
     * 签名，详见签名DEMO
     * 52fff5f7a4bf4a921c2daf83c75cf0e716432c73
     */
    @JsonProperty("type")
    private String type;

    /**
     * 订单信息的 json 字符串
     */
    @NoArgsConstructor
    @Data
    public static class MsgDTO {
        /**
         * 当前交易发起的小程序id
         * tt07e3715e98c9aac0
         */
        @JsonProperty("appid")
        private String appid;

        /**
         * 开发者侧的退款订单号
         * 401020220222383677956847349760
         */
        @JsonProperty("cp_refundno")
        private String cpRefundno;

        /**
         * 预下单时开发者传入字段
         * 一些附加信息
         */
        @JsonProperty("cp_extra")
        private String cpExtra;

        /**
         * 状态枚举值：
         * SUCCESS：成功
         * FAIL：失败
         */
        @JsonProperty("status")
        private String status;

        /**
         * 退款金额，单位为分
         * 100，即1元
         */
        @JsonProperty("refund_amount")
        private Integer refundAmount;

        /**
         * 退款时间，Unix 时间戳，10 位，整型数，秒级
         * 1644399124
         */
        @JsonProperty("refunded_at")
        private Integer refundedAt;

        /**
         * 退款失败原因描述，详见发起退款错误码
         * 商户余额不足
         */
        @JsonProperty("message")
        private String message;

        /**
         * 抖音侧订单号
         * 7064214528778700000
         */
        @JsonProperty("order_id")
        private String orderId;

        /**
         * 抖音侧退款单号
         * N6926510404499680000
         */
        @JsonProperty("refund_no")
        private String refundNo;

        /**
         * 是否为分账后退款
         * false
         */
        @JsonProperty("is_all_settled")
        private Boolean isAllSettled;

    }
}
