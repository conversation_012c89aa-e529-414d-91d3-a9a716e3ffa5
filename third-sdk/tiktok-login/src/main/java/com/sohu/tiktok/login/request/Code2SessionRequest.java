package com.sohu.tiktok.login.request;

import lombok.Builder;
import lombok.Data;

/**
 * 抖音登录
 *
 * @author: lyw
 * @date: 2023/5/30 15:33
 * @version: 1.0.0
 */
@Data
@Builder
public class Code2SessionRequest {


    /**
     *小程序 ID
     */
    private String appid;

    /**
     *小程序的 APP Secret，可以在开发者后台获取
     */
    private String secret;

    /**
     *login 接口返回的登录凭证
     */
    private String code;

    /**
     *login 接口返回的匿名登录凭证
     */
    private String anonymous_code;

    /**
     * 线上地址
     * POST https://developer.toutiao.com/api/apps/v2/jscode2session
     * 沙盒地址
     * POST https://open-sandbox.douyin.com/api/apps/v2/jscode2session
     *
     * @return 请求地址
     */

    public transient static final String URL = "https://developer.toutiao.com/api/apps/v2/jscode2session";

}
