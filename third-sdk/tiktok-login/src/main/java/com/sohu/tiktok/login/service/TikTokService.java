package com.sohu.tiktok.login.service;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.sohu.third.core.util.JsonUtils;
import com.sohu.tiktok.login.request.Code2SessionRequest;
import com.sohu.tiktok.login.response.Code2SessionResponse;
import com.sohu.tiktok.login.util.TikTokDecrypt;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 抖音接口实现类
 *
 * @author: lyw
 * @date: 2023/5/31 10:16
 * @version: 1.0.0
 */
@Slf4j
public class TikTokService {

    /**
     * 抖音登录
     *
     * @param appid
     * @param secret
     * @param code
     * @param anonymous_code
     * @return
     */
    public static Code2SessionResponse login(String appid, String secret, String code, String anonymous_code) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("appid", appid);
        paramMap.put("secret", secret);
        paramMap.put("code", code);
        paramMap.put("anonymous_code", anonymous_code);
        String post = HttpUtil.post(Code2SessionRequest.URL, JSONUtil.toJsonStr(paramMap));
        log.info(post);
        Code2SessionResponse code2SessionResponse = JsonUtils.parseObject(post, Code2SessionResponse.class);
        log.info(JSONUtil.toJsonStr(code2SessionResponse));
        return code2SessionResponse;
    }

    /**
     * 解密手机号
     *
     * @param encryptedData
     * @param sessionKey
     * @param iv
     * @return
     */
    public static String decodePhone(String encryptedData, String sessionKey, String iv) {
        return TikTokDecrypt.decrypt(encryptedData, sessionKey, iv);
    }

}
