package com.sohu.story.converter;

import com.google.common.base.Strings;
import com.sohu.story.api.vo.AnimeChapterVo;
import com.sohu.story.api.vo.AnimeVo;
import com.sohu.story.response.bookflag.BookContentResponse;
import com.sohu.story.response.bookflag.BookInfoResponse;
import com.sohu.story.response.bookflag.VolumeResponse;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 书旗小说转换器
 *
 * @Author: leibo
 * @Date: 2025/5/6 09:35
 **/
public class BookFlagConverter {

    /**
     * 对象转换处理
     *
     * @param animeVo
     * @return
     */
    public static BookInfoResponse toBookInfoResponse(AnimeVo animeVo, String imageUrl) {
        if (Objects.isNull(animeVo)) {
            return null;
        }
        BookInfoResponse infoResponse = new BookInfoResponse();
        infoResponse.setBookId(animeVo.getId());
        infoResponse.setBookName(animeVo.getTitle());
        infoResponse.setAuthorName(animeVo.getAuthor());
        infoResponse.setTopCategory(animeVo.getIssex() == 2L ? "女频" : "男频");
        infoResponse.setBookStatus(1);
        infoResponse.setCover(Strings.isNullOrEmpty(imageUrl) ? animeVo.getCoverpic() : imageUrl);
        infoResponse.setScore(0.0);
        infoResponse.setCommentUpdateTime(0L);
        infoResponse.setUpdateTime(animeVo.getUpdatedAt().getTime()/1000);
        infoResponse.setTopClass(502);
        return infoResponse;
    }

    /**
     * 章节对象处理
     *
     * @param chapterList
     * @param bookId
     * @return
     */
    public static VolumeResponse toVolumeResponse(List<AnimeChapterVo> chapterList, Long bookId) {
        if (CollectionUtils.isEmpty(chapterList)) {
            return null;
        }
        VolumeResponse volumeResponse = new VolumeResponse();
        volumeResponse.setBookId(bookId);
        List<VolumeResponse.Volume> volumeList = new ArrayList<>();
        VolumeResponse.Volume volume = new VolumeResponse.Volume();
        volume.setVolumeId(0L);
        volume.setVolumeName("正文");
        VolumeResponse.ChapterInfo chapterInfo = new VolumeResponse.ChapterInfo();
        List<VolumeResponse.Chapter> chapters = new ArrayList<>();
        for (AnimeChapterVo animeChapterVo: chapterList) {
            VolumeResponse.Chapter chapter = new VolumeResponse.Chapter();
            chapter.setChapterId(animeChapterVo.getId());
            chapter.setChapterName(animeChapterVo.getTitle());
            chapter.setCpUpdateTime(animeChapterVo.getUpdatedAt().getTime()/1000);
            chapters.add(chapter);
        }
        chapterInfo.setChapterList(chapters);
        volume.setChapterInfo(chapterInfo);
        volumeList.add(volume);
        volumeResponse.setVolumeList(volumeList);
        return volumeResponse;
    }

    /**
     * 章节详情对象处理
     *
     * @param chapterList
     * @return
     */
    public static BookContentResponse toBookContentResponse(List<AnimeChapterVo> chapterList) {
        if (CollectionUtils.isEmpty(chapterList)) {
            return null;
        }
        AnimeChapterVo animeChapterVo = chapterList.get(0);
        BookContentResponse bookContentResponse = new BookContentResponse();
        bookContentResponse.setBookId(animeChapterVo.getAnid());
        bookContentResponse.setChapterId(animeChapterVo.getId());
        bookContentResponse.setContent(animeChapterVo.getInfo());
        return bookContentResponse;
    }
}
