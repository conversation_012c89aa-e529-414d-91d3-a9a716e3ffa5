package com.sohu.story.service.impl;

import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.story.api.bo.AnimeBo;
import com.sohu.story.api.vo.AnimeVo;
import com.sohu.story.domain.Anime;
import com.sohu.story.mapper.AnimeMapper;
import com.sohu.story.service.IAnimeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@RequiredArgsConstructor
@Service
public class AnimeServiceImpl implements IAnimeService {

    private final AnimeMapper baseMapper;

    /**
     * 查询【请填写功能名称】
     */
    @Override
    public AnimeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public TableDataInfo<AnimeVo> queryPageList(AnimeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Anime> lqw = buildQueryWrapper(bo);
        Page<AnimeVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public List<AnimeVo> queryList(AnimeBo bo) {
        LambdaQueryWrapper<Anime> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<AnimeVo> listAnime() {
        return baseMapper.selectVoList(Wrappers.<Anime>lambdaQuery()
                .eq(Anime::getBtype, 2)
                .eq(Anime::getStatus, 1)
                .eq(Anime::getReviewStatus, 1));
    }

    @Override
    public void updatePicture(Long id, String imageUrl) {
        Anime anime = new Anime();
        anime.setId(id);
        anime.setPicture(imageUrl);
        baseMapper.updateById(anime);
    }

    @Override
    public Long queryItemId(Long bookId) {
        return baseMapper.selectCount(Wrappers.<Anime>lambdaQuery()
                .eq(Anime::getBtype, 2)
                .eq(Anime::getStatus, 1)
                .eq(Anime::getReviewStatus, 1)
                .le(Anime :: getId, bookId));
    }

    @Override
    public List<AnimeVo> listAnimeByIds(List<Long> ids) {
        return baseMapper.selectVoList(Wrappers.<Anime>lambdaQuery()
                .eq(Anime::getBtype, 2)
                .eq(Anime::getStatus, 1)
                .eq(Anime::getReviewStatus, 1)
                .in(Anime::getId, ids));
    }

    private LambdaQueryWrapper<Anime> buildQueryWrapper(AnimeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Anime> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBtype() != null, Anime::getBtype, bo.getBtype());
        lqw.eq(bo.getStatus() != null, Anime::getStatus, bo.getStatus());
        lqw.eq(bo.getReviewStatus() != null, Anime::getReviewStatus, bo.getReviewStatus());
        return lqw;
    }

}
