apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-admin-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-admin
  template:
    metadata:
      labels:
        app: sohu-admin
    spec:
      containers:
        - name: sohu-admin-container
          image: sohu-admin
          ports:
            - containerPort: 9211
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-app-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-app
  template:
    metadata:
      labels:
        app: sohu-app
    spec:
      containers:
        - name: sohu-app-container
          image: sohu-app
          ports:
            - containerPort: 9212
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-auth-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-auth
  template:
    metadata:
      labels:
        app: sohu-auth
    spec:
      containers:
        - name: sohu-auth-container
          image: sohu-auth
          ports:
            - containerPort: 9210
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-busy-order-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-busy-order
  template:
    metadata:
      labels:
        app: sohu-busy-order
    spec:
      containers:
        - name: sohu-busy-order-container
          image: sohu-busy-order
          ports:
            - containerPort: 9213
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-entry-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-entry
  template:
    metadata:
      labels:
        app: sohu-entry
    spec:
      containers:
        - name: sohu-entry-container
          image: sohu-entry
          ports:
            - containerPort: 9214
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-gateway-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-gateway
  template:
    metadata:
      labels:
        app: sohu-gateway
    spec:
      containers:
        - name: sohu-gateway-container
          image: sohu-gateway
          ports:
            - containerPort: 8080
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-gen-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-gen
  template:
    metadata:
      labels:
        app: sohu-gen
    spec:
      containers:
        - name: sohu-gen-container
          image: sohu-gen
          ports:
            - containerPort: 9215
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-im-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-im
  template:
    metadata:
      labels:
        app: sohu-im
    spec:
      containers:
        - name: sohu-im-container
          image: sohu-im
          ports:
            - containerPort: 9216
            - containerPort: 9326
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-job-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-job
  template:
    metadata:
      labels:
        app: sohu-job
    spec:
      containers:
        - name: sohu-job-container
          image: sohu-job
          ports:
            - containerPort: 9217
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-monitor-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-monitor
  template:
    metadata:
      labels:
        app: sohu-monitor
    spec:
      containers:
        - name: sohu-monitor-container
          image: sohu-monitor
          ports:
            - containerPort: 9100
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-nacos-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-nacos
  template:
    metadata:
      labels:
        app: sohu-nacos
    spec:
      containers:
        - name: sohu-nacos-container
          image: sohu-nacos
          ports:
            - containerPort: 8848
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-pay-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-pay
  template:
    metadata:
      labels:
        app: sohu-pay
    spec:
      containers:
        - name: sohu-pay-container
          image: sohu-pay
          ports:
            - containerPort: 9219
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-resource-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-resource
  template:
    metadata:
      labels:
        app: sohu-resource
    spec:
      containers:
        - name: sohu-resource-container
          image: sohu-resource
          ports:
            - containerPort: 9220
            - containerPort: 645
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-sentinel-dashboard-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-sentinel-dashboard
  template:
    metadata:
      labels:
        app: sohu-sentinel-dashboard
    spec:
      containers:
        - name: sohu-sentinel-dashboard-container
          image: sohu-sentinel-dashboard
          ports:
            - containerPort: 8718
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-shop-goods-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-shop-goods
  template:
    metadata:
      labels:
        app: sohu-shop-goods
    spec:
      containers:
        - name: sohu-shop-goods-container
          image: sohu-shop-goods
          ports:
            - containerPort: 9221
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-shop-order-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-shop-order
  template:
    metadata:
      labels:
        app: sohu-shop-order
    spec:
      containers:
        - name: sohu-shop-order-container
          image: sohu-shop-order
          ports:
            - containerPort: 9222
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-stream-rocketmq-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-stream-rocketmq
  template:
    metadata:
      labels:
        app: sohu-stream-rocketmq
    spec:
      containers:
        - name: sohu-stream-rocketmq-container
          image: sohu-stream-rocketmq
          ports:
            - containerPort: 9233
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-system-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-system
  template:
    metadata:
      labels:
        app: sohu-system
    spec:
      containers:
        - name: sohu-system-container
          image: sohu-system
          ports:
            - containerPort: 9223
          imagePullPolicy: Never
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sohu-xxl-job-admin-deployment
spec:
  replicas: 4
  selector:
    matchLabels:
      app: sohu-xxl-job-admin
  template:
    metadata:
      labels:
        app: sohu-xxl-job-admin
    spec:
      containers:
        - name: sohu-xxl-job-admin-container
          image: sohu-xxl-job-admin
          ports:
            - containerPort: 9900
          imagePullPolicy: Never   