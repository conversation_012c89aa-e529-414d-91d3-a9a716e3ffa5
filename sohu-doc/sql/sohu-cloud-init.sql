/**
  初始化数据
 */
-- ----------------------------
-- Table structure for sohu_article
-- ----------------------------
DROP TABLE IF EXISTS `sohu_article`;
CREATE TABLE `sohu_article` (
                                `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                `user_id` bigint unsigned NOT NULL COMMENT '作者id',
                                `site_id` bigint unsigned NOT NULL COMMENT '站点ID',
                                `category_id` bigint unsigned NOT NULL COMMENT '分类ID',
                                `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
                                `digest` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '摘要',
                                `original` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'N' COMMENT '是否是原创;Y:是 N:否',
                                `origin_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转载地址',
                                `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '封面图',
                                `images` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '子图',
                                `sort_index` int unsigned NOT NULL DEFAULT '1' COMMENT '排序',
                                `state` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态;Delete:删除，Edit:草稿，WaitApprove：待审核,OnShelf：上架，OffShelf：下架，CompelOff：强制下架',
                                `reject_reason` tinytext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '驳回理由',
                                `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
                                `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                PRIMARY KEY (`id`) USING BTREE,
                                KEY `idx_user` (`user_id`) USING BTREE,
                                KEY `idx_site` (`user_id`,`category_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='图文主体表';

-- ----------------------------
-- Table structure for sohu_article_info
-- ----------------------------
DROP TABLE IF EXISTS `sohu_article_info`;
CREATE TABLE `sohu_article_info`  (
                                      `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                      `site_id` bigint UNSIGNED NOT NULL COMMENT '站点ID',
                                      `article_id` bigint UNSIGNED NOT NULL COMMENT '图文ID',
                                      `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
                                      `view_count` int UNSIGNED NULL DEFAULT 0 COMMENT '阅读数',
                                      `comment_count` int UNSIGNED NULL DEFAULT 0 COMMENT '评论数',
                                      `praise_count` int UNSIGNED NULL DEFAULT 0 COMMENT '点赞数',
                                      `collect_count` int UNSIGNED NULL DEFAULT 0 COMMENT '收藏数',
                                      PRIMARY KEY (`id`) USING BTREE,
                                      UNIQUE INDEX `unq`(`site_id`, `article_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '图文拓展表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sohu_article_relate
-- ----------------------------
DROP TABLE IF EXISTS `sohu_article_relate`;
CREATE TABLE `sohu_article_relate`  (
                                        `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `article_id` bigint UNSIGNED NOT NULL COMMENT '图片或视频id',
                                        `busy_type` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'dict::问答关联类型:[User:用户,Project:项目,Goods:商品,BusyOrder:商单,Shop:店铺,Address:地址]',
                                        `busy_code` bigint UNSIGNED NOT NULL COMMENT '业务id',
                                        PRIMARY KEY (`id`) USING BTREE,
                                        INDEX `idx_article`(`article_id`) USING BTREE
) ENGINE = InnoDB  CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '图文与关联项的关联表' ROW_FORMAT = Dynamic;



-- ----------------------------
-- Table structure for sohu_video
-- ----------------------------
DROP TABLE IF EXISTS `sohu_video`;
CREATE TABLE `sohu_video` (
                              `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                              `site_id` bigint unsigned NOT NULL COMMENT '站点ID',
                              `user_id` bigint unsigned NOT NULL COMMENT '作者id',
                              `title` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '视频标题',
                              `category` bigint unsigned NOT NULL COMMENT '分类ID',
                              `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '封面图',
                              `video_url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '子图',
                              `sort_index` int unsigned NOT NULL DEFAULT '1' COMMENT '排序',
                              `state` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态;Delete:删除，Edit:草稿，WaitApprove：待审核,OnShelf：上架，OffShelf：下架，CompelOff：强制下架，Private:仅自己可见',
                              `reject_reason` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '拒绝理由',
                              `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                              `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
                              `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              PRIMARY KEY (`id`) USING BTREE,
                              KEY `idx_site` (`site_id`,`category`) USING BTREE,
                              KEY `idx_user` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='视频';

-- ----------------------------
-- Records of sohu_video
-- ----------------------------

-- ----------------------------
-- Table structure for sohu_video_info
-- ----------------------------
DROP TABLE IF EXISTS `sohu_video_info`;
CREATE TABLE `sohu_video_info`  (
                                    `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `site_id` bigint UNSIGNED NOT NULL COMMENT '站点ID',
                                    `video_id` bigint UNSIGNED NOT NULL COMMENT '视频主体表id',
                                    `view_count` int UNSIGNED NULL DEFAULT 0 COMMENT '阅读数',
                                    `comment_count` int UNSIGNED NULL DEFAULT 0 COMMENT '评论数',
                                    `praise_count` int UNSIGNED NULL DEFAULT 0 COMMENT '点赞数',
                                    `collect_count` int UNSIGNED NULL DEFAULT 0 COMMENT '收藏数',
                                    PRIMARY KEY (`id`) USING BTREE,
                                    UNIQUE INDEX `unq`(`video_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '视频拓展信息' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for sohu_video_relate
-- ----------------------------
DROP TABLE IF EXISTS `sohu_video_relate`;
CREATE TABLE `sohu_video_relate`  (
                                      `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
                                      `video_id` bigint UNSIGNED NOT NULL COMMENT '视频id',
                                      `busy_type` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'dict::问答关联类型:[User:用户,Project:项目,Goods:商品,BusyOrder:商单,Shop:店铺,Address:地址]',
                                      `busy_code` bigint UNSIGNED NOT NULL COMMENT '业务id',
                                      PRIMARY KEY (`id`) USING BTREE,
                                      INDEX `idx`(`video_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '视频与关联项的关联表' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for sohu_audit
-- ----------------------------
DROP TABLE IF EXISTS `sohu_audit`;
CREATE TABLE `sohu_audit`  (
                               `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
                               `busy_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核业务类型:[Article:图文,Video视频,Project:项目,Question:问题,Answer:回答,Card:资源名片,BusyModel:生意模式,BusyOrder:商单]',
                               `busy_code` bigint UNSIGNED NOT NULL COMMENT '业务id',
                               `busy_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务标题（回答的标题指的是：对应问题的标题）',
                               `busy_cover_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务封面图',
                               `busy_belonger` bigint NOT NULL COMMENT '业务归属人',
                               `city_site_id` bigint UNSIGNED NOT NULL COMMENT '城市站点ID',
                               `country_site_id` bigint UNSIGNED NOT NULL COMMENT '国家站点ID',
                               `city_audit_state` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市站审核状态',
                               `country_audit_state` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '国家站审核状态',
                               `sys_audit_state` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '管理员审核状态',
                               `reject_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '驳回理由',
                               `publish_time` datetime(0) NULL DEFAULT NULL COMMENT '发布时间',
                               PRIMARY KEY (`id`) USING BTREE,
                               INDEX `idx_busy_belonger`(`busy_belonger`) USING BTREE,
                               INDEX `tp_cd_city_state_index`(`busy_type`, `busy_code`, `city_site_id`, `city_audit_state`) USING BTREE,
                               INDEX `tp_cd_country_state_index`(`busy_type`, `busy_code`, `country_site_id`, `country_audit_state`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '业务审核表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sohu_audit
-- ----------------------------

-- ----------------------------
-- Table structure for sohu_audit_record
-- ----------------------------
DROP TABLE IF EXISTS `sohu_audit_record`;
CREATE TABLE `sohu_audit_record` (
                                     `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                     `audit_id` bigint unsigned NOT NULL COMMENT '审核表ID',
                                     `busy_type` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务类型',
                                     `busy_code` bigint unsigned NOT NULL COMMENT '业务Code',
                                     `auditer` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核人ID',
                                     `audit_state` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核状态',
                                     `reject_reason` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '拒绝理由',
                                     `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     KEY `audit_id_index` (`audit_id`) USING BTREE,
                                     KEY `busy_code_index` (`busy_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='审核记录表';

DROP TABLE IF EXISTS `sohu_content_main`;
CREATE TABLE `sohu_content_main` (
                                     `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                     `site_id` bigint NOT NULL COMMENT '站点ID',
                                     `user_id` bigint NOT NULL COMMENT '用户id',
                                     `obj_id` bigint NOT NULL COMMENT '对象id',
                                     `obj_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'dict::类型:[Question:问题,Answer:回答,Article:图文,Video:视频]',
                                     `obj_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
                                     `category` bigint unsigned NOT NULL DEFAULT '0' COMMENT '分类id',
                                     `view_count` int unsigned DEFAULT '0' COMMENT '阅读数',
                                     `comment_count` int unsigned DEFAULT '0' COMMENT '评论数',
                                     `praise_count` int unsigned DEFAULT '0' COMMENT '点赞数',
                                     `collect_count` int unsigned DEFAULT '0' COMMENT '收藏数',
                                     `state` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '状态（Edit=草稿 WaitApprove=审核中 OnShelf=上架  OffShelf=下架  CompelOff=强制下架  Refuse=审核拒绝）',
                                     `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '封面图',
                                     `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '视频地址',
                                     `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                     `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
                                     `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     UNIQUE KEY `unq` (`obj_id`,`obj_type`) USING BTREE,
                                     KEY `idx_user` (`user_id`) USING BTREE,
                                     KEY `idx_category` (`category`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='万能表';


DROP TABLE IF EXISTS `sohu_project`;
CREATE TABLE `sohu_project` (
                                `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '项目主键',
                                `site_id` bigint NOT NULL COMMENT '站点',
                                `category_id` bigint NOT NULL COMMENT '项目分类id',
                                `user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
                                `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
                                `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '内容',
                                `main_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主图',
                                `sub_img` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '子图',
                                `price` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '价格',
                                `currency_id` bigint NOT NULL COMMENT '币种id(sohu_currency表主键ID)',
                                `unit_id` bigint NOT NULL COMMENT '单位id(sohu_unit表主键ID)',
                                `state` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目状态:[Edit:编辑,WaitApprove:待审核,OnShelf:上架,Refuse:审核失败,OffShelf:下架,CompelOff:强制下架,Del:删除]',
                                `reject_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除或下架理由',
                                `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人id',
                                `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
                                `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                PRIMARY KEY (`id`) USING BTREE,
                                KEY `site_id` (`site_id`) USING BTREE,
                                KEY `creator` (`create_by`) USING BTREE,
                                KEY `type_id` (`category_id`) USING BTREE,
                                KEY `create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='项目实体表';

DROP TABLE IF EXISTS `sohu_project_info`;
CREATE TABLE `sohu_project_info` (
                                     `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '项目记录主键',
                                     `project_id` bigint unsigned NOT NULL COMMENT '项目主键',
                                     `view_count` int unsigned DEFAULT '0' COMMENT '阅读数',
                                     `comment_count` int unsigned DEFAULT '0' COMMENT '评论数',
                                     `praise_count` int unsigned DEFAULT '0' COMMENT '点赞数',
                                     `collect_count` int unsigned DEFAULT '0' COMMENT '收藏数',
                                     `publish_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     UNIQUE KEY `project_id` (`project_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='项目记录表';

DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor` (
                                  `info_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '访问ID',
                                  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户账号',
                                  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '登录IP地址',
                                  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '登录地点',
                                  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '浏览器类型',
                                  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '操作系统',
                                  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
                                  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '提示消息',
                                  `login_time` datetime DEFAULT NULL COMMENT '访问时间',
                                  PRIMARY KEY (`info_id`) USING BTREE,
                                  KEY `idx_sys_logininfor_s` (`status`) USING BTREE,
                                  KEY `idx_sys_logininfor_lt` (`login_time`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='系统访问记录';


DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log` (
                                `oper_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志主键',
                                `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '模块标题',
                                `business_type` int DEFAULT '0' COMMENT '业务类型（0其它 1新增 2修改 3删除）',
                                `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '方法名称',
                                `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求方式',
                                `operator_type` int DEFAULT '0' COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
                                `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '操作人员',
                                `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '部门名称',
                                `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求URL',
                                `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '主机地址',
                                `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '操作地点',
                                `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求参数',
                                `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '返回参数',
                                `status` int DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
                                `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '错误消息',
                                `oper_time` datetime DEFAULT NULL COMMENT '操作时间',
                                PRIMARY KEY (`oper_id`) USING BTREE,
                                KEY `idx_sys_oper_log_bt` (`business_type`) USING BTREE,
                                KEY `idx_sys_oper_log_s` (`status`) USING BTREE,
                                KEY `idx_sys_oper_log_ot` (`oper_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='操作日志记录';
