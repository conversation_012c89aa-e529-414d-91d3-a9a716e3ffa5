/*
 Navicat Premium Data Transfer

 Source Server         : rm-2ze157pzb2l3yjyak
 Source Server Type    : MySQL
 Source Server Version : 80028
 Source Host           : sohuglobal.mysql.rds.aliyuncs.com:3306
 Source Schema         : ry-im

 Target Server Type    : MySQL
 Target Server Version : 80028
 File Encoding         : 65001

 Date: 25/04/2024 17:45:10
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sohu_im_apply
-- ----------------------------
DROP TABLE IF EXISTS `sohu_im_apply`;
CREATE TABLE `sohu_im_apply`  (
                                  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
                                  `apply_user_id` bigint UNSIGNED NOT NULL COMMENT '申请人id',
                                  `apply_attach` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请的附加附件',
                                  `apply_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请理由',
                                  `target_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '被申请者的id，可能是人或者群',
                                  `apply_type` enum('group','user') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'group' COMMENT '申请类型（group=群,user=加用户）',
                                  `apply_state` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核状态:[WaitApprove:待审核,Pass:认可,Refuse:拒绝]',
                                  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝原因',
                                  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '申请时间',
                                  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                  `invite_user` bigint NULL DEFAULT 0 COMMENT '邀请人ID',
                                  PRIMARY KEY (`id`) USING BTREE,
                                  UNIQUE INDEX `unq`(`apply_user_id`, `target_id`, `apply_type`) USING BTREE,
                                  INDEX `idx`(`apply_user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'im申请表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sohu_im_chat_last_message
-- ----------------------------
DROP TABLE IF EXISTS `sohu_im_chat_last_message`;
CREATE TABLE `sohu_im_chat_last_message`  (
                                              `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
                                              `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
                                              `err` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '被拦截的原因，比如文字涉恐、反共、非好友、或其他规则',
                                              `sender_id` bigint NOT NULL DEFAULT 0 COMMENT '发送人ID',
                                              `receiver_id` bigint NOT NULL DEFAULT 0 COMMENT '接收人id，用户ID或群ID',
                                              `session_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'personal' COMMENT '会话类型:system - 系统类型、single - 单聊、group-群聊',
                                              `message_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'text' COMMENT '消息类型;(text - 文本、photo - 图片、video - 视频、voice - 语音、share - 分享、voiceCall - 语音通话、groupVoiceCall-群语音通话 ,videoCall - 视频通话、groupCall - 群视频、notice - 公告、file - 文件、command - 命令)',
                                              `share_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'none' COMMENT '分享类型，task-任务，good-商品，article-图文，video-视频，goodWindow-商品橱窗',
                                              `read_type` tinyint NULL DEFAULT 0 COMMENT '消息是否已读(1=已读 0=未读)',
                                              `share_id` bigint NULL DEFAULT 0 COMMENT '分享数据的id',
                                              `chat_id` bigint NOT NULL COMMENT '唯一值，纳秒',
                                              `msg_id` bigint NOT NULL DEFAULT 0 COMMENT '会话id',
                                              `duration` int NULL DEFAULT 0 COMMENT '语音消息或视频消息的时长',
                                              `file_size` int NULL DEFAULT 0 COMMENT '文件大小，单位kb',
                                              `local_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '这个是用来本地识别消息关联发送状态',
                                              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '发送消息时间',
                                              `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                              PRIMARY KEY (`id`) USING BTREE,
                                              UNIQUE INDEX `unq`(`sender_id`, `chat_id`) USING BTREE,
                                              INDEX `idx_sender`(`sender_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'im外层消息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sohu_im_chat_message
-- ----------------------------
DROP TABLE IF EXISTS `sohu_im_chat_message`;
CREATE TABLE `sohu_im_chat_message`  (
                                         `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
                                         `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
                                         `err` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '被拦截的原因，比如文字涉恐、反共、非好友、或其他规则',
                                         `sender_id` bigint NOT NULL DEFAULT 0 COMMENT '发送人ID',
                                         `receiver_id` bigint NOT NULL DEFAULT 0 COMMENT '接收人id，用户ID或群ID',
                                         `session_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'personal' COMMENT '会话类型:system - 系统类型、single - 单聊、group-群聊',
                                         `message_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'text' COMMENT '消息类型;(text - 文本、photo - 图片、video - 视频、voice - 语音、share - 分享、voiceCall - 语音通话、groupVoiceCall-群语音通话 ,videoCall - 视频通话、groupCall - 群视频、notice - 公告、file - 文件、command - 命令)',
                                         `share_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'none' COMMENT '分享类型，task-任务，good-商品，article-图文，video-视频，goodWindow-商品橱窗',
                                         `read_type` tinyint NULL DEFAULT 0 COMMENT '消息是否已读(1=已读 0=未读)',
                                         `hidden` tinyint NOT NULL DEFAULT 0 COMMENT '是否显示（1=隐藏 0=显示）',
                                         `share_id` bigint NULL DEFAULT 0 COMMENT '分享数据的id',
                                         `chat_id` bigint NOT NULL COMMENT '唯一值，纳秒',
                                         `msg_id` bigint NOT NULL DEFAULT 0 COMMENT '会话id',
                                         `duration` int NULL DEFAULT 0 COMMENT '语音消息或视频消息的时长',
                                         `file_size` int NULL DEFAULT 0 COMMENT '文件大小，单位kb',
                                         `local_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '这个是用来本地识别消息关联发送状态',
                                         `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '发送消息时间',
                                         `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                         PRIMARY KEY (`id`) USING BTREE,
                                         UNIQUE INDEX `unq`(`chat_id`) USING BTREE,
                                         INDEX `idx_sender`(`sender_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'im消息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sohu_im_group
-- ----------------------------
DROP TABLE IF EXISTS `sohu_im_group`;
CREATE TABLE `sohu_im_group`  (
                                  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '群组ID',
                                  `pid` bigint NULL DEFAULT 0 COMMENT '父群组ID',
                                  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
                                  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '群名称',
                                  `user_id` bigint UNSIGNED NOT NULL COMMENT '群主ID',
                                  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '群logo',
                                  `group_greet` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '进群招呼',
                                  `group_notice` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '群公告',
                                  `need_confirm` tinyint NULL DEFAULT 0 COMMENT '进群是否需要确认（0=不需要 1=需要）',
                                  `group_user_num` int UNSIGNED NULL DEFAULT 0 COMMENT '群成员数',
                                  `max_group_user_num` int UNSIGNED NULL DEFAULT 200 COMMENT '最大成员数',
                                  `max_group_admin_num` int UNSIGNED NULL DEFAULT 10 COMMENT '最大群管理员人数',
                                  `forbid` tinyint NULL DEFAULT 0 COMMENT '是否禁言（0=否 1=是）',
                                  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                  PRIMARY KEY (`id`) USING BTREE,
                                  INDEX `idx_onwer`(`user_id`) USING BTREE,
                                  INDEX `idx_name`(`name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'im群组表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sohu_im_group_user
-- ----------------------------
DROP TABLE IF EXISTS `sohu_im_group_user`;
CREATE TABLE `sohu_im_group_user`  (
                                       `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                       `group_id` bigint UNSIGNED NOT NULL COMMENT '群id',
                                       `user_id` bigint UNSIGNED NOT NULL COMMENT '用户id',
                                       `notify_level` enum('group','user') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'group' COMMENT '通知级别(group=群级别,user=用户级别)',
                                       `permission_type` enum('group_leader','group_admin','group_user') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'group_user' COMMENT '群权限(group_leader=群主 group_admin=管理员 group_user=普通成员)',
                                       `card_id` bigint NULL DEFAULT NULL COMMENT '资源名片id',
                                       `forbid` tinyint NULL DEFAULT 0 COMMENT '是否禁言（0=否 1=是）',
                                       `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '入群时间',
                                       `update_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                       `invite_user` bigint NULL DEFAULT 0 COMMENT '邀请人ID',
                                       `sort_index` int NULL DEFAULT 3 COMMENT '排序',
                                       PRIMARY KEY (`id`) USING BTREE,
                                       UNIQUE INDEX `unq`(`group_id`, `user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'im群用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sohu_im_message
-- ----------------------------
DROP TABLE IF EXISTS `sohu_im_message`;
CREATE TABLE `sohu_im_message`  (
                                    `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
                                    `ext` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附件',
                                    `title` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息标题',
                                    `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
                                    `group_id` bigint NULL DEFAULT 0 COMMENT '群组id，可为空，接收人可能为人',
                                    `user_id` bigint NOT NULL DEFAULT 0 COMMENT '当前用户ID',
                                    `receiver_id` bigint NULL DEFAULT 0 COMMENT '接收人id，可为空，接收人可能为群',
                                    `chat_type` enum('group','personal') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'personal' COMMENT '聊天类型(group=群聊 personal=私聊)',
                                    `msg_id` bigint NULL DEFAULT 0 COMMENT '会话id',
                                    `msg_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'text' COMMENT '消息类型;(如：text、image、voice、vedio、music、busyOrder、good)',
                                    `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '发送消息时间',
                                    `system_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'none' COMMENT '系统消息类型系统消息类型;(如：非系统消息-none,系统消息-system)',
                                    `read_type` enum('Y','N') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '读取状态(N=未读 Y=已读)',
                                    `hidden` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否显示',
                                    `nano_time` bigint NULL DEFAULT NULL COMMENT '唯一值，纳秒',
                                    PRIMARY KEY (`id`) USING BTREE,
                                    INDEX `idx_sender`(`user_id`) USING BTREE,
                                    INDEX `idx_group`(`group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'im消息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sohu_im_message_last
-- ----------------------------
DROP TABLE IF EXISTS `sohu_im_message_last`;
CREATE TABLE `sohu_im_message_last`  (
                                         `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
                                         `ext` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '附件',
                                         `title` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息标题',
                                         `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
                                         `group_id` bigint NULL DEFAULT 0 COMMENT '群组id，可为空，接收人可能为人',
                                         `user_id` bigint NOT NULL DEFAULT 0 COMMENT '当前用户ID',
                                         `receiver_id` bigint NULL DEFAULT 0 COMMENT '接收人id',
                                         `chat_type` enum('group','personal') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'personal' COMMENT '聊天类型(group=群聊 personal=私聊)',
                                         `msg_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'text' COMMENT '消息类型;(如：text、image、voice、vedio、music、busyOrder、good)',
                                         `msg_id` bigint NULL DEFAULT 0 COMMENT '会话id',
                                         `read_type` enum('Y','N') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '读取状态(N=未读 Y=已读)',
                                         `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '发送消息时间',
                                         `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最后消息时间',
                                         `system_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'none' COMMENT '系统消息类型系统消息类型;(如：非系统消息-none,系统消息-system)',
                                         `nano_time` bigint NULL DEFAULT NULL COMMENT '唯一值，纳秒',
                                         PRIMARY KEY (`id`) USING BTREE,
                                         INDEX `idx_sender`(`user_id`) USING BTREE,
                                         INDEX `idx_group`(`group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'im消息最后一条表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sohu_im_notice
-- ----------------------------
DROP TABLE IF EXISTS `sohu_im_notice`;
CREATE TABLE `sohu_im_notice`  (
                                   `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
                                   `sender_id` bigint UNSIGNED NOT NULL DEFAULT 1 COMMENT '发送人ID，一般是超管ID',
                                   `receiver_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '接收人id，即用户ID',
                                   `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
                                   `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
                                   `message_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'text' COMMENT '通知类型;(互动[点赞，评论]消息-interact，mcn-mcn通知，task-任务通知，applyFriend-新朋友通知)',
                                   `sub_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子类型',
                                   `read_type` tinyint NULL DEFAULT 0 COMMENT '通知是否已读(1=已读 0=未读)',
                                   `share_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'none' COMMENT '分享类型，task-任务，good-商品，article-图文，video-视频，goodWindow-商品橱窗',
                                   `share_id` bigint NULL DEFAULT NULL COMMENT '分享数据的id',
                                   `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '发送通知时间',
                                   `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   INDEX `idx_sender`(`sender_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统通知消息表' ROW_FORMAT = Dynamic;



SET FOREIGN_KEY_CHECKS = 1;
