server {
    listen 80;
    listen 443 ssl http2;
    server_name world.sohuglobal.com;
    index index.php index.html index.htm default.php default.htm default.html;
    # 避免f5刷新后404
    try_files  $uri  $uri/  /index.html;

    #SSL-START SSL相关配置，请勿删除或修改下一行带注释的404规则
    #error_page 404/404.html;
    ssl_certificate    /etc/nginx/cert/h5.sohuglobal.com/fullchain.pem;
    ssl_certificate_key    /etc/nginx/cert/h5.sohuglobal.com/privkey.pem;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_ciphers EECDH+CHACHA20:EECDH+CHACHA20-draft:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    add_header Strict-Transport-Security "max-age=31536000";
    error_page 497  https://$host$request_uri;

    location / {
        root /usr/share/nginx/html/h5_sohuglobal; # 设置项目的路径
        index  index.html index.htm;
        try_files  $uri  $uri/  /index.html;
    }

}
