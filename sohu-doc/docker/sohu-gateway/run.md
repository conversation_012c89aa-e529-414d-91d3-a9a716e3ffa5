### 使用示例命令

# 运行
docker build -f Dockerfile  -t "sohu-gateway" . --no-cache

# 线上
docker run -d --network sohu_bridge_network -p 8080:8080 --name sohu-gateway -it -v /docker/sohu-gateway/:/docker/sohu-gateway/ --restart=always sohu-gateway

# 测试环境
docker run -d --network sohu_bridge_network -p 18080:8080 --name sohu-gateway -it -v /docker/sohu-gateway/:/docker/sohu-gateway/ --restart=always sohu-gateway


```
