
# 使用自制jdk
FROM openjdk:11

# 维护者信息
MAINTAINER kezhen

ENV TZ=Asia/Shanghai JAVA_OPTS="-Xms128m -Xmx256m"

# 添加jar包到容器中 -- tips: xx.jar 和 Dockerfile 在同一级
ADD *.jar /docker/sohu-auth/sohu-auth.jar

# 对外暴漏的端口号
# [注：EXPOSE指令只是声明容器运行时提供的服务端口，给读者看有哪些端口，在运行时只会开启程序自身的端口！！]
EXPOSE 9210

# 以exec格式的CMD指令 -- 可实现优雅停止容器服务
# "sh", "-c" : 可通过exec模式执行shell  =》 获得环境变量
ENTRYPOINT ["java","-jar","/docker/sohu-auth/sohu-auth.jar"]
