### 使用示例命令

此版本提供出一个`JAVA_OPTS`去设置jar的运行参数

```shell
# 打包镜像 -f:指定Dockerfile文件路径 --no-cache:构建镜像时不使用缓存
docker build -f Dockerfile --build-arg JAVA_OPTS="-Xms256m -Xmx512m -Xss256K" -t "sohu-nacos" . --no-cache

# 运行

docker run -d --network sohu_bridge_network -p 8848:8848 --name sohu-nacos -e server.port=8848 --privileged=true -it -v /docker/sohu-nacos:/docker/sohu-nacos --restart=always sohu-nacos



```
