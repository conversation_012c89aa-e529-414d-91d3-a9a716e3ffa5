package com.sohu.shoporder.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shoporder.api.bo.SohuShopOrderLogisticsBo;
import com.sohu.shoporder.api.vo.SohuShopOrderLogisticsVo;
import com.sohu.shoporder.domain.SohuShopOrderLogistics;
import com.sohu.shoporder.service.IPlayletShopOrderLogisticsService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 订单快递记录控制器
 * 前端访问路由地址为:/shoporder/shopOrderLogistics
 *
 * <AUTHOR>
 * @date 2023-06-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/shopOrderLogistics")
public class SohuShopOrderLogisticsController extends BaseController {

    private final IPlayletShopOrderLogisticsService iPlayletShopOrderLogisticsService;

    /**
     * 查询订单快递记录列表
     */
    @SaCheckPermission("shoporder:shopOrderLogistics:list")
    @GetMapping("/list")
    public TableDataInfo<SohuShopOrderLogisticsVo> list(SohuShopOrderLogisticsBo bo, PageQuery pageQuery) {
        return iPlayletShopOrderLogisticsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出订单快递记录列表
     */
    @SaCheckPermission("shoporder:shopOrderLogistics:export")
    @Log(title = "订单快递记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuShopOrderLogisticsBo bo, HttpServletResponse response) {
        List<SohuShopOrderLogisticsVo> list = iPlayletShopOrderLogisticsService.queryList(bo);
        ExcelUtil.exportExcel(list, "订单快递记录", SohuShopOrderLogisticsVo.class, response);
    }

    /**
     * 获取订单快递记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("shoporder:shopOrderLogistics:query")
    @GetMapping("/{id}")
    public R<SohuShopOrderLogisticsVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iPlayletShopOrderLogisticsService.queryById(id));
    }

    /**
     * 新增订单快递记录
     */
    @SaCheckPermission("shoporder:shopOrderLogistics:add")
    @Log(title = "订单快递记录", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuShopOrderLogisticsBo bo) {
        return toAjax(iPlayletShopOrderLogisticsService.insertByBo(bo));
    }

    /**
     * 修改订单快递记录
     */
    @SaCheckPermission("shoporder:shopOrderLogistics:edit")
    @Log(title = "订单快递记录", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuShopOrderLogisticsBo bo) {
        return toAjax(iPlayletShopOrderLogisticsService.updateByBo(bo));
    }

    /**
     * 删除订单快递记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("shoporder:shopOrderLogistics:remove")
    @Log(title = "订单快递记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iPlayletShopOrderLogisticsService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 获取订单物流详情
     *
     * @param orderNo 商家订单号
     */
    @SaCheckPermission("shoporder:shopOrderLogistics:query")
    @GetMapping("/getLogisticsInfo/{orderNo}")
    public R<SohuShopOrderLogistics> getInfo(@NotNull(message = "商家订单号不能为空") @PathVariable String orderNo) {
        return R.ok(iPlayletShopOrderLogisticsService.queryByOrderNo(orderNo));
    }

    /**
     * 获取订单物流详情
     *
     * @param orderNo 商家订单号
     */
    @SaCheckPermission("shoporder:shopOrderLogistics:query")
    @GetMapping("/pc/getLogisticsInfo/{orderNo}")
    public R<SohuShopOrderLogistics> getPcInfo(@NotNull(message = "商家订单号不能为空") @PathVariable String orderNo) {
        return R.ok(iPlayletShopOrderLogisticsService.queryByOrderNo(orderNo));
    }

}
