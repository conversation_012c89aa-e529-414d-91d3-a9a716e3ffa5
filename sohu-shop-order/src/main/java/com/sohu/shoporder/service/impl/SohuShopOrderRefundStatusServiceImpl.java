package com.sohu.shoporder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shoporder.api.bo.SohuShopOrderRefundStatusBo;
import com.sohu.shoporder.api.vo.SohuShopOrderRefundStatusVo;
import com.sohu.shoporder.domain.SohuShopOrderRefundStatus;
import com.sohu.shoporder.mapper.SohuShopOrderRefundStatusMapper;
import com.sohu.shoporder.service.ISohuShopOrderRefundStatusService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 商户订单退款操作记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-29
 */
@RequiredArgsConstructor
@Service
public class SohuShopOrderRefundStatusServiceImpl implements ISohuShopOrderRefundStatusService {

    private final SohuShopOrderRefundStatusMapper baseMapper;

    /**
     * 查询商户订单退款操作记录
     */
    @Override
    public SohuShopOrderRefundStatusVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商户订单退款操作记录列表
     */
    @Override
    public TableDataInfo<SohuShopOrderRefundStatusVo> queryPageList(SohuShopOrderRefundStatusBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuShopOrderRefundStatus> lqw = buildQueryWrapper(bo);
        Page<SohuShopOrderRefundStatusVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询商户订单退款操作记录列表
     */
    @Override
    public List<SohuShopOrderRefundStatusVo> queryList(SohuShopOrderRefundStatusBo bo) {
        LambdaQueryWrapper<SohuShopOrderRefundStatus> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuShopOrderRefundStatus> buildQueryWrapper(SohuShopOrderRefundStatusBo bo) {
//        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuShopOrderRefundStatus> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getRefundOrderNo()), SohuShopOrderRefundStatus::getRefundOrderNo, bo.getRefundOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeType()), SohuShopOrderRefundStatus::getChangeType, bo.getChangeType());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeMessage()), SohuShopOrderRefundStatus::getChangeMessage, bo.getChangeMessage());
        return lqw;
    }

    /**
     * 新增商户订单退款操作记录
     */
    @Override
    public Boolean insertByBo(SohuShopOrderRefundStatusBo bo) {
        SohuShopOrderRefundStatus add = BeanUtil.toBean(bo, SohuShopOrderRefundStatus.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商户订单退款操作记录
     */
    @Override
    public Boolean updateByBo(SohuShopOrderRefundStatusBo bo) {
        SohuShopOrderRefundStatus update = BeanUtil.toBean(bo, SohuShopOrderRefundStatus.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuShopOrderRefundStatus entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商户订单退款操作记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean createLog(String refundOrderNo, String type, String message) {
        SohuShopOrderRefundStatus orderRefundStatus = new SohuShopOrderRefundStatus();
        orderRefundStatus.setRefundOrderNo(refundOrderNo);
        orderRefundStatus.setChangeType(type);
        orderRefundStatus.setChangeMessage(message);
        orderRefundStatus.setCreateTime(new Date());
        return this.baseMapper.insert(orderRefundStatus) > 0;
    }

}
