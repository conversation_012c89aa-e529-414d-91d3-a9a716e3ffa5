package com.sohu.shoporder.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 商户订单状态操作记录对象 sohu_shop_order_status
 *
 * <AUTHOR>
 * @date 2023-06-29
 */
@Data
@TableName("sohu_shop_order_status")
public class SohuShopOrderStatus {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 商户订单号
     */
    private String shopOrderNo;
    /**
     * 操作类型：CREATE-订单生成，CANCEL-订单取消，PAY-支付，EXPRESS-发货，RECEIPT-收货，COMPLETE-完成,rREFUND-退款
     */
    private String changeType;
    /**
     * 操作备注
     */
    private String changeMessage;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime = new Date();

}
