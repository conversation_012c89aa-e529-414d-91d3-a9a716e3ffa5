package com.sohu.shoporder.dubbo;

import cn.hutool.core.bean.BeanUtil;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shoporder.api.RemoteShopRefundOrderService;
import com.sohu.shoporder.api.bo.*;
import com.sohu.shoporder.api.domain.SohuRefundRemarkOrderReqBo;
import com.sohu.shoporder.api.domain.SohuShopRefundQueryReqBo;
import com.sohu.shoporder.api.model.SohuShopRefundOrderModel;
import com.sohu.shoporder.api.vo.*;
import com.sohu.shoporder.domain.SohuShopRefundOrder;
import com.sohu.shoporder.service.IPlayletShopRefundOrderService;
import com.sohu.shoporder.service.ISohuShopRefundOrderService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 退款单服务
 *
 * @author: zc
 * @date: 2023/9/14 10:15
 * @version: 1.0.0
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteShopRefundOrderServiceImpl implements RemoteShopRefundOrderService {

    private final IPlayletShopRefundOrderService playletShopRefundOrderService;
    private final ISohuShopRefundOrderService sohuShopRefundOrderService;

    @Override
    public SohuShopRefundOrderModel getByRefundOrderNo(String refundOrderNo) {
        SohuShopRefundOrder refundOrder = playletShopRefundOrderService.getByRefundOrderNo(refundOrderNo);
        SohuShopRefundOrderModel sohuShopRefundOrderModel = new SohuShopRefundOrderModel();
        BeanUtils.copyProperties(refundOrder, sohuShopRefundOrderModel);
        return sohuShopRefundOrderModel;
    }

    @Override
    public Boolean updateById(SohuShopRefundOrderModel refundOrder) {
        return playletShopRefundOrderService.updateByBo(BeanCopyUtils.copy(refundOrder, SohuShopRefundOrderBo.class));
    }

    @Override
    public List<SohuShopRefundOrderModel> queryListByMasterOrderNo(String masterOrderNo) {
        List<SohuShopRefundOrder> refundOrderList = playletShopRefundOrderService.queryListByMasterOrderNo(masterOrderNo);
        List<SohuShopRefundOrderModel> refundOrderModelList = Lists.newArrayList();
        for (SohuShopRefundOrder refundOrder : refundOrderList) {
            SohuShopRefundOrderModel sohuShopRefundOrderModel = new SohuShopRefundOrderModel();
            BeanUtils.copyProperties(refundOrder, sohuShopRefundOrderModel);
            refundOrderModelList.add(sohuShopRefundOrderModel);
        }
        return refundOrderModelList;
    }

    @Override
    public TableDataInfo<SohuShopOrderRefundVo> queryPageStoreList(SohuShopOrderRefundQueryBo bo, PageQuery pageQuery) {
        SohuShopRefundQueryBo refundQueryBo = BeanCopyUtils.copy(bo, SohuShopRefundQueryBo.class);
        return TableDataInfoUtils.copyInfo(playletShopRefundOrderService.queryPageStoreList(refundQueryBo, pageQuery), SohuShopOrderRefundVo.class);
    }

    @Override
    public SohuShopDelayRefundVo delayRefund(SohuShopSendRefundBo bo) {
//        SohuShopDelayRefundVo sohuShopDelayRefundVo = refundOrderService.delayRefund(BeanCopyUtils.copy(bo, SohuShopSendRefundBo.class));
//        return BeanCopyUtils.copy(sohuShopDelayRefundVo, SohuShopRefundOrderModel.class);
        return this.playletShopRefundOrderService.delayRefund(bo);
    }

    @Override
    public SohuShopDelayRefundVo openDelayRefund(SohuOpenShopSendRefundBo bo) {
        return this.playletShopRefundOrderService.delayRefund(BeanUtil.copyProperties(bo,SohuShopSendRefundBo.class));
    }

    @Override
    public Boolean refund(SohuShopRefundQueryReqBo bo) {
        return playletShopRefundOrderService.refund(BeanCopyUtils.copy(bo, SohuShopSendRefundBo.class));
    }

    @Override
    public Boolean refundRefuse(SohuShopRefundQueryReqBo bo) {
        return playletShopRefundOrderService.refundRefuse(BeanCopyUtils.copy(bo, SohuShopSendRefundBo.class));
    }

    @Override
    public Boolean remark(SohuRefundRemarkOrderReqBo bo) {
        return playletShopRefundOrderService.remark(BeanCopyUtils.copy(bo, SohuRefundRemarkOrderBo.class));
    }

    @Override
    public TableDataInfo<SohuShopOrderRefundVo> queryPagePcList(SohuShopRefundQueryReqBo bo, PageQuery pageQuery) {
        SohuShopRefundQueryBo refundQueryBo = BeanCopyUtils.copy(bo, SohuShopRefundQueryBo.class);
        TableDataInfo<SohuShopRefundOrderVo> dataInfo = playletShopRefundOrderService.queryPagePcList(refundQueryBo, pageQuery);
        return TableDataInfoUtils.copyInfo(dataInfo, SohuShopOrderRefundVo.class);
    }

    @Override
    public SohuShopRefundOrderModel queryById(Long id) {
        return BeanCopyUtils.copy(playletShopRefundOrderService.queryById(id), SohuShopRefundOrderModel.class);
    }

    @Override
    public Boolean deleteById(Long id) {
        return playletShopRefundOrderService.deleteById(id);
    }

    @Override
    public Boolean cancel(Long id) {
        return playletShopRefundOrderService.cancel(id);
    }

    @Override
    public void updateOrderStateSigned() {
        playletShopRefundOrderService.updateRefundOrderListWaitGoods();
    }

    @Override
    public Boolean deleteRefundOrder(String refundOrderNo) {
        return playletShopRefundOrderService.deleteRefundOrder(refundOrderNo);
    }

    @Override
    public void updateRefundOrderListReturnGoods() {
        playletShopRefundOrderService.updateRefundOrderListReturnGoods();
    }

    @Override
    public TableDataInfo<SohuOpenShopRefundOrderVo> openList(SohuOpenShopRefundOrderPageQueryBo bo, PageQuery pageQuery) {
        return sohuShopRefundOrderService.openList(bo,pageQuery);
    }

    @Override
    public SohuOpenShopRefundOrderVo getInfoByRefundOrderNo(String refundOrderNo,Long openClientId) {
        return sohuShopRefundOrderService.getInfoByRefundOrderNo(refundOrderNo,openClientId);
    }

    @Override
    public Boolean openAudit(SohuOpenShopRefundOrderAuditBo bo) {
        return sohuShopRefundOrderService.openAudit(bo);
    }

    @Override
    public SohuShopOrderVo getInfoByOrderNo(String refundOrderNo) {
        return sohuShopRefundOrderService.getInfoByOrderNo(refundOrderNo);
    }

    @Override
    public Long countByRefundStatusList(List<String> refundStatusList) {
        return sohuShopRefundOrderService.countByRefundStatusList(refundStatusList);
    }
}
